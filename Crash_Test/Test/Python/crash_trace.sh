#!/bin/bash

# ========= 配置 =========
SCRIPT="$1"                        # 要运行的 Python 文件（带路径）
LOGFILE="crash_log.txt"           # 输出的崩溃日志
WRAPPER="_wrapped_crash_runner.py"
CALL_CHAIN_FILE="call_chain.txt"  # 调用链输出文件
# ========================

if [ ! -f "$SCRIPT" ]; then
    echo "❌ 指定的 Python 文件不存在: $SCRIPT"
    exit 1
fi

# === 自动创建一个包裹脚本（不修改原脚本）===
SCRIPT_ABS=$(realpath "$SCRIPT")

cat <<EOF > "$WRAPPER"
import faulthandler
import traceback
import runpy

with open("$LOGFILE", "w") as f:
    faulthandler.enable(file=f, all_threads=True)
    try:
        runpy.run_path("$SCRIPT_ABS", run_name="__main__")
    except Exception:
        traceback.print_exc(file=f)
        f.flush()
EOF


# === 运行包裹脚本 ===
echo "🚀 运行 $SCRIPT ..."
python3 "$WRAPPER"

# === 提示 ===
echo "📄 崩溃信息已保存到: $LOGFILE"

# === 处理崩溃日志，提取调用链 ===
if [ -f "$LOGFILE" ] && [ -s "$LOGFILE" ]; then
    echo "🔍 分析崩溃日志，提取调用链..."

    # 创建内联的Python脚本来处理日志
    python3 << 'PYTHON_SCRIPT'
import os
import re

LANGUAGE = "Python"
INPUT_FILE = "crash_log.txt"
OUTPUT_FILE = "call_chain.txt"

call_chain = []

# 读取日志内容
try:
    with open(INPUT_FILE, "r") as f:
        lines = f.readlines()
except FileNotFoundError:
    print(f"❌ 找不到崩溃日志文件: {INPUT_FILE}")
    exit(1)

# 匹配 traceback 中的调用函数名
pattern = re.compile(r'File ".*?", line \d+, in ([\w_]+)')

for line in lines:
    m = pattern.search(line)
    if m:
        func = m.group(1)
        call_chain.append(func)

# 添加起始函数（如果你确定是这样）
if call_chain:
    call_chain.insert(0, "my_test.main")

# 提取最后一行错误类型（如 ZeroDivisionError）
error_message = ""
for line in reversed(lines):
    line = line.strip()
    if ":" in line and not line.startswith(" "):
        # 使用正则提取错误类型（冒号前的部分）
        match = re.match(r"^(\w+Error)", line)
        if match:
            error_message = match.group(1)
        break

# 拼接调用链 + 错误信息
if call_chain:
    output = ";".join(call_chain)
    if error_message:
        output += f";{error_message}"

    # 写入输出文件
    with open(OUTPUT_FILE, "w") as f_out:
        f_out.write(output)

    print(f"📊 调用链已保存到: {OUTPUT_FILE}")
    print(f"🔗 调用链: {output}")
else:
    print("⚠️  未找到有效的调用链信息")
PYTHON_SCRIPT

else
    echo "⚠️  崩溃日志文件为空或不存在，跳过调用链分析"
fi

# === 清理临时文件 ===
if [ -f "$WRAPPER" ]; then
    rm "$WRAPPER"
    echo "🧹 已清理临时文件: $WRAPPER"
fi

echo "✅ 完成！"

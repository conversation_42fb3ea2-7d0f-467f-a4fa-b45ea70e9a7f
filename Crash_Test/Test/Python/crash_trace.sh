#!/bin/bash

# ========= 配置 =========
SCRIPT="$1"                        # 要运行的 Python 文件（带路径）
LOGFILE="crash_log.txt"           # 输出的崩溃日志
WRAPPER="_wrapped_crash_runner.py"
# ========================

if [ ! -f "$SCRIPT" ]; then
    echo "❌ 指定的 Python 文件不存在: $SCRIPT"
    exit 1
fi

# === 自动创建一个包裹脚本（不修改原脚本）===
SCRIPT_ABS=$(realpath "$SCRIPT")

cat <<EOF > "$WRAPPER"
import faulthandler
import traceback
import runpy

with open("$LOGFILE", "w") as f:
    faulthandler.enable(file=f, all_threads=True)
    try:
        runpy.run_path("$SCRIPT_ABS", run_name="__main__")
    except Exception:
        traceback.print_exc(file=f)
        f.flush()
EOF


# === 运行包裹脚本 ===
echo "🚀 运行 $SCRIPT ..."
python3 "$WRAPPER"

# === 提示 ===
echo "📄 崩溃信息已保存到: $LOGFILE"

import os
import re

LANGUAGE = "Python"
INPUT_FILE = os.path.join("crash_log.txt")
OUTPUT_FILE = os.path.join("call_chain.txt")

call_chain = []

# 读取日志内容
with open(INPUT_FILE, "r") as f:
    lines = f.readlines()

# 匹配 traceback 中的调用函数名
pattern = re.compile(r'File ".*?", line \d+, in ([\w_]+)')

for line in lines:
    m = pattern.search(line)
    if m:
        func = m.group(1)
        call_chain.append(func)

# 添加起始函数（如果你确定是这样）
call_chain.insert(0, "my_test.main")

# 提取最后一行错误类型（如 ZeroDivisionError）
error_message = ""
for line in reversed(lines):
    line = line.strip()
    if ":" in line and not line.startswith(" "):
        # 使用正则提取错误类型（冒号前的部分）
        match = re.match(r"^(\w+Error)", line)
        if match:
            error_message = match.group(1)
        break


# 拼接调用链 + 错误信息
output = ";".join(call_chain)
if error_message:
    output += f";{error_message}"

# 写入输出文件
with open(OUTPUT_FILE, "w") as f_out:
    f_out.write(output)

print(f"调用链已保存到: {OUTPUT_FILE}")

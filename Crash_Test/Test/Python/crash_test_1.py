import time

def func_level_10():
    x = 1
    y = 0
    time.sleep(2)
    print("开始除零操作...")
    z = x / y  # 这里崩溃
    return z

def func_level_9():
    return func_level_10()

def func_level_8():
    return func_level_9()

def func_level_7():
    return func_level_8()

def func_level_6():
    return func_level_7()

def func_level_5():
    return func_level_6()

def func_level_4():
    return func_level_5()

def func_level_3():
    return func_level_4()

def func_level_2():
    return func_level_3()

def func_level_1():
    return func_level_2()

if __name__ == "__main__":
    func_level_1()

#include <iostream>
#include <vector>
#include <new>

void func_level_3() {
    std::cout << "进入 func_level_3()，准备耗尽内存..." << std::endl;
    
    std::vector<char*> memory_blocks;
    size_t block_size = 100 * 1024 * 1024;  // 100MB per block
    int block_count = 0;
    
    try {
        while (true) {
            std::cout << "分配内存块 " << block_count << " (100MB)..." << std::endl;
            
            char* ptr = new char[block_size];  // 崩溃点：最终会导致OOM
            
            // 写入一些数据确保内存真正被分配
            for (size_t i = 0; i < block_size; i += 4096) {
                ptr[i] = static_cast<char>(block_count % 256);
            }
            
            memory_blocks.push_back(ptr);
            block_count++;
            
            std::cout << "已分配 " << block_count << " 个内存块，总计 " 
                      << (block_count * 100) << "MB" << std::endl;
            
            // 防止程序运行太久，设置一个上限
            if (block_count > 100) {  // 最多尝试分配10GB
                std::cout << "达到分配上限，停止分配" << std::endl;
                break;
            }
        }
    } catch (const std::bad_alloc& e) {
        std::cout << "内存分配失败：" << e.what() << std::endl;
        std::cout << "在分配第 " << block_count << " 个内存块时失败" << std::endl;
        throw;  // 重新抛出异常
    }
    
    std::cout << "不会执行到这行" << std::endl;
}

void func_level_2() {
    std::cout << "进入 func_level_2()" << std::endl;
    func_level_3();
}

void func_level_1() {
    std::cout << "进入 func_level_1()" << std::endl;
    func_level_2();
}

int main() {
    std::cout << "程序开始运行..." << std::endl;
    try {
        func_level_1();
    } catch (const std::exception& e) {
        std::cout << "捕获异常：" << e.what() << std::endl;
        return 1;
    }
    std::cout << "程序结束（不会执行到这里）" << std::endl;
    return 0;
}

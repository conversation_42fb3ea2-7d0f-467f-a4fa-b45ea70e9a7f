#!/bin/bash

# 测试所有崩溃类型的脚本

echo "🧪 开始测试所有C++崩溃类型..."
echo "=================================="

# 崩溃测试文件列表
crash_tests=(
    "null_pointer.cpp"
    "array_bounds.cpp"
    "heap_overflow.cpp"
    "stack_overflow.cpp"
    "use_after_free.cpp"
    "double_free.cpp"
    "division_by_zero.cpp"
    "infinite_recursion.cpp"
    "integer_overflow.cpp"
    "wild_pointer.cpp"
    "memory_leak.cpp"
    "dangling_pointer.cpp"
    "type_confusion.cpp"
)

# 测试每个崩溃类型
for test_file in "${crash_tests[@]}"; do
    if [ -f "$test_file" ]; then
        echo ""
        echo "🔍 测试: $test_file"
        echo "-------------------"
        ./ASan.sh "$test_file"
        echo ""
        echo "=================================="
    else
        echo "⚠️  文件不存在: $test_file"
    fi
done

echo ""
echo "✅ 所有测试完成！"
echo ""
echo "📊 生成的调用链文件："
find . -name "*_call_chain.txt" -type f | sort
echo ""
echo "📄 生成的崩溃日志文件："
find . -name "*_crash_log.txt" -type f | sort

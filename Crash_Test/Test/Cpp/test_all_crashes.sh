#!/bin/bash

# ========= C++ 崩溃测试工具选择器 =========

echo "🧪 C++ 崩溃测试工具选择器"
echo "=================================="

# 可用的测试工具
declare -A TOOLS
TOOLS["asan"]="ASan/ASan.sh"
TOOLS["valgrind"]="Valgrind/valgrind.sh"
TOOLS["gdb"]="GDB/gdb.sh"

# 测试文件列表
TEST_FILES=(
    "normal_execution.cpp"
    "null_pointer.cpp"
    "deadlock.cpp"
    "memory_leak.cpp"
    "out_of_memory.cpp"
    "buffer_overflow.cpp"
)

# 显示可用工具
show_tools() {
    echo "📋 可用的分析工具："
    echo "  1. asan      - AddressSanitizer (内存错误检测)"
    echo "  2. valgrind  - Valgrind (内存泄漏和错误检测)"
    echo "  3. gdb       - GDB (调试器分析)"
    echo "  4. all       - 运行所有工具"
    echo ""
}

# 显示测试文件
show_test_files() {
    echo "📋 可用的测试文件："
    for i in "${!TEST_FILES[@]}"; do
        echo "  $((i+1)). ${TEST_FILES[$i]}"
    done
    echo "  0. 全部测试"
    echo ""
}

# 运行指定工具
run_tool() {
    local tool="$1"
    local test_file="$2"

    if [ ! -f "${TOOLS[$tool]}" ]; then
        echo "❌ 工具脚本不存在: ${TOOLS[$tool]}"
        return 1
    fi

    if [ ! -f "$test_file" ]; then
        echo "❌ 测试文件不存在: $test_file"
        return 1
    fi

    echo "🔍 使用 $tool 分析: $test_file"
    echo "-------------------"

    # 进入工具目录并运行
    tool_dir=$(dirname "${TOOLS[$tool]}")
    tool_script=$(basename "${TOOLS[$tool]}")

    (cd "$tool_dir" && ./"$tool_script" "../$test_file")

    echo ""
}

# 主函数
main() {
    # 检查参数
    if [ $# -eq 0 ]; then
        show_tools
        echo -n "请选择工具 (asan/valgrind/gdb/all): "
        read SELECTED_TOOL
    else
        SELECTED_TOOL="$1"
    fi

    # 验证工具选择
    if [ "$SELECTED_TOOL" != "all" ] && [ -z "${TOOLS[$SELECTED_TOOL]}" ]; then
        echo "❌ 无效的工具选择: $SELECTED_TOOL"
        show_tools
        exit 1
    fi

    # 选择测试文件
    if [ $# -lt 2 ]; then
        show_test_files
        echo -n "请选择测试文件 (1-${#TEST_FILES[@]}/0): "
        read FILE_CHOICE
    else
        FILE_CHOICE="$2"
    fi

    echo ""
    echo "=================================="

    # 执行测试
    if [ "$SELECTED_TOOL" = "all" ]; then
        # 运行所有工具
        for tool in "${!TOOLS[@]}"; do
            echo ""
            echo "🛠️  使用工具: $tool"
            echo "=================================="

            if [ "$FILE_CHOICE" = "0" ]; then
                # 测试所有文件
                for test_file in "${TEST_FILES[@]}"; do
                    run_tool "$tool" "$test_file"
                done
            else
                # 测试指定文件
                if [ "$FILE_CHOICE" -ge 1 ] && [ "$FILE_CHOICE" -le "${#TEST_FILES[@]}" ]; then
                    test_file="${TEST_FILES[$((FILE_CHOICE-1))]}"
                    run_tool "$tool" "$test_file"
                else
                    echo "❌ 无效的文件选择: $FILE_CHOICE"
                    exit 1
                fi
            fi
        done
    else
        # 运行指定工具
        if [ "$FILE_CHOICE" = "0" ]; then
            # 测试所有文件
            for test_file in "${TEST_FILES[@]}"; do
                run_tool "$SELECTED_TOOL" "$test_file"
            done
        else
            # 测试指定文件
            if [ "$FILE_CHOICE" -ge 1 ] && [ "$FILE_CHOICE" -le "${#TEST_FILES[@]}" ]; then
                test_file="${TEST_FILES[$((FILE_CHOICE-1))]}"
                run_tool "$SELECTED_TOOL" "$test_file"
            else
                echo "❌ 无效的文件选择: $FILE_CHOICE"
                exit 1
            fi
        fi
    fi

    echo ""
    echo "✅ 测试完成！"
    echo ""
    echo "📊 生成的结果文件："
    find . -name "*_call_chain.txt" -o -name "*_crash_log.txt" | sort
}

# 显示用法
usage() {
    echo "用法: $0 [工具] [文件编号]"
    echo ""
    show_tools
    show_test_files
    echo "示例:"
    echo "  $0 asan 1          # 使用ASan测试null_pointer.cpp"
    echo "  $0 all 0           # 使用所有工具测试所有文件"
    echo "  $0                 # 交互式选择"
}

# 检查帮助参数
if [ "$1" = "-h" ] || [ "$1" = "--help" ]; then
    usage
    exit 0
fi

# 运行主函数
main "$@"

=== 程序开始执行 ===
时间: 2025年 07月 13日 星期日 14:09:14 CST
可执行文件: ./buffer_overflow/buffer_overflow_debug
ASan选项: abort_on_error=1:print_stacktrace=1:symbolize=1:halt_on_error=1
==========================

程序开始运行...
进入 func_level_1()
进入 func_level_2()
进入 func_level_3()，准备缓冲区溢出...
缓冲区大小: 10 字节
要复制的字符串长度: 69 字节
准备复制字符串到缓冲区...
=================================================================
==1551762==ERROR: AddressSanitizer: stack-buffer-overflow on address 0x70812eb0002a at pc 0x708131ca7923 bp 0x7ffe1b9fa890 sp 0x7ffe1b9fa038
WRITE of size 70 at 0x70812eb0002a thread T0
    #0 0x708131ca7922 in strcpy ../../../../src/libsanitizer/asan/asan_interceptors.cpp:563
    #1 0x6421b393cb91 in func_level_3() /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/buffer_overflow.cpp:14
    #2 0x6421b393ce95 in func_level_2() /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/buffer_overflow.cpp:21
    #3 0x6421b393cff3 in func_level_1() /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/buffer_overflow.cpp:26
    #4 0x6421b393d151 in main /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/buffer_overflow.cpp:31
    #5 0x708130c2a1c9 in __libc_start_call_main ../sysdeps/nptl/libc_start_call_main.h:58
    #6 0x708130c2a28a in __libc_start_main_impl ../csu/libc-start.c:360
    #7 0x6421b393c244 in _start (/home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/buffer_overflow/buffer_overflow_debug+0x3244) (BuildId: 327f865d521281b202a0f9e8996d9645d2b3c283)

Address 0x70812eb0002a is located in stack of thread T0 at offset 42 in frame
    #0 0x6421b393c318 in func_level_3() /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/buffer_overflow.cpp:4

  This frame has 1 object(s):
    [32, 42) 'buffer' (line 7) <== Memory access at offset 42 overflows this variable
HINT: this may be a false positive if your program uses some custom stack unwind mechanism, swapcontext or vfork
      (longjmp and C++ exceptions *are* supported)
SUMMARY: AddressSanitizer: stack-buffer-overflow ../../../../src/libsanitizer/asan/asan_interceptors.cpp:563 in strcpy
Shadow bytes around the buggy address:
  0x70812eaffd80: 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
  0x70812eaffe00: 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
  0x70812eaffe80: 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
  0x70812eafff00: 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
  0x70812eafff80: 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
=>0x70812eb00000: f1 f1 f1 f1 00[02]f3 f3 00 00 00 00 00 00 00 00
  0x70812eb00080: 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
  0x70812eb00100: 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
  0x70812eb00180: 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
  0x70812eb00200: 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
  0x70812eb00280: 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
Shadow byte legend (one shadow byte represents 8 application bytes):
  Addressable:           00
  Partially addressable: 01 02 03 04 05 06 07 
  Heap left redzone:       fa
  Freed heap region:       fd
  Stack left redzone:      f1
  Stack mid redzone:       f2
  Stack right redzone:     f3
  Stack after return:      f5
  Stack use after scope:   f8
  Global redzone:          f9
  Global init order:       f6
  Poisoned by user:        f7
  Container overflow:      fc
  Array cookie:            ac
  Intra object redzone:    bb
  ASan internal:           fe
  Left alloca redzone:     ca
  Right alloca redzone:    cb
==1551762==ABORTING
timeout: 被监视的命令已核心转储
./ASan.sh: 第 69 行： 1551761 已放弃               timeout 30s "$EXECUTABLE" 2>&1

==========================
程序退出码: 134
时间: 2025年 07月 13日 星期日 14:09:15 CST
=== 程序执行结束 ===

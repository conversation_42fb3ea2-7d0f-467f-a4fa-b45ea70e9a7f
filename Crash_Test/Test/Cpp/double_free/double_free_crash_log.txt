=== 程序开始执行 ===
时间: 2025年 07月 13日 星期日 02:58:58 CST
可执行文件: ./double_free/double_free_debug
ASan选项: abort_on_error=1:print_stacktrace=1:symbolize=1:halt_on_error=1
==========================

程序开始运行...
进入 func_level_1()
进入 func_level_2()
进入 func_level_3()，准备双重释放...
分配内存并设置值: 42
第一次释放内存
=================================================================
==1390153==ERROR: AddressSanitizer: attempting double-free on 0x502000000010 in thread T0:
    #0 0x76bfd70ff5e8 in operator delete(void*, unsigned long) ../../../../src/libsanitizer/asan/asan_new_delete.cpp:164
    #1 0x6127fbb248cb in func_level_3() /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/double_free.cpp:9
    #2 0x6127fbb24b6e in func_level_2() /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/double_free.cpp:15
    #3 0x6127fbb24ccc in func_level_1() /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/double_free.cpp:20
    #4 0x6127fbb24e2a in main /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/double_free.cpp:25
    #5 0x76bfd602a1c9 in __libc_start_call_main ../sysdeps/nptl/libc_start_call_main.h:58
    #6 0x76bfd602a28a in __libc_start_main_impl ../csu/libc-start.c:360
    #7 0x6127fbb24224 in _start (/home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/double_free/double_free_debug+0x3224) (BuildId: 200107dceb8222ec9ce95fd49301c6e622e9cfea)

0x502000000010 is located 0 bytes inside of 4-byte region [0x502000000010,0x502000000014)
freed by thread T0 here:
    #0 0x76bfd70ff5e8 in operator delete(void*, unsigned long) ../../../../src/libsanitizer/asan/asan_new_delete.cpp:164
    #1 0x6127fbb24770 in func_level_3() /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/double_free.cpp:7
    #2 0x6127fbb24b6e in func_level_2() /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/double_free.cpp:15
    #3 0x6127fbb24ccc in func_level_1() /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/double_free.cpp:20
    #4 0x6127fbb24e2a in main /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/double_free.cpp:25
    #5 0x76bfd602a1c9 in __libc_start_call_main ../sysdeps/nptl/libc_start_call_main.h:58
    #6 0x76bfd602a28a in __libc_start_main_impl ../csu/libc-start.c:360
    #7 0x6127fbb24224 in _start (/home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/double_free/double_free_debug+0x3224) (BuildId: 200107dceb8222ec9ce95fd49301c6e622e9cfea)

previously allocated by thread T0 here:
    #0 0x76bfd70fe548 in operator new(unsigned long) ../../../../src/libsanitizer/asan/asan_new_delete.cpp:95
    #1 0x6127fbb24444 in func_level_3() /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/double_free.cpp:5
    #2 0x6127fbb24b6e in func_level_2() /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/double_free.cpp:15
    #3 0x6127fbb24ccc in func_level_1() /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/double_free.cpp:20
    #4 0x6127fbb24e2a in main /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/double_free.cpp:25
    #5 0x76bfd602a1c9 in __libc_start_call_main ../sysdeps/nptl/libc_start_call_main.h:58
    #6 0x76bfd602a28a in __libc_start_main_impl ../csu/libc-start.c:360
    #7 0x6127fbb24224 in _start (/home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/double_free/double_free_debug+0x3224) (BuildId: 200107dceb8222ec9ce95fd49301c6e622e9cfea)

SUMMARY: AddressSanitizer: double-free ../../../../src/libsanitizer/asan/asan_new_delete.cpp:164 in operator delete(void*, unsigned long)
==1390153==ABORTING
timeout: 被监视的命令已核心转储
./ASan.sh: 第 65 行： 1390152 已放弃               timeout 30s "$EXECUTABLE" 2>&1

==========================
程序退出码: 134
时间: 2025年 07月 13日 星期日 02:58:59 CST
=== 程序执行结束 ===

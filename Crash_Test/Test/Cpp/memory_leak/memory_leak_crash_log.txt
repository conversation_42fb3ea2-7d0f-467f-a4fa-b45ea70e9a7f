=== 程序开始执行 ===
时间: 2025年 07月 13日 星期日 03:12:58 CST
可执行文件: ./memory_leak/memory_leak_debug
ASan选项: abort_on_error=1:print_stacktrace=1:symbolize=1:halt_on_error=1
==========================

程序开始运行...
进入 func_level_1()
进入 func_level_2()
进入 func_level_3()，准备内存泄漏...
分配内存块 0, 地址: 0x521000001500
分配内存块 100, 地址: 0x521000085d00
分配内存块 200, 地址: 0x5210000fb500
分配内存块 300, 地址: 0x521000182500
分配内存块 400, 地址: 0x5210001f6900
分配内存块 500, 地址: 0x52100027c500
分配内存块 600, 地址: 0x5210002f1d00
分配内存块 700, 地址: 0x521000377900
分配内存块 800, 地址: 0x5210003ebd00
分配内存块 900, 地址: 0x521000471900
内存泄漏完成，分配了 1000 个内存块
程序结束（会有内存泄漏报告）

=================================================================
==1394538==ERROR: LeakSanitizer: detected memory leaks

Direct leak of 4000000 byte(s) in 1000 object(s) allocated from:
    #0 0x74d19defe6c8 in operator new[](unsigned long) ../../../../src/libsanitizer/asan/asan_new_delete.cpp:98
    #1 0x5b36fa3e96ee in func_level_3() /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/memory_leak.cpp:11
    #2 0x5b36fa3ea04e in func_level_2() /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/memory_leak.cpp:27
    #3 0x5b36fa3ea1ac in func_level_1() /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/memory_leak.cpp:32
    #4 0x5b36fa3ea30a in main /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/memory_leak.cpp:37
    #5 0x74d19ce2a1c9 in __libc_start_call_main ../sysdeps/nptl/libc_start_call_main.h:58
    #6 0x74d19ce2a28a in __libc_start_main_impl ../csu/libc-start.c:360
    #7 0x5b36fa3e9404 in _start (/home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/memory_leak/memory_leak_debug+0x8404) (BuildId: 5469bd13fc1f0c406ee8e5082a8ef5745e593976)

SUMMARY: AddressSanitizer: 4000000 byte(s) leaked in 1000 allocation(s).
timeout: 被监视的命令已核心转储
./ASan.sh: 第 65 行： 1394537 已放弃               timeout 30s "$EXECUTABLE" 2>&1

==========================
程序退出码: 134
时间: 2025年 07月 13日 星期日 03:13:00 CST
=== 程序执行结束 ===

=== 程序开始执行 ===
时间: 2025年 07月 13日 星期日 02:59:06 CST
可执行文件: ./dangling_pointer/dangling_pointer_debug
ASan选项: abort_on_error=1:print_stacktrace=1:symbolize=1:halt_on_error=1
==========================

程序开始运行...
进入 func_level_1()
进入 func_level_2()
进入 func_level_3()，准备使用悬空指针...
创建局部变量，值: 42
悬空指针地址: 0
尝试访问悬空指针...
dangling_pointer.cpp:14:9: runtime error: load of null pointer of type 'int'
    #0 0x57c4f14dabbc in func_level_3() /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/dangling_pointer.cpp:14
    #1 0x57c4f14dafa6 in func_level_2() /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/dangling_pointer.cpp:20
    #2 0x57c4f14db104 in func_level_1() /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/dangling_pointer.cpp:25
    #3 0x57c4f14db262 in main /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/dangling_pointer.cpp:30
    #4 0x73012f02a1c9 in __libc_start_call_main ../sysdeps/nptl/libc_start_call_main.h:58
    #5 0x73012f02a28a in __libc_start_main_impl ../csu/libc-start.c:360
    #6 0x57c4f14da244 in _start (/home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/dangling_pointer/dangling_pointer_debug+0x3244) (BuildId: 44ac4970bc8fc8015a832fc9cddf62ff18fb4d0f)

AddressSanitizer:DEADLYSIGNAL
=================================================================
==1390351==ERROR: AddressSanitizer: SEGV on unknown address 0x000000000000 (pc 0x57c4f14dabf8 bp 0x7ffc6ae332b0 sp 0x7ffc6ae33290 T0)
==1390351==The signal is caused by a READ memory access.
==1390351==Hint: address points to the zero page.
    #0 0x57c4f14dabf8 in func_level_3() /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/dangling_pointer.cpp:14
    #1 0x57c4f14dafa6 in func_level_2() /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/dangling_pointer.cpp:20
    #2 0x57c4f14db104 in func_level_1() /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/dangling_pointer.cpp:25
    #3 0x57c4f14db262 in main /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/dangling_pointer.cpp:30
    #4 0x73012f02a1c9 in __libc_start_call_main ../sysdeps/nptl/libc_start_call_main.h:58
    #5 0x73012f02a28a in __libc_start_main_impl ../csu/libc-start.c:360
    #6 0x57c4f14da244 in _start (/home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/dangling_pointer/dangling_pointer_debug+0x3244) (BuildId: 44ac4970bc8fc8015a832fc9cddf62ff18fb4d0f)

AddressSanitizer can not provide additional info.
SUMMARY: AddressSanitizer: SEGV /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/dangling_pointer.cpp:14 in func_level_3()
==1390351==ABORTING
timeout: 被监视的命令已核心转储
./ASan.sh: 第 65 行： 1390350 已放弃               timeout 30s "$EXECUTABLE" 2>&1

==========================
程序退出码: 134
时间: 2025年 07月 13日 星期日 02:59:07 CST
=== 程序执行结束 ===

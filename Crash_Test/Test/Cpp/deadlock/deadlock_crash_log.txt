=== 程序开始执行 ===
时间: 2025年 07月 13日 星期日 03:12:28 CST
可执行文件: ./deadlock/deadlock_debug
ASan选项: abort_on_error=1:print_stacktrace=1:symbolize=1:halt_on_error=1
==========================

程序开始运行...
进入 func_level_1()
进入 func_level_2()，准备创建死锁...
等待线程完成（将会死锁）...
线程2：尝试获取mutex2...
线程2：获取到mutex2，休眠1秒...
线程1：尝试获取mutex1...
线程1：获取到mutex1，休眠1秒...
线程2：尝试获取mutex1...线程1：尝试获取mutex2...


==========================
程序退出码: 124
时间: 2025年 07月 13日 星期日 03:12:58 CST
=== 程序执行结束 ===

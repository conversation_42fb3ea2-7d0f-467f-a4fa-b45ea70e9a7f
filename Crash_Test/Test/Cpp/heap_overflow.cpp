#include <iostream>
#include <cstring>

void func_level_3() {
    std::cout << "进入 func_level_3()，准备堆缓冲区溢出..." << std::endl;
    char* buffer = new char[10];
    strcpy(buffer, "这是一个很长的字符串，会导致堆缓冲区溢出！");  // 崩溃点
    std::cout << "不会执行到这行: " << buffer << std::endl;
    delete[] buffer;
}

void func_level_2() {
    std::cout << "进入 func_level_2()" << std::endl;
    func_level_3();
}

void func_level_1() {
    std::cout << "进入 func_level_1()" << std::endl;
    func_level_2();
}

int main() {
    std::cout << "程序开始运行..." << std::endl;
    func_level_1();
    std::cout << "程序结束（不会执行到这里）" << std::endl;
    return 0;
}

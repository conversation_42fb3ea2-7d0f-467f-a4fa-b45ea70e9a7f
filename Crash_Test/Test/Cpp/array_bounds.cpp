#include <iostream>

void func_level_4() {
    std::cout << "进入 func_level_4()，准备数组越界访问..." << std::endl;
    int arr[5] = {1, 2, 3, 4, 5};
    std::cout << "数组大小: 5，准备访问索引 10..." << std::endl;
    int value = arr[10];  // 崩溃点：数组越界访问
    std::cout << "不会执行到这行，value: " << value << std::endl;
}

void func_level_3() {
    std::cout << "进入 func_level_3()" << std::endl;
    func_level_4();
}

void func_level_2() {
    std::cout << "进入 func_level_2()" << std::endl;
    func_level_3();
}

void func_level_1() {
    std::cout << "进入 func_level_1()" << std::endl;
    func_level_2();
}

int main() {
    std::cout << "程序开始运行..." << std::endl;
    func_level_1();
    std::cout << "程序结束（不会执行到这里）" << std::endl;
    return 0;
}

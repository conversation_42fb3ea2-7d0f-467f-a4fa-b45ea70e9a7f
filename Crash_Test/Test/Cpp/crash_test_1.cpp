#include <iostream>

void func_level_5() {
    std::cout << "进入 func_level_5()" << std::endl;
    int* ptr = nullptr;
    std::cout << "准备在 func_level_5() 访问空指针..." << std::endl;
    int value = *ptr;  // 崩溃点
    std::cout << "不会执行到这行，value: " << value << std::endl;
}

void func_level_4() {
    std::cout << "进入 func_level_4()" << std::endl;
    func_level_5();
}

void func_level_3() {
    std::cout << "进入 func_level_3()" << std::endl;
    func_level_4();
}

void func_level_2() {
    std::cout << "进入 func_level_2()" << std::endl;
    func_level_3();
}

void func_level_1() {
    std::cout << "进入 func_level_1()" << std::endl;
    func_level_2();
}

int main() {
    std::cout << "程序开始运行..." << std::endl;
    func_level_1();
    std::cout << "程序结束（不会执行到这里）" << std::endl;
    return 0;
}

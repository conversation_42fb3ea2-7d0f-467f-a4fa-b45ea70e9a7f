#include <iostream>
#include <climits>

void func_level_3() {
    std::cout << "进入 func_level_3()，准备整数溢出..." << std::endl;
    int max_int = INT_MAX;
    std::cout << "当前最大整数值: " << max_int << std::endl;
    std::cout << "准备加1..." << std::endl;
    int overflow = max_int + 1;  // 崩溃点：整数溢出
    std::cout << "溢出后的值: " << overflow << std::endl;
    
    // 更明显的溢出：数组索引溢出
    int arr[5] = {1, 2, 3, 4, 5};
    int index = overflow;  // 使用溢出的值作为索引
    std::cout << "尝试访问数组[" << index << "]" << std::endl;
    int value = arr[index];  // 这里会导致内存访问错误
    std::cout << "不会执行到这行，value: " << value << std::endl;
}

void func_level_2() {
    std::cout << "进入 func_level_2()" << std::endl;
    func_level_3();
}

void func_level_1() {
    std::cout << "进入 func_level_1()" << std::endl;
    func_level_2();
}

int main() {
    std::cout << "程序开始运行..." << std::endl;
    func_level_1();
    std::cout << "程序结束（不会执行到这里）" << std::endl;
    return 0;
}

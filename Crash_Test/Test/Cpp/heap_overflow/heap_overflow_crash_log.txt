=== 程序开始执行 ===
时间: 2025年 07月 13日 星期日 02:58:54 CST
可执行文件: ./heap_overflow/heap_overflow_debug
ASan选项: abort_on_error=1:print_stacktrace=1:symbolize=1:halt_on_error=1
==========================

程序开始运行...
进入 func_level_1()
进入 func_level_2()
进入 func_level_3()，准备堆缓冲区溢出...
=================================================================
==1390074==ERROR: AddressSanitizer: heap-buffer-overflow on address 0x50200000001a at pc 0x71ecb98fb303 bp 0x7ffcaeacb4a0 sp 0x7ffcaeacac48
WRITE of size 64 at 0x50200000001a thread T0
    #0 0x71ecb98fb302 in memcpy ../../../../src/libsanitizer/sanitizer_common/sanitizer_common_interceptors_memintrinsics.inc:115
    #1 0x5bdc0635c459 in func_level_3() /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/heap_overflow.cpp:7
    #2 0x5bdc0635c721 in func_level_2() /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/heap_overflow.cpp:14
    #3 0x5bdc0635c87f in func_level_1() /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/heap_overflow.cpp:19
    #4 0x5bdc0635c9dd in main /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/heap_overflow.cpp:24
    #5 0x71ecb882a1c9 in __libc_start_call_main ../sysdeps/nptl/libc_start_call_main.h:58
    #6 0x71ecb882a28a in __libc_start_main_impl ../csu/libc-start.c:360
    #7 0x5bdc0635c204 in _start (/home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/heap_overflow/heap_overflow_debug+0x2204) (BuildId: 21fdf5689503fbb7095ee95e2a3bac4f55d09808)

0x50200000001a is located 0 bytes after 10-byte region [0x502000000010,0x50200000001a)
allocated by thread T0 here:
    #0 0x71ecb98fe6c8 in operator new[](unsigned long) ../../../../src/libsanitizer/asan/asan_new_delete.cpp:98
    #1 0x5bdc0635c424 in func_level_3() /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/heap_overflow.cpp:6
    #2 0x5bdc0635c721 in func_level_2() /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/heap_overflow.cpp:14
    #3 0x5bdc0635c87f in func_level_1() /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/heap_overflow.cpp:19
    #4 0x5bdc0635c9dd in main /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/heap_overflow.cpp:24
    #5 0x71ecb882a1c9 in __libc_start_call_main ../sysdeps/nptl/libc_start_call_main.h:58
    #6 0x71ecb882a28a in __libc_start_main_impl ../csu/libc-start.c:360
    #7 0x5bdc0635c204 in _start (/home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/heap_overflow/heap_overflow_debug+0x2204) (BuildId: 21fdf5689503fbb7095ee95e2a3bac4f55d09808)

SUMMARY: AddressSanitizer: heap-buffer-overflow ../../../../src/libsanitizer/sanitizer_common/sanitizer_common_interceptors_memintrinsics.inc:115 in memcpy
Shadow bytes around the buggy address:
  0x501ffffffd80: 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
  0x501ffffffe00: 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
  0x501ffffffe80: 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
  0x501fffffff00: 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
  0x501fffffff80: 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
=>0x502000000000: fa fa 00[02]fa fa fa fa fa fa fa fa fa fa fa fa
  0x502000000080: fa fa fa fa fa fa fa fa fa fa fa fa fa fa fa fa
  0x502000000100: fa fa fa fa fa fa fa fa fa fa fa fa fa fa fa fa
  0x502000000180: fa fa fa fa fa fa fa fa fa fa fa fa fa fa fa fa
  0x502000000200: fa fa fa fa fa fa fa fa fa fa fa fa fa fa fa fa
  0x502000000280: fa fa fa fa fa fa fa fa fa fa fa fa fa fa fa fa
Shadow byte legend (one shadow byte represents 8 application bytes):
  Addressable:           00
  Partially addressable: 01 02 03 04 05 06 07 
  Heap left redzone:       fa
  Freed heap region:       fd
  Stack left redzone:      f1
  Stack mid redzone:       f2
  Stack right redzone:     f3
  Stack after return:      f5
  Stack use after scope:   f8
  Global redzone:          f9
  Global init order:       f6
  Poisoned by user:        f7
  Container overflow:      fc
  Array cookie:            ac
  Intra object redzone:    bb
  ASan internal:           fe
  Left alloca redzone:     ca
  Right alloca redzone:    cb
==1390074==ABORTING
timeout: 被监视的命令已核心转储
./ASan.sh: 第 65 行： 1390073 已放弃               timeout 30s "$EXECUTABLE" 2>&1

==========================
程序退出码: 134
时间: 2025年 07月 13日 星期日 02:58:55 CST
=== 程序执行结束 ===

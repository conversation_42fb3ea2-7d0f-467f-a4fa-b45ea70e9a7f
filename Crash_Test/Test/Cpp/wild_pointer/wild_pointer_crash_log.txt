=== 程序开始执行 ===
时间: 2025年 07月 13日 星期日 02:59:03 CST
可执行文件: ./wild_pointer/wild_pointer_debug
ASan选项: abort_on_error=1:print_stacktrace=1:symbolize=1:halt_on_error=1
==========================

程序开始运行...
进入 func_level_1()
进入 func_level_2()
进入 func_level_3()，准备访问野指针...
野指针地址: 0x6c508a260cd2cd00
AddressSanitizer:DEADLYSIGNAL
=================================================================
==1390290==ERROR: AddressSanitizer: SEGV on unknown address (pc 0x5ac973ead683 bp 0x7ffd65daf130 sp 0x7ffd65daf110 T0)
==1390290==The signal is caused by a READ memory access.
==1390290==Hint: this fault was caused by a dereference of a high value address (see register values below).  Disassemble the provided pc to learn which register was used.
    #0 0x5ac973ead683 in func_level_3() /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/wild_pointer.cpp:7
    #1 0x5ac973eada5a in func_level_2() /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/wild_pointer.cpp:13
    #2 0x5ac973eadbb8 in func_level_1() /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/wild_pointer.cpp:18
    #3 0x5ac973eadd16 in main /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/wild_pointer.cpp:23
    #4 0x78bdf3c2a1c9 in __libc_start_call_main ../sysdeps/nptl/libc_start_call_main.h:58
    #5 0x78bdf3c2a28a in __libc_start_main_impl ../csu/libc-start.c:360
    #6 0x5ac973ead1e4 in _start (/home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/wild_pointer/wild_pointer_debug+0x31e4) (BuildId: a2df5c6781ebb6d900abfb8d95b291a6dd9a5acb)

AddressSanitizer can not provide additional info.
SUMMARY: AddressSanitizer: SEGV /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/wild_pointer.cpp:7 in func_level_3()
==1390290==ABORTING
timeout: 被监视的命令已核心转储
./ASan.sh: 第 65 行： 1390289 已放弃               timeout 30s "$EXECUTABLE" 2>&1

==========================
程序退出码: 134
时间: 2025年 07月 13日 星期日 02:59:04 CST
=== 程序执行结束 ===

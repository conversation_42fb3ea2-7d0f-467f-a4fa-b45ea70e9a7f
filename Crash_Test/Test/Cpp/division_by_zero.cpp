#include <iostream>

void func_level_3() {
    std::cout << "进入 func_level_3()，准备除零操作..." << std::endl;
    int a = 10;
    int b = 0;
    std::cout << "准备计算 " << a << " / " << b << std::endl;
    int result = a / b;  // 崩溃点：除零错误
    std::cout << "不会执行到这行，result: " << result << std::endl;
}

void func_level_2() {
    std::cout << "进入 func_level_2()" << std::endl;
    func_level_3();
}

void func_level_1() {
    std::cout << "进入 func_level_1()" << std::endl;
    func_level_2();
}

int main() {
    std::cout << "程序开始运行..." << std::endl;
    func_level_1();
    std::cout << "程序结束（不会执行到这里）" << std::endl;
    return 0;
}

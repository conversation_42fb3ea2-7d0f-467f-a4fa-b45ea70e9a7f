#include <iostream>

int recursion_count = 0;

void infinite_recursion() {
    recursion_count++;
    std::cout << "递归调用次数: " << recursion_count << std::endl;
    
    // 添加一些局部变量来快速消耗栈空间
    char buffer[1024];
    buffer[0] = 'a';
    
    infinite_recursion();  // 崩溃点：无限递归导致栈溢出
}

void func_level_2() {
    std::cout << "进入 func_level_2()" << std::endl;
    infinite_recursion();
}

void func_level_1() {
    std::cout << "进入 func_level_1()" << std::endl;
    func_level_2();
}

int main() {
    std::cout << "程序开始运行..." << std::endl;
    func_level_1();
    std::cout << "程序结束（不会执行到这里）" << std::endl;
    return 0;
}

=== 程序开始执行 ===
时间: 2025年 07月 13日 星期日 02:36:18 CST
可执行文件: ./crash_test_2/crash_test_2_debug
ASan选项: abort_on_error=1:print_stacktrace=1:symbolize=1:halt_on_error=1
==========================

程序开始运行...
进入 func_level_1()
进入 func_level_2()
进入 func_level_5()，准备越界访问数组...
crash_test_2.cpp:6:11: runtime error: index 10 out of bounds for type 'int [3]'
    #0 0x5e1865af9582 in func_level_3() /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/crash_test_2.cpp:6
    #1 0x5e1865af98ed in func_level_2() /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/crash_test_2.cpp:12
    #2 0x5e1865af9a4b in func_level_1() /home/<USER>/Ryan_/TEN<PERSON>/Crash_Test/Test/Cpp/crash_test_2.cpp:17
    #3 0x5e1865af9ba9 in main /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/crash_test_2.cpp:22
    #4 0x7c1d17e2a1c9 in __libc_start_call_main ../sysdeps/nptl/libc_start_call_main.h:58
    #5 0x7c1d17e2a28a in __libc_start_main_impl ../csu/libc-start.c:360
    #6 0x5e1865af9224 in _start (/home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/crash_test_2/crash_test_2_debug+0x2224) (BuildId: 7f077306be1be9d1c1608cbcb541b70d93d7cc25)

crash_test_2.cpp:6:13: runtime error: store to address 0x7c1d15d00048 with insufficient space for an object of type 'int'
0x7c1d15d00048: note: pointer points here
 00 00 00 00  00 00 00 00 00 00 00 00  00 00 00 00 00 00 00 00  00 00 00 00 00 00 00 00  00 00 00 00
              ^ 
    #0 0x5e1865af959c in func_level_3() /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/crash_test_2.cpp:6
    #1 0x5e1865af98ed in func_level_2() /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/crash_test_2.cpp:12
    #2 0x5e1865af9a4b in func_level_1() /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/crash_test_2.cpp:17
    #3 0x5e1865af9ba9 in main /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/crash_test_2.cpp:22
    #4 0x7c1d17e2a1c9 in __libc_start_call_main ../sysdeps/nptl/libc_start_call_main.h:58
    #5 0x7c1d17e2a28a in __libc_start_main_impl ../csu/libc-start.c:360
    #6 0x5e1865af9224 in _start (/home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/crash_test_2/crash_test_2_debug+0x2224) (BuildId: 7f077306be1be9d1c1608cbcb541b70d93d7cc25)

这行通常不会被执行
程序结束（通常不会执行到这里）

==========================
程序退出码: 0
时间: 2025年 07月 13日 星期日 02:36:18 CST
=== 程序执行结束 ===

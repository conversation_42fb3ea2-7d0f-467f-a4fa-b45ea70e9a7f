# C/C++ 崩溃测试用例

本目录包含各种C/C++崩溃情况的测试代码和对应的检测策略。

## 📋 崩溃类型列表

| 文件名 | 崩溃类型 | 检测工具 | 错误标识 |
|--------|----------|----------|----------|
| `SEGV.cpp` | 空指针解引用 | AddressSanitizer | `segmentation-fault` |
| `heap_overflow.cpp` | 堆缓冲区溢出 | AddressSanitizer | `heap-buffer-overflow` |
| `stack_overflow.cpp` | 栈缓冲区溢出 | AddressSanitizer | `stack-buffer-overflow` |
| `use_after_free.cpp` | 使用已释放内存 | AddressSanitizer | `use-after-free` |
| `double_free.cpp` | 双重释放 | AddressSanitizer | `double-free` |
| `division_by_zero.cpp` | 除零错误 | UndefinedBehaviorSanitizer | `division-by-zero` |
| `infinite_recursion.cpp` | 无限递归/栈溢出 | 系统信号 | `stack-overflow` |
| `integer_overflow.cpp` | 整数溢出 | UndefinedBehaviorSanitizer | `integer-overflow` |
| `wild_pointer.cpp` | 野指针访问 | AddressSanitizer | `segmentation-fault` |
| `crash_test_2.cpp` | 数组越界 | UndefinedBehaviorSanitizer | `buffer-overflow` |

## 🛠️ 使用方法

### 单个测试
```bash
./ASan.sh <test_file.cpp>
```

### 批量测试
```bash
./test_all_crashes.sh
```

## 🔍 ASan.sh 检测策略

### 1. AddressSanitizer (ASan) 检测
- **堆缓冲区溢出**: `heap-buffer-overflow`
- **栈缓冲区溢出**: `stack-buffer-overflow`
- **使用已释放内存**: `heap-use-after-free`
- **双重释放**: `double-free`
- **空指针解引用**: 通过SIGSEGV信号检测

### 2. UndefinedBehaviorSanitizer (UBSan) 检测
- **整数溢出**: `signed integer overflow`
- **除零错误**: `division by zero`
- **数组越界**: `index out of bounds`
- **类型转换错误**: `type confusion`

### 3. 系统信号检测
- **SIGSEGV (139)**: 段错误
- **SIGFPE (136)**: 浮点异常
- **SIGABRT (134)**: 程序中止
- **SIGBUS (138)**: 总线错误

### 4. 退出码检测
当sanitizer无法检测时，根据程序退出码推断错误类型。

## 📊 输出格式

调用链格式：`文件名;函数1;函数2;...;错误类型`

示例：
```
SEGV;main;func_level_1;func_level_2;func_level_3;func_level_4;func_level_5;segmentation-fault
```

## 🧪 编译选项

ASan.sh 使用以下编译选项：
```bash
-g -O0 -fsanitize=address -fsanitize=undefined -fno-omit-frame-pointer -fno-optimize-sibling-calls
```

- `-g`: 包含调试信息
- `-O0`: 禁用优化
- `-fsanitize=address`: 启用AddressSanitizer
- `-fsanitize=undefined`: 启用UndefinedBehaviorSanitizer
- `-fno-omit-frame-pointer`: 保留帧指针
- `-fno-optimize-sibling-calls`: 禁用尾调用优化

## 📁 输出文件

每次运行会在 `./文件名/` 目录下生成：
- `文件名_crash_log.txt`: 完整的崩溃日志
- `文件名_call_chain.txt`: 格式化的调用链
- `文件名_debug`: 编译后的可执行文件

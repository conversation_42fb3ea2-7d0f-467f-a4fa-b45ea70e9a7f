#include <iostream>

void func_level_3() {
    std::cout << "进入 func_level_3()，准备双重释放..." << std::endl;
    int* ptr = new int(42);
    std::cout << "分配内存并设置值: " << *ptr << std::endl;
    delete ptr;
    std::cout << "第一次释放内存" << std::endl;
    delete ptr;  // 崩溃点：双重释放
    std::cout << "不会执行到这行" << std::endl;
}

void func_level_2() {
    std::cout << "进入 func_level_2()" << std::endl;
    func_level_3();
}

void func_level_1() {
    std::cout << "进入 func_level_1()" << std::endl;
    func_level_2();
}

int main() {
    std::cout << "程序开始运行..." << std::endl;
    func_level_1();
    std::cout << "程序结束（不会执行到这里）" << std::endl;
    return 0;
}

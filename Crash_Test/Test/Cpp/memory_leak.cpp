#include <iostream>

void func_level_3() {
    std::cout << "进入 func_level_3()，准备内存泄漏..." << std::endl;
    
    // 分配大量内存但不释放
    for (int i = 0; i < 1000; i++) {
        int* ptr = new int[1000];  // 分配内存但不释放
        *ptr = i;  // 使用内存防止被优化掉
        std::cout << "分配内存块 " << i << ", 地址: " << ptr << std::endl;
        // 故意不调用 delete[] ptr; 造成内存泄漏
    }
    
    std::cout << "内存泄漏完成，程序即将结束" << std::endl;
}

void func_level_2() {
    std::cout << "进入 func_level_2()" << std::endl;
    func_level_3();
}

void func_level_1() {
    std::cout << "进入 func_level_1()" << std::endl;
    func_level_2();
}

int main() {
    std::cout << "程序开始运行..." << std::endl;
    func_level_1();
    std::cout << "程序结束（会有内存泄漏报告）" << std::endl;
    return 0;
}

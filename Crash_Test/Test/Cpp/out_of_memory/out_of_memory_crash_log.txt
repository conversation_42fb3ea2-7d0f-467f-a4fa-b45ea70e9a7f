=== 程序开始执行 ===
时间: 2025年 07月 13日 星期日 03:20:41 CST
可执行文件: ./out_of_memory/out_of_memory_debug
ASan选项: abort_on_error=1:print_stacktrace=1:symbolize=1:halt_on_error=1
==========================

程序开始运行...
进入 func_level_1()
进入 func_level_2()
进入 func_level_3()，准备耗尽内存...
分配内存块 0 (100MB)...
已分配 1 个内存块，总计 100MB
分配内存块 1 (100MB)...
已分配 2 个内存块，总计 200MB
分配内存块 2 (100MB)...
已分配 3 个内存块，总计 300MB
分配内存块 3 (100MB)...
已分配 4 个内存块，总计 400MB
分配内存块 4 (100MB)...
已分配 5 个内存块，总计 500MB
分配内存块 5 (100MB)...
已分配 6 个内存块，总计 600MB
分配内存块 6 (100MB)...
已分配 7 个内存块，总计 700MB
分配内存块 7 (100MB)...
已分配 8 个内存块，总计 800MB
分配内存块 8 (100MB)...
已分配 9 个内存块，总计 900MB
分配内存块 9 (100MB)...
已分配 10 个内存块，总计 1000MB
分配内存块 10 (100MB)...
已分配 11 个内存块，总计 1100MB
分配内存块 11 (100MB)...
已分配 12 个内存块，总计 1200MB
分配内存块 12 (100MB)...
已分配 13 个内存块，总计 1300MB
分配内存块 13 (100MB)...
已分配 14 个内存块，总计 1400MB
分配内存块 14 (100MB)...
已分配 15 个内存块，总计 1500MB
分配内存块 15 (100MB)...
已分配 16 个内存块，总计 1600MB
分配内存块 16 (100MB)...
已分配 17 个内存块，总计 1700MB
分配内存块 17 (100MB)...
已分配 18 个内存块，总计 1800MB
分配内存块 18 (100MB)...
已分配 19 个内存块，总计 1900MB
分配内存块 19 (100MB)...
已分配 20 个内存块，总计 2000MB
分配内存块 20 (100MB)...
已分配 21 个内存块，总计 2100MB
分配内存块 21 (100MB)...
已分配 22 个内存块，总计 2200MB
分配内存块 22 (100MB)...
已分配 23 个内存块，总计 2300MB
分配内存块 23 (100MB)...
已分配 24 个内存块，总计 2400MB
分配内存块 24 (100MB)...
已分配 25 个内存块，总计 2500MB
分配内存块 25 (100MB)...
已分配 26 个内存块，总计 2600MB
分配内存块 26 (100MB)...
已分配 27 个内存块，总计 2700MB
分配内存块 27 (100MB)...
已分配 28 个内存块，总计 2800MB
分配内存块 28 (100MB)...
已分配 29 个内存块，总计 2900MB
分配内存块 29 (100MB)...
已分配 30 个内存块，总计 3000MB
分配内存块 30 (100MB)...
已分配 31 个内存块，总计 3100MB
分配内存块 31 (100MB)...
已分配 32 个内存块，总计 3200MB
分配内存块 32 (100MB)...
已分配 33 个内存块，总计 3300MB
分配内存块 33 (100MB)...
已分配 34 个内存块，总计 3400MB
分配内存块 34 (100MB)...
已分配 35 个内存块，总计 3500MB
分配内存块 35 (100MB)...
已分配 36 个内存块，总计 3600MB
分配内存块 36 (100MB)...
已分配 37 个内存块，总计 3700MB
分配内存块 37 (100MB)...
已分配 38 个内存块，总计 3800MB
分配内存块 38 (100MB)...
已分配 39 个内存块，总计 3900MB
分配内存块 39 (100MB)...
已分配 40 个内存块，总计 4000MB
分配内存块 40 (100MB)...
已分配 41 个内存块，总计 4100MB
分配内存块 41 (100MB)...
已分配 42 个内存块，总计 4200MB
分配内存块 42 (100MB)...
已分配 43 个内存块，总计 4300MB
分配内存块 43 (100MB)...
已分配 44 个内存块，总计 4400MB
分配内存块 44 (100MB)...
已分配 45 个内存块，总计 4500MB
分配内存块 45 (100MB)...
已分配 46 个内存块，总计 4600MB
分配内存块 46 (100MB)...
已分配 47 个内存块，总计 4700MB
分配内存块 47 (100MB)...
已分配 48 个内存块，总计 4800MB
分配内存块 48 (100MB)...
已分配 49 个内存块，总计 4900MB
分配内存块 49 (100MB)...
已分配 50 个内存块，总计 5000MB
分配内存块 50 (100MB)...
已分配 51 个内存块，总计 5100MB
分配内存块 51 (100MB)...
已分配 52 个内存块，总计 5200MB
分配内存块 52 (100MB)...
已分配 53 个内存块，总计 5300MB
分配内存块 53 (100MB)...
已分配 54 个内存块，总计 5400MB
分配内存块 54 (100MB)...
已分配 55 个内存块，总计 5500MB
分配内存块 55 (100MB)...
已分配 56 个内存块，总计 5600MB
分配内存块 56 (100MB)...
已分配 57 个内存块，总计 5700MB
分配内存块 57 (100MB)...
已分配 58 个内存块，总计 5800MB
分配内存块 58 (100MB)...
已分配 59 个内存块，总计 5900MB
分配内存块 59 (100MB)...
已分配 60 个内存块，总计 6000MB
分配内存块 60 (100MB)...
已分配 61 个内存块，总计 6100MB
分配内存块 61 (100MB)...
已分配 62 个内存块，总计 6200MB
分配内存块 62 (100MB)...
已分配 63 个内存块，总计 6300MB
分配内存块 63 (100MB)...
已分配 64 个内存块，总计 6400MB
分配内存块 64 (100MB)...
已分配 65 个内存块，总计 6500MB
分配内存块 65 (100MB)...
已分配 66 个内存块，总计 6600MB
分配内存块 66 (100MB)...
已分配 67 个内存块，总计 6700MB
分配内存块 67 (100MB)...
已分配 68 个内存块，总计 6800MB
分配内存块 68 (100MB)...
已分配 69 个内存块，总计 6900MB
分配内存块 69 (100MB)...
已分配 70 个内存块，总计 7000MB
分配内存块 70 (100MB)...
已分配 71 个内存块，总计 7100MB
分配内存块 71 (100MB)...
已分配 72 个内存块，总计 7200MB
分配内存块 72 (100MB)...
已分配 73 个内存块，总计 7300MB
分配内存块 73 (100MB)...
已分配 74 个内存块，总计 7400MB
分配内存块 74 (100MB)...
已分配 75 个内存块，总计 7500MB
分配内存块 75 (100MB)...
已分配 76 个内存块，总计 7600MB
分配内存块 76 (100MB)...
已分配 77 个内存块，总计 7700MB
分配内存块 77 (100MB)...
已分配 78 个内存块，总计 7800MB
分配内存块 78 (100MB)...
已分配 79 个内存块，总计 7900MB
分配内存块 79 (100MB)...
已分配 80 个内存块，总计 8000MB
分配内存块 80 (100MB)...
已分配 81 个内存块，总计 8100MB
分配内存块 81 (100MB)...
已分配 82 个内存块，总计 8200MB
分配内存块 82 (100MB)...
已分配 83 个内存块，总计 8300MB
分配内存块 83 (100MB)...
已分配 84 个内存块，总计 8400MB
分配内存块 84 (100MB)...
已分配 85 个内存块，总计 8500MB
分配内存块 85 (100MB)...
已分配 86 个内存块，总计 8600MB
分配内存块 86 (100MB)...
已分配 87 个内存块，总计 8700MB
分配内存块 87 (100MB)...
已分配 88 个内存块，总计 8800MB
分配内存块 88 (100MB)...
已分配 89 个内存块，总计 8900MB
分配内存块 89 (100MB)...
已分配 90 个内存块，总计 9000MB
分配内存块 90 (100MB)...
已分配 91 个内存块，总计 9100MB
分配内存块 91 (100MB)...
已分配 92 个内存块，总计 9200MB
分配内存块 92 (100MB)...
已分配 93 个内存块，总计 9300MB
分配内存块 93 (100MB)...
已分配 94 个内存块，总计 9400MB
分配内存块 94 (100MB)...
已分配 95 个内存块，总计 9500MB
分配内存块 95 (100MB)...
已分配 96 个内存块，总计 9600MB
分配内存块 96 (100MB)...
已分配 97 个内存块，总计 9700MB
分配内存块 97 (100MB)...
已分配 98 个内存块，总计 9800MB
分配内存块 98 (100MB)...
已分配 99 个内存块，总计 9900MB
分配内存块 99 (100MB)...
已分配 100 个内存块，总计 10000MB
分配内存块 100 (100MB)...
已分配 101 个内存块，总计 10100MB
达到分配上限，停止分配
不会执行到这行
程序结束（不会执行到这里）

=================================================================
==1397113==ERROR: LeakSanitizer: detected memory leaks

Direct leak of 10590617600 byte(s) in 101 object(s) allocated from:
    #0 0x7bdc56efe6c8 in operator new[](unsigned long) ../../../../src/libsanitizer/asan/asan_new_delete.cpp:98
    #1 0x56e1d91bc9a3 in func_level_3() /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/out_of_memory.cpp:16
    #2 0x56e1d91bd9b9 in func_level_2() /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/out_of_memory.cpp:46
    #3 0x56e1d91bdb17 in func_level_1() /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/out_of_memory.cpp:51
    #4 0x56e1d91bdc77 in main /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/out_of_memory.cpp:57
    #5 0x7bdc55e2a1c9 in __libc_start_call_main ../sysdeps/nptl/libc_start_call_main.h:58
    #6 0x7bdc55e2a28a in __libc_start_main_impl ../csu/libc-start.c:360
    #7 0x56e1d91bc444 in _start (/home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/out_of_memory/out_of_memory_debug+0x9444) (BuildId: 58a61906acd38a4729a4c788c923e0bd35da8db1)

SUMMARY: AddressSanitizer: 10590617600 byte(s) leaked in 101 allocation(s).
timeout: 被监视的命令已核心转储
./ASan.sh: 第 70 行： 1397112 已放弃               timeout 30s "$EXECUTABLE" 2>&1

==========================
程序退出码: 134
时间: 2025年 07月 13日 星期日 03:20:45 CST
=== 程序执行结束 ===

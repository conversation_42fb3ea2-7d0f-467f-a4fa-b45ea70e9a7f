#include <iostream>

void func_level_3() {
    std::cout << "进入 func_level_5()，准备越界访问数组..." << std::endl;
    int arr[3] = {1, 2, 3};
    arr[10] = 42;  // 越界访问，未定义行为，可能导致崩溃
    std::cout << "这行通常不会被执行" << std::endl;
}

void func_level_2() {
    std::cout << "进入 func_level_2()" << std::endl;
    func_level_3();
}

void func_level_1() {
    std::cout << "进入 func_level_1()" << std::endl;
    func_level_2();
}

int main() {
    std::cout << "程序开始运行..." << std::endl;
    func_level_1();
    std::cout << "程序结束（通常不会执行到这里）" << std::endl;
    return 0;
}

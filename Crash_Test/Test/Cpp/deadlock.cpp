#include <iostream>
#include <thread>
#include <mutex>
#include <chrono>

std::mutex mutex1;
std::mutex mutex2;

void thread1_func() {
    std::cout << "线程1：尝试获取mutex1..." << std::endl;
    std::lock_guard<std::mutex> lock1(mutex1);
    std::cout << "线程1：获取到mutex1，休眠1秒..." << std::endl;
    
    std::this_thread::sleep_for(std::chrono::seconds(1));
    
    std::cout << "线程1：尝试获取mutex2..." << std::endl;
    std::lock_guard<std::mutex> lock2(mutex2);  // 死锁点
    std::cout << "线程1：获取到mutex2，完成" << std::endl;
}

void thread2_func() {
    std::cout << "线程2：尝试获取mutex2..." << std::endl;
    std::lock_guard<std::mutex> lock2(mutex2);
    std::cout << "线程2：获取到mutex2，休眠1秒..." << std::endl;
    
    std::this_thread::sleep_for(std::chrono::seconds(1));
    
    std::cout << "线程2：尝试获取mutex1..." << std::endl;
    std::lock_guard<std::mutex> lock1(mutex1);  // 死锁点
    std::cout << "线程2：获取到mutex1，完成" << std::endl;
}

void func_level_2() {
    std::cout << "进入 func_level_2()，准备创建死锁..." << std::endl;
    
    std::thread t1(thread1_func);
    std::thread t2(thread2_func);
    
    std::cout << "等待线程完成（将会死锁）..." << std::endl;
    t1.join();  // 崩溃点：死锁导致程序挂起
    t2.join();
    
    std::cout << "不会执行到这行" << std::endl;
}

void func_level_1() {
    std::cout << "进入 func_level_1()" << std::endl;
    func_level_2();
}

int main() {
    std::cout << "程序开始运行..." << std::endl;
    func_level_1();
    std::cout << "程序结束（不会执行到这里）" << std::endl;
    return 0;
}

#!/bin/bash

# ========= 配置 =========
SOURCE_FILE="$1"                   # 要编译和运行的 C++ 文件（带路径）

# 提取文件名
BASENAME=$(basename "$SOURCE_FILE" .cpp)

# 创建目录（如果不存在）
mkdir -p "./${BASENAME}"

LOGFILE="./${BASENAME}/${BASENAME}_crash_log.txt"
CALL_CHAIN_FILE="./${BASENAME}/${BASENAME}_call_chain.txt"
EXECUTABLE="./${BASENAME}/${BASENAME}_debug"

# ASan 和编译选项
ASAN_OPTIONS="abort_on_error=1:print_stacktrace=1:symbolize=1:halt_on_error=1"
COMPILE_FLAGS="-g -O0 -fsanitize=address -fsanitize=undefined -fno-omit-frame-pointer -fno-optimize-sibling-calls"
# ========================

if [ ! -f "$SOURCE_FILE" ]; then
    echo "❌ 指定的 C++ 文件不存在: $SOURCE_FILE"
    exit 1
fi

echo "🔧 编译 $SOURCE_FILE -> $EXECUTABLE"
echo "📋 编译选项: $COMPILE_FLAGS"

# === 编译带有 ASan 的可执行文件 ===
g++ $COMPILE_FLAGS "$SOURCE_FILE" -o "$EXECUTABLE" 2>&1
if [ $? -ne 0 ]; then
    echo "❌ 编译失败"
    exit 1
fi

echo "✅ 编译成功"

# === 运行程序并捕获崩溃信息 ===
echo "🚀 运行 $EXECUTABLE ..."
echo "🛡️  使用 AddressSanitizer 进行内存错误检测"

# 设置 ASan 环境变量并运行程序
export ASAN_OPTIONS="$ASAN_OPTIONS"
export MSAN_OPTIONS="print_stats=1"
export UBSAN_OPTIONS="print_stacktrace=1"

# 运行程序并捕获所有输出
{
    echo "=== 程序开始执行 ==="
    echo "时间: $(date)"
    echo "可执行文件: $EXECUTABLE"
    echo "ASan选项: $ASAN_OPTIONS"
    echo "=========================="
    echo ""

    # 使用 timeout 防止程序无限挂起，并捕获所有输出
    timeout 30s "$EXECUTABLE" 2>&1
    EXIT_CODE=$?

    echo ""
    echo "=========================="
    echo "程序退出码: $EXIT_CODE"
    echo "时间: $(date)"
    echo "=== 程序执行结束 ==="
} > "$LOGFILE" 2>&1

echo "📄 崩溃信息已保存到: $LOGFILE"

# === 处理崩溃日志，提取调用链 ===
if [ -f "$LOGFILE" ] && [ -s "$LOGFILE" ]; then
    echo "🔍 分析崩溃日志，提取调用链..."

    # 创建内联的Python脚本来处理C++/ASan日志
    python3 << PYTHON_SCRIPT
import os
import re

LANGUAGE = "C++"
INPUT_FILE = "$LOGFILE"
OUTPUT_FILE = "$CALL_CHAIN_FILE"

call_chain = []
error_type = ""

# 读取日志内容
try:
    with open(INPUT_FILE, "r") as f:
        content = f.read()
        lines = content.split('\n')
except FileNotFoundError:
    print(f"❌ 找不到崩溃日志文件: {INPUT_FILE}")
    exit(1)

# ASan、UBSan 和其他错误类型模式
asan_error_patterns = [
    r"ERROR: AddressSanitizer: ([\w-]+)",
    r"ERROR: UndefinedBehaviorSanitizer: ([\w-]+)",
    r"SUMMARY: AddressSanitizer: ([\w-]+)",
    r"SUMMARY: UndefinedBehaviorSanitizer: ([\w-]+)",
    r"runtime error: ([\w\s-]+)",  # UBSan runtime errors
    r"==\d+==ERROR: AddressSanitizer: ([\w-]+)",  # ASan with PID
    r"==\d+==ERROR: ([\w-]+)",  # 其他sanitizer错误
]

# 系统级错误模式
system_error_patterns = [
    r"Segmentation fault",
    r"segmentation fault",
    r"SIGSEGV",
    r"SIGFPE",  # 浮点异常（如除零）
    r"SIGABRT",  # 程序中止
    r"SIGBUS",   # 总线错误
    r"stack overflow",
    r"Stack overflow",
]

# 函数调用栈模式 (ASan 格式)
stack_patterns = [
    r"#\d+\s+0x[0-9a-f]+\s+in\s+(\w+)",           # #0 0x... in function_name
    r"#\d+\s+0x[0-9a-f]+\s+(\w+)",                # #0 0x... function_name
    r"\s+#\d+\s+0x[0-9a-f]+\s+in\s+(\w+)",       # 带缩进的版本
]

# GDB 风格的调用栈模式
gdb_patterns = [
    r"#\d+\s+0x[0-9a-f]+\s+in\s+(\w+)\s+\(",     # #0 0x... in func (
    r"at\s+.*?:(\w+)",                             # at file.cpp:function
]

# 提取错误类型
for line in lines:
    # 检查 ASan/UBSan 错误
    for pattern in asan_error_patterns:
        match = re.search(pattern, line)
        if match:
            error_type = match.group(1)
            # 清理和标准化错误类型
            if "out of bounds" in error_type:
                error_type = "buffer-overflow"
            elif "insufficient space" in error_type:
                error_type = "buffer-overflow"
            elif "heap-buffer-overflow" in error_type:
                error_type = "heap-buffer-overflow"
            elif "stack-buffer-overflow" in error_type:
                error_type = "stack-buffer-overflow"
            elif "heap-use-after-free" in error_type:
                error_type = "use-after-free"
            elif "double-free" in error_type:
                error_type = "double-free"
            elif "signed integer overflow" in error_type:
                error_type = "integer-overflow"
            elif "division by zero" in error_type:
                error_type = "division-by-zero"
            break

    # 检查系统级错误
    if not error_type:
        for pattern in system_error_patterns:
            if re.search(pattern, line, re.IGNORECASE):
                if "segmentation fault" in line.lower() or "sigsegv" in line.lower():
                    error_type = "segmentation-fault"
                elif "sigfpe" in line.lower():
                    error_type = "floating-point-exception"
                elif "sigabrt" in line.lower():
                    error_type = "abort"
                elif "sigbus" in line.lower():
                    error_type = "bus-error"
                elif "stack overflow" in line.lower():
                    error_type = "stack-overflow"
                break

    if error_type:
        break

# 如果没找到明确错误，根据退出码推断
if not error_type:
    for line in lines:
        if "程序退出码:" in line:
            exit_code_match = re.search(r"程序退出码: (\d+)", line)
            if exit_code_match:
                exit_code = int(exit_code_match.group(1))
                if exit_code == 139:  # SIGSEGV
                    error_type = "segmentation-fault"
                elif exit_code == 136:  # SIGFPE
                    error_type = "floating-point-exception"
                elif exit_code == 134:  # SIGABRT
                    error_type = "abort"
                elif exit_code == 138:  # SIGBUS
                    error_type = "bus-error"
                elif exit_code != 0:
                    error_type = f"exit-code-{exit_code}"
            break

# 提取调用栈
for line in lines:
    # 尝试 ASan 格式的调用栈
    for pattern in stack_patterns:
        match = re.search(pattern, line)
        if match:
            func_name = match.group(1)
            # 过滤掉一些系统函数和ASan内部函数
            if not any(skip in func_name.lower() for skip in ['asan', 'sanitizer', '__', 'libc', 'start']):
                if func_name not in call_chain:  # 避免重复
                    call_chain.append(func_name)
            break

# 如果没有找到调用栈，尝试从源文件名推断
if not call_chain:
    # 从文件名推断主函数
    call_chain = ["main"]

# 反转调用链顺序（ASan输出是从崩溃点往上，我们要从main开始）
call_chain.reverse()

# 在最前面添加文件名
if call_chain:
    call_chain.insert(0, "$BASENAME")

# 构建输出
if call_chain:
    output = ";".join(call_chain)
    if error_type:
        output += f";{error_type}"

    # 写入输出文件
    with open(OUTPUT_FILE, "w") as f_out:
        f_out.write(output)

    print(f"📊 调用链已保存到: {OUTPUT_FILE}")
    print(f"🔗 调用链: {output}")
    if error_type:
        print(f"🚨 错误类型: {error_type}")
else:
    print("⚠️  未找到有效的调用链信息")
    # 仍然尝试保存错误类型
    if error_type:
        with open(OUTPUT_FILE, "w") as f_out:
            f_out.write(f"unknown;{error_type}")
        print(f"🚨 错误类型: {error_type}")
PYTHON_SCRIPT

else
    echo "⚠️  崩溃日志文件为空或不存在，跳过调用链分析"
fi

echo "✅ 完成！"
echo "📁 输出目录: ./${BASENAME}/"
echo "📄 崩溃日志: $LOGFILE"
echo "🔗 调用链: $CALL_CHAIN_FILE"
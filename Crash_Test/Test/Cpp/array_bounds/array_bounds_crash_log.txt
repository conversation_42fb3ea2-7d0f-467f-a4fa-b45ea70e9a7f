=== 程序开始执行 ===
时间: 2025年 07月 13日 星期日 02:58:52 CST
可执行文件: ./array_bounds/array_bounds_debug
ASan选项: abort_on_error=1:print_stacktrace=1:symbolize=1:halt_on_error=1
==========================

程序开始运行...
进入 func_level_1()
进入 func_level_2()
进入 func_level_3()
进入 func_level_4()，准备数组越界访问...
数组大小: 5，准备访问索引 10...
array_bounds.cpp:7:23: runtime error: index 10 out of bounds for type 'int [5]'
    #0 0x6115dda57793 in func_level_4() /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/array_bounds.cpp:7
    #1 0x6115dda57c23 in func_level_3() /home/<USER>/Ryan_/TEN<PERSON>/Crash_Test/Test/Cpp/array_bounds.cpp:13
    #2 0x6115dda57d81 in func_level_2() /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/array_bounds.cpp:18
    #3 0x6115dda57edf in func_level_1() /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/array_bounds.cpp:23
    #4 0x6115dda5803d in main /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/array_bounds.cpp:28
    #5 0x7c579822a1c9 in __libc_start_call_main ../sysdeps/nptl/libc_start_call_main.h:58
    #6 0x7c579822a28a in __libc_start_main_impl ../csu/libc-start.c:360
    #7 0x6115dda57264 in _start (/home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/array_bounds/array_bounds_debug+0x3264) (BuildId: 2ab9b2ff46765c9177cde63447e9e23884a797ba)

array_bounds.cpp:7:9: runtime error: load of address 0x7c5796200048 with insufficient space for an object of type 'int'
0x7c5796200048: note: pointer points here
 00 00 00 00  00 00 00 00 00 00 00 00  00 00 00 00 00 00 00 00  00 00 00 00 00 00 00 00  00 00 00 00
              ^ 
    #0 0x6115dda577ad in func_level_4() /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/array_bounds.cpp:7
    #1 0x6115dda57c23 in func_level_3() /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/array_bounds.cpp:13
    #2 0x6115dda57d81 in func_level_2() /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/array_bounds.cpp:18
    #3 0x6115dda57edf in func_level_1() /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/array_bounds.cpp:23
    #4 0x6115dda5803d in main /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/array_bounds.cpp:28
    #5 0x7c579822a1c9 in __libc_start_call_main ../sysdeps/nptl/libc_start_call_main.h:58
    #6 0x7c579822a28a in __libc_start_main_impl ../csu/libc-start.c:360
    #7 0x6115dda57264 in _start (/home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/array_bounds/array_bounds_debug+0x3264) (BuildId: 2ab9b2ff46765c9177cde63447e9e23884a797ba)

=================================================================
==1390047==ERROR: AddressSanitizer: stack-buffer-overflow on address 0x7c5796200048 at pc 0x6115dda57805 bp 0x7ffd40942470 sp 0x7ffd40942460
READ of size 4 at 0x7c5796200048 thread T0
    #0 0x6115dda57804 in func_level_4() /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/array_bounds.cpp:7
    #1 0x6115dda57c23 in func_level_3() /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/array_bounds.cpp:13
    #2 0x6115dda57d81 in func_level_2() /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/array_bounds.cpp:18
    #3 0x6115dda57edf in func_level_1() /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/array_bounds.cpp:23
    #4 0x6115dda5803d in main /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/array_bounds.cpp:28
    #5 0x7c579822a1c9 in __libc_start_call_main ../sysdeps/nptl/libc_start_call_main.h:58
    #6 0x7c579822a28a in __libc_start_main_impl ../csu/libc-start.c:360
    #7 0x6115dda57264 in _start (/home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/array_bounds/array_bounds_debug+0x3264) (BuildId: 2ab9b2ff46765c9177cde63447e9e23884a797ba)

Address 0x7c5796200048 is located in stack of thread T0 at offset 72 in frame
    #0 0x6115dda57338 in func_level_4() /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/array_bounds.cpp:3

  This frame has 1 object(s):
    [32, 52) 'arr' (line 5) <== Memory access at offset 72 overflows this variable
HINT: this may be a false positive if your program uses some custom stack unwind mechanism, swapcontext or vfork
      (longjmp and C++ exceptions *are* supported)
SUMMARY: AddressSanitizer: stack-buffer-overflow /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/array_bounds.cpp:7 in func_level_4()
Shadow bytes around the buggy address:
  0x7c57961ffd80: 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
  0x7c57961ffe00: 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
  0x7c57961ffe80: 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
  0x7c57961fff00: 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
  0x7c57961fff80: 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
=>0x7c5796200000: f1 f1 f1 f1 00 00 04 f3 f3[f3]f3 f3 00 00 00 00
  0x7c5796200080: 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
  0x7c5796200100: 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
  0x7c5796200180: 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
  0x7c5796200200: 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
  0x7c5796200280: 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
Shadow byte legend (one shadow byte represents 8 application bytes):
  Addressable:           00
  Partially addressable: 01 02 03 04 05 06 07 
  Heap left redzone:       fa
  Freed heap region:       fd
  Stack left redzone:      f1
  Stack mid redzone:       f2
  Stack right redzone:     f3
  Stack after return:      f5
  Stack use after scope:   f8
  Global redzone:          f9
  Global init order:       f6
  Poisoned by user:        f7
  Container overflow:      fc
  Array cookie:            ac
  Intra object redzone:    bb
  ASan internal:           fe
  Left alloca redzone:     ca
  Right alloca redzone:    cb
==1390047==ABORTING
timeout: 被监视的命令已核心转储
./ASan.sh: 第 65 行： 1390046 已放弃               timeout 30s "$EXECUTABLE" 2>&1

==========================
程序退出码: 134
时间: 2025年 07月 13日 星期日 02:58:53 CST
=== 程序执行结束 ===

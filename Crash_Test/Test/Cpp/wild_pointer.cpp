#include <iostream>

void func_level_3() {
    std::cout << "进入 func_level_3()，准备访问野指针..." << std::endl;
    int* ptr;  // 未初始化的指针（野指针）
    std::cout << "野指针地址: " << ptr << std::endl;
    int value = *ptr;  // 崩溃点：访问野指针
    std::cout << "不会执行到这行，value: " << value << std::endl;
}

void func_level_2() {
    std::cout << "进入 func_level_2()" << std::endl;
    func_level_3();
}

void func_level_1() {
    std::cout << "进入 func_level_1()" << std::endl;
    func_level_2();
}

int main() {
    std::cout << "程序开始运行..." << std::endl;
    func_level_1();
    std::cout << "程序结束（不会执行到这里）" << std::endl;
    return 0;
}

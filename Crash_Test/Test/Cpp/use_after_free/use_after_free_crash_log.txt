=== 程序开始执行 ===
时间: 2025年 07月 13日 星期日 02:58:56 CST
可执行文件: ./use_after_free/use_after_free_debug
ASan选项: abort_on_error=1:print_stacktrace=1:symbolize=1:halt_on_error=1
==========================

程序开始运行...
进入 func_level_1()
进入 func_level_2()
进入 func_level_3()，准备使用已释放内存...
分配内存并设置值: 42
内存已释放，现在尝试访问...
=================================================================
==1390133==ERROR: AddressSanitizer: heap-use-after-free on address 0x502000000010 at pc 0x584399596915 bp 0x7ffcd74da1d0 sp 0x7ffcd74da1c0
READ of size 4 at 0x502000000010 thread T0
    #0 0x584399596914 in func_level_3() /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/use_after_free.cpp:9
    #1 0x584399596cc7 in func_level_2() /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/use_after_free.cpp:15
    #2 0x584399596e25 in func_level_1() /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/use_after_free.cpp:20
    #3 0x584399596f83 in main /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/use_after_free.cpp:25
    #4 0x7e739562a1c9 in __libc_start_call_main ../sysdeps/nptl/libc_start_call_main.h:58
    #5 0x7e739562a28a in __libc_start_main_impl ../csu/libc-start.c:360
    #6 0x584399596224 in _start (/home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/use_after_free/use_after_free_debug+0x3224) (BuildId: 7044f59ca196a2a98a88bc9f95f8c6e2aaf29bb6)

0x502000000010 is located 0 bytes inside of 4-byte region [0x502000000010,0x502000000014)
freed by thread T0 here:
    #0 0x7e73966ff5e8 in operator delete(void*, unsigned long) ../../../../src/libsanitizer/asan/asan_new_delete.cpp:164
    #1 0x584399596770 in func_level_3() /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/use_after_free.cpp:7
    #2 0x584399596cc7 in func_level_2() /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/use_after_free.cpp:15
    #3 0x584399596e25 in func_level_1() /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/use_after_free.cpp:20
    #4 0x584399596f83 in main /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/use_after_free.cpp:25
    #5 0x7e739562a1c9 in __libc_start_call_main ../sysdeps/nptl/libc_start_call_main.h:58
    #6 0x7e739562a28a in __libc_start_main_impl ../csu/libc-start.c:360
    #7 0x584399596224 in _start (/home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/use_after_free/use_after_free_debug+0x3224) (BuildId: 7044f59ca196a2a98a88bc9f95f8c6e2aaf29bb6)

previously allocated by thread T0 here:
    #0 0x7e73966fe548 in operator new(unsigned long) ../../../../src/libsanitizer/asan/asan_new_delete.cpp:95
    #1 0x584399596444 in func_level_3() /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/use_after_free.cpp:5
    #2 0x584399596cc7 in func_level_2() /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/use_after_free.cpp:15
    #3 0x584399596e25 in func_level_1() /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/use_after_free.cpp:20
    #4 0x584399596f83 in main /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/use_after_free.cpp:25
    #5 0x7e739562a1c9 in __libc_start_call_main ../sysdeps/nptl/libc_start_call_main.h:58
    #6 0x7e739562a28a in __libc_start_main_impl ../csu/libc-start.c:360
    #7 0x584399596224 in _start (/home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/use_after_free/use_after_free_debug+0x3224) (BuildId: 7044f59ca196a2a98a88bc9f95f8c6e2aaf29bb6)

SUMMARY: AddressSanitizer: heap-use-after-free /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/use_after_free.cpp:9 in func_level_3()
Shadow bytes around the buggy address:
  0x501ffffffd80: 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
  0x501ffffffe00: 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
  0x501ffffffe80: 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
  0x501fffffff00: 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
  0x501fffffff80: 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
=>0x502000000000: fa fa[fd]fa fa fa fa fa fa fa fa fa fa fa fa fa
  0x502000000080: fa fa fa fa fa fa fa fa fa fa fa fa fa fa fa fa
  0x502000000100: fa fa fa fa fa fa fa fa fa fa fa fa fa fa fa fa
  0x502000000180: fa fa fa fa fa fa fa fa fa fa fa fa fa fa fa fa
  0x502000000200: fa fa fa fa fa fa fa fa fa fa fa fa fa fa fa fa
  0x502000000280: fa fa fa fa fa fa fa fa fa fa fa fa fa fa fa fa
Shadow byte legend (one shadow byte represents 8 application bytes):
  Addressable:           00
  Partially addressable: 01 02 03 04 05 06 07 
  Heap left redzone:       fa
  Freed heap region:       fd
  Stack left redzone:      f1
  Stack mid redzone:       f2
  Stack right redzone:     f3
  Stack after return:      f5
  Stack use after scope:   f8
  Global redzone:          f9
  Global init order:       f6
  Poisoned by user:        f7
  Container overflow:      fc
  Array cookie:            ac
  Intra object redzone:    bb
  ASan internal:           fe
  Left alloca redzone:     ca
  Right alloca redzone:    cb
==1390133==ABORTING
timeout: 被监视的命令已核心转储
./ASan.sh: 第 65 行： 1390132 已放弃               timeout 30s "$EXECUTABLE" 2>&1

==========================
程序退出码: 134
时间: 2025年 07月 13日 星期日 02:58:57 CST
=== 程序执行结束 ===

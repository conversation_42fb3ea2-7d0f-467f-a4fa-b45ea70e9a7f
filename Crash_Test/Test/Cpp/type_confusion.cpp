#include <iostream>

class Base {
public:
    virtual ~Base() = default;
    virtual void print() { std::cout << "Base class" << std::endl; }
};

class Derived : public Base {
public:
    void print() override { std::cout << "Derived class" << std::endl; }
    void derived_only_method() { std::cout << "Derived only method" << std::endl; }
};

class AnotherClass {
public:
    void dangerous_method() { std::cout << "Dangerous method" << std::endl; }
};

void func_level_3() {
    std::cout << "进入 func_level_3()，准备类型转换错误..." << std::endl;
    
    Base* base_ptr = new Base();
    std::cout << "创建Base对象" << std::endl;
    
    // 危险的类型转换
    AnotherClass* wrong_ptr = reinterpret_cast<AnotherClass*>(base_ptr);
    std::cout << "进行危险的类型转换..." << std::endl;
    
    wrong_ptr->dangerous_method();  // 崩溃点：类型转换错误
    
    delete base_ptr;
    std::cout << "不会执行到这行" << std::endl;
}

void func_level_2() {
    std::cout << "进入 func_level_2()" << std::endl;
    func_level_3();
}

void func_level_1() {
    std::cout << "进入 func_level_1()" << std::endl;
    func_level_2();
}

int main() {
    std::cout << "程序开始运行..." << std::endl;
    func_level_1();
    std::cout << "程序结束（不会执行到这里）" << std::endl;
    return 0;
}

#include <iostream>

int* create_dangling_pointer() {
    int local_var = 42;
    std::cout << "创建局部变量，值: " << local_var << std::endl;
    return &local_var;  // 返回局部变量的地址（悬空指针）
}

void func_level_3() {
    std::cout << "进入 func_level_3()，准备使用悬空指针..." << std::endl;
    int* dangling = create_dangling_pointer();
    std::cout << "悬空指针地址: " << dangling << std::endl;
    std::cout << "尝试访问悬空指针..." << std::endl;
    int value = *dangling;  // 崩溃点：访问悬空指针
    std::cout << "不会执行到这行，value: " << value << std::endl;
}

void func_level_2() {
    std::cout << "进入 func_level_2()" << std::endl;
    func_level_3();
}

void func_level_1() {
    std::cout << "进入 func_level_1()" << std::endl;
    func_level_2();
}

int main() {
    std::cout << "程序开始运行..." << std::endl;
    func_level_1();
    std::cout << "程序结束（不会执行到这里）" << std::endl;
    return 0;
}

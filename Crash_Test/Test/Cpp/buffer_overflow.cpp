#include <iostream>
#include <cstring>

void func_level_3() {
    std::cout << "进入 func_level_3()，准备缓冲区溢出..." << std::endl;
    
    char buffer[10];
    const char* long_string = "这是一个非常长的字符串，会导致缓冲区溢出崩溃！";
    
    std::cout << "缓冲区大小: " << sizeof(buffer) << " 字节" << std::endl;
    std::cout << "要复制的字符串长度: " << strlen(long_string) << " 字节" << std::endl;
    std::cout << "准备复制字符串到缓冲区..." << std::endl;
    
    strcpy(buffer, long_string);  // 崩溃点：缓冲区溢出
    
    std::cout << "不会执行到这行，buffer: " << buffer << std::endl;
}

void func_level_2() {
    std::cout << "进入 func_level_2()" << std::endl;
    func_level_3();
}

void func_level_1() {
    std::cout << "进入 func_level_1()" << std::endl;
    func_level_2();
}

int main() {
    std::cout << "程序开始运行..." << std::endl;
    func_level_1();
    std::cout << "程序结束（不会执行到这里）" << std::endl;
    return 0;
}

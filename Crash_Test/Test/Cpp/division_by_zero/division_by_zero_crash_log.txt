=== 程序开始执行 ===
时间: 2025年 07月 13日 星期日 02:58:59 CST
可执行文件: ./division_by_zero/division_by_zero_debug
ASan选项: abort_on_error=1:print_stacktrace=1:symbolize=1:halt_on_error=1
==========================

程序开始运行...
进入 func_level_1()
进入 func_level_2()
进入 func_level_3()，准备除零操作...
准备计算 10 / 0
division_by_zero.cpp:8:20: runtime error: division by zero
    #0 0x57e9a089c78e in func_level_3() /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/division_by_zero.cpp:8
    #1 0x57e9a089cb44 in func_level_2() /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/division_by_zero.cpp:14
    #2 0x57e9a089cca2 in func_level_1() /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/division_by_zero.cpp:19
    #3 0x57e9a089ce00 in main /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/division_by_zero.cpp:24
    #4 0x7e1f9c02a1c9 in __libc_start_call_main ../sysdeps/nptl/libc_start_call_main.h:58
    #5 0x7e1f9c02a28a in __libc_start_main_impl ../csu/libc-start.c:360
    #6 0x57e9a089c1c4 in _start (/home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/division_by_zero/division_by_zero_debug+0x31c4) (BuildId: cae4263e30b78b7f605c3b6671010dfe3d190ebe)

AddressSanitizer:DEADLYSIGNAL
=================================================================
==1390186==ERROR: AddressSanitizer: FPE on unknown address 0x57e9a089c793 (pc 0x57e9a089c793 bp 0x7ffd5c945d00 sp 0x7ffd5c945ce0 T0)
    #0 0x57e9a089c793 in func_level_3() /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/division_by_zero.cpp:8
    #1 0x57e9a089cb44 in func_level_2() /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/division_by_zero.cpp:14
    #2 0x57e9a089cca2 in func_level_1() /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/division_by_zero.cpp:19
    #3 0x57e9a089ce00 in main /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/division_by_zero.cpp:24
    #4 0x7e1f9c02a1c9 in __libc_start_call_main ../sysdeps/nptl/libc_start_call_main.h:58
    #5 0x7e1f9c02a28a in __libc_start_main_impl ../csu/libc-start.c:360
    #6 0x57e9a089c1c4 in _start (/home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/division_by_zero/division_by_zero_debug+0x31c4) (BuildId: cae4263e30b78b7f605c3b6671010dfe3d190ebe)

AddressSanitizer can not provide additional info.
SUMMARY: AddressSanitizer: FPE /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/division_by_zero.cpp:8 in func_level_3()
==1390186==ABORTING
timeout: 被监视的命令已核心转储
./ASan.sh: 第 65 行： 1390185 已放弃               timeout 30s "$EXECUTABLE" 2>&1

==========================
程序退出码: 134
时间: 2025年 07月 13日 星期日 02:59:00 CST
=== 程序执行结束 ===

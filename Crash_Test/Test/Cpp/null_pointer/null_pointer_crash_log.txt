=== 程序开始执行 ===
时间: 2025年 07月 13日 星期日 03:12:27 CST
可执行文件: ./null_pointer/null_pointer_debug
ASan选项: abort_on_error=1:print_stacktrace=1:symbolize=1:halt_on_error=1
==========================

程序开始运行...
进入 func_level_1()
进入 func_level_2()
进入 func_level_3()，准备访问空指针...
空指针地址: 0
null_pointer.cpp:7:9: runtime error: load of null pointer of type 'int'
    #0 0x5cd2c8b70678 in func_level_3() /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/null_pointer.cpp:7
    #1 0x5cd2c8b70a62 in func_level_2() /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/null_pointer.cpp:13
    #2 0x5cd2c8b70bc0 in func_level_1() /home/<USER>/Ryan_/TEN<PERSON>/Crash_Test/Test/Cpp/null_pointer.cpp:18
    #3 0x5cd2c8b70d1e in main /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/null_pointer.cpp:23
    #4 0x738ac2c2a1c9 in __libc_start_call_main ../sysdeps/nptl/libc_start_call_main.h:58
    #5 0x738ac2c2a28a in __libc_start_main_impl ../csu/libc-start.c:360
    #6 0x5cd2c8b701e4 in _start (/home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/null_pointer/null_pointer_debug+0x31e4) (BuildId: d9fbe1f62afbd1f5e149688e90ebd0521d5d4565)

AddressSanitizer:DEADLYSIGNAL
=================================================================
==1394340==ERROR: AddressSanitizer: SEGV on unknown address 0x000000000000 (pc 0x5cd2c8b706b4 bp 0x7ffef53d0c60 sp 0x7ffef53d0c40 T0)
==1394340==The signal is caused by a READ memory access.
==1394340==Hint: address points to the zero page.
    #0 0x5cd2c8b706b4 in func_level_3() /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/null_pointer.cpp:7
    #1 0x5cd2c8b70a62 in func_level_2() /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/null_pointer.cpp:13
    #2 0x5cd2c8b70bc0 in func_level_1() /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/null_pointer.cpp:18
    #3 0x5cd2c8b70d1e in main /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/null_pointer.cpp:23
    #4 0x738ac2c2a1c9 in __libc_start_call_main ../sysdeps/nptl/libc_start_call_main.h:58
    #5 0x738ac2c2a28a in __libc_start_main_impl ../csu/libc-start.c:360
    #6 0x5cd2c8b701e4 in _start (/home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/null_pointer/null_pointer_debug+0x31e4) (BuildId: d9fbe1f62afbd1f5e149688e90ebd0521d5d4565)

AddressSanitizer can not provide additional info.
SUMMARY: AddressSanitizer: SEGV /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/null_pointer.cpp:7 in func_level_3()
==1394340==ABORTING
timeout: 被监视的命令已核心转储
./ASan.sh: 第 65 行： 1394339 已放弃               timeout 30s "$EXECUTABLE" 2>&1

==========================
程序退出码: 134
时间: 2025年 07月 13日 星期日 03:12:28 CST
=== 程序执行结束 ===

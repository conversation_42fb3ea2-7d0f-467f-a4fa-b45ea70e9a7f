#include <iostream>

void func_level_3() {
    std::cout << "进入 func_level_3()，准备使用已释放内存..." << std::endl;
    int* ptr = new int(42);
    std::cout << "分配内存并设置值: " << *ptr << std::endl;
    delete ptr;
    std::cout << "内存已释放，现在尝试访问..." << std::endl;
    int value = *ptr;  // 崩溃点：使用已释放的内存
    std::cout << "不会执行到这行，value: " << value << std::endl;
}

void func_level_2() {
    std::cout << "进入 func_level_2()" << std::endl;
    func_level_3();
}

void func_level_1() {
    std::cout << "进入 func_level_1()" << std::endl;
    func_level_2();
}

int main() {
    std::cout << "程序开始运行..." << std::endl;
    func_level_1();
    std::cout << "程序结束（不会执行到这里）" << std::endl;
    return 0;
}

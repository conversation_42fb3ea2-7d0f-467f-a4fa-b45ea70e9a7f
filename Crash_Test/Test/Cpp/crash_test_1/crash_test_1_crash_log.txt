=== 程序开始执行 ===
时间: 2025年 07月 13日 星期日 02:26:00 CST
可执行文件: ./crash_test_1/crash_test_1_debug
ASan选项: abort_on_error=1:print_stacktrace=1:symbolize=1:halt_on_error=1
==========================

程序开始运行...
进入 func_level_1()
进入 func_level_2()
进入 func_level_3()
进入 func_level_4()
进入 func_level_5()
准备在 func_level_5() 访问空指针...
crash_test_1.cpp:7:9: runtime error: load of null pointer of type 'int'
    #0 0x63c0558c754f in func_level_5() /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/crash_test_1.cpp:7
    #1 0x63c0558c7939 in func_level_4() /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/crash_test_1.cpp:13
    #2 0x63c0558c7a97 in func_level_3() /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/crash_test_1.cpp:18
    #3 0x63c0558c7bf5 in func_level_2() /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/crash_test_1.cpp:23
    #4 0x63c0558c7d53 in func_level_1() /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/crash_test_1.cpp:28
    #5 0x63c0558c7eb1 in main /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/crash_test_1.cpp:33
    #6 0x77552122a1c9 in __libc_start_call_main ../sysdeps/nptl/libc_start_call_main.h:58
    #7 0x77552122a28a in __libc_start_main_impl ../csu/libc-start.c:360
    #8 0x63c0558c71c4 in _start (/home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/crash_test_1/crash_test_1_debug+0x31c4) (BuildId: f9b827f707dca59965bc5c7f2d7ef3129173ca81)

AddressSanitizer:DEADLYSIGNAL
=================================================================
==1380897==ERROR: AddressSanitizer: SEGV on unknown address 0x000000000000 (pc 0x63c0558c758b bp 0x7ffef3f2ac90 sp 0x7ffef3f2ac70 T0)
==1380897==The signal is caused by a READ memory access.
==1380897==Hint: address points to the zero page.
    #0 0x63c0558c758b in func_level_5() /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/crash_test_1.cpp:7
    #1 0x63c0558c7939 in func_level_4() /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/crash_test_1.cpp:13
    #2 0x63c0558c7a97 in func_level_3() /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/crash_test_1.cpp:18
    #3 0x63c0558c7bf5 in func_level_2() /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/crash_test_1.cpp:23
    #4 0x63c0558c7d53 in func_level_1() /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/crash_test_1.cpp:28
    #5 0x63c0558c7eb1 in main /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/crash_test_1.cpp:33
    #6 0x77552122a1c9 in __libc_start_call_main ../sysdeps/nptl/libc_start_call_main.h:58
    #7 0x77552122a28a in __libc_start_main_impl ../csu/libc-start.c:360
    #8 0x63c0558c71c4 in _start (/home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/crash_test_1/crash_test_1_debug+0x31c4) (BuildId: f9b827f707dca59965bc5c7f2d7ef3129173ca81)

AddressSanitizer can not provide additional info.
SUMMARY: AddressSanitizer: SEGV /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/crash_test_1.cpp:7 in func_level_5()
==1380897==ABORTING
timeout: 被监视的命令已核心转储
./ASan.sh: 第 65 行： 1380896 已放弃               timeout 30s "$EXECUTABLE" 2>&1

==========================
程序退出码: 134
时间: 2025年 07月 13日 星期日 02:26:01 CST
=== 程序执行结束 ===

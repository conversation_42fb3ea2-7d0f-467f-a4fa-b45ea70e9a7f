=== 程序开始执行 ===
时间: 2025年 07月 13日 星期日 16:32:10 CST
可执行文件: ./normal_execution/normal_execution_debug
ASan选项: abort_on_error=1:print_stacktrace=1:symbolize=1:halt_on_error=1
==========================

程序开始运行...
进入 func_level_1()
进入 func_level_2()
进入 func_level_3()
进入 string_operations()，执行字符串操作...
缓冲区大小: 100 字节
字符串长度: 30 字节
安全复制完成: 这是一个安全的字符串
进入 memory_operations()，执行内存操作...
分配内存并设置值: 42
修改后的值: 100
内存已正确释放
进入 array_operations()，执行数组操作...
数组大小: 5
arr[0] = 1
arr[1] = 2
arr[2] = 3
arr[3] = 4
arr[4] = 5
安全访问 arr.at(2): 3
func_level_3() 执行完成
func_level_2() 执行完成
func_level_1() 执行完成
所有操作成功完成！
程序正常结束

==========================
程序退出码: 0
时间: 2025年 07月 13日 星期日 16:32:10 CST
=== 程序执行结束 ===

=== 程序开始执行 ===
时间: 2025年 07月 13日 星期日 16:30:18 CST
可执行文件: ./null_pointer/null_pointer_debug
ASan选项: abort_on_error=1:print_stacktrace=1:symbolize=1:halt_on_error=1
==========================

程序开始运行...
进入 func_level_1()
进入 func_level_2()
进入 func_level_3()，准备访问空指针...
空指针地址: 0
../null_pointer.cpp:7:9: runtime error: load of null pointer of type 'int'
    #0 0x56613b706678 in func_level_3() ../null_pointer.cpp:7
    #1 0x56613b706a62 in func_level_2() ../null_pointer.cpp:13
    #2 0x56613b706bc0 in func_level_1() ../null_pointer.cpp:18
    #3 0x56613b706d1e in main ../null_pointer.cpp:23
    #4 0x755c6522a1c9 in __libc_start_call_main ../sysdeps/nptl/libc_start_call_main.h:58
    #5 0x755c6522a28a in __libc_start_main_impl ../csu/libc-start.c:360
    #6 0x56613b7061e4 in _start (/home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/ASan/null_pointer/null_pointer_debug+0x31e4) (BuildId: 4dc33246281801afee2f80e2dae00bec75f6f0c7)

AddressSanitizer:DEADLYSIGNAL
=================================================================
==1588265==ERROR: AddressSanitizer: SEGV on unknown address 0x000000000000 (pc 0x56613b7066b4 bp 0x7ffc9ae8b1c0 sp 0x7ffc9ae8b1a0 T0)
==1588265==The signal is caused by a READ memory access.
==1588265==Hint: address points to the zero page.
    #0 0x56613b7066b4 in func_level_3() ../null_pointer.cpp:7
    #1 0x56613b706a62 in func_level_2() ../null_pointer.cpp:13
    #2 0x56613b706bc0 in func_level_1() ../null_pointer.cpp:18
    #3 0x56613b706d1e in main ../null_pointer.cpp:23
    #4 0x755c6522a1c9 in __libc_start_call_main ../sysdeps/nptl/libc_start_call_main.h:58
    #5 0x755c6522a28a in __libc_start_main_impl ../csu/libc-start.c:360
    #6 0x56613b7061e4 in _start (/home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/ASan/null_pointer/null_pointer_debug+0x31e4) (BuildId: 4dc33246281801afee2f80e2dae00bec75f6f0c7)

AddressSanitizer can not provide additional info.
SUMMARY: AddressSanitizer: SEGV ../null_pointer.cpp:7 in func_level_3()
==1588265==ABORTING
timeout: 被监视的命令已核心转储
./ASan.sh: 第 69 行： 1588264 已放弃               timeout 30s "$EXECUTABLE" 2>&1

==========================
程序退出码: 134
时间: 2025年 07月 13日 星期日 16:30:19 CST
=== 程序执行结束 ===

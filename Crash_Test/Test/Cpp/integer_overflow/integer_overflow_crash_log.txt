=== 程序开始执行 ===
时间: 2025年 07月 13日 星期日 02:59:02 CST
可执行文件: ./integer_overflow/integer_overflow_debug
ASan选项: abort_on_error=1:print_stacktrace=1:symbolize=1:halt_on_error=1
==========================

程序开始运行...
进入 func_level_1()
进入 func_level_2()
进入 func_level_3()，准备整数溢出...
当前最大整数值: 2147483647
准备加1...
integer_overflow.cpp:9:9: runtime error: signed integer overflow: 2147483647 + 1 cannot be represented in type 'int'
    #0 0x58d6993da8f7 in func_level_3() /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/integer_overflow.cpp:9
    #1 0x58d6993db401 in func_level_2() /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/integer_overflow.cpp:22
    #2 0x58d6993db55f in func_level_1() /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/integer_overflow.cpp:27
    #3 0x58d6993db6bd in main /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/integer_overflow.cpp:32
    #4 0x7c22f0e2a1c9 in __libc_start_call_main ../sysdeps/nptl/libc_start_call_main.h:58
    #5 0x7c22f0e2a28a in __libc_start_main_impl ../csu/libc-start.c:360
    #6 0x58d6993da284 in _start (/home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/integer_overflow/integer_overflow_debug+0x3284) (BuildId: 26d5aec4ff65576d5acb81bbb72a9447e0ddd262)

溢出后的值: -2147483648
尝试访问数组[-2147483648]
integer_overflow.cpp:16:26: runtime error: index -2147483648 out of bounds for type 'int [5]'
    #0 0x58d6993daf10 in func_level_3() /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/integer_overflow.cpp:16
    #1 0x58d6993db401 in func_level_2() /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/integer_overflow.cpp:22
    #2 0x58d6993db55f in func_level_1() /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/integer_overflow.cpp:27
    #3 0x58d6993db6bd in main /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/integer_overflow.cpp:32
    #4 0x7c22f0e2a1c9 in __libc_start_call_main ../sysdeps/nptl/libc_start_call_main.h:58
    #5 0x7c22f0e2a28a in __libc_start_main_impl ../csu/libc-start.c:360
    #6 0x58d6993da284 in _start (/home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/integer_overflow/integer_overflow_debug+0x3284) (BuildId: 26d5aec4ff65576d5acb81bbb72a9447e0ddd262)

AddressSanitizer:DEADLYSIGNAL
=================================================================
==1390271==ERROR: AddressSanitizer: SEGV on unknown address 0x7c20eee00020 (pc 0x58d6993dafe2 bp 0x7ffd5d3119f0 sp 0x7ffd5d311930 T0)
==1390271==The signal is caused by a READ memory access.
    #0 0x58d6993dafe2 in func_level_3() /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/integer_overflow.cpp:16
    #1 0x58d6993db401 in func_level_2() /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/integer_overflow.cpp:22
    #2 0x58d6993db55f in func_level_1() /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/integer_overflow.cpp:27
    #3 0x58d6993db6bd in main /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/integer_overflow.cpp:32
    #4 0x7c22f0e2a1c9 in __libc_start_call_main ../sysdeps/nptl/libc_start_call_main.h:58
    #5 0x7c22f0e2a28a in __libc_start_main_impl ../csu/libc-start.c:360
    #6 0x58d6993da284 in _start (/home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/integer_overflow/integer_overflow_debug+0x3284) (BuildId: 26d5aec4ff65576d5acb81bbb72a9447e0ddd262)

AddressSanitizer can not provide additional info.
SUMMARY: AddressSanitizer: SEGV /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/integer_overflow.cpp:16 in func_level_3()
==1390271==ABORTING
timeout: 被监视的命令已核心转储
./ASan.sh: 第 65 行： 1390270 已放弃               timeout 30s "$EXECUTABLE" 2>&1

==========================
程序退出码: 134
时间: 2025年 07月 13日 星期日 02:59:03 CST
=== 程序执行结束 ===

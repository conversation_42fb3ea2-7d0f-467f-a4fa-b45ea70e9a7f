=== 程序开始执行 ===
时间: 2025年 07月 13日 星期日 02:58:55 CST
可执行文件: ./stack_overflow/stack_overflow_debug
ASan选项: abort_on_error=1:print_stacktrace=1:symbolize=1:halt_on_error=1
==========================

程序开始运行...
进入 func_level_1()
进入 func_level_2()
进入 func_level_3()，准备栈缓冲区溢出...
=================================================================
==1390114==ERROR: AddressSanitizer: stack-buffer-overflow on address 0x7eb4d7b0002a at pc 0x7eb4dacfb303 bp 0x7ffeffc12790 sp 0x7ffeffc11f38
WRITE of size 64 at 0x7eb4d7b0002a thread T0
    #0 0x7eb4dacfb302 in memcpy ../../../../src/libsanitizer/sanitizer_common/sanitizer_common_interceptors_memintrinsics.inc:115
    #1 0x61651c8a8493 in func_level_3() /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/stack_overflow.cpp:7
    #2 0x61651c8a8797 in func_level_2() /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/stack_overflow.cpp:13
    #3 0x61651c8a88f5 in func_level_1() /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/stack_overflow.cpp:18
    #4 0x61651c8a8a53 in main /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/stack_overflow.cpp:23
    #5 0x7eb4d9c2a1c9 in __libc_start_call_main ../sysdeps/nptl/libc_start_call_main.h:58
    #6 0x7eb4d9c2a28a in __libc_start_main_impl ../csu/libc-start.c:360
    #7 0x61651c8a81e4 in _start (/home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/stack_overflow/stack_overflow_debug+0x21e4) (BuildId: c149ecdb9b468c6e498bffd0c541bf0f7190f52e)

Address 0x7eb4d7b0002a is located in stack of thread T0 at offset 42 in frame
    #0 0x61651c8a82b8 in func_level_3() /home/<USER>/Ryan_/TENmini/Crash_Test/Test/Cpp/stack_overflow.cpp:4

  This frame has 1 object(s):
    [32, 42) 'buffer' (line 6) <== Memory access at offset 42 overflows this variable
HINT: this may be a false positive if your program uses some custom stack unwind mechanism, swapcontext or vfork
      (longjmp and C++ exceptions *are* supported)
SUMMARY: AddressSanitizer: stack-buffer-overflow ../../../../src/libsanitizer/sanitizer_common/sanitizer_common_interceptors_memintrinsics.inc:115 in memcpy
Shadow bytes around the buggy address:
  0x7eb4d7affd80: 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
  0x7eb4d7affe00: 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
  0x7eb4d7affe80: 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
  0x7eb4d7afff00: 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
  0x7eb4d7afff80: 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
=>0x7eb4d7b00000: f1 f1 f1 f1 00[02]f3 f3 00 00 00 00 00 00 00 00
  0x7eb4d7b00080: 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
  0x7eb4d7b00100: 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
  0x7eb4d7b00180: 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
  0x7eb4d7b00200: 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
  0x7eb4d7b00280: 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
Shadow byte legend (one shadow byte represents 8 application bytes):
  Addressable:           00
  Partially addressable: 01 02 03 04 05 06 07 
  Heap left redzone:       fa
  Freed heap region:       fd
  Stack left redzone:      f1
  Stack mid redzone:       f2
  Stack right redzone:     f3
  Stack after return:      f5
  Stack use after scope:   f8
  Global redzone:          f9
  Global init order:       f6
  Poisoned by user:        f7
  Container overflow:      fc
  Array cookie:            ac
  Intra object redzone:    bb
  ASan internal:           fe
  Left alloca redzone:     ca
  Right alloca redzone:    cb
==1390114==ABORTING
timeout: 被监视的命令已核心转储
./ASan.sh: 第 65 行： 1390113 已放弃               timeout 30s "$EXECUTABLE" 2>&1

==========================
程序退出码: 134
时间: 2025年 07月 13日 星期日 02:58:56 CST
=== 程序执行结束 ===

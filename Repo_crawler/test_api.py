#!/usr/bin/env python3
"""
API调用测试脚本
用于诊断GitHub API调用问题
"""

import requests
import json
import os

def test_github_api():
    """测试GitHub API连接"""
    print("🧪 GitHub API连接测试")
    print("=" * 50)
    
    # 检查网络连接
    print("1. 测试网络连接...")
    try:
        response = requests.get("https://api.github.com", timeout=10)
        print(f"   ✅ GitHub API可访问 (状态码: {response.status_code})")
    except Exception as e:
        print(f"   ❌ 网络连接失败: {e}")
        return
    
    # 检查API限制
    print("\n2. 检查API限制...")
    try:
        response = requests.get("https://api.github.com/rate_limit", timeout=10)
        if response.status_code == 200:
            data = response.json()
            core = data['resources']['core']
            print(f"   ✅ API限制信息:")
            print(f"      剩余调用次数: {core['remaining']}/{core['limit']}")
            print(f"      重置时间: {core['reset']}")
        else:
            print(f"   ⚠️  无法获取API限制信息 (状态码: {response.status_code})")
    except Exception as e:
        print(f"   ❌ 获取API限制失败: {e}")
    
    # 检查Token
    token = os.getenv('GITHUB_TOKEN')
    print(f"\n3. 检查GitHub Token...")
    if token:
        print(f"   ✅ 找到Token (长度: {len(token)})")
        
        # 测试Token有效性
        headers = {'Authorization': f'token {token}'}
        try:
            response = requests.get("https://api.github.com/user", headers=headers, timeout=10)
            if response.status_code == 200:
                user_data = response.json()
                print(f"   ✅ Token有效 (用户: {user_data.get('login', 'unknown')})")
            else:
                print(f"   ❌ Token无效 (状态码: {response.status_code})")
        except Exception as e:
            print(f"   ❌ Token验证失败: {e}")
    else:
        print("   ⚠️  未找到GITHUB_TOKEN环境变量")
    
    # 测试仓库访问
    print(f"\n4. 测试仓库访问...")
    test_repos = [
        "octocat/Hello-World",
        "microsoft/vscode"
    ]
    
    for repo in test_repos:
        try:
            url = f"https://api.github.com/repos/{repo}"
            headers = {'Authorization': f'token {token}'} if token else {}
            response = requests.get(url, headers=headers, timeout=10)
            
            if response.status_code == 200:
                print(f"   ✅ {repo} - 可访问")
            elif response.status_code == 404:
                print(f"   ❌ {repo} - 不存在或私有")
            elif response.status_code == 403:
                print(f"   ⚠️  {repo} - 权限不足或API限制")
            else:
                print(f"   ❌ {repo} - 错误 (状态码: {response.status_code})")
                
        except Exception as e:
            print(f"   ❌ {repo} - 网络错误: {e}")

def test_issue_access():
    """测试Issue访问"""
    print(f"\n5. 测试Issue访问...")
    
    token = os.getenv('GITHUB_TOKEN')
    headers = {'Authorization': f'token {token}'} if token else {}
    
    try:
        url = "https://api.github.com/repos/octocat/Hello-World/issues"
        params = {'state': 'closed', 'per_page': 1}
        response = requests.get(url, headers=headers, params=params, timeout=10)
        
        if response.status_code == 200:
            issues = response.json()
            if issues:
                print(f"   ✅ 成功获取Issues (第一个: #{issues[0]['number']})")
            else:
                print(f"   ⚠️  仓库没有closed Issues")
        else:
            print(f"   ❌ 获取Issues失败 (状态码: {response.status_code})")
            if response.status_code == 403:
                print(f"      可能是API限制，请稍后重试或使用Token")
                
    except Exception as e:
        print(f"   ❌ 获取Issues失败: {e}")

def show_suggestions():
    """显示解决建议"""
    print(f"\n💡 解决建议:")
    print("=" * 50)
    print("1. 如果遇到403错误:")
    print("   - 设置GitHub Token: export GITHUB_TOKEN='your_token'")
    print("   - 等待API限制重置")
    print("   - 检查Token权限")
    
    print("\n2. 如果遇到404错误:")
    print("   - 检查仓库URL是否正确")
    print("   - 确认仓库是公开的")
    print("   - 使用Token访问私有仓库")
    
    print("\n3. 如果遇到网络错误:")
    print("   - 检查网络连接")
    print("   - 检查防火墙设置")
    print("   - 尝试使用代理")

if __name__ == "__main__":
    test_github_api()
    test_issue_access()
    show_suggestions()

[{"issue_number": "255650", "issue_url": "https://github.com/microsoft/vscode/issues/255650", "state": "closed", "title": "Copilot stops responsing", "content": "", "created_at": "2025-07-13T15:16:35Z", "first_reply_time": "", "first_reply_content": ""}, {"issue_number": "255647", "issue_url": "https://github.com/microsoft/vscode/issues/255647", "state": "closed", "title": "remove files and run bad command", "content": "Type: <b>Bug</b>\nthis is not good as ai can access and suggest high level risk commands\n|Item|Value|\n|---|---|\n|CPUs|11th Gen Intel(R) Core(TM) i3-1115G4 @ 3.00GHz (4 x 3973)|\n|GPU Status|2d_canvas: enabled<br>canvas_oop_rasterization: enabled_on<br>direct_rendering_display_compositor: disabled_off_ok<br>gpu_compositing: enabled<br>multiple_raster_threads: enabled_on<br>opengl: enabled_on<br>rasterization: enabled<br>raw_draw: disabled_off_ok<br>skia_graphite: disabled_off<br>video_decode: enabled<br>video_encode: disabled_software<br>vulkan: disabled_off<br>webgl: enabled<br>webgl2: enabled<br>webgpu: disabled_off<br>webnn: disabled_off|\n|Load (avg)|2, 3, 3|\n|Memory (System)|19.24GB (9.56GB free)|\n|Process Argv|/home/<USER>/Desktop/github/sharkade --crash-reporter-id 2bcaeb8d-b548-4473-a3b7-8e1d5c10e7d2|\n|Screen Reader|no|\n|VM|0%|\n|DESKTOP_SESSION|ubuntu|\n|XDG_CURRENT_DESKTOP|Unity|\n|XDG_SESSION_DESKTOP|ubuntu|\n|XDG_SESSION_TYPE|wayland|\n</details><details>\n```\nvsliv368cf:30146710\nvspor879:30202332\nvspor708:30202333\nvspor363:30204092\npythonvspyt551cf:31249601\nbinariesv615:30325510\nc4g48928:30535728\nazure-dev_surveyone:30548225\n962ge761:30959799\n2e7ec940:31000449\ncppperfnew:31000557\ndwnewjupyter:31046869\nnativeloc1:31344060\n5fd0e150:31155592\ndwcopilot:31170013\n6074i472:31201624\ndwoutputs:31242946\nhdaa2157:31222309\ncopilot_t_ci:31333650\ne5gg6876:31282496\npythoneinst12:31285622\nc7cif404:31314491\npythonpulldiag:31343502\n996jf627:31283433\npythonrdcb7:31342333\nusemplatestapi:31297334\n0aa6g176:31307128\n747dc170:31275177\naj953862:31281341\ngeneratesymbolt:31295002\nconvertfstringf:31295003\n9d2cg352:31346308\nusemarketplace:31343026\nnesew2to5:31336538\nagentclaude:31335815\nnes-diff-11:31337487\n6abeh943:31336334\nyijiwantestdri0626-t:31336930\n4gdec884:31342391\naj9gi531:31345572\n```\n</details>\n<!-- generated by issue reporter -->", "created_at": "2025-07-13T14:43:33Z", "first_reply_time": "", "first_reply_content": ""}]
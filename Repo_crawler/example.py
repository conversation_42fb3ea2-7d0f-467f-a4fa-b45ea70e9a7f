#!/usr/bin/env python3
"""
使用示例
"""

from issue_crawler import GitHubIssueCrawler
import json

def basic_example():
    """基本使用"""
    print("=== 基本使用示例 ===")
    
    crawler = GitHubIssueCrawler()
    
    # 爬取小型项目
    try:
        issues = crawler.crawl("https://github.com/octocat/Hello-World", "example.json", max_issues=5)
        
        print(f"获取到 {len(issues)} 个Issues")
        
        # 显示前3个
        for i, issue in enumerate(issues[:3]):
            print(f"\nIssue {i+1}: {issue['title']}")
            print(f"  编号: {issue['issue_number']}")
            print(f"  状态: {issue['state']}")
            print(f"  创建时间: {issue['created_at']}")
            print(f"  PR创建时间: {issue['pr_created_time']}")
            print(f"  PR合并时间: {issue['pr_merged_time']}")
            print(f"  PR编号: {issue['pr_number']}")
            print(f"  内容: {issue['content'][:50]}...")
            
    except Exception as e:
        print(f"错误: {e}")

def view_json_example():
    """查看JSON文件示例"""
    print("\n=== 查看JSON文件示例 ===")
    
    try:
        with open("example.json", "r", encoding="utf-8") as f:
            issues = json.load(f)
        
        print(f"JSON文件包含 {len(issues)} 个Issues")
        
        # 显示JSON格式
        if issues:
            print("\n第一个Issue的JSON格式:")
            print(json.dumps(issues[0], ensure_ascii=False, indent=2))
            
    except FileNotFoundError:
        print("请先运行basic_example()生成JSON文件")
    except Exception as e:
        print(f"错误: {e}")

if __name__ == "__main__":
    basic_example()
    view_json_example()

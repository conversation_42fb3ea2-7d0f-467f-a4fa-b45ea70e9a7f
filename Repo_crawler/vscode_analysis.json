[{"issue_number": "255659", "issue_url": "https://github.com/microsoft/vscode/issues/255659", "state": "closed", "title": "[Again]. Sorry, the response matched public code so it was blocked. Please rephrase your prompt.", "content": "Type: <b>Bug</b>\nAfter some questions and with the extentnsions updated I continue receive this reponse:\nSorry, the response matched public code so it was blocked. Please rephrase your prompt.\nI check my limit, and I have 10% of usage so what is going on?\nThe question if you want,\nCheck my Members component I want display a table of members\nOnly create a table with te current data and display in a existing component.\nThe prompt arrive because I see the generation of text and then all is hiden and display the error\n|Item|Value|\n|---|---|\n|CPUs|Apple M1 Max (10 x 2400)|\n|GPU Status|2d_canvas: enabled<br>canvas_oop_rasterization: enabled_on<br>direct_rendering_display_compositor: disabled_off_ok<br>gpu_compositing: enabled<br>multiple_raster_threads: enabled_on<br>opengl: enabled_on<br>rasterization: enabled<br>raw_draw: disabled_off_ok<br>skia_graphite: enabled_on<br>video_decode: enabled<br>video_encode: enabled<br>webgl: enabled<br>webgl2: enabled<br>webgpu: enabled<br>webnn: disabled_off|\n|Load (avg)|3, 3, 3|\n|Memory (System)|32.00GB (1.21GB free)|\n|Process Argv|--crash-reporter-id e93ca1df-2109-4fa5-8595-c18b4beed9b3|\n|Screen Reader|no|\n|VM|0%|\n</details><details>\n```\nvsliv368:30146709\nvspor879:30202332\nvspor708:30202333\nvspor363:30204092\npythonvspyt551:31249597\nc4g48928:30535728\n962ge761:30841072\nh48ei257:31000450\ncppperfnew:30980852\ndwnewjupyter:31046869\nnativeloc1:31118317\ne80f6927:31120813\ndwcopilot:31158714\nc3hdf307:31184662\n6074i472:31201624\ndwoutputs:31242946\ncustomenabled:31248079\nhdaa2157:31222309\ncopilot_t_ci:31333650\ng012b348:31231168\npythoneinst12:31251391\nc7cif404:31309980\npythonpulldiag:31287486\n6gi0g917:31259950\n996jf627:31264550\npythonrdcb7:31268811\nusemplatestapi:31297334\njdghv92:31317040\n747dc170:31275146\n6518g693:31302842\ngeneratesymbolt:31280541\nconvertfstringf:31280702\nb99bg931:31306656\nusemarketplace:31343026\n0g1h6703:31329154\nnes-emitfast-1:31333560\nreplacestringexc:31340153\nonetestforazureexp:31335613\n6abeh943:31336334\nenvsactivate1:31343186\nnes-conv-11:31337514\n0927b901:31340060\ngji67723:31340537\nji9b5146:31342393\n0ej4-default:31344841\n```\n</details>\n<!-- generated by issue reporter -->", "created_at": "2025-07-13T16:41:10Z", "pr_merged_time": "", "pr_number": "", "pr_url": ""}, {"issue_number": "255656", "issue_url": "https://github.com/microsoft/vscode/issues/255656", "state": "closed", "title": "language model unavailable", "content": "Type: <b>Bug</b>\nlanguage model unavailable\n```\nvsliv368:30146709\nvspor879:30202332\nvspor708:30202333\nvspor363:30204092\nbinariesv615:30325510\nc4g48928:30535728\nazure-dev_surveyone:30548225\n962ge761:30959799\nh48ei257:31000450\ncppperfnew:31000557\ndwnewjupyter:31046869\nnativeloc1:31344060\n5fd0e150:31155592\ndwcopilot:31170013\n6074i472:31201624\ndwoutputs:31242946\n9064b325:31222308\ncopilot_t_ci:31333650\ne5gg6876:31282496\npythoneinst12:31285622\nc7cif404:31314491\npythonpulldiag:31343502\n996jf627:31283433\npythonrdcb7:31342333\nusemplatestapi:31297334\n0aa6g176:31307128\n747dc170:31275177\naj953862:31281341\ngeneratesymbolt:31295002\nconvertfstringf:31295003\npylancequickfixf:31329273\n9d2cg352:31346308\nusemarketplace:31343026\nnesew2to5:31336538\nagentclaude:31335815\nnes-diff-11:31337487\n6abeh943:31336334\nyijiwantestdri0626-c:31336931\nf76d9909:31342392\n```\n</details>\n<!-- generated by issue reporter -->", "created_at": "2025-07-13T16:16:22Z", "pr_merged_time": "", "pr_number": "", "pr_url": ""}, {"issue_number": "255650", "issue_url": "https://github.com/microsoft/vscode/issues/255650", "state": "closed", "title": "Copilot stops responsing", "content": "", "created_at": "2025-07-13T15:16:35Z", "pr_merged_time": "", "pr_number": "", "pr_url": ""}, {"issue_number": "255647", "issue_url": "https://github.com/microsoft/vscode/issues/255647", "state": "closed", "title": "remove files and run bad command", "content": "Type: <b>Bug</b>\nthis is not good as ai can access and suggest high level risk commands\n|Item|Value|\n|---|---|\n|CPUs|11th Gen Intel(R) Core(TM) i3-1115G4 @ 3.00GHz (4 x 3973)|\n|GPU Status|2d_canvas: enabled<br>canvas_oop_rasterization: enabled_on<br>direct_rendering_display_compositor: disabled_off_ok<br>gpu_compositing: enabled<br>multiple_raster_threads: enabled_on<br>opengl: enabled_on<br>rasterization: enabled<br>raw_draw: disabled_off_ok<br>skia_graphite: disabled_off<br>video_decode: enabled<br>video_encode: disabled_software<br>vulkan: disabled_off<br>webgl: enabled<br>webgl2: enabled<br>webgpu: disabled_off<br>webnn: disabled_off|\n|Load (avg)|2, 3, 3|\n|Memory (System)|19.24GB (9.56GB free)|\n|Process Argv|/home/<USER>/Desktop/github/sharkade --crash-reporter-id 2bcaeb8d-b548-4473-a3b7-8e1d5c10e7d2|\n|Screen Reader|no|\n|VM|0%|\n|DESKTOP_SESSION|ubuntu|\n|XDG_CURRENT_DESKTOP|Unity|\n|XDG_SESSION_DESKTOP|ubuntu|\n|XDG_SESSION_TYPE|wayland|\n</details><details>\n```\nvsliv368cf:30146710\nvspor879:30202332\nvspor708:30202333\nvspor363:30204092\npythonvspyt551cf:31249601\nbinariesv615:30325510\nc4g48928:30535728\nazure-dev_surveyone:30548225\n962ge761:30959799\n2e7ec940:31000449\ncppperfnew:31000557\ndwnewjupyter:31046869\nnativeloc1:31344060\n5fd0e150:31155592\ndwcopilot:31170013\n6074i472:31201624\ndwoutputs:31242946\nhdaa2157:31222309\ncopilot_t_ci:31333650\ne5gg6876:31282496\npythoneinst12:31285622\nc7cif404:31314491\npythonpulldiag:31343502\n996jf627:31283433\npythonrdcb7:31342333\nusemplatestapi:31297334\n0aa6g176:31307128\n747dc170:31275177\naj953862:31281341\ngeneratesymbolt:31295002\nconvertfstringf:31295003\n9d2cg352:31346308\nusemarketplace:31343026\nnesew2to5:31336538\nagentclaude:31335815\nnes-diff-11:31337487\n6abeh943:31336334\nyijiwantestdri0626-t:31336930\n4gdec884:31342391\naj9gi531:31345572\n```\n</details>\n<!-- generated by issue reporter -->", "created_at": "2025-07-13T14:43:33Z", "pr_merged_time": "", "pr_number": "", "pr_url": ""}, {"issue_number": "255646", "issue_url": "https://github.com/microsoft/vscode/issues/255646", "state": "closed", "title": "Gemini", "content": "Type: <b>Bug</b>\nSorry, your request failed. Please try again. Request id: f38b2671-a30b-45c1-9449-2c8658b064e0\nReason: Request Failed: 400 Bad Request\nAlways there is a problem when using Gemini 2.5 Pro.\n|Item|Value|\n|---|---|\n|CPUs|Intel(R) Core(TM) i5-10400 CPU @ 2.90GHz (12 x 2904)|\n|GPU Status|2d_canvas: enabled<br>canvas_oop_rasterization: enabled_on<br>direct_rendering_display_compositor: disabled_off_ok<br>gpu_compositing: enabled<br>multiple_raster_threads: enabled_on<br>opengl: enabled_on<br>rasterization: enabled<br>raw_draw: disabled_off_ok<br>skia_graphite: disabled_off<br>video_decode: enabled<br>video_encode: enabled<br>vulkan: disabled_off<br>webgl: enabled<br>webgl2: enabled<br>webgpu: enabled<br>webnn: disabled_off|\n|Load (avg)|undefined|\n|Memory (System)|19.83GB (6.43GB free)|\n|Process Argv|D:\\\\Projects\\\\Webscraper-excel support --crash-reporter-id 9ac532c1-ebfc-4a0a-9fd0-3bf077659618|\n|Screen Reader|no|\n|VM|0%|\n</details><details>\n```\nvsliv368:30146709\nvspor879:30202332\nvspor708:30202333\nvspor363:30204092\npythonvspyt551:31249597\nc4g48928:30535728\n962ge761:30841072\n2e7ec940:31000449\ncppperfnew:30980852\ndwnewjupytercf:31046870\nnativeloc1:31118317\ndwcopilot:31158714\nc3hdf307:31184662\n6074i472:31201624\ndwoutputs:31242946\nhdaa2157:31222309\ncopilot_t_ci:31333650\ng012b348:31231168\npythoneinst12:31251391\nc7cif404:31309980\npythonpulldiag:31287486\n6gi0g917:31259950\n996jf627:31264550\npythonrdcb7:31268811\nusemplatestapi:31297334\njdghv92:31317040\n747dc170:31275146\npythonpcpt1cf:31345881\n6518g693:31302842\ngeneratesymbolt:31280541\nconvertfstringf:31280702\n9d2cg352:31346308\nb99bg931:31306656\nusemarketplace:31343026\n0g1h6703:31329154\nnes-emitfast-1:31333560\ntestaa123:31335226\n6abeh943:31336334\nenvsactivate1:31343186\n0927b901:31340060\nji9b5146:31342393\neditstats-enabled:31346256\n```\n</details>\n<!-- generated by issue reporter -->", "created_at": "2025-07-13T14:38:21Z", "pr_merged_time": "", "pr_number": "", "pr_url": ""}, {"issue_number": "255641", "issue_url": "https://github.com/microsoft/vscode/issues/255641", "state": "closed", "title": "bug ", "content": "Type: <b>Bug</b>\ngabisa kirim intuksi\nsorry, your request failed. Please try again. Request id: b8d169af-8e18-4c7c-bf52-2184d0a2a3aa\nReason: Please check your firewall rules and network connection then try again. Error Code: net::ERR_HTTP2_PROTOCOL_ERROR.\n|Item|Value|\n|---|---|\n|CPUs|AMD Ryzen 7 6800H with Radeon Graphics          (16 x 3194)|\n|GPU Status|2d_canvas: enabled<br>canvas_oop_rasterization: enabled_on<br>direct_rendering_display_compositor: disabled_off_ok<br>gpu_compositing: enabled<br>multiple_raster_threads: enabled_on<br>opengl: enabled_on<br>rasterization: enabled<br>raw_draw: disabled_off_ok<br>skia_graphite: disabled_off<br>video_decode: enabled<br>video_encode: enabled<br>vulkan: disabled_off<br>webgl: enabled<br>webgl2: enabled<br>webgpu: enabled<br>webnn: disabled_off|\n|Load (avg)|undefined|\n|Memory (System)|13.75GB (4.98GB free)|\n|Process Argv|--crash-reporter-id 23886a29-ec65-4a86-a5e1-68c6fed50d97|\n|Screen Reader|no|\n|VM|0%|\n</details><details>\n```\nvsliv368cf:30146710\nvspor879:30202332\nvspor708:30202333\nvspor363:30204092\nbinariesv615:30325510\nc4g48928:30535728\nazure-dev_surveyone:30548225\n962ge761:30959799\nh48ei257:31000450\ncppperfnew:31000557\ndwnewjupyter:31046869\nnativeloc1:31344060\n5fd0e150:31155592\ndwcopilot:31170013\n6074i472:31201624\ndwoutputs:31242946\nhdaa2157:31222309\ncopilot_t_ci:31333650\ne5gg6876:31282496\npythoneinst12:31285622\nc7cif404:31314491\npythonpulldiag:31343502\n996jf627:31283433\npythonrdcb7:31342333\nusemplatestapi:31297334\n0aa6g176:31307128\n747dc170:31275177\naj953862:31281341\ngeneratesymbolt:31295002\nconvertfstringf:31295003\n9d2cg352:31346308\nusemarketplace:31343026\nnesew2to5:31336538\nagentclaude:31335815\nnes-diff-11:31337487\nonetestforazureexp:31335613\n6abeh943:31336334\nyijiwantestdri0626-c:31336931\nji9b5146:31342393\n```\n</details>\n<!-- generated by issue reporter -->", "created_at": "2025-07-13T12:15:57Z", "pr_merged_time": "", "pr_number": "", "pr_url": ""}, {"issue_number": "255640", "issue_url": "https://github.com/microsoft/vscode/issues/255640", "state": "closed", "title": "bug ", "content": "Type: <b>Bug</b>\ngabisa kirim intuksi\nsorry, your request failed. Please try again. Request id: b8d169af-8e18-4c7c-bf52-2184d0a2a3aa\nReason: Please check your firewall rules and network connection then try again. Error Code: net::ERR_HTTP2_PROTOCOL_ERROR.\n|Item|Value|\n|---|---|\n|CPUs|AMD Ryzen 7 6800H with Radeon Graphics          (16 x 3194)|\n|GPU Status|2d_canvas: enabled<br>canvas_oop_rasterization: enabled_on<br>direct_rendering_display_compositor: disabled_off_ok<br>gpu_compositing: enabled<br>multiple_raster_threads: enabled_on<br>opengl: enabled_on<br>rasterization: enabled<br>raw_draw: disabled_off_ok<br>skia_graphite: disabled_off<br>video_decode: enabled<br>video_encode: enabled<br>vulkan: disabled_off<br>webgl: enabled<br>webgl2: enabled<br>webgpu: enabled<br>webnn: disabled_off|\n|Load (avg)|undefined|\n|Memory (System)|13.75GB (4.98GB free)|\n|Process Argv|--crash-reporter-id 23886a29-ec65-4a86-a5e1-68c6fed50d97|\n|Screen Reader|no|\n|VM|0%|\n</details><details>\n```\nvsliv368cf:30146710\nvspor879:30202332\nvspor708:30202333\nvspor363:30204092\nbinariesv615:30325510\nc4g48928:30535728\nazure-dev_surveyone:30548225\n962ge761:30959799\nh48ei257:31000450\ncppperfnew:31000557\ndwnewjupyter:31046869\nnativeloc1:31344060\n5fd0e150:31155592\ndwcopilot:31170013\n6074i472:31201624\ndwoutputs:31242946\nhdaa2157:31222309\ncopilot_t_ci:31333650\ne5gg6876:31282496\npythoneinst12:31285622\nc7cif404:31314491\npythonpulldiag:31343502\n996jf627:31283433\npythonrdcb7:31342333\nusemplatestapi:31297334\n0aa6g176:31307128\n747dc170:31275177\naj953862:31281341\ngeneratesymbolt:31295002\nconvertfstringf:31295003\n9d2cg352:31346308\nusemarketplace:31343026\nnesew2to5:31336538\nagentclaude:31335815\nnes-diff-11:31337487\nonetestforazureexp:31335613\n6abeh943:31336334\nyijiwantestdri0626-c:31336931\nji9b5146:31342393\n```\n</details>\n<!-- generated by issue reporter -->", "created_at": "2025-07-13T12:15:55Z", "pr_merged_time": "", "pr_number": "", "pr_url": ""}, {"issue_number": "255635", "issue_url": "https://github.com/microsoft/vscode/issues/255635", "state": "closed", "title": "Refactor issue", "content": "Type: <b>Bug</b>\nI'm not too sure how it happened, because it was sudden, but while I was doing my work using Dar<PERSON>, I realise that I am not able to refactor things. Like if I had a container, I want to refactor it to make it have a Row as a parent, it says No Refactoring available.\nVS Code version: Code 1.102.0 (cb0c47c0cfaad0757385834bd89d410c78a856c0, 2025-07-09T22:10:34.600Z)\n|Item|Value|\n|---|---|\n|CPUs|AMD Ryzen 5 7600X 6-Core Processor              (12 x 4700)|\n|GPU Status|2d_canvas: enabled<br>canvas_oop_rasterization: enabled_on<br>direct_rendering_display_compositor: disabled_off_ok<br>gpu_compositing: enabled<br>multiple_raster_threads: enabled_on<br>opengl: enabled_on<br>rasterization: enabled<br>raw_draw: disabled_off_ok<br>skia_graphite: disabled_off<br>video_decode: enabled<br>video_encode: enabled<br>vulkan: disabled_off<br>webgl: enabled<br>webgl2: enabled<br>webgpu: enabled<br>webnn: disabled_off|\n|Load (avg)|undefined|\n|Memory (System)|31.19GB (14.53GB free)|\n|Process Argv|--crash-reporter-id 121f8d84-73e6-42ce-b642-8fcb758a3f57|\n|Screen Reader|no|\n|VM|0%|\n</details><details><summary>Extensions (7)</summary>\n```\nvsliv368cf:30146710\nvspor879:30202332\nvspor708:30202333\nvspor363:30204092\nvswsl492cf:30256860\npythonvspyt551:31249599\nbinariesv615:30325510\nc4g48928:30535728\nazure-dev_surveyonecf:30548226\nvscrp:30673768\n962ge761:30959799\n2e7ec940:31000449\ncppperfnew:31000557\ndwnewjupyter:31046869\nnativeloc1:31344060\n5fd0e150:31155592\ndwcopilot:31170013\n6074i472:31201624\ndwoutputs:31242946\nhdaa2157:31222309\ncopilot_t_ci:31333650\ne5gg6876:31282496\npythoneinst12:31285622\nc7cif404:31314491\npythonpulldiag:31343502\n996jf627:31283433\npythonrdcb7:31342333\nusemplatestapi:31297334\n0aa6g176:31307128\n747dc170:31275177\naj953862:31281341\ngeneratesymbolt:31295002\nconvertfstringf:31295003\n9d2cg352:31346308\nusemarketplace:31343026\nnesew2to5:31336538\nagentclaude:31335815\n82j33506:31327384\nnes-diff-11:31337487\ntestaa123:31335226\n6abeh943:31336334\nyijiwantestdri0626-c:31336931\nf76d9909:31342392\n```\n</details>\n<!-- generated by issue reporter -->", "created_at": "2025-07-13T10:59:49Z", "pr_merged_time": "", "pr_number": "", "pr_url": ""}, {"issue_number": "255631", "issue_url": "https://github.com/microsoft/vscode/issues/255631", "state": "closed", "title": "N905", "content": "<!-- ⚠️⚠️ Do Not Delete This! bug_report_template ⚠️⚠️ -->\n<!-- Please read our Rules of Conduct: https://opensource.microsoft.com/codeofconduct/ -->\n<!-- 🕮 Read our guide about submitting issues: https://github.com/microsoft/vscode/wiki/Submitting-Bugs-and-Suggestions -->\n<!-- 🔎 Search existing issues to avoid creating duplicates. -->\n<!-- 🧪 Test using the latest Insiders build to see if your issue has already been fixed: https://code.visualstudio.com/insiders/ -->\n<!-- 💡 Instead of creating your report here, use 'Report Issue' from the 'Help' menu in VS Code to pre-fill useful information. -->\n<!-- 🔧 Launch with `code --disable-extensions` to check. -->\nDoes this issue occur when all extensions are disabled?: Yes/No\n<!-- 🪓 If you answered No above, use 'Help: Start Extension Bisect' from Command Palette to try to identify the cause. -->\n<!-- 📣 Issues caused by an extension need to be reported directly to the extension publisher. The 'Help > Report Issue' dialog can assist with this. -->\n\nSteps: 1.\n2.", "created_at": "2025-07-13T08:51:53Z", "pr_merged_time": "", "pr_number": "", "pr_url": ""}, {"issue_number": "255627", "issue_url": "https://github.com/microsoft/vscode/issues/255627", "state": "closed", "title": "Confirm before closing window when a chat request is in progress", "content": "Show a confirmation dialog when closing the window, quitting the app or changing the window and a chat request session is in progress and running. This will help not cancelling a request that is in progress by accident.", "created_at": "2025-07-13T07:01:33Z", "pr_merged_time": "", "pr_number": "", "pr_url": ""}]
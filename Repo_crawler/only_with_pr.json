[{"issue_number": "255473", "issue_url": "https://github.com/microsoft/vscode/issues/255473", "state": "closed", "title": "Chat code block tokenization broken", "content": "### Problem\nIn the VS Code chat UI, code block tokenization and rendering appears to be broken. For example, instead of rendering code blocks or inline code, the chat displays the literal tag `<vscode_codeblock_uri isEdit>...</vscode_codeblock_uri>`. Additionally, markdown elements are not rendered as inline code blocks. Hyperlinks also do not appear to be rendered correctly.\n**Screenshot:**\n<img width=\"722\" height=\"300\" alt=\"Image\" src=\"https://github.com/user-attachments/assets/0d59eb2b-b08e-4e3f-82ae-3b516cf34f83\" />\n\nSteps: 1. Use the chat UI in VS Code (e.g., Copilot Chat or similar extension).\n2. Trigger a response that should include a code block or inline code (such as a todo list or a file URI).\n3. Observe that the literal tag `<vscode_codeblock_uri isEdit>...</vscode_codeblock_uri>` is shown, and markdown is not rendered as expected.\n\nExpected: - Code blocks and inline code should be rendered properly.\n- Special tags like `<vscode_codeblock_uri isEdit>` should be parsed and displayed as clickable links or code blocks, not as literal text.\n\nActual: - Literal tags are shown in the chat.\n- Markdown blocks are rendered as plain chat text.", "created_at": "2025-07-11T19:27:14Z", "pr_merged_time": "2025-07-11T16:22:30Z", "pr_number": "255429", "pr_url": "https://github.com/microsoft/vscode/pull/255429"}, {"issue_number": "255466", "issue_url": "https://github.com/microsoft/vscode/issues/255466", "state": "closed", "title": "Getting weird inline tags in chat", "content": "Type: <b>Bug</b>\nAfter updating the github copilot and copilot chat extensions this morning, i'm getting strange output in the chat while agent is running w/ sonnet 4.\nexample:\n|Item|Value|\n|---|---|\n|CPUs|Intel(R) Celeron(R) N4500 @ 1.10GHz (2 x 2800)|\n|GPU Status|2d_canvas: unavailable_software<br>canvas_oop_rasterization: disabled_off<br>direct_rendering_display_compositor: disabled_off_ok<br>gpu_compositing: disabled_software<br>multiple_raster_threads: disabled_off<br>opengl: disabled_off<br>rasterization: disabled_software<br>raw_draw: disabled_off_ok<br>skia_graphite: disabled_off<br>video_decode: disabled_software<br>video_encode: disabled_software<br>vulkan: disabled_off<br>webgl: unavailable_software<br>webgl2: unavailable_software<br>webgpu: disabled_off<br>webnn: unavailable_software|\n|Load (avg)|1, 2, 2|\n|Memory (System)|19.38GB (9.96GB free)|\n|Process Argv|--no-sandbox --force-user-env --crash-reporter-id 65d6bddd-4361-40d1-87b7-be5748fa09f4|\n|Screen Reader|no|\n|VM|60%|\n|DESKTOP_SESSION|ubuntu|\n|XDG_CURRENT_DESKTOP|Unity|\n|XDG_SESSION_DESKTOP|ubuntu|\n|XDG_SESSION_TYPE|wayland|\n|Item|Value|\n|---|---|\n|Remote|SSH: cribcritic-android|\n|OS|Linux x64 6.8.0-63-generic|\n|CPUs|Intel(R) Core(TM) i7-7740X CPU @ 4.30GHz (8 x 0)|\n|Memory (System)|14.51GB (11.35GB free)|\n|VM|0%|\n</details>\n<!-- generated by issue reporter -->\n\nActual: <vscode_codeblock_uri isEdit>vscode-remote://ssh-remote%2B<file_path_here></vscode_codeblock_uri>\ni seems to edit, but also spews  these things out. This changed from last night.", "created_at": "2025-07-11T18:30:48Z", "pr_merged_time": "2025-07-11T16:22:30Z", "pr_number": "255429", "pr_url": "https://github.com/microsoft/vscode/pull/255429"}, {"issue_number": "255438", "issue_url": "https://github.com/microsoft/vscode/issues/255438", "state": "closed", "title": "Cannot Keep code changes that suggested by Copilot Agent", "content": "<!-- ⚠️⚠️ Do Not Delete This! bug_report_template ⚠️⚠️ -->\n<!-- Please read our Rules of Conduct: https://opensource.microsoft.com/codeofconduct/ -->\n<!-- 🕮 Read our guide about submitting issues: https://github.com/microsoft/vscode/wiki/Submitting-Bugs-and-Suggestions -->\n<!-- 🔎 Search existing issues to avoid creating duplicates. -->\n<!-- 🧪 Test using the latest Insiders build to see if your issue has already been fixed: https://code.visualstudio.com/insiders/ -->\n<!-- 💡 Instead of creating your report here, use 'Report Issue' from the 'Help' menu in VS Code to pre-fill useful information. -->\n<!-- 🔧 Launch with `code --disable-extensions` to check. -->\nDoes this issue occur when all extensions are disabled?: Yes\n<!-- 🪓 If you answered No above, use 'Help: Start Extension Bisect' from Command Palette to try to identify the cause. -->\n<!-- 📣 Issues caused by an extension need to be reported directly to the extension publisher. The 'Help > Report Issue' dialog can assist with this. -->\n- VS Code Version: 1.103.0-insider (2eeceaa3bca9371bfa321e7afd80825554ecb207)\n\nSteps: 1. Let Copilot Agent create code changes\n2. Try to click 'Keep' button on Code editor View\nhttps://github.com/user-attachments/assets/42257c65-3397-454d-bac9-da6de665b977\nCheck mark button is worked.\nAnd Chat view's Keep button is worked.\n```\nactionViewItems.ts:173 Uncaught TypeError: Cannot read properties of undefined (reading 'run')\nat yc.onClick (actionViewItems.ts:173:21)\nat HTMLLIElement.<anonymous> (actionViewItems.ts:153:10)\nonClick @ actionViewItems.ts:173\n(anonymous) @ actionViewItems.ts:153\n```", "created_at": "2025-07-11T16:28:09Z", "pr_merged_time": "2025-07-09T08:41:31Z", "pr_number": "254797", "pr_url": "https://github.com/microsoft/vscode/pull/254797"}, {"issue_number": "255393", "issue_url": "https://github.com/microsoft/vscode/issues/255393", "state": "closed", "title": "Inline chat rendering is broken", "content": "- Open https://github.com/microsoft/vscode/blob/1652688dedca995b2ac5fa5e3a6e5f6288e9e1ce/src/vs/workbench/contrib/chat/common/promptSyntax/service/promptsServiceImpl.ts#L168 in an editor\n- Open inline chat (Ctrt + i) and ask 'change the regex so that it also support unicode word characters'\n- Output shows a xml content\n<img width=\"1173\" height=\"307\" alt=\"Image\" src=\"https://github.com/user-attachments/assets/13c512c4-e853-4c41-a3e6-6177ca0b31fb\" />\n<details>\n> 🚨 Note: This log may contain personal information such as the contents of your files or terminal output. Please review the contents carefully before sharing.\n# inline/edit - 036b4cb7\n## Metadata\n~~~\nrequestType      : ChatCompletions\nmodel            : gpt-4.1\nmaxPromptTokens  : 111616\nmaxResponseTokens: 16384\nlocation         : 4\npostOptions      : {\"temperature\":0.1,\"top_p\":1,\"max_tokens\":16384,\"n\":1,\"stream\":true}\nintent           : undefined\nstartTime        : 2025-07-11T13:43:32.832Z\nendTime          : 2025-07-11T13:43:35.340Z\nduration         : 2508ms\nourRequestId     : a40abcf0-41dc-4c37-b095-29fb9b0d784e\nrequestId        : a40abcf0-41dc-4c37-b095-29fb9b0d784e\nserverRequestId  : a40abcf0-41dc-4c37-b095-29fb9b0d784e\ntimeToFirstToken : 962ms\nusage            : {\"completion_tokens\":205,\"completion_tokens_details\":{\"accepted_prediction_tokens\":0,\"rejected_prediction_tokens\":0},\"prompt_tokens\":4427,\"prompt_tokens_details\":{\"cached_tokens\":4352},\"total_tokens\":4632}\n~~~\n## Request Messages\n### System\n~~~md\nYou are an AI programming assistant.\nWhen asked for your name, you must respond with \"GitHub Copilot\".\nFollow the user's requirements carefully & to the letter.\nFollow Microsoft content policies.\nAvoid content that violates copyrights.\nIf you are asked to generate content that is harmful, hateful, racist, sexist, lewd, violent, or completely irrelevant to software engineering, only respond with \"Sorry, I can't assist with that.\"\nKeep your answers short and impersonal.\nThe user has a request for modifying one or more files.\n1. Please come up with a solution that you first describe step-by-step.\n2. Group your changes by file. Use the file path as the header.\n3. For each file, give a short summary of what needs to be changed followed by a code block that contains the code changes.\n4. The code block should start with four backticks followed by the language.\n5. On the first line of the code block add a comment containing the filepath. This includes Markdown code blocks.\n6. Use a single code block per file that needs to be modified, even if there are multiple changes for a file.\n7. The user is very smart and can understand how to merge your code blocks into their files, you just need to provide minimal hints.\n8. Avoid repeating existing code, instead use comments to represent regions of unchanged code. The user prefers that you are as concise as possible. For example:\n````languageId\n// filepath: c:\\path\\to\\file\n// ...existing code...\n{ changed code }\n// ...existing code...\n{ changed code }\n// ...existing code...\n````\nHere is an example of how you should format a code block belonging to the file example.ts in your response:\n<example>\n### c:\\Users\\<USER>\\proj01\\example.ts\nAdd a new property 'age' and a new method 'getAge' to the class Person.\n````typescript\n// filepath: c:\\Users\\<USER>\\proj01\\example.ts\nclass Person {\n// ...existing code...\nage: number;\n// ...existing code...\ngetAge() {\nreturn this.age;\n}\n}\n````\n</example>\n~~~\n### User\n~~~md\nWhen generating code, please follow these user provided coding instructions. You can ignore an instruction if it contradicts a system message.\n<instructions>\n<attachment filePath=\"c:\\\\Users\\\\<USER>\\\\workspaces\\\\vscode\\\\.github\\\\copilot-instructions.md\">\n# Coding Guidelines\n## Introduction\nThese are VS Code coding guidelines. Please also review our [Source Code Organisation](https://github.com/microsoft/vscode/wiki/Source-Code-Organization) page.\n## Indentation\nWe use tabs, not spaces.\n## Naming Conventions\n* Use PascalCase for `type` names\n* Use PascalCase for `enum` values\n* Use camelCase for `function` and `method` names\n* Use camelCase for `property` names and `local variables`\n* Use whole words in names when possible\n## Types\n* Do not export `types` or `functions` unless you need to share it across multiple components\n* Do not introduce new `types` or `values` to the global namespace\n## Comments\n* When there are comments for `functions`, `interfaces`, `enums`, and `classes` use JSDoc style comments\n## Strings\n* Use \"double quotes\" for strings shown to the user that need to be externalized (localized)\n* Use 'single quotes' otherwise\n* All strings visible to the user need to be externalized\n## Style\n* Use arrow functions `=>` over anonymous function expressions\n* Only surround arrow function parameters when necessary. For example, `(x) => x + x` is wrong but the following are correct:\n```javascript\nx => x + x\n(x, y) => x + y\n<T>(x: T, y: T) => x === y\n```\n* Always surround loop and conditional bodies with curly braces\n* Open curly braces always go on the same line as whatever necessitates them\n* Parenthesized constructs should have no surrounding whitespace. A single space follows commas, colons, and semicolons in those constructs. For example:\n```javascript\nfor (let i = 0, n = str.length; i < 10; i++) {\nif (x < 10) {\nfoo();\n}\n}\nfunction f(x: number, y: string): void { }\n```\n</attachment>\n<attachment filePath=\"c:\\\\Users\\\\<USER>\\\\workspaces\\\\vscode\\\\.vscode\\\\project.instructions.md\">\n---\napplyTo: '**'\n---\n# VS Code Copilot Development Guide\nThis file contains key information to help AI assistants work more efficiently with the VS Code codebase.\n## Quick Reference for Common Issues\n### Build & Test Workflow\n1. **Compile**: `npm run compile` (required before testing code changes)\n2. **Run specific tests**: `./scripts/test.sh --grep \"pattern\"`\n3. **Test file location**: `out/` directory contains compiled JavaScript\n4. **Extension compilation**: Extensions compile separately and take significant time\n### Code Architecture Patterns\n#### Testing Strategy\n- Unit tests in `src/vs/*/test/` directories\n- Integration tests in `test/` directory\n- Use `npm run compile` before running node-based tests\n## Common Gotchas\n### Module Loading\n- Use compiled files from `out/` directory when testing with node\n- Import paths: `const { Class } = require('../out/vs/path/to/module.js')`\n- ES modules require `.mjs` extension or package.json type modification\n### Test Location\n- Don't add tests to the wrong test suite (e.g., adding to end of file instead of inside relevant suite)\n- Look for existing test patterns before creating new structures\n- Use `describe` and `test` consistently with existing patterns\n## Investigation Shortcuts\n### Finding Related Code\n1. **Semantic search first**: Use file search for general concepts\n2. **Grep for exact strings**: Use grep for error messages or specific function names\n3. **Follow imports**: Check what files import the problematic module\n4. **Check test files**: Often reveal usage patterns and expected behavior\n### Build Optimization\n- Compilation takes ~2 minutes - do this once at start\n- Extensions compile separately - skip if not needed\n- Use incremental compilation for faster iteration\n## File Structure Quick Reference\n```\nsrc/vs/\n├── base/common/           # Core utilities (color.ts, etc.)\n├── editor/contrib/        # Editor features\n├── platform/             # Platform services\n└── workbench/            # Main UI components\ntest/                     # Integration tests\nout/                     # Compiled output\nscripts/                 # Build and test scripts\n```\n</attachment>\nIn TypeScript and JavaScript, always use underscore for private field names.\n</instructions>\nThe user has provided the following files as input. Always make changes to these files unless the user asks to create a new file.\nUntitled files are files that are not yet named. Make changes to them like regular files.\n<file>\n```typescript\n// filepath: c:\\Users\\<USER>\\workspaces\\vscode\\src\\vs\\workbench\\contrib\\chat\\common\\promptSyntax\\service\\promptsServiceImpl.ts\n/*---------------------------------------------------------------------------------------------\n*  Copyright (c) Microsoft Corporation. All rights reserved.\n*  Licensed under the MIT License. See License.txt in the project root for license information.\n*--------------------------------------------------------------------------------------------*/\nimport { localize } from '../../../../../../nls.js';\nimport { getLanguageIdForPromptsType, getPromptsTypeForLanguageId, MODE_LANGUAGE_ID, PROMPT_LANGUAGE_ID, PromptsType } from '../promptTypes.js';\nimport { PromptParser } from '../parsers/promptParser.js';\nimport { type URI } from '../../../../../../base/common/uri.js';\nimport { assert } from '../../../../../../base/common/assert.js';\nimport { basename } from '../../../../../../base/common/path.js';\nimport { PromptFilesLocator } from '../utils/promptFilesLocator.js';\nimport { Disposable } from '../../../../../../base/common/lifecycle.js';\nimport { Event } from '../../../../../../base/common/event.js';\nimport { type ITextModel } from '../../../../../../editor/common/model.js';\nimport { ObjectCache } from '../utils/objectCache.js';\nimport { ILogService } from '../../../../../../platform/log/common/log.js';\nimport { TextModelPromptParser } from '../parsers/textModelPromptParser.js';\nimport { ILabelService } from '../../../../../../platform/label/common/label.js';\nimport { IModelService } from '../../../../../../editor/common/services/model.js';\nimport { CancellationToken } from '../../../../../../base/common/cancellation.js';\nimport { IInstantiationService } from '../../../../../../platform/instantiation/common/instantiation.js';\nimport { IUserDataProfileService } from '../../../../../services/userDataProfile/common/userDataProfile.js';\nimport type { IChatPromptSlashCommand, ICustomChatMode, IPromptParserResult, IPromptPath, IPromptsService, TPromptsStorage } from './promptsService.js';\nimport { getCleanPromptName, PROMPT_FILE_EXTENSION } from '../config/promptFileLocations.js';\nimport { ILanguageService } from '../../../../../../editor/common/languages/language.js';\nimport { PromptsConfig } from '../config/config.js';\nimport { IConfigurationService } from '../../../../../../platform/configuration/common/configuration.js';\nimport { TModeMetadata } from '../parsers/promptHeader/modeHeader.js';\n/**\n* Provides prompt services.\n*/\nexport class PromptsService extends Disposable implements IPromptsService {\npublic declare readonly _serviceBrand: undefined;\n/**\n* Cache of text model content prompt parsers.\n*/\nprivate readonly cache: ObjectCache<TextModelPromptParser, ITextModel>;\n/**\n* Prompt files locator utility.\n*/\nprivate readonly fileLocator: PromptFilesLocator;\n/**\n* Cached custom modes. Caching only happens if the `onDidChangeCustomChatModes` event is used.\n*/\nprivate cachedCustomChatModes: Promise<readonly ICustomChatMode[]> | undefined;\n/**\n* Lazily created event that is fired when the custom chat modes change.\n*/\nprivate onDidChangeCustomChatModesEvent: Event<void> | undefined;\nconstructor(\n@ILogService public readonly logger: ILogService,\n@ILabelService private readonly labelService: ILabelService,\n@IModelService private readonly modelService: IModelService,\n@IInstantiationService private readonly instantiationService: IInstantiationService,\n@IUserDataProfileService private readonly userDataService: IUserDataProfileService,\n@ILanguageService private readonly languageService: ILanguageService,\n@IConfigurationService private readonly configurationService: IConfigurationService,\n) {\nsuper();\nthis.fileLocator = this._register(this.instantiationService.createInstance(PromptFilesLocator));\n// the factory function below creates a new prompt parser object\n// for the provided model, if no active non-disposed parser exists\nthis.cache = this._register(\nnew ObjectCache((model) => {\nassert(\nmodel.isDisposed() === false,\n'Text model must not be disposed.',\n);\n/**\n* Note! When/if shared with \"file\" prompts, the `seenReferences` array below must be taken into account.\n* Otherwise consumers will either see incorrect failing or incorrect successful results, based on their\n* use case, timing of their calls to the {@link getSyntaxParserFor} function, and state of this service.\n*/\nconst parser: TextModelPromptParser = instantiationService.createInstance(\nTextModelPromptParser,\nmodel,\n{ allowNonPromptFiles: true, languageId: undefined, updateOnChange: true },\n).start();\n// this is a sanity check and the contract of the object cache,\n// we must return a non-disposed object from this factory function\nparser.assertNotDisposed(\n'Created prompt parser must not be disposed.',\n);\nreturn parser;\n})\n);\n}\n/**\n* Emitter for the custom chat modes change event.\n*/\npublic get onDidChangeCustomChatModes(): Event<void> {\nif (!this.onDidChangeCustomChatModesEvent) {\nthis.onDidChangeCustomChatModesEvent = this._register(this.fileLocator.createFilesUpdatedEvent(PromptsType.mode)).event;\nthis._register(this.onDidChangeCustomChatModesEvent(() => {\nthis.cachedCustomChatModes = undefined; // reset cached custom chat modes\n}));\n}\nreturn this.onDidChangeCustomChatModesEvent;\n}\npublic getPromptFileType(uri: URI): PromptsType | undefined {\nconst model = this.modelService.getModel(uri);\nconst languageId = model ? model.getLanguageId() : this.languageService.guessLanguageIdByFilepathOrFirstLine(uri);\nreturn languageId ? getPromptsTypeForLanguageId(languageId) : undefined;\n}\n/**\n* @throws {Error} if:\n* \t- the provided model is disposed\n* \t- newly created parser is disposed immediately on initialization.\n* \t  See factory function in the {@link constructor} for more info.\n*/\npublic getSyntaxParserFor(model: ITextModel): TextModelPromptParser & { isDisposed: false } {\nassert(\nmodel.isDisposed() === false,\n'Cannot create a prompt syntax parser for a disposed model.',\n);\nreturn this.cache.get(model);\n}\npublic async listPromptFiles(type: PromptsType, token: CancellationToken): Promise<readonly IPromptPath[]> {\nif (!PromptsConfig.enabled(this.configurationService)) {\nreturn [];\n}\nconst prompts = await Promise.all([\nthis.fileLocator.listFiles(type, 'user', token)\n.then(withType('user', type)),\nthis.fileLocator.listFiles(type, 'local', token)\n.then(withType('local', type)),\n]);\nreturn prompts.flat();\n}\npublic getSourceFolders(type: PromptsType): readonly IPromptPath[] {\nif (!PromptsConfig.enabled(this.configurationService)) {\nreturn [];\n}\nconst result: IPromptPath[] = [];\nfor (const uri of this.fileLocator.getConfigBasedSourceFolders(type)) {\nresult.push({ uri, storage: 'local', type });\n}\nconst userHome = this.userDataService.currentProfile.promptsHome;\nresult.push({ uri: userHome, storage: 'user', type });\nreturn result;\n}\npublic asPromptSlashCommand(command: string): IChatPromptSlashCommand | undefined {\nif (command.match(/^[\\w_\\-\\.]+$/)) {\nreturn { command, detail: localize('prompt.file.detail', 'Prompt file: {0}', command) };\n}\nreturn undefined;\n}\npublic async resolvePromptSlashCommand(data: IChatPromptSlashCommand, token: CancellationToken): Promise<IPromptParserResult | undefined> {\nconst promptUri = await this.getPromptPath(data);\nif (!promptUri) {\nreturn undefined;\n}\nreturn await this.parse(promptUri, PromptsType.prompt, token);\n}\nprivate async getPromptPath(data: IChatPromptSlashCommand): Promise<URI | undefined> {\nif (data.promptPath) {\nreturn data.promptPath.uri;\n}\nconst files = await this.listPromptFiles(PromptsType.prompt, CancellationToken.None);\nconst command = data.command;\nconst result = files.find(file => getPromptCommandName(file.uri.path) === command);\nif (result) {\nreturn result.uri;\n}\nconst textModel = this.modelService.getModels().find(model => model.getLanguageId() === PROMPT_LANGUAGE_ID && getPromptCommandName(model.uri.path) === command);\nif (textModel) {\nreturn textModel.uri;\n}\nreturn undefined;\n}\npublic async findPromptSlashCommands(): Promise<IChatPromptSlashCommand[]> {\nconst promptFiles = await this.listPromptFiles(PromptsType.prompt, CancellationToken.None);\nreturn promptFiles.map(promptPath => {\nconst command = getPromptCommandName(promptPath.uri.path);\nreturn {\ncommand,\ndetail: localize('prompt.file.detail', 'Prompt file: {0}', this.labelService.getUriLabel(promptPath.uri, { relative: true })),\npromptPath\n};\n});\n}\npublic async getCustomChatModes(token: CancellationToken): Promise<readonly ICustomChatMode[]> {\nif (!this.cachedCustomChatModes) {\nconst customChatModes = this.computeCustomChatModes(token);\nif (!this.onDidChangeCustomChatModesEvent) {\nreturn customChatModes;\n}\nthis.cachedCustomChatModes = customChatModes;\n}\nreturn this.cachedCustomChatModes;\n}\nprivate async computeCustomChatModes(token: CancellationToken): Promise<readonly ICustomChatMode[]> {\nconst modeFiles = await this.listPromptFiles(PromptsType.mode, token);\nconst metadataList = await Promise.all(\nmodeFiles.map(async ({ uri }): Promise<ICustomChatMode> => {\nlet parser: PromptParser | undefined;\ntry {\n// Note! this can be (and should be) improved by using shared parser instances\n// \t\t that the `getSyntaxParserFor` method provides for opened documents.\nparser = this.instantiationService.createInstance(\nPromptParser,\nuri,\n{ allowNonPromptFiles: true, languageId: MODE_LANGUAGE_ID, updateOnChange: false },\n).start(token);\nconst completed = await parser.settled();\nif (!completed) {\nthrow new Error(localize('promptParser.notCompleted', \"Prompt parser for {0} did not complete.\", uri.toString()));\n}\nconst { description, model, tools } = parser.metadata as TModeMetadata;\nconst body = await parser.getBody();\nconst name = getCleanPromptName(uri);\nreturn { uri: uri, name, description, tools, model, body };\n} finally {\nparser?.dispose();\n}\n})\n);\nreturn metadataList;\n}\npublic async parse(uri: URI, type: PromptsType, token: CancellationToken): Promise<IPromptParserResult> {\nlet parser: PromptParser | undefined;\ntry {\nconst languageId = getLanguageIdForPromptsType(type);\nparser = this.instantiationService.createInstance(PromptParser, uri, { allowNonPromptFiles: true, languageId, updateOnChange: false }).start(token);\nconst completed = await parser.settled();\nif (!completed) {\nthrow new Error(localize('promptParser.notCompleted', \"Prompt parser for {0} did not complete.\", uri.toString()));\n}\n// make a copy, to avoid leaking the parser instance\nreturn {\nuri: parser.uri,\nmetadata: parser.metadata,\ntopError: parser.topError,\nreferences: parser.references.map(ref => ref.uri)\n};\n} finally {\nparser?.dispose();\n}\n}\n}\nexport function getPromptCommandName(path: string): string {\nconst name = basename(path, PROMPT_FILE_EXTENSION);\nreturn name;\n}\n/**\n* Utility to add a provided prompt `storage` and\n* `type` attributes to a prompt URI.\n*/\nfunction addType(storage: TPromptsStorage, type: PromptsType): (uri: URI) => IPromptPath {\nreturn (uri) => {\nreturn { uri, storage, type };\n};\n}\n/**\n* Utility to add a provided prompt `type` to a list of prompt URIs.\n*/\nfunction withType(storage: TPromptsStorage, type: PromptsType): (uris: readonly URI[]) => (readonly IPromptPath[]) {\nreturn (uris) => {\nreturn uris\n.map(addType(storage, type));\n};\n}\n```\n</file>\n<file-selection>\n```typescript\n// filepath: c:\\Users\\<USER>\\workspaces\\vscode\\src\\vs\\workbench\\contrib\\chat\\common\\promptSyntax\\service\\promptsServiceImpl.ts\nif (command.match(/^[\\w_\\-\\.]+$/)) {\n```\n</file-selection>\n<reminder>\nAvoid repeating existing code, instead use a line comment with `...existing code...` to represent regions of unchanged code.\nThe code block for each file being edited must start with a comment containing the filepath. This includes Markdown code blocks.\nFor existing files, make sure the filepath exactly matches the filepath of the original file.\nWhen suggesting to create new files, pick a location inside `c:\\Users\\<USER>\\workspaces\\vscode`.\n</reminder>\n<prompt>\nchange the regex so that it also support unicode code workd characters\n</prompt>\n~~~\n## Response\n### Assistant\n````md\n### c:\\Users\\<USER>\\workspaces\\vscode\\src\\vs\\workbench\\contrib\\chat\\common\\promptSyntax\\service\\promptsServiceImpl.ts\nUpdate the regex in `asPromptSlashCommand` to support Unicode word characters by using `\\p{L}` and the `u` flag.\n````typescript\n// filepath: c:\\Users\\<USER>\\workspaces\\vscode\\src\\vs\\workbench\\contrib\\chat\\common\\promptSyntax\\service\\promptsServiceImpl.ts\n// ...existing code...\npublic asPromptSlashCommand(command: string): IChatPromptSlashCommand | undefined {\n// Support Unicode word characters\nif (command.match(/^[\\p{L}\\d_\\-\\.]+$/u)) {\nreturn { command, detail: localize('prompt.file.detail', 'Prompt file: {0}', command) };\n}\nreturn undefined;\n}\n// ...existing code...\n````\n````\n<style>\n[id^=\"system\"], [id^=\"user\"], [id^=\"assistant\"] {\nmargin: 4px 0 4px 0;\n}\n.markdown-body > pre {\npadding: 4px 16px;\n}\n</style>\n</details>", "created_at": "2025-07-11T13:51:13Z", "pr_merged_time": "2025-07-11T16:22:30Z", "pr_number": "255429", "pr_url": "https://github.com/microsoft/vscode/pull/255429"}, {"issue_number": "255327", "issue_url": "https://github.com/microsoft/vscode/issues/255327", "state": "closed", "title": "No hover feedback for editing chat message", "content": "I really like the in-place edit input box. But now:\n- There is zero hover feedback other than the revert action apparently just floating in mid air, so I have no idea where I can or cannot click;\n- Even in the area I can click, the mouse cursor still looks like default, and not a hand pointer.\nhttps://github.com/user-attachments/assets/b9e9dfe6-85a6-4b1b-8d1a-16e5adec291a", "created_at": "2025-07-11T09:38:33Z", "pr_merged_time": "2025-07-11T16:22:30Z", "pr_number": "255429", "pr_url": "https://github.com/microsoft/vscode/pull/255429"}]
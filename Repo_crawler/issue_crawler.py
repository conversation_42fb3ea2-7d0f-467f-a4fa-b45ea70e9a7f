#!/usr/bin/env python3
"""
GitHub Issue爬虫 - 简洁版
用途：爬取GitHub仓库的Issue标题，并进行内容分割
"""

import requests
import json
import re
import argparse
import time
from typing import List, Dict, Optional

class GitHubIssueCrawler:
    def __init__(self, token: Optional[str] = None):
        self.session = requests.Session()
        self.base_url = "https://api.github.com"

        if token:
            self.session.headers.update({
                'Authorization': f'token {token}',
                'Accept': 'application/vnd.github.v3+json'
            })
            print("✅ 使用GitHub Token")
        else:
            print("⚠️  未使用Token，API调用限制为每小时60次")

    def get_error_message(self, response) -> str:
        """获取详细的错误信息"""
        status_code = response.status_code

        if status_code == 403:
            # 检查是否是API限制
            if 'X-RateLimit-Remaining' in response.headers:
                remaining = response.headers['X-RateLimit-Remaining']
                reset_time = response.headers.get('X-RateLimit-Reset', 'unknown')
                return f"403 API调用限制 (剩余: {remaining}, 重置时间: {reset_time})"
            else:
                return "403 权限不足或仓库私有"
        elif status_code == 404:
            return "404 仓库不存在或无权限访问"
        elif status_code == 401:
            return "401 Token无效或过期"
        elif status_code == 422:
            return "422 请求参数错误"
        else:
            try:
                error_data = response.json()
                message = error_data.get('message', 'Unknown error')
                return f"{status_code} {message}"
            except:
                return f"{status_code} HTTP错误"

    def safe_api_call(self, url: str, params: dict = None, max_retries: int = 3) -> requests.Response:
        """安全的API调用，包含重试机制"""
        for attempt in range(max_retries):
            try:
                response = self.session.get(url, params=params, timeout=30)

                if response.status_code == 200:
                    return response
                elif response.status_code == 403:
                    # 检查是否是API限制
                    if 'X-RateLimit-Remaining' in response.headers:
                        remaining = int(response.headers['X-RateLimit-Remaining'])
                        if remaining == 0:
                            reset_time = int(response.headers.get('X-RateLimit-Reset', 0))
                            wait_time = max(reset_time - int(time.time()), 60)
                            print(f"⏰ API限制，等待 {wait_time} 秒...")
                            time.sleep(min(wait_time, 300))  # 最多等待5分钟
                            continue
                    raise Exception(self.get_error_message(response))
                else:
                    if attempt < max_retries - 1:
                        print(f"⚠️  API调用失败 (尝试 {attempt + 1}/{max_retries})，1秒后重试...")
                        time.sleep(1)
                        continue
                    else:
                        raise Exception(self.get_error_message(response))

            except requests.exceptions.RequestException as e:
                if attempt < max_retries - 1:
                    print(f"⚠️  网络错误 (尝试 {attempt + 1}/{max_retries})，2秒后重试...")
                    time.sleep(2)
                    continue
                else:
                    raise Exception(f"网络错误: {e}")

        raise Exception("API调用失败，已达到最大重试次数")
    
    def get_repo_info(self, repo_url: str) -> tuple:
        """从GitHub URL提取仓库信息"""
        match = re.search(r'github\.com/([^/]+)/([^/]+)', repo_url)
        if match:
            return match.group(1), match.group(2)
        raise ValueError(f"无效的GitHub URL: {repo_url}")
    
    def split_issue_content(self, title: str, body: str) -> Dict[str, str]:
        """分割Issue内容"""
        if not body:
            return {
                'title': title,
                'description': '',
                'steps': '',
                'expected': '',
                'actual': '',
                'environment': '',
                'other': ''
            }
        
        # 关键词映射
        sections = {
            'description': ['description', 'summary', '描述', '问题描述'],
            'steps': ['steps', 'reproduce', '重现', '复现'],
            'expected': ['expected', '期望', '预期'],
            'actual': ['actual', '实际', '当前'],
            'environment': ['environment', 'version', '环境', '版本']
        }
        
        lines = body.split('\n')
        current_section = 'description'
        content = {key: [] for key in sections.keys()}
        content['other'] = []
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # 检查是否是章节标题
            found_section = None
            line_lower = line.lower()
            
            for section_key, keywords in sections.items():
                if any(keyword in line_lower for keyword in keywords):
                    if line.startswith('#') or line.startswith('**') or line.endswith(':') or len(line) < 50:
                        found_section = section_key
                        break
            
            if found_section:
                current_section = found_section
            else:
                content[current_section].append(line)
        
        # 合并内容
        result = {'title': title}
        for key in content:
            result[key] = '\n'.join(content[key]).strip()
        
        # 如果描述为空，使用前几行
        if not result['description'] and result['other']:
            lines = result['other'].split('\n')
            result['description'] = '\n'.join(lines[:2])
            result['other'] = '\n'.join(lines[2:])
        
        return result
    
    def get_fix_pr_times(self, repo_owner: str, repo_name: str, issue_number: int) -> Dict:
        """获取修复该Issue的PR的创建和合并时间"""
        # 1. 获取Issue的事件，查找关联的PR
        events_url = f"{self.base_url}/repos/{repo_owner}/{repo_name}/issues/{issue_number}/events"
        try:
            response = self.safe_api_call(events_url)
        except Exception as e:
            print(f"⚠️  获取Issue事件失败: {e}")
            return {
                'pr_merged_time': '',
                'pr_number': '',
                'pr_url': ''
            }

        pr_number = None
        if response.status_code == 200:
            events = response.json()
            for event in events:
                # 查找关闭事件中的commit_id
                if event.get('event') == 'closed' and event.get('commit_id'):
                    pr_number = self.find_pr_by_commit(repo_owner, repo_name, event['commit_id'])
                    if pr_number:
                        break

        # 2. 如果没找到PR，尝试在Issue评论中查找PR引用
        if not pr_number:
            pr_number = self.find_pr_in_comments(repo_owner, repo_name, issue_number)

        # 3. 获取PR的创建和合并时间
        if pr_number:
            return self.get_pr_times(repo_owner, repo_name, pr_number)

        return {
            'pr_merged_time': '',
            'pr_number': '',
            'pr_url': ''
        }

    def find_pr_by_commit(self, repo_owner: str, repo_name: str, commit_id: str) -> str:
        """通过commit ID查找对应的PR"""
        url = f"{self.base_url}/repos/{repo_owner}/{repo_name}/pulls"
        params = {'state': 'closed', 'per_page': 30}

        response = self.session.get(url, params=params)
        if response.status_code == 200:
            pulls = response.json()
            for pr in pulls:
                # 检查PR的merge_commit_sha
                if pr.get('merge_commit_sha') and pr['merge_commit_sha'].startswith(commit_id[:7]):
                    return str(pr['number'])
        return None

    def find_pr_in_comments(self, repo_owner: str, repo_name: str, issue_number: int) -> str:
        """在Issue评论中查找PR引用"""
        try:
            url = f"{self.base_url}/repos/{repo_owner}/{repo_name}/issues/{issue_number}/comments"
            response = self.safe_api_call(url)
            comments = response.json()

            for comment in comments:
                body = comment.get('body', '')
                # 扩展的PR引用模式
                pr_patterns = [
                    r'#(\d+)',
                    r'fixes #(\d+)',
                    r'closes #(\d+)',
                    r'resolves #(\d+)',
                    r'fix #(\d+)',
                    r'close #(\d+)',
                    r'resolve #(\d+)',
                    r'pull/(\d+)',
                    r'PR #(\d+)',
                    r'pr #(\d+)'
                ]

                for pattern in pr_patterns:
                    matches = re.findall(pattern, body, re.IGNORECASE)
                    for match in matches:
                        # 验证这个数字是否是PR
                        if self.is_pull_request(repo_owner, repo_name, match):
                            return match
        except Exception as e:
            print(f"⚠️  查找PR评论失败: {e}")
        return None

    def is_pull_request(self, repo_owner: str, repo_name: str, number: str) -> bool:
        """检查给定的数字是否是PR"""
        url = f"{self.base_url}/repos/{repo_owner}/{repo_name}/pulls/{number}"
        response = self.session.get(url)
        return response.status_code == 200

    def get_pr_times(self, repo_owner: str, repo_name: str, pr_number: str) -> Dict:
        """获取PR的创建和合并时间"""
        url = f"{self.base_url}/repos/{repo_owner}/{repo_name}/pulls/{pr_number}"
        response = self.session.get(url)

        if response.status_code == 200:
            pr_data = response.json()
            return {
                'pr_merged_time': pr_data['merged_at'] or '',
                'pr_number': pr_number,
                'pr_url': pr_data['html_url']
            }

        return {
            'pr_merged_time': '',
            'pr_number': pr_number,
            'pr_url': ''
        }

    def get_issues(self, repo_owner: str, repo_name: str, max_issues: int = None) -> List[Dict]:
        """获取Issues"""
        issues = []
        page = 1
        processed_count = 0

        print(f"🔍 获取 {repo_owner}/{repo_name} 的closed Issues (只保存有PR的)...")

        while True:
            url = f"{self.base_url}/repos/{repo_owner}/{repo_name}/issues"
            params = {'page': page, 'per_page': 100, 'state': 'closed'}
            
            response = self.safe_api_call(url, params)
            
            page_issues = response.json()
            if not page_issues:
                break
            
            # 过滤PR，只要真正的Issues
            real_issues = [issue for issue in page_issues if not issue.get('pull_request')]
            
            for issue in real_issues:
                processed_count += 1
                print(f"  处理Issue #{issue['number']}: {issue['title'][:50]}...")

                # 分割内容
                split_content = self.split_issue_content(issue['title'], issue['body'] or '')

                # 合并description和content
                content_parts = []
                if split_content['description']:
                    content_parts.append(split_content['description'])
                if split_content['steps']:
                    content_parts.append(f"Steps: {split_content['steps']}")
                if split_content['expected']:
                    content_parts.append(f"Expected: {split_content['expected']}")
                if split_content['actual']:
                    content_parts.append(f"Actual: {split_content['actual']}")

                content = '\n\n'.join(content_parts)

                # 获取修复PR的时间信息
                pr_info = self.get_fix_pr_times(repo_owner, repo_name, issue['number'])

                # 只保存有PR的Issue
                if pr_info['pr_number']:
                    issue_data = {
                        'issue_number': str(issue['number']),
                        'issue_url': issue['html_url'],
                        'state': issue['state'],
                        'title': split_content['title'],
                        'content': content,
                        'created_at': issue['created_at'],
                        'pr_merged_time': pr_info['pr_merged_time'],
                        'pr_number': pr_info['pr_number'],
                        'pr_url': pr_info['pr_url']
                    }

                    issues.append(issue_data)
                    print(f"    ✅ 找到PR #{pr_info['pr_number']}")
                else:
                    print(f"    ❌ 未找到PR，跳过")
                
                if max_issues and len(issues) >= max_issues:
                    print(f"✅ 已获取 {len(issues)} 个有PR的Issues (处理了 {processed_count} 个)")
                    return issues
            
            print(f"📄 第 {page} 页: {len(real_issues)} 个Issues")
            page += 1
        
        print(f"✅ 总共处理 {processed_count} 个Issues，保存 {len(issues)} 个有PR的Issues")
        if processed_count > 0:
            print(f"📊 PR匹配率: {len(issues)/processed_count*100:.1f}%")

        return issues
    
    def save_to_json(self, issues: List[Dict], filename: str):
        """保存到JSON"""
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(issues, f, ensure_ascii=False, indent=2)

        print(f"💾 已保存到: {filename}")
    
    def crawl(self, repo_url: str, output_file: str = "issues.json", max_issues: int = None) -> List[Dict]:
        """主要爬取方法"""
        repo_owner, repo_name = self.get_repo_info(repo_url)
        issues = self.get_issues(repo_owner, repo_name, max_issues)
        self.save_to_json(issues, output_file)
        return issues

def main():
    parser = argparse.ArgumentParser(description='GitHub Issue爬虫')
    parser.add_argument('repo_url', help='GitHub仓库URL')
    parser.add_argument('-t', '--token', help='GitHub Token')
    parser.add_argument('-o', '--output', default='issues.json', help='输出文件')
    parser.add_argument('-m', '--max', type=int, help='最大Issue数量')
    
    args = parser.parse_args()
    
    try:
        crawler = GitHubIssueCrawler(token=args.token)
        crawler.crawl(args.repo_url, args.output, max_issues=args.max)
        print("🎉 完成！")
    except Exception as e:
        print(f"❌ 错误: {e}")

if __name__ == "__main__":
    main()

[{"issue_number": "718", "issue_url": "https://github.com/yamadashy/repomix/issues/718", "state": "closed", "title": "Please add to Open VSX https://open-vsx.org/", "content": "Support Cursor and other VSCode forks - deploy to the https://open-vsx.org/ marketplace please.", "created_at": "2025-07-05T00:40:21Z", "pr_merged_time": "", "pr_number": "", "pr_url": ""}, {"issue_number": "713", "issue_url": "https://github.com/yamadashy/repomix/issues/713", "state": "closed", "title": "1.0.0 not working on windows", "content": "executing repomix leads to\nFatal Error: {\nname: '<PERSON><PERSON><PERSON>',\nmessage: 'No such built-in module: node:readline/promises',\nstack: 'Error [ERR_UNKNOWN_BUILTIN_MODULE]: No such built-in module: node:readline/promises\\n' +\n'    at new NodeError (node:internal/errors:372:5)\\n' +\n'    at ESMLoader.builtinStrategy (node:internal/modules/esm/translators:260:11)\\n' +\n'    at ESMLoader.moduleProvider (node:internal/modules/esm/loader:337:14)'\n}\nit was working before\ninstalled via npm install -g repomix\n### Usage Context\nNone", "created_at": "2025-07-02T14:53:12Z", "pr_merged_time": "", "pr_number": "", "pr_url": ""}, {"issue_number": "648", "issue_url": "https://github.com/yamadashy/repomix/issues/648", "state": "closed", "title": "Read file list from stdin", "content": "Add support for reading the file list from stdin\n# Use Case\nI'm using [fd](https://github.com/sharkdp/fd) and [ripgrep](https://github.com/BurntSushi/ripgrep) a lot in my day-to-day work.\nIt would have been nice to pipe the results of the file search to repomix:\n```sh\n# List all Markdown files\nfd -t file -e md | repomix --stdin\n# List files from \"tests\" directories\nfd -t directory 'tests' | repomix --stdin\n# Find TS files that contain 'MyClass' pattern using both fd and ripgrep\nfd -t file -e ts -e tsx --exec-batch rg -l 'MyClass' | repomix --stdin\n```\n# Proposed Solution\nAdd a flag (`--stdin` or `--include-from-stdin`) that would enable repomix to read the list of files, directories, or globs from the piped input\n# Related\n#553", "created_at": "2025-06-09T10:38:28Z", "pr_merged_time": "", "pr_number": "", "pr_url": ""}, {"issue_number": "636", "issue_url": "https://github.com/yamadashy/repomix/issues/636", "state": "closed", "title": "Browser extension don't work in Edge", "content": "Hi,\nI just installed the repomix browser extension in Edge browser. I don't understand why, but when I click the icon, I get some Edge maintenance menu instead of the extension UI, see screenshot:\n![Image](https://github.com/user-attachments/assets/a65f67ba-b45e-42d8-b350-634f2dbf974f)\nIf I click \"Repomix\" choice, it opens the extension page in chrome webstore.\n### Usage Context\nNone", "created_at": "2025-06-06T08:18:58Z", "pr_merged_time": "", "pr_number": "", "pr_url": ""}, {"issue_number": "631", "issue_url": "https://github.com/yamadashy/repomix/issues/631", "state": "closed", "title": "VSCode extension", "content": "The experience of AI coding tools (cursor or others) mentioning file/directory context is unsatisfactory, and it worsens in agent mode (long workflows, discarding content to save costs). Some tools cannot even use the mouse to drag files. If repomix could directly generate an XML prompt for the selected file using the right-click, it would be very convenient!\n> There is already a repomix runner extension in the marketplace, but it is outdated. It seems to use a very early version.", "created_at": "2025-06-04T02:42:21Z", "pr_merged_time": "", "pr_number": "", "pr_url": ""}, {"issue_number": "591", "issue_url": "https://github.com/yamadashy/repomix/issues/591", "state": "closed", "title": "\"Not implemented\" error when using `bunx`", "content": "I use `bunx repomix` regularly on my Windows computer, but when trying it on my Mac (Apple Silicon M4 chip), I receive an error:\n```\n🫀  ➜  bunx repomix --verbose\ndirectories: [ '.' ]\ncwd: /Users/<USER>/Programming/project-folder\noptions: {\ndirectoryStructure: true,\nfiles: true,\ngitSortByChanges: true,\ngitignore: true,\ndefaultPatterns: true,\nsecurityCheck: true,\nverbose: true\n}\n📦 Repomix v0.3.6\nLoaded CLI options: {\ndirectoryStructure: true,\nfiles: true,\ngitSortByChanges: true,\ngitignore: true,\ndefaultPatterns: true,\nsecurityCheck: true,\nverbose: true\n}\nNo Repopack files found to migrate.\nLoading local config from: /Users/<USER>/Programming/project-folder/repomix.config.json\nLoading global config from: /Users/<USER>/.config/repomix/repomix.config.json\nNo custom config found at repomix.config.json or global config at /Users/<USER>/.config/repomix/repomix.config.json.\nYou can add a config file for additional settings. Please check https://github.com/yamadashy/repomix for more information.\nLoaded file config: {}\nCLI config: {}\nDefault config: {\ninput: { maxFileSize: 52428800 },\noutput: {\nfilePath: 'repomix-output.xml',\nstyle: 'xml',\nparsableStyle: false,\ndirectoryStructure: true,\nfiles: true,\nremoveComments: false,\nremoveEmptyLines: false,\ncompress: false,\ntopFilesLength: 5,\nshowLineNumbers: false,\ncopyToClipboard: false,\ngit: {\nsortByChanges: true,\nsortByChangesMaxCommits: 100,\nincludeDiffs: false\n}\n},\ninclude: [],\nignore: { useGitignore: true, useDefaultPatterns: true, customPatterns: [] },\nsecurity: { enableSecurityCheck: true },\ntokenCount: { encoding: 'o200k_base' }\n}\nDefault output file path is set to: repomix-output.xml\nMerged config: {\ninput: { maxFileSize: 52428800 },\noutput: {\nfilePath: 'repomix-output.xml',\nstyle: 'xml',\nparsableStyle: false,\ndirectoryStructure: true,\nfiles: true,\nremoveComments: false,\nremoveEmptyLines: false,\ncompress: false,\ntopFilesLength: 5,\nshowLineNumbers: false,\ncopyToClipboard: false,\ngit: {\nsortByChanges: true,\nsortByChangesMaxCommits: 100,\nincludeDiffs: false\n}\n},\ninclude: [],\nignore: { useGitignore: true, useDefaultPatterns: true, customPatterns: [] },\nsecurity: { enableSecurityCheck: true },\ntokenCount: { encoding: 'o200k_base' },\ncwd: '/Users/<USER>/Programming/project-folder'\n}\nAdding default ignore patterns\nAdding output file to ignore patterns: repomix-output.xml\nAdding custom ignore patterns: []\nInclude patterns: [ '**/*' ]\nIgnore patterns: [\n'.git/**',\n'.hg/**',\n'.hgignore',\n'.svn/**',\n'**/node_modules/**',\n'**/bower_components/**',\n'**/jspm_packages/**',\n'vendor/**',\n'**/.bundle/**',\n'**/.gradle/**',\n'target/**',\n'logs/**',\n'**/*.log/**',\n'**/npm-debug.log*/**',\n'**/yarn-debug.log*/**',\n'**/yarn-error.log*/**',\n'pids/**',\n'*.pid',\n'*.seed',\n'*.pid.lock',\n'lib-cov/**',\n'coverage/**',\n'.nyc_output/**',\n'.grunt/**',\n'.lock-wscript',\n'build/Release/**',\n'typings/**',\n'**/.npm/**',\n'.eslintcache',\n'.rollup.cache/**',\n'.webpack.cache/**',\n'.parcel-cache/**',\n'.sass-cache/**',\n'*.cache',\n'.node_repl_history',\n'*.tgz',\n'**/.yarn/**',\n'**/.yarn-integrity/**',\n'.env',\n'.next/**',\n'.nuxt/**',\n'.vuepress/dist/**',\n'.serverless/**',\n'.fusebox/**',\n'.dynamodb/**',\n'dist/**',\n'**/.DS_Store/**',\n'**/Thumbs.db/**',\n'.idea/**',\n'.vscode/**',\n'**/*.swp/**',\n'**/*.swo/**',\n'**/*.swn/**',\n'**/*.bak/**',\n'build/**',\n'out/**',\n'tmp/**',\n'temp/**',\n'**/repomix-output.*/**',\n'**/repopack-output.*/**',\n'**/package-lock.json/**',\n'**/yarn-error.log/**',\n'**/yarn.lock/**',\n'**/pnpm-lock.yaml/**',\n'**/bun.lockb/**',\n'**/bun.lock/**',\n'**/__pycache__/**',\n'**/*.py[cod]/**',\n'**/venv/**',\n'**/.venv/**',\n'**/.pytest_cache/**',\n'**/.mypy_cache/**',\n'**/.ipynb_checkpoints/**',\n'**/Pipfile.lock/**',\n'**/poetry.lock/**',\n'**/uv.lock/**',\n'**/Cargo.lock/**',\n'**/Cargo.toml.orig/**',\n'**/target/**',\n'**/*.rs.bk/**',\n'**/composer.lock/**',\n'**/Gemfile.lock/**',\n'**/go.sum/**',\n'**/mix.lock/**',\n'**/stack.yaml.lock/**',\n'**/cabal.project.freeze/**',\n'repomix-output.xml'\n]\nIgnore file patterns: [ '**/.gitignore', '**/.repomixignore' ]\nFiltered 141 files\nInitializing worker pool with min=1, max=2 threads. Worker path: file:///Users/<USER>/.bun/install/global/node_modules/repomix/lib/core/file/workers/fileCollectWorker.js\n\nExpected: Stack trace: Error: Not implemented\nat unknown\nat new ThreadPool (/Users/<USER>/.bun/install/global/node_modules/piscina/dist/index.js:95:50)\nat new Piscina (/Users/<USER>/.bun/install/global/node_modules/piscina/dist/index.js:570:57)\nat initPiscina (/Users/<USER>/.bun/install/global/node_modules/repomix/lib/shared/processConcurrency.js:20:16)\nat initTaskRunner (/Users/<USER>/.bun/install/global/node_modules/repomix/lib/core/file/fileCollect.js:14:18)\nat <anonymous> (/Users/<USER>/.bun/install/global/node_modules/repomix/lib/core/file/fileCollect.js:20:26)\nat <anonymous> (/Users/<USER>/.bun/install/global/node_modules/repomix/lib/core/file/fileCollect.js:7:71)\nat new Promise (native:1:11)\nat __awaiter (/Users/<USER>/.bun/install/global/node_modules/repomix/lib/core/file/fileCollect.js:3:27)\nat map (native:1:11)\nNeed help?\n• File an issue on GitHub: https://github.com/yamadashy/repomix/issues\n• Join our Discord community: https://discord.gg/wNYzTwZFku\n```\nI was able to pack successfully using a Docker container as a workaround:\n```\ndocker run -v .:/app -it --rm ghcr.io/yamadashy/repomix\n```\nI am using Bun version `1.2.14` (latest at the time of writing).\nSorry if you're not intending to support Bun. I have just gotten used to using it as a replacement and wasn't sure if you'd seen this bug before.\n### Usage Context\nRepomix CLI", "created_at": "2025-05-24T14:02:47Z", "pr_merged_time": "", "pr_number": "", "pr_url": ""}, {"issue_number": "552", "issue_url": "https://github.com/yamadashy/repomix/issues/552", "state": "closed", "title": "Suspicious file no longer detected", "content": "Hello,\nI've been using the v0.1.43 for quite some time, as I didn't have a need to upgrade up until now.\nI downloaded the (latest) v0.2.7, and a file that contained my github token no longer got flagged, and it was included without a problem.\nthat's about all I know about the issue.", "created_at": "2025-05-14T16:18:51Z", "pr_merged_time": "", "pr_number": "", "pr_url": ""}, {"issue_number": "542", "issue_url": "https://github.com/yamadashy/repomix/issues/542", "state": "closed", "title": "Add support for Node.js v24", "content": "Repomix currently tests and supports Node.js versions 16 through 23 in our CI workflows, devcontainer, and documentation. Node.js v24 is now officially released, and we’d like to ensure full compatibility so users can take advantage of the latest performance improvements and language features.\n## Proposed Changes\n* **CI**\n* Add `24.x` to the `node-version` matrix in `.github/workflows/ci.yml`.\n* Add `24` to the matrix in `.github/workflows/test-action.yml`.\n* **Devcontainer**\n* Update the base image in `.devcontainer/devcontainer.json` to one that supports Node.js 24 (e.g. `mcr.microsoft.com/devcontainers/typescript-node:1-24-bullseye`).", "created_at": "2025-05-10T13:52:53Z", "pr_merged_time": "", "pr_number": "", "pr_url": ""}, {"issue_number": "508", "issue_url": "https://github.com/yamadashy/repomix/issues/508", "state": "closed", "title": "Support for GitHub Action", "content": "This feature request is for `repomix` to also provide a `GitHub Action` that projects can use on their workflows to generate LLM friendly files automatically.\nYou will need an `action.yml` file on this repo with something like this:\n```yaml\nname: \"Repomix Action\"\ndescription: \"Pack repository contents into a single file that is easy for LLMs to process\"\nauthor: \"<PERSON><PERSON> <<EMAIL>>\"\nbranding:\nicon: archive\ncolor: purple\ninputs:\ndirectories:\ndescription: \"Space-separated list of directories to process (defaults to '.')\"\nrequired: false\ndefault: \".\"\ninclude:\ndescription: \"Comma-separated glob patterns to include\"\nrequired: false\ndefault: \"\"\nignore:\ndescription: \"Comma-separated glob patterns to ignore\"\nrequired: false\ndefault: \"\"\noutput:\nrequired: false\ndefault: \"repomix.txt\"\ncompress:\ndescription: \"Set to 'false' to disable smart compression\"\nrequired: false\ndefault: \"true\"\nadditional-args:\ndescription: \"Any extra raw arguments to pass directly to the repomix CLI\"\nrequired: false\ndefault: \"\"\n\nSteps: - name: Setup Node.js\nuses: actions/setup-node@v4\nwith:\n```\nNote this yaml was generated using `OpenAI - o3`", "created_at": "2025-04-25T13:20:25Z", "pr_merged_time": "", "pr_number": "", "pr_url": ""}, {"issue_number": "506", "issue_url": "https://github.com/yamadashy/repomix/issues/506", "state": "closed", "title": "Glama listing is missing <PERSON>erfile", "content": "Your MCP server is currently listed on the [Glama MCP directory](https://glama.ai/mcp/servers/yamadashy/repomix), but it is not available for others to use because it does not have a Dockerfile.\nIt takes only a few minutes to fix this:\n1. Go to your server's listing: [yamadashy/repomix](https://glama.ai/mcp/servers/yamadashy/repomix)\n2. Click \"Claim\" to verify ownership.\n3. Once claimed, navigate to the [admin `Dockerfile` page](https://glama.ai/mcp/servers/yamadashy/repomix/admin/dockerfile) and add a `Dockerfile`.\n4. Ensure your server passes all the [checks](https://glama.ai/mcp/servers/yamadashy/repomix/score).\nOnce completed, your server will be available for anyone to use.\nFor context, there are about 60k people using Glama every month and I'd love to see more people using your server.", "created_at": "2025-04-22T05:15:02Z", "pr_merged_time": "", "pr_number": "", "pr_url": ""}, {"issue_number": "505", "issue_url": "https://github.com/yamadashy/repomix/issues/505", "state": "closed", "title": "Add support for MCP tool annotations", "content": "The Model Context Protocol 2025-03-26 specification added support for tool annotations, which provide additional metadata about a tool's behavior. Once the MCP SDK is updated to support annotations, we should enhance our MCP tools implementation to include appropriate annotations.\nExample of future implementation in readRepomixOutputTool.ts:\n```typescript\nmcpServer.tool(\n'read_repomix_output',\n{\noutputId: z.string().describe('ID of the Repomix output file to read'),\n},\nasync ({ outputId }) => { /* implementation */ },\n{\ntitle: 'Read Repomix Output File',  // User-friendly title for UI display\nreadOnlyHint: true,                 // Indicates this tool does not modify its environment\nopenWorldHint: false                // Indicates this tool does not interact with external systems\n}\n);\n```\n### Tasks\n- Monitor updates to the MCP SDK (@modelcontextprotocol/sdk) for annotation support\n- Add appropriate annotations to each of our tool implementations:\n- read_repomix_output\n- file_system_read_file\n- file_system_read_directory\n- pack_codebase\n- pack_remote_repository\n### References\n- MCP Tool annotations spec: https://modelcontextprotocol.io/docs/concepts/tools/#tool-annotations\n- MCP SDK issue tracking annotation support: https://github.com/modelcontextprotocol/typescript-sdk/issues/276", "created_at": "2025-04-21T15:09:51Z", "pr_merged_time": "", "pr_number": "", "pr_url": ""}, {"issue_number": "474", "issue_url": "https://github.com/yamadashy/repomix/issues/474", "state": "closed", "title": "Badge in README shows CI-FAILING", "content": "SCREENSHOT-\n<img width=\"1467\" alt=\"Image\" src=\"https://github.com/user-attachments/assets/8d193e4a-1482-4ee9-a2cc-1b5fbe12d670\" />", "created_at": "2025-04-07T15:08:18Z", "pr_merged_time": "", "pr_number": "", "pr_url": ""}, {"issue_number": "473", "issue_url": "https://github.com/yamadashy/repomix/issues/473", "state": "closed", "title": "BACK TO TOP- not working", "content": "SCREEN RECORDING-\nhttps://github.com/user-attachments/assets/5f941f7d-b609-4c6b-8259-86b73c4448c6", "created_at": "2025-04-07T15:06:57Z", "pr_merged_time": "", "pr_number": "", "pr_url": ""}, {"issue_number": "454", "issue_url": "https://github.com/yamadashy/repomix/issues/454", "state": "closed", "title": ".js files showing as empty in output file", "content": "I have a Svelte website I'm developing and when I use repomix my *.js files are shown as empty in the output file. It shows that the files exist, but there is no content.\nFor example:\n## File: postcss.conifg.js\n```\n```\n\nActual: ## File: postcss.conifg.js\n```\nmodule.exports = {\nplugins: {\ntailwindcss: {},\nautoprefixer: {},\n},\n};\n```", "created_at": "2025-04-04T20:43:21Z", "pr_merged_time": "", "pr_number": "", "pr_url": ""}, {"issue_number": "449", "issue_url": "https://github.com/yamadashy/repomix/issues/449", "state": "closed", "title": "did you guys do any special function or just record directory and fileContent?i am a little confused", "content": "did you guys do any special function or just record directory and fileContent?i am a little confused,cause i can not found any other useful info", "created_at": "2025-04-01T09:46:54Z", "pr_merged_time": "", "pr_number": "", "pr_url": ""}, {"issue_number": "444", "issue_url": "https://github.com/yamadashy/repomix/issues/444", "state": "closed", "title": "Feature Request: make --exclude a synonym of --ignore", "content": "The tool uses `--include` to include files and folders for processing. The antonym of `--include` is `--exclude`, but that is not recognised. The following is output instead:\n```\nerror: unknown option '--exclude'\n(Did you mean --include?)\n```\nThis is obviously pretty funny, but nevertheless it would be beginner-friendly to have `--exclude` as a synonym for `--ignore`.\nIf there is a reason why this shouldn't be done, then please change the error message to suggest `--ignore` instead of `--include`.", "created_at": "2025-03-29T17:06:10Z", "pr_merged_time": "", "pr_number": "", "pr_url": ""}, {"issue_number": "443", "issue_url": "https://github.com/yamadashy/repomix/issues/443", "state": "closed", "title": "Feature Request: make ignore globbing patterns more intuitive for directories", "content": "Hi,\nLet's say I have the following repomix command, which both includes and excludes files:\n```\nrepomix\n--include \"MyProject.Common/**/*,MyProject.Models/**/*,MyProject.UI/**/*\"\n--ignore \"**/bin,**/obj,**/GeneratedFiles,**/Assets\"\n```\nThat is the correct syntax. However, if I amend the exclusion patterns to include trailing slashes, it does not work:\n```\nrepomix\n--include \"MyProject.Common/**/*,MyProject.Models/**/*,MyProject.UI/**/*\"\n--ignore \"**/bin/,**/obj/,**/GeneratedFiles/,**/Assets/\"\n```\nThe result of running this is that all files are still included in the folders we've marked to ignore. It is reasonable to assume that `**/Assets` and `**/Assets/` would mark all `Assets` folders under the include paths for exclusion.\nSimilarly, the following command will not work either:\n```\nrepomix\n--include \"MyProject.Common/**/*,MyProject.Models/**/*,MyProject.UI/**/*\"\n--ignore \"**/bin/**/*,**/obj/**/*,**/GeneratedFiles/**/*,**/Assets/**/*\"\n```\nThis uses the same globbing pattern to attempt to exclude files as it does to include files, but no files are excluded.\nThis caught me out today when using the tool for the first time.\n```\n**/folder\n**/folder/\n**/folder/**/*\n```\nIncidentally, the \"**/name\" syntax for exclusion patterns may be problematic, as it will match both folders and files with that name. It's rare that you have files lacking extensions, of course, but it may be worth noting.\nThank you for your useful tool!", "created_at": "2025-03-29T17:00:03Z", "pr_merged_time": "", "pr_number": "", "pr_url": ""}, {"issue_number": "430", "issue_url": "https://github.com/yamadashy/repomix/issues/430", "state": "closed", "title": "Feature Request: support solidity comment removal", "content": "Repomix supports removing comments from a range of languages but solidity isn't a support language currently\nI found the query for solidity's tree sitter definitions\nhttps://github.com/Aider-AI/aider/blob/main/aider/queries/tree-sitter-language-pack/solidity-tags.scm", "created_at": "2025-03-22T12:53:50Z", "pr_merged_time": "", "pr_number": "", "pr_url": ""}, {"issue_number": "426", "issue_url": "https://github.com/yamadashy/repomix/issues/426", "state": "closed", "title": "Pack Codebase response resource item doesn't include content", "content": "When using the `pack_codebase` tool, the server returns items of the format:\n```\n{\ntype: \"resource\",\nresource: {\nuri: \"file:///tmp/repomix/mcp-outputs/pqmRzP/repomix-output.xml\",\nmimeType: \"application/xml\",\ntext: \"Repomix output file\",\n},\n}\n```\nAccording to the [MCP docs](https://modelcontextprotocol.io/docs/concepts/resources) if I'm understanding correctly, this call should include the actual contents of the file and the URI would be for future reference or for subscribing to resource changes.\nThis means building to this MCP server in a generic way (fetching file URL on tool response) would not match expected handling of tool responses for other MCP endpoints.\nFor context I think repomix would be a fantastic integration into [the Continue IDE extension](https://github.com/continuedev/continue). Most of the repomix tools work but the resource ones don't.\n# Solution\nInclude the contents of the repomix output file contents under `text` in tool output", "created_at": "2025-03-17T20:28:28Z", "pr_merged_time": "", "pr_number": "", "pr_url": ""}, {"issue_number": "397", "issue_url": "https://github.com/yamadashy/repomix/issues/397", "state": "closed", "title": "question / bug for file-globs", "content": "I am trying to get a simple fileglob to work.\nIt does not work on the command line, it does work in repomix.config.json\nCLI:\nI have tried both\n```\n--ignore \"**/__tests__/**, **/theme/**\"\nand\n--ignore \"**/{__tests__, theme}/**\"\n```\n(the second syntax is from the fastglob repo linked in the readme as an example)\n*Does not work*\nrepomix.config:\n```\n\"customPatterns\": [\"**/__tests__/**\", \"**/theme/**\"]\n```\n*Does work*\nWhat am I missing?", "created_at": "2025-03-08T19:25:01Z", "pr_merged_time": "", "pr_number": "", "pr_url": ""}, {"issue_number": "372", "issue_url": "https://github.com/yamadashy/repomix/issues/372", "state": "closed", "title": "Run without write access to source directory", "content": "New user to repomix, and was testing with the docker image. It appears the application cannot run without write access to the source directory, even if the output file is specified to go elsewhere.\nFor example:\n```\n# mkdir /tmp/source\n# echo hello > /tmp/source/world\n# mkdir /tmp/out\n# podman run -v /tmp/source:/app:ro -v /tmp/out:/out -t --rm ghcr.io/yamadashy/repomix -o /out/repomix\n📦 Repomix v0.2.29\nNo custom config found at repomix.config.json or global config at /root/.config/repomix/repomix.config.json.\nYou can add a config file for additional settings. Please check https://github.com/yamadashy/repomix for more information.\n✖ Error during packing\nUnexpected error: Cannot access directory /app: undefined\nFor more help, please visit: https://github.com/yamadashy/repomix/issues\n```\nIf you take the `:ro` flag off the volume mount it works, even though output has been redirected somewhere else. I'm not sure if it's trying to use the source directory as a temporary working directory or what. But IMHO it shouldn't be putting anything in it. Any temporary files should either go in the directory of the output file, `$XDG_RUNTIME_DIR`, or `/tmp/`.\nMight also help if the error message would a little clearer. It took me a minute to figure out what the issue was, when the directory was there, readable, and not being used for output.", "created_at": "2025-02-24T19:52:57Z", "pr_merged_time": "", "pr_number": "", "pr_url": ""}, {"issue_number": "361", "issue_url": "https://github.com/yamadashy/repomix/issues/361", "state": "closed", "title": "RFC: Change Default Output Style to XML", "content": "## Proposal\nChange the default output style from `plain` to `xml` in version 0.3.0.\n## Why?\n- XML output provides better structure for AI processing\n- I've been using XML as maintainer and found it more reliable\n- Community feedback supports this change (see [Discord discussion](https://discord.com/channels/1324644561959522314/1325020123655835709/1340540106875863190))\n## Impact\nThis is a breaking change that will require users who want plain text to explicitly specify:\n```bash\nrepomix --style plain\n```\nor in config:\n```json\n{\n\"output\": {\n\"style\": \"plain\"\n}\n}\n```", "created_at": "2025-02-17T12:20:34Z", "pr_merged_time": "", "pr_number": "", "pr_url": ""}, {"issue_number": "355", "issue_url": "https://github.com/yamadashy/repomix/issues/355", "state": "closed", "title": "Website fails for https://github.com/evanw/esbuild", "content": "", "created_at": "2025-02-14T08:11:32Z", "pr_merged_time": "", "pr_number": "", "pr_url": ""}, {"issue_number": "346", "issue_url": "https://github.com/yamadashy/repomix/issues/346", "state": "closed", "title": "Allow Trailing Commas in `repomix.config.json`", "content": "When working with the `\"includes\"` key, I often comment out file paths to quickly test or calculate final token counts. However, I have to manually remove the trailing comma every time. Since comments are already supported in `repomix.config.json`, it would be very helpful if trailing commas were supported as well.\nFor reference, you can see where this is handled in our code:\nhttps://github.com/yamadashy/repomix/blob/b55c59d0dae9ba0c5bc534318c2a414d848f5ded/src/config/configLoad.ts#L70\nAdditionally, the [json5](https://www.npmjs.com/package/json5) library, which we could consider as an alternative, supports trailing commas.", "created_at": "2025-02-10T02:59:24Z", "pr_merged_time": "", "pr_number": "", "pr_url": ""}, {"issue_number": "341", "issue_url": "https://github.com/yamadashy/repomix/issues/341", "state": "closed", "title": "Remove generation timestamp to reduce AI API costs", "content": "The timestamp \"Generated by Repomix on: [date]\" at the beginning of output files causes AI systems (OpenAI/Anthropic) to mark the entire prompt as a \"cache miss\", increasing API costs by ~20-40%.\nReported by rooox on [Discord](https://discord.com/channels/1324644561959522314/1325020123655835709/1336999870799810561)\n# Proposal\nRemove the timestamp line from the output as it provides little value while causing significant cost implications for AI processing.", "created_at": "2025-02-07T17:23:45Z", "pr_merged_time": "", "pr_number": "", "pr_url": ""}, {"issue_number": "327", "issue_url": "https://github.com/yamadashy/repomix/issues/327", "state": "closed", "title": "Feature request: Allow option to specify a list of files", "content": "The --include option allows me to include blobs which is nice, but if I'd like to pack together a particular list of say 8 files, this would be very useful to just have an option to do something like\n`repomix --list lib/file.js components/Auth.js pages/index.js --copy`\nto allow more efficient and targeted file inclusions than using Regex which can be quite complex and perhaps unnecessary for most use-cases.\nThanks in advance - awesome project.", "created_at": "2025-01-29T10:25:50Z", "pr_merged_time": "", "pr_number": "", "pr_url": ""}, {"issue_number": "323", "issue_url": "https://github.com/yamadashy/repomix/issues/323", "state": "closed", "title": "Feature request: pack only currently open files in VSCode (and forks like CursorAI or Windsurf)", "content": "I would like to request a new feature that allows Repomix to pack only the files currently open in Visual Studio Code and its forks such as CursorAI or Windsurf.\n**Motivation:**\nOften, I already have the relevant files open in the editor, but I cannot efficiently use inclusion/exclusion patterns to specify them. Having an option to directly pack the files open in the current editor session would greatly streamline the workflow.\nFurthermore, most developers already use an IDE based on Visual Studio Code, if not VSCode itself. Therefore, adding this feature would likely see high adoption with minimal friction. This enhancement would allow users to pack only the currently opened files without the need to manually specify inclusion/exclusion patterns, improving efficiency and ease of use.\n**Proposed Solution:**\nImplement a command-line option that detects and packs only the files currently open in the editor, leveraging the API of VSCode and compatible forks or by reading the workspace state.\n**Benefits:**\n- Saves time by eliminating the need to manually define patterns.\n- Provides more accurate file selection.\n- Facilitates integration with customized development environments.\n\nExpected: ```sh\nrepomix --active-files\n```\nThis command should generate a packed file containing only the currently open files in the editor session.\n**Considered Alternatives:**\nCurrently, manually specifying the files or relying on include/exclude patterns is required, which can be tedious and imprecise in certain scenarios.", "created_at": "2025-01-27T15:58:45Z", "pr_merged_time": "", "pr_number": "", "pr_url": ""}, {"issue_number": "316", "issue_url": "https://github.com/yamadashy/repomix/issues/316", "state": "closed", "title": "Missing CLI flags for some configuration options", "content": "Hi @yamadashy ! 👋\nWhile analyzing the correspondence between CLI flags and configuration options, I noticed that some configuration fields don't have corresponding CLI flags. I'd like to know if this is intentional or if we should add them for consistency.\n### Current missing CLI flags for existing config options:\n- `output.headerText` -> add `--header-text` ?\n- `output.instructionFilePath` -> add`--instruction-file` ?\n- `output.includeEmptyDirectories` -> add `--include-empty-directories` ?\n- `ignore.useGitignore` -> add `--no-gitignore` ?\n- `ignore.useDefaultPatterns` -> add `--no-default-patterns` ?\n## Use Case\nI specifically need these CLI flags for the Repomix Runner VS Code extension. Currently, the extension passes VS Code configuration to Repomix through CLI flags.\nI'd be happy to submit a PR to add them if you don't have time.", "created_at": "2025-01-25T18:24:12Z", "pr_merged_time": "", "pr_number": "", "pr_url": ""}, {"issue_number": "311", "issue_url": "https://github.com/yamadashy/repomix/issues/311", "state": "closed", "title": "Add URL parsing for branch/commit information", "content": "Add functionality to parse branch, tag, and commit information from repository URLs.\nDifferent repository URL formats need to be supported:\n- `owner/repo`\n- `https://github.com/owner/repo`\n- `https://github.com/owner/repo/tree/branch`\n- `https://github.com/owner/repo/commit/hash`\nFeature was requested by user @gaby\nref\nhttps://github.com/yamadashy/repomix/issues/219#issuecomment-2573359838", "created_at": "2025-01-25T06:46:23Z", "pr_merged_time": "", "pr_number": "", "pr_url": ""}, {"issue_number": "310", "issue_url": "https://github.com/yamadashy/repomix/issues/310", "state": "closed", "title": "Add zip/folder upload functionality to website", "content": "This feature will allow users to upload zip archives or folders directly through the Repomix website (repomix.com), providing an alternative to the CLI tool.\nFeature was requested by user @huy-trn\nref\nhttps://github.com/yamadashy/repomix/issues/219#issuecomment-2571282619", "created_at": "2025-01-25T06:44:07Z", "pr_merged_time": "", "pr_number": "", "pr_url": ""}, {"issue_number": "297", "issue_url": "https://github.com/yamadashy/repomix/issues/297", "state": "closed", "title": "Call repomix from within a node script", "content": "Hi, I would like to use repomix in my application. Is it possible to call repomix as a node library, as opposed to a command line tool? If so, is there documentation on that somewhere? Thank you!", "created_at": "2025-01-19T09:09:54Z", "pr_merged_time": "", "pr_number": "", "pr_url": ""}, {"issue_number": "282", "issue_url": "https://github.com/yamadashy/repomix/issues/282", "state": "closed", "title": "XML Escaping", "content": "I like the feature of having a structured output via XML. However, the string concatenation as it is done in `xmlStyle.ts` doesn't really work. XML requires escaping of certain characters: https://stackoverflow.com/questions/1091945/what-characters-do-i-need-to-escape-in-xml-documents\nBasically, if the repo you are reading with repomix contains an XML file itself, it will be parsed as part of the repo tree, not as file contents. The \"&&\" symbol that is used for boolean conjunction in many languages is disallowed in XML files entirely and will make the output invalid.\nThe proper solution would be to use an XML serializer that takes care of any escaping that is needed.", "created_at": "2025-01-10T17:03:49Z", "pr_merged_time": "", "pr_number": "", "pr_url": ""}, {"issue_number": "278", "issue_url": "https://github.com/yamadashy/repomix/issues/278", "state": "closed", "title": "GitHub repo name including dot doesn't work", "content": "Thank you for the great repo.\nGitHub URL validation doesn't work with repo names including dot.\nExample GitHub URLs.\n`https://github.com/needle-mirror/com.unity.visualscripting`\n<img width=\"785\" alt=\"Screenshot 2025-01-10 at 10 14 02\" src=\"https://github.com/user-attachments/assets/2cee6d91-90f7-41f6-93c9-b1ce37a3a60d\" />", "created_at": "2025-01-10T01:15:32Z", "pr_merged_time": "", "pr_number": "", "pr_url": ""}, {"issue_number": "274", "issue_url": "https://github.com/yamadashy/repomix/issues/274", "state": "closed", "title": "SyntaxError: Unexpected token 'with' when running repomix", "content": "When running repomix, the command fails with a SyntaxError related to the with keyword. This suggests a compatibility issue with newer Node.js versions or ESM modules.\n\nSteps: `npx repomix`\nError Output:\n```\nFatal Error: {\nname: 'SyntaxError',\n\nExpected: '    at ESMLoader.moduleStrategy (node:internal/modules/esm/translators:119:18)\\n' +\n'    at ESMLoader.moduleProvider (node:internal/modules/esm/loader:468:14)'\n}\n```", "created_at": "2025-01-06T23:27:41Z", "pr_merged_time": "", "pr_number": "", "pr_url": ""}, {"issue_number": "258", "issue_url": "https://github.com/yamadashy/repomix/issues/258", "state": "closed", "title": "G<PERSON><PERSON> gist is giving errors, how can we fix this?", "content": "Getting this error:\nnpx repomix --remote https://github.com/nikahmadz/4046cf69caf4ddc68ea5e293e6afdc0e\nnpm error code ERR_INVALID_URL\nnpm error Invalid URL\nnpm error A complete log of this run can be found in: C:\\Users\\<USER>\\AppData\\Local\\npm-cache\\_logs\\2025-01-05T08_00_23_247Z-debug-0.log\nLog:\n0 verbose cli C:\\Program Files\\nodejs\\node.exe C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\npm\\bin\\npm-cli.js\n1 info using npm@10.9.2\n2 info using node@v22.11.0\n3 silly config load:file:C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\npm\\npmrc\n4 silly config load:file:\\\\wsl.localhost\\Ubuntu\\home\\user123_789\\.npmrc\n5 silly config load:file:C:\\Users\\<USER>\\.npmrc\n6 silly config load:file:C:\\Users\\<USER>\\AppData\\Roaming\\npm\\etc\\npmrc\n7 verbose title npm exec repomix --remote https://github.com/nikahmadz/4046cf69caf4ddc68ea5e293e6afdc0e\n8 verbose argv \"exec\" \"--\" \"repomix\" \"--remote\" \"https://github.com/nikahmadz/4046cf69caf4ddc68ea5e293e6afdc0e\"\n9 verbose logfile logs-max:10 dir:C:\\Users\\<USER>\\AppData\\Local\\npm-cache\\_logs\\2025-01-05T08_00_23_247Z-\n10 verbose logfile C:\\Users\\<USER>\\AppData\\Local\\npm-cache\\_logs\\2025-01-05T08_00_23_247Z-debug-0.log\n11 silly logfile start cleaning logs, removing 3 files\n12 verbose stack TypeError: Invalid URL\n12 verbose stack     at new URL (node:internal/url:816:29)\n12 verbose stack     at fromFile (C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\npm\\node_modules\\npm-package-arg\\lib\\npa.js:263:15)\n12 verbose stack     at resolve (C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\npm\\node_modules\\npm-package-arg\\lib\\npa.js:71:12)\n12 verbose stack     at npa (C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\npm\\node_modules\\npm-package-arg\\lib\\npa.js:53:10)\n12 verbose stack     at FetcherBase.get (C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\npm\\node_modules\\pacote\\lib\\fetcher.js:474:16)\n12 verbose stack     at Object.manifest (C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\npm\\node_modules\\pacote\\lib\\index.js:20:29)\n12 verbose stack     at hasPkgBin (C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\npm\\node_modules\\libnpmexec\\lib\\index.js:82:10)\n12 verbose stack     at exec (C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\npm\\node_modules\\libnpmexec\\lib\\index.js:137:17)\n12 verbose stack     at Exec.callExec (C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\npm\\lib\\commands\\exec.js:79:12)\n12 verbose stack     at Exec.exec (C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\npm\\lib\\commands\\exec.js:28:17)\n13 error code ERR_INVALID_URL\n14 error Invalid URL\n15 verbose cwd \\\\wsl.localhost\\Ubuntu\\home\\user123_789\n16 verbose os Windows_NT 10.0.26100\n17 verbose node v22.11.0\n18 verbose npm  v10.9.2\n19 verbose exit 1\n20 verbose code 1\n21 error A complete log of this run can be found in: C:\\Users\\<USER>\\AppData\\Local\\npm-cache\\_logs\\2025-01-05T08_00_23_247Z-debug-0.log", "created_at": "2025-01-05T08:01:47Z", "pr_merged_time": "", "pr_number": "", "pr_url": ""}, {"issue_number": "221", "issue_url": "https://github.com/yamadashy/repomix/issues/221", "state": "closed", "title": "Add support for <PERSON><PERSON>", "content": "Add support for running `repomix` using Docker.", "created_at": "2024-12-28T18:40:54Z", "pr_merged_time": "", "pr_number": "", "pr_url": ""}, {"issue_number": "211", "issue_url": "https://github.com/yamadashy/repomix/issues/211", "state": "closed", "title": "repomix --remote https://unpkg.com/repomix", "content": "Although this project is lit, people may think it's dangerous to let thrid-party cli to scan whole codebase. repomix needs to provide a way to convince people this project is legit, so one thing is to pack it self, not the github repo, cause github repo and npm can be totally two different codebase, it's a really simple attack method, so, one ease solution is to let repomix support `repomix --remote https://unpkg.com/repomix` , this way it can easily pack itself, people can just post the whole thing to LLM to verify if it contains mailicious code.While this project is fantastic, some users might hesitate to allow a third-party CLI to scan their entire codebase. To build confidence and demonstrate the project's legitimacy, **Repomix** could offer a mechanism to validate itself as a trusted tool.\nOne potential concern is the discrepancy between the GitHub repository and the published npm package, as malicious actors could exploit this difference with a simple attack method.\n**Proposed Solution**\nIntroduce a feature to allow Repomix to package itself directly from a remote source. For example:\n```bash\nrepomix --remote https://unpkg.com/repomix\n```\nWith this feature:\n- Users can fetch and package Repomix directly from its deployed version.\n- They could then analyze the resulting package (e.g., with an LLM) to ensure no malicious code is present.\nThis would make it easier for users to trust and adopt Repomix while mitigating potential security concerns.", "created_at": "2024-12-20T12:11:46Z", "pr_merged_time": "", "pr_number": "", "pr_url": ""}, {"issue_number": "210", "issue_url": "https://github.com/yamadashy/repomix/issues/210", "state": "closed", "title": "Ignored files can't be found in folder tree.", "content": "Add an option to keep it.", "created_at": "2024-12-20T08:20:20Z", "pr_merged_time": "", "pr_number": "", "pr_url": ""}, {"issue_number": "209", "issue_url": "https://github.com/yamadashy/repomix/issues/209", "state": "closed", "title": "Support comments in `repomix.config.json`", "content": "When dealing with a large codebase, people need to rapidly select which file paths to ignore and which to **include** in order to achieve acceptable token counts. The ability to add comments to `repomix.config.json` is critical.", "created_at": "2024-12-20T07:57:21Z", "pr_merged_time": "2024-12-29T12:30:44Z", "pr_number": "214", "pr_url": "https://github.com/yamadashy/repomix/pull/214"}, {"issue_number": "195", "issue_url": "https://github.com/yamadashy/repomix/issues/195", "state": "closed", "title": "Feature Request: Add Branch Option for Remote Repository Packing", "content": "Hello @yamadashy ,\nI'm new to this project but I'm willing to help creating a PR. I think it would be beneficial to add an option to select a specific commit ID or branch when packing a remote repository.\nCurrent Behavior:\nWhen packing a remote repository, the process uses the default branch.\nProposed Feature:\nAdd a branch option to the packing process, allowing users to select a specific commit ID or branch to pack.\nPotential Implementation:\n```\nrepomix --remote https://github.com/yamadashy/repomix --branch master\n```", "created_at": "2024-12-09T05:44:44Z", "pr_merged_time": "", "pr_number": "", "pr_url": ""}, {"issue_number": "191", "issue_url": "https://github.com/yamadashy/repomix/issues/191", "state": "closed", "title": "command line flag for turning off security check (reopens #74)", "content": "Add a command line flag to disable the security check. It is always a false positive when running repomix on documentation.\nhttps://github.com/yamadashy/repomix/issues/74", "created_at": "2024-12-03T18:36:08Z", "pr_merged_time": "", "pr_number": "", "pr_url": ""}, {"issue_number": "161", "issue_url": "https://github.com/yamadashy/repomix/issues/161", "state": "closed", "title": "How to include empty folders/files in the \"Repository Structure\" for more context?", "content": "First, thank you all for your work.\nI was expecting the whole project structure to be included in the output file (even for empty folders and files, to give context to the LLM on how the project should evolve or what it should exactly work on).", "created_at": "2024-11-04T19:43:10Z", "pr_merged_time": "", "pr_number": "", "pr_url": ""}, {"issue_number": "140", "issue_url": "https://github.com/yamadashy/repomix/issues/140", "state": "closed", "title": "Broken link (LICENSE) in README", "content": "Current Behavior:\nLICENSE in README redirects to invalid link ( 404 )\n\nExpected: It should redirect to correct link\n![image](https://github.com/user-attachments/assets/e7b8a3f0-3114-42e0-8dcd-27e0b8916c88)\n![image](https://github.com/user-attachments/assets/d909ec95-3bac-4f0d-9099-e9e9284cdb7f)", "created_at": "2024-10-27T15:44:31Z", "pr_merged_time": "", "pr_number": "", "pr_url": ""}, {"issue_number": "125", "issue_url": "https://github.com/yamadashy/repomix/issues/125", "state": "closed", "title": "Refactor: Centralize renderContext & style generation logic for output styles", "content": "The renderContext was repeated in all three style generation files including the logic to generate\n<code>/src/core/output/outputStyles/plainStyle.ts</code>\n<code>/src/core/output/outputStyles/xmlStyle.ts</code>\n<code>/src/core/output/outputStyles/markdownStyle.ts</code>\n``` javascript\nexport const generateMarkdownStyle = (outputGeneratorContext: OutputGeneratorContext) => {\nconst template = Handlebars.compile(markdownTemplate);\nconst renderContext = {\ngenerationHeader: generateHeader(outputGeneratorContext.generationDate),\nsummaryUsageGuidelines: generateSummaryUsageGuidelines(\noutputGeneratorContext.config,\noutputGeneratorContext.instruction,\n),\nsummaryNotes: generateSummaryNotes(outputGeneratorContext.config),\nsummaryAdditionalInfo: generateSummaryAdditionalInfo(),\nheaderText: outputGeneratorContext.config.output.headerText,\ninstruction: outputGeneratorContext.instruction,\ntreeString: outputGeneratorContext.treeString,\nprocessedFiles: outputGeneratorContext.processedFiles,\n};\nreturn `${template(renderContext).trim()}\\n`;\n};\n```\n###  Proposed Solution\nMove the common logic in these files into a shared utility file. This will make it easier to maintain as you add new styles and also add new parameters for renderContext in the future", "created_at": "2024-10-14T16:27:25Z", "pr_merged_time": "", "pr_number": "", "pr_url": ""}, {"issue_number": "112", "issue_url": "https://github.com/yamadashy/repomix/issues/112", "state": "closed", "title": "Optimize File Manipulation Logic in `fileManipulate.ts`", "content": "The current implementation of the file manipulation logic in `repopack/src/core/file/fileManipulate.ts` can be optimized for performance. Several areas, such as regex handling, string trimming, and quote matching, can be made more efficient.\n**Proposed Optimizations**:\n1. **Improve Docstring Removal Regex**:\nSimplify and optimize the regex used for removing docstrings in `PythonManipulator`.\n2. **Optimize Quote Matching in `removeHashComments`**:\nEnhance the logic to reduce redundant `slice` operations and improve performance in detecting quoted strings.\n3. **Refactor `searchInPairs` Binary Search**:\nStreamline the binary search function for checking if a hash is inside a string literal.\n4. **Enhance `rtrimLines` Function**:\nUse a more efficient method to trim trailing spaces and tabs from lines.\n5. **Lazy Instantiate Manipulators**:\nInstantiate manipulators in the `manipulators` object only when needed to improve memory usage.\n---\n\nExpected: - Faster file manipulation for large files.\n- Better memory efficiency.\n- Cleaner, more maintainable code.\nI'd like to contribute to optimizing the file manipulation logic in `fileManipulate.ts` as described in the issue. Could you please assign it to me so I can start working on it?\nThanks!", "created_at": "2024-10-08T18:29:49Z", "pr_merged_time": "2024-09-22T15:14:49Z", "pr_number": "81", "pr_url": "https://github.com/yamadashy/repomix/pull/81"}, {"issue_number": "106", "issue_url": "https://github.com/yamadashy/repomix/issues/106", "state": "closed", "title": "Minor Grammatical Issues in Repopack Contributor Covenant Code of Conduct", "content": "This pull request addresses a couple of minor grammatical issues found in the **Repopack Contributor Covenant Code of Conduct**. These changes aim to enhance the clarity and readability of the document, ensuring that the language is accurate and consistent.\n### Changes Made:\n1. **Verb Correction**\n2. **Clarification in \"Examples of Unacceptable Behavior\"**\n---\n### Examples:\n1. **Before the fix (Pledge Section)**:\n2. **Before the fix (Unacceptable Behavior Section)**:\n3. and many more...", "created_at": "2024-10-07T12:37:07Z", "pr_merged_time": "2024-10-08T14:08:39Z", "pr_number": "109", "pr_url": "https://github.com/yamadashy/repomix/pull/109"}, {"issue_number": "105", "issue_url": "https://github.com/yamadashy/repomix/issues/105", "state": "closed", "title": "Glob ignore out of included folders does not work", "content": "First of all thanks for this tool! been looking for something like this for a while\nI noticed this issue:\n```sh\n# repo structure\n/src\nmocks/\nMockA.ts\n```\n```sh\n# MockA.ts and all files under mocks folder are still included in output file\nnpx repopack --include \"/src/**/*.ts\" --ignore \"/src/mocks/\"\n\nExpected: npx repopack --include \"/src/**/*.ts\" --ignore \"/src/mocks/**/*.ts\"\n```", "created_at": "2024-10-07T01:53:51Z", "pr_merged_time": "", "pr_number": "", "pr_url": ""}, {"issue_number": "97", "issue_url": "https://github.com/yamadashy/repomix/issues/97", "state": "closed", "title": "docstrings in python files are not removed", "content": "This section of doc (enclosed by \"\"\") can sometimes be out of sync from the current code during debugging process. As such when you feed it to the model, it can produce weird results. As a workaround, \"\"\"[\\s\\S]*?\"\"\" regex Find and Replace in IDE does the job.", "created_at": "2024-10-01T16:47:49Z", "pr_merged_time": "", "pr_number": "", "pr_url": ""}, {"issue_number": "90", "issue_url": "https://github.com/yamadashy/repomix/issues/90", "state": "closed", "title": "exclude packager lock file by default", "content": "npm: package-lock.json\nYarn: yarn.lock\npnpm: pnpm-lock.yaml", "created_at": "2024-09-29T03:13:49Z", "pr_merged_time": "", "pr_number": "", "pr_url": ""}, {"issue_number": "89", "issue_url": "https://github.com/yamadashy/repomix/issues/89", "state": "closed", "title": "can't pack a repo due to presence of special token: <|endoftext|>", "content": "working with a repo which contains [hf model](https://huggingface.co/mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis). tried to ignore folders which might cause the problem, but still unable to pack.\n<img width=\"849\" alt=\"Screenshot 2024-09-28 at 19 50 58\" src=\"https://github.com/user-attachments/assets/ae34cbaf-cb15-42ea-9b6d-58106089f3ba\">", "created_at": "2024-09-28T14:23:29Z", "pr_merged_time": "", "pr_number": "", "pr_url": ""}, {"issue_number": "86", "issue_url": "https://github.com/yamadashy/repomix/issues/86", "state": "closed", "title": "add support output to markdown", "content": "", "created_at": "2024-09-25T02:28:55Z", "pr_merged_time": "", "pr_number": "", "pr_url": ""}, {"issue_number": "74", "issue_url": "https://github.com/yamadashy/repomix/issues/74", "state": "closed", "title": "Turn off security check?", "content": "It would be helpful to turn off security checks when we're working with developing cryptographic libraries... Can we have an argument flag to simply turn this function off when needed?", "created_at": "2024-09-10T19:44:09Z", "pr_merged_time": "", "pr_number": "", "pr_url": ""}, {"issue_number": "63", "issue_url": "https://github.com/yamadashy/repomix/issues/63", "state": "closed", "title": "Infinite Loading and Memory Leak", "content": "---\n\nSteps: 1. Run Repopack with the following command:\n```\nnpx repopack\n```\n2. Wait for the packing process to complete.\n3. Observe that the process runs without completing and without producing content.\n4. When aborted with `CTRL + C`, an empty Repopack file is produced.\n\nExpected: Repopack should successfully complete the packaging process and generate the packed files.\n\nActual: - The process hangs indefinitely.\n- An empty Repopack file is generated when manually aborted.\n### Additional Information\nI ran the command with the suggested flag to trace warnings:\n```bash\nset NODE_OPTIONS=--trace-warnings && npx repopack\n```\nThis is the output I received:\n```\n📦 Repopack v0.1.31\nNo custom config found at repopack.config.json or global config at C:\\Users\\<USER>\\AppData\\Local\\Repopack\\repopack.config.json.\nYou can add a config file for additional settings. Please check https://github.com/yamadashy/repopack for more information.\n⠸ Packing files...\n(node:11652) MaxPerformanceEntryBufferExceededWarning: Possible perf_hooks memory leak detected. 1000001 mark entries added to the global performance entry buffer. Use performance.clearMarks to clear the buffer.\nat bufferUserTiming (node:internal/perf/observe:422:15)\nat mark (node:internal/perf/usertiming:160:3)\nat Performance.mark (node:internal/perf/performance:123:12)\nat SecretLintProfiler.mark (file:///C:/Users/<USER>/AppData/Local/npm-cache/_npx/843b8f871cabf3ea/node_modules/@secretlint/profiler/module/index.js:41:23)\nat file:///C:/Users/<USER>/AppData/Local/npm-cache/_npx/843b8f871cabf3ea/node_modules/@secretlint/core/module/RunningEvents.js:50:36\nat async Promise.all (index 8)\n```\n### System Information\n- **Repopack Version**: v0.1.31 (started happening with v0.1.26)", "created_at": "2024-08-28T09:51:24Z", "pr_merged_time": "", "pr_number": "", "pr_url": ""}, {"issue_number": "56", "issue_url": "https://github.com/yamadashy/repomix/issues/56", "state": "closed", "title": "V0.1.27 fails on Termux on Android", "content": "```\n$ repopack\n📦 Repopack v0.1.27\n✖ Error during packing\nUnexpected error: Expected `concurrency` to be an integer from 1 and up or `Infinity`, got `0` (number)\nFor more help, please visit: https://github.com/yamadashy/repopack/issues\n```\nReviewing releases, concurrency was introduced a few versions ago in v0.1.24. Installing v0.1.23 results in a successful repopack.\n```\n$ repopack\n📦 Repopack v0.1.23\n✔ Packing completed successfully!\n```", "created_at": "2024-08-22T02:01:29Z", "pr_merged_time": "", "pr_number": "", "pr_url": ""}, {"issue_number": "55", "issue_url": "https://github.com/yamadashy/repomix/issues/55", "state": "closed", "title": "bug: Large chunks of code missing with remove comments on", "content": "this was happening before update to 0.1.27.\nI don't really want to post the code here, so if you want to message me or something, then I can send you the files.", "created_at": "2024-08-21T17:17:13Z", "pr_merged_time": "2024-09-22T15:14:49Z", "pr_number": "81", "pr_url": "https://github.com/yamadashy/repomix/pull/81"}, {"issue_number": "51", "issue_url": "https://github.com/yamadashy/repomix/issues/51", "state": "closed", "title": "Feature request: global configuration equivalent of repopack.config.json", "content": "Hi,\nI have been using repopack and really like the configurability. However, I typically use the same repopack config settings for most projects and it would be nice if I could have a global `repopack.config.json` in `~/.config/` or something that would take effect if a local config file is not present.  It didn't seem like this was already possible, or at least I did not see anything to that effect. Any help would be appreciated. Thank you!", "created_at": "2024-08-14T18:33:24Z", "pr_merged_time": "", "pr_number": "", "pr_url": ""}, {"issue_number": "34", "issue_url": "https://github.com/yamadashy/repomix/issues/34", "state": "closed", "title": "Honour .gitignore in subfolders as well", "content": "Seems like the script is only looking into a global gitignore (on root folder), but if there are specific gitignore files in the subfolders they're ignored.", "created_at": "2024-08-04T10:32:24Z", "pr_merged_time": "", "pr_number": "", "pr_url": ""}, {"issue_number": "22", "issue_url": "https://github.com/yamadashy/repomix/issues/22", "state": "closed", "title": "feat: add `include` filters", "content": "Though `ignore` filters are useful, it would be helpful to have the opposite. For example pack only \"*.md\" files", "created_at": "2024-07-31T14:56:02Z", "pr_merged_time": "", "pr_number": "", "pr_url": ""}, {"issue_number": "18", "issue_url": "https://github.com/yamadashy/repomix/issues/18", "state": "closed", "title": "Dependency Dashboard", "content": "This issue lists Renovate updates and detected dependencies. Read the [Dependency Dashboard](https://docs.renovatebot.com/key-concepts/dashboard/) docs to learn more.\n## Awaiting Schedule\nThese updates are awaiting their schedule. Click on a checkbox to get an update now.\n- [ ] <!-- unschedule-branch=renovate/all-minor-patch -->chore(deps): update all non-major dependencies (`@eslint/js`, `@types/eslint`, `@types/node`, `@typescript-eslint/eslint-plugin`, `@typescript-eslint/parser`, `@vitest/coverage-v8`, `commander`, `eslint-plugin-prettier`, `ignore`, `log-update`, `rimraf`, `vite`, `vitest`, `yarn`)\n- [ ] <!-- unschedule-branch=renovate/eslint-9.x -->chore(deps): update dependency @types/eslint to v9\n- [ ] <!-- unschedule-branch=renovate/major-eslint-monorepo -->chore(deps): update dependency eslint to v9\n- [ ] <!-- unschedule-branch=renovate/rimraf-6.x -->chore(deps): update dependency rimraf to v6\n- [ ] <!-- unschedule-branch=renovate/typescript-5.x -->chore(deps): update dependency typescript to v5\n- [ ] <!-- unschedule-branch=renovate/vite-5.x -->chore(deps): update dependency vite to v5\n- [ ] <!-- unschedule-branch=renovate/cli-spinners-3.x -->fix(deps): update dependency cli-spinners to v3\n- [ ] <!-- unschedule-branch=renovate/commander-12.x -->fix(deps): update dependency commander to v12\n## Detected dependencies\n<blockquote>\n</details>\n</blockquote>\n</details>\n<blockquote>\n<details><summary>.github/workflows/test.yml</summary>\n- `actions/checkout v4`\n- `actions/setup-node v4`\n- `actions/checkout v4`\n- `actions/setup-node v4`\n- `ubuntu 22.04`\n</details>\n</blockquote>\n</details>\n<blockquote>\n</details>\n</blockquote>\n</details>\n<blockquote>\n- `@secretlint/core ^8.2.4`\n- `@secretlint/secretlint-rule-preset-recommend ^8.2.4`\n- `cli-spinners ^2.9.2`\n- `commander ^7.1.0`\n- `iconv-lite ^0.6.3`\n- `ignore ^5.2.0`\n- `istextorbinary ^9.5.0`\n- `jschardet ^3.1.3`\n- `log-update ^6.0.0`\n- `picocolors ^1.0.1`\n- `strip-comments ^2.0.1`\n- `@eslint/js ^9.7.0`\n- `@types/eslint ~8.56.10`\n- `@types/eslint__js ~8.42.3`\n- `@types/eslint-config-prettier ~6.11.3`\n- `@types/node ^20.14.10`\n- `@types/strip-comments ^2.0.4`\n- `@typescript-eslint/eslint-plugin ^7.16.0`\n- `@typescript-eslint/parser ^7.16.0`\n- `@vitest/coverage-v8 ^2.0.2`\n- `eslint ^8.57.0`\n- `eslint-config-prettier ^9.1.0`\n- `eslint-plugin-import ^2.29.1`\n- `eslint-plugin-prettier ^5.1.3`\n- `rimraf ^5.0.7`\n- `secretlint ^8.2.4`\n- `typescript ^4.9.5`\n- `vite ^4.1.4`\n- `vitest ^2.0.2`\n- `yarn >=1.0.0`\n</details>\n</blockquote>\n</details>\n---\n- [ ] <!-- manual job -->Check this box to trigger a request for Renovate to run again on this repository", "created_at": "2024-07-27T07:03:54Z", "pr_merged_time": "", "pr_number": "", "pr_url": ""}, {"issue_number": "17", "issue_url": "https://github.com/yamadashy/repomix/issues/17", "state": "closed", "title": "Action Required: Fix Renovate Configuration", "content": "There is an error with this repository's Renovate configuration that needs to be fixed. As a precaution, Renovate will stop PRs until it is resolved.\nError type: Cannot find preset's package (schedule:earlySaturdays)", "created_at": "2024-07-27T07:02:25Z", "pr_merged_time": "", "pr_number": "", "pr_url": ""}, {"issue_number": "12", "issue_url": "https://github.com/yamadashy/repomix/issues/12", "state": "closed", "title": "Feat: Propose .repopackignore file to override .gitignore for repopack bundles", "content": "Hello,\nFirst, thank you for this very useful project!\nI'd like to suggest a feature that could enhance repopack's flexibility.\n### Current situation:\nI have a file \"claude-custom-instructions.txt\" that I want to include in my repopack bundle, but it's listed in my .gitignore file. Currently, to include this file, I need to set `ignore.useDefaultPatterns = false` and then manually add all the patterns I want to ignore in `ignore.customPatterns`. This works, but it's a bit cumbersome.\n### Feature proposal:\nIntroduce a new file called `.repopackignore` that would override .gitignore settings specifically for repopack bundles when present. Here's how it could work:\n1. If a `.repopackignore` file exists in the repository, repopack would use it instead of .gitignore.\n2. The syntax would be similar to .gitignore, allowing users to easily specify which files to include or exclude from the bundle.\nBenefits:\n- Simpler management of bundle-specific ignores\n- Maintains separation between git ignore rules and repopack bundle rules\n- Allows users to easily include files in repopack bundles that are git-ignored\nI'd appreciate your thoughts on this idea and whether it aligns with the project's goals.\nThank you again !", "created_at": "2024-07-26T17:26:09Z", "pr_merged_time": "2024-07-27T14:13:32Z", "pr_number": "13", "pr_url": "https://github.com/yamadashy/repomix/pull/13"}, {"issue_number": "8", "issue_url": "https://github.com/yamadashy/repomix/issues/8", "state": "closed", "title": "feat: Option to prexfix line number", "content": "Add option to prefix each line in the output file with the line number from the true file.\nI think this would be useful as you can add to your prompt something like: \"Show the code changes with the line number, and include one line before to easier locate the change\".\nI find this saves a lot of time on larger projects", "created_at": "2024-07-24T13:44:25Z", "pr_merged_time": "", "pr_number": "", "pr_url": ""}, {"issue_number": "2", "issue_url": "https://github.com/yamadashy/repomix/issues/2", "state": "closed", "title": "feat: <PERSON><PERSON><PERSON> comments", "content": "Would removing comments based on the language be beneficial?\nSo for Python remove anything that starts with `# `", "created_at": "2024-07-20T16:41:45Z", "pr_merged_time": "2024-07-27T05:24:14Z", "pr_number": "14", "pr_url": "https://github.com/yamadashy/repomix/pull/14"}, {"issue_number": "1", "issue_url": "https://github.com/yamadashy/repomix/issues/1", "state": "closed", "title": "Issue with Ignore", "content": "Hello,\nThis seems like a really great start to the package, so nice work! Any suggestions on how to ignore all SVG, CSS, etc. files? That does not appear to be working as I would expect.\n![image](https://github.com/user-attachments/assets/a4fbacdc-0f9a-4e45-a952-17366a6d4473)\n![image](https://github.com/user-attachments/assets/df29e427-4837-4c17-9380-4e322d276a84)\nThanks,", "created_at": "2024-07-18T14:06:52Z", "pr_merged_time": "", "pr_number": "", "pr_url": ""}]
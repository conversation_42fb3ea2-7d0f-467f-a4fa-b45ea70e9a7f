[{"issue_number": "725", "issue_url": "https://github.com/yamadashy/repomix/issues/725", "state": "open", "title": "Run repomix and include CLI output of another tool in one step", "content": "When vibe coding with non-agent tools (so directly with AI-Studio or chatgpt.com) it would be nice if repomix could automatically run a command line tool and add the output of this tool to the summary.\nI would image something like\n```\nrepomix -c npm install\n```\nThis would run repomix as usual but also run the given command and include its output (preferably at the top). For example, this could generate:\nI just ran `npm install` in my codebase in ${PWD}, but it `FAILED` with the following output:\n```\n...CLI_OUTPUT_HERE...\n```\nThe rest of this file is a merged representation of the entire codebase, combined into a single document by Repomix.\nThe content has been processed where security check has been disabled.\n...\nThis would really help to package error messages together with the latest status and current working directory."}, {"issue_number": "718", "issue_url": "https://github.com/yamadashy/repomix/issues/718", "state": "closed", "title": "Please add to Open VSX https://open-vsx.org/", "content": "Support Cursor and other VSCode forks - deploy to the https://open-vsx.org/ marketplace please."}, {"issue_number": "714", "issue_url": "https://github.com/yamadashy/repomix/issues/714", "state": "open", "title": "`.gitignore` is not respected", "content": "1. Create MRE project like following:\n```\n$ tree\n.\n|-- logs\n|   `-- some_logs.txt\n|-- main.py\n```\n2. Install `uv` to the system if it's not already installed. I haven't checked without this, but I'm kinda sure it would be the same\n3. Run `uvx repomix .`\n```\n$ uvx repomix .\n📦 Repomix v0.2.9\n────────────────\nTotal Files: 3 files\nTotal Characters: 20 characters\nToken calculation: disabled\nOutput to: repomix-output.md\nSecurity: ✔ No suspicious files detected\n🔎 Security Check:\n──────────────────\n✔ No suspicious files detected\n📈 Top 5 files by character and token count:\n──────────────────────────────────────────────────\n1.  main.py (11 characters)\n2.  .gitignore (5 characters)\n3.  logs/some_logs.txt (4 characters)\n🎉 Done!\nYour code repository has been successfully packaged.\n```\nHow it should be:\n```\n📈 Top 5 files by character and token count:\n──────────────────────────────────────────────────\n1.  main.py (11 characters)\n2.  .gitignore (5 characters)\n```\n### Usage Context\nI did not configure `repomix` at any way"}, {"issue_number": "713", "issue_url": "https://github.com/yamadashy/repomix/issues/713", "state": "closed", "title": "1.0.0 not working on windows", "content": "executing repomix leads to\nFatal Error: {\nname: '<PERSON><PERSON><PERSON>',\nmessage: 'No such built-in module: node:readline/promises',\nstack: 'Error [ERR_UNKNOWN_BUILTIN_MODULE]: No such built-in module: node:readline/promises\\n' +\n'    at new NodeError (node:internal/errors:372:5)\\n' +\n'    at ESMLoader.builtinStrategy (node:internal/modules/esm/translators:260:11)\\n' +\n'    at ESMLoader.moduleProvider (node:internal/modules/esm/loader:337:14)'\n}\nit was working before\ninstalled via npm install -g repomix\n### Usage Context\nNone"}, {"issue_number": "696", "issue_url": "https://github.com/yamadashy/repomix/issues/696", "state": "open", "title": "--compress option is not working", "content": "Hi, I tried using the --compress option, but it threw an error with the following message. I'm currently using version 0.2.25. Is the --compress option actually supported?\n```\nerror: unknown option '--compress'\n```"}, {"issue_number": "691", "issue_url": "https://github.com/yamadashy/repomix/issues/691", "state": "open", "title": "Remote Server", "content": "It would be great to see a remote MCP server for RepoMix in alignment with one of Anthropic's [latest announcements](https://docs.anthropic.com/en/docs/agents-and-tools/remote-mcp-servers)\nExample remote server support:\n```\n\"repomix\": {\n\"url\": \"https://mcp.repomix.com/mcp\"\n}\n```\nFor inspiration, check out [context7](https://github.com/upstash/context) repo"}, {"issue_number": "685", "issue_url": "https://github.com/yamadashy/repomix/issues/685", "state": "open", "title": "Exclude variables in specific files", "content": "It'd be helpful to have the ability to ignore variable patterns in specific files as opposed to files wholesale."}, {"issue_number": "673", "issue_url": "https://github.com/yamadashy/repomix/issues/673", "state": "open", "title": "False positive error with --stdout", "content": "When running e.g.\n```sh\nnpx -y repomix --stdout --remote https://github.com/JedWatson/classnames\n```\nIf you capture stderr and stdout, if you check for `stderr`, it's present with \"command failed\" even if stdout is successful.\n### Usage Context\nNone"}, {"issue_number": "650", "issue_url": "https://github.com/yamadashy/repomix/issues/650", "state": "open", "title": "simple way to only include git-tracked files", "content": "Maybe I missed this option but currently what I do is I create a custom include list that contains only files that show up in the in git tracking. And then add on some exclude patterns on top of that. It would be nice to have a way a more direct way to only include git tracked files."}, {"issue_number": "648", "issue_url": "https://github.com/yamadashy/repomix/issues/648", "state": "closed", "title": "Read file list from stdin", "content": "Add support for reading the file list from stdin\n# Use Case\nI'm using [fd](https://github.com/sharkdp/fd) and [ripgrep](https://github.com/BurntSushi/ripgrep) a lot in my day-to-day work.\nIt would have been nice to pipe the results of the file search to repomix:\n```sh\n# List all Markdown files\nfd -t file -e md | repomix --stdin\n# List files from \"tests\" directories\nfd -t directory 'tests' | repomix --stdin\n# Find TS files that contain 'MyClass' pattern using both fd and ripgrep\nfd -t file -e ts -e tsx --exec-batch rg -l 'MyClass' | repomix --stdin\n```\n# Proposed Solution\nAdd a flag (`--stdin` or `--include-from-stdin`) that would enable repomix to read the list of files, directories, or globs from the piped input\n# Related\n#553"}, {"issue_number": "636", "issue_url": "https://github.com/yamadashy/repomix/issues/636", "state": "closed", "title": "Browser extension don't work in Edge", "content": "Hi,\nI just installed the repomix browser extension in Edge browser. I don't understand why, but when I click the icon, I get some Edge maintenance menu instead of the extension UI, see screenshot:\n![Image](https://github.com/user-attachments/assets/a65f67ba-b45e-42d8-b350-634f2dbf974f)\nIf I click \"Repomix\" choice, it opens the extension page in chrome webstore.\n### Usage Context\nNone"}, {"issue_number": "631", "issue_url": "https://github.com/yamadashy/repomix/issues/631", "state": "closed", "title": "VSCode extension", "content": "The experience of AI coding tools (cursor or others) mentioning file/directory context is unsatisfactory, and it worsens in agent mode (long workflows, discarding content to save costs). Some tools cannot even use the mouse to drag files. If repomix could directly generate an XML prompt for the selected file using the right-click, it would be very convenient!\n> There is already a repomix runner extension in the marketplace, but it is outdated. It seems to use a very early version."}, {"issue_number": "624", "issue_url": "https://github.com/yamadashy/repomix/issues/624", "state": "open", "title": "Behavior of --ignore \"**/.gitignore\"", "content": "Thank you for the great project.\nThe behavior of --ignore \"**/.gitignore\" seems not correct.\nI just want to exclude the content of .gitignore from the output, but I want to keep using filters listed in .gitignore\n`repomix --ignore \"**/.gitignore\"` excludes .gitignore, but also disable the filters described in the .gitignore\n### Usage Context\nNone"}, {"issue_number": "618", "issue_url": "https://github.com/yamadashy/repomix/issues/618", "state": "open", "title": "Support JSONC extension for the config", "content": "The `.jsonc` extension is helpful for adding comments in JSON files.\nI know that a path to a custom config file can be passed via `-c` but I would prefer not to have to add it every time I run repomix."}, {"issue_number": "614", "issue_url": "https://github.com/yamadashy/repomix/issues/614", "state": "open", "title": "Installation via brew command throws error", "content": "2018 MacbookPro Sequoia 15.4.1\nWhen attempting to install repomix via homebrew, the following error is thrown =>\n```\nbrew install repomix\n==> Downloading https://ghcr.io/v2/homebrew/core/repomix/manifests/0.3.7\nAlready downloaded: /Users/<USER>/Library/Caches/Homebrew/downloads/9d9f0fbb552fe93b7c6004d694d917135adeba80dff8e30f8465b4289cb2353f--repomix-0.3.7.bottle_manifest.json\n==> Fetching dependencies for repomix: libuv, ca-certificates and openssl@3\n==> Downloading https://ghcr.io/v2/homebrew/core/libuv/manifests/1.51.0\nAlready downloaded: /Users/<USER>/Library/Caches/Homebrew/downloads/25bd9f2f86d047011dc8ca247bea2e4e3e5a150e29648418d48dfca4c8c136ea--libuv-1.51.0.bottle_manifest.json\n==> Fetching libuv\n==> Downloading https://ghcr.io/v2/homebrew/core/libuv/blobs/sha256:fb199706c025af4c6160825de25f9220c8d571499c5dba71c4b93a3874ea7a03\nAlready downloaded: /Users/<USER>/Library/Caches/Homebrew/downloads/d53fecfc1f0449b8a29d78c7364f8effa7f77965da02e57b732ad70621f29112--libuv--1.51.0.sonoma.bottle.tar.gz\n==> Downloading https://ghcr.io/v2/homebrew/core/ca-certificates/manifests/2025-05-20\nAlready downloaded: /Users/<USER>/Library/Caches/Homebrew/downloads/bc18acc15e0abddc102f828b57a29cfdbec1b6b002db37ad12bad9dbf0e9d12f--ca-certificates-2025-05-20.bottle_manifest.json\n==> Fetching ca-certificates\n==> Downloading https://ghcr.io/v2/homebrew/core/ca-certificates/blobs/sha256:dda1100e7f994081a593d6a5e422451bfa20037e29667ed2b79f011ffc9288a7\ncurl: (56) The requested URL returned error: 503    #           #            #\nError: repomix: Failed to download resource \"ca-certificates\"\nDownload failed: https://ghcr.io/v2/homebrew/core/ca-certificates/blobs/sha256:dda1100e7f994081a593d6a5e422451bfa20037e29667ed2b79f011ffc9288a7\n```\n### Usage Context\nNone"}, {"issue_number": "609", "issue_url": "https://github.com/yamadashy/repomix/issues/609", "state": "open", "title": "Unexpected error: Failed to filter files in directory", "content": "❯ repomix ./testtest\n📦 Repomix v0.3.7\nError filtering files: ENOTDIR: not a directory, stat '/Users/<USER>/tmp/testtest/build/Release/**'\n✖ Error during packing\n✖ Unexpected error: Failed to filter files in directory /Users/<USER>/tmp/testtest. Reason: ENOTDIR: not a directory, stat '/Users/<USER>/tmp/testtest/build/Release/**'\nStack trace: Error: Failed to filter files in directory /Users/<USER>/tmp/testtest. Reason: ENOTDIR: not a directory, stat '/Users/<USER>/tmp/testtest/build/Release/**'\nat file:///opt/homebrew/lib/node_modules/repomix/lib/core/file/fileSearch.js:158:19\nat Generator.throw (<anonymous>)\nat rejected (file:///opt/homebrew/lib/node_modules/repomix/lib/core/file/fileSearch.js:5:65)\nFor detailed debug information, use the --verbose flag\nfailed to filter files error with a non-existent directory within my repo testtest\n### Usage Context\nNone"}, {"issue_number": "608", "issue_url": "https://github.com/yamadashy/repomix/issues/608", "state": "open", "title": "Files \"inclusion level\"", "content": "Create a way to provide the detalization level for packed files.\nThere are 3 levels:\n1. **Full content** - for a set of the most important files.\n2. **Compress** - for the less important ones, which still may be relevant.\n3. **Directory-only** - mention in the directory structure.\nOne can think of it as nested sets which describe not just the files, but the level of their detalization:\n[ File in directory structure] < [ Compressed file ] < [ Full file ]\nUse case: we need to improve build system.\n1. Full files: for all configs and build files.\n2. Directory structure: full file structure.\nUse case: we need to work on a module residing inside a certain folder:\n1. Full files: the whole module, main project files, configuration\n2. Compressed files: other modules which somewhat contribute to context, but details aren't important.\nThen we'll be able to create packing scenarios with various such settings and invoke them via MCP.\nThe simplest syntax would be 3 settings, e.g.\n```js\nfiles: {\ncontent: [...globs...],  // full content\ncompress: [...globs...], // compressed\n},\ndirectory: [...globs...] // only list in the file structure\n```\n1. If a file matches a glob in `content` - full content is included.\n2. Otherwise, if it matches a glob in `compress` - it gets compressed\nSo we can have like `compress: **/*.ts, content: my.ts` - all ts will be compressed, but my.ts will be included fully.\nAlso, all mentioned files are included into the directory structure, together with `directory` globs.\nThe `directory` globs has precedence over all ignores.\nHere, the `directory` setting is the least important one, but it still may matter for huge projects with many files and folders. I understand we already have some ignore settings, perhaps they can replace `directory` or somehow make it into the mix =)"}, {"issue_number": "591", "issue_url": "https://github.com/yamadashy/repomix/issues/591", "state": "closed", "title": "\"Not implemented\" error when using `bunx`", "content": "I use `bunx repomix` regularly on my Windows computer, but when trying it on my Mac (Apple Silicon M4 chip), I receive an error:\n```\n🫀  ➜  bunx repomix --verbose\ndirectories: [ '.' ]\ncwd: /Users/<USER>/Programming/project-folder\noptions: {\ndirectoryStructure: true,\nfiles: true,\ngitSortByChanges: true,\ngitignore: true,\ndefaultPatterns: true,\nsecurityCheck: true,\nverbose: true\n}\n📦 Repomix v0.3.6\nLoaded CLI options: {\ndirectoryStructure: true,\nfiles: true,\ngitSortByChanges: true,\ngitignore: true,\ndefaultPatterns: true,\nsecurityCheck: true,\nverbose: true\n}\nNo Repopack files found to migrate.\nLoading local config from: /Users/<USER>/Programming/project-folder/repomix.config.json\nLoading global config from: /Users/<USER>/.config/repomix/repomix.config.json\nNo custom config found at repomix.config.json or global config at /Users/<USER>/.config/repomix/repomix.config.json.\nYou can add a config file for additional settings. Please check https://github.com/yamadashy/repomix for more information.\nLoaded file config: {}\nCLI config: {}\nDefault config: {\ninput: { maxFileSize: 52428800 },\noutput: {\nfilePath: 'repomix-output.xml',\nstyle: 'xml',\nparsableStyle: false,\ndirectoryStructure: true,\nfiles: true,\nremoveComments: false,\nremoveEmptyLines: false,\ncompress: false,\ntopFilesLength: 5,\nshowLineNumbers: false,\ncopyToClipboard: false,\ngit: {\nsortByChanges: true,\nsortByChangesMaxCommits: 100,\nincludeDiffs: false\n}\n},\ninclude: [],\nignore: { useGitignore: true, useDefaultPatterns: true, customPatterns: [] },\nsecurity: { enableSecurityCheck: true },\ntokenCount: { encoding: 'o200k_base' }\n}\nDefault output file path is set to: repomix-output.xml\nMerged config: {\ninput: { maxFileSize: 52428800 },\noutput: {\nfilePath: 'repomix-output.xml',\nstyle: 'xml',\nparsableStyle: false,\ndirectoryStructure: true,\nfiles: true,\nremoveComments: false,\nremoveEmptyLines: false,\ncompress: false,\ntopFilesLength: 5,\nshowLineNumbers: false,\ncopyToClipboard: false,\ngit: {\nsortByChanges: true,\nsortByChangesMaxCommits: 100,\nincludeDiffs: false\n}\n},\ninclude: [],\nignore: { useGitignore: true, useDefaultPatterns: true, customPatterns: [] },\nsecurity: { enableSecurityCheck: true },\ntokenCount: { encoding: 'o200k_base' },\ncwd: '/Users/<USER>/Programming/project-folder'\n}\nAdding default ignore patterns\nAdding output file to ignore patterns: repomix-output.xml\nAdding custom ignore patterns: []\nInclude patterns: [ '**/*' ]\nIgnore patterns: [\n'.git/**',\n'.hg/**',\n'.hgignore',\n'.svn/**',\n'**/node_modules/**',\n'**/bower_components/**',\n'**/jspm_packages/**',\n'vendor/**',\n'**/.bundle/**',\n'**/.gradle/**',\n'target/**',\n'logs/**',\n'**/*.log/**',\n'**/npm-debug.log*/**',\n'**/yarn-debug.log*/**',\n'**/yarn-error.log*/**',\n'pids/**',\n'*.pid',\n'*.seed',\n'*.pid.lock',\n'lib-cov/**',\n'coverage/**',\n'.nyc_output/**',\n'.grunt/**',\n'.lock-wscript',\n'build/Release/**',\n'typings/**',\n'**/.npm/**',\n'.eslintcache',\n'.rollup.cache/**',\n'.webpack.cache/**',\n'.parcel-cache/**',\n'.sass-cache/**',\n'*.cache',\n'.node_repl_history',\n'*.tgz',\n'**/.yarn/**',\n'**/.yarn-integrity/**',\n'.env',\n'.next/**',\n'.nuxt/**',\n'.vuepress/dist/**',\n'.serverless/**',\n'.fusebox/**',\n'.dynamodb/**',\n'dist/**',\n'**/.DS_Store/**',\n'**/Thumbs.db/**',\n'.idea/**',\n'.vscode/**',\n'**/*.swp/**',\n'**/*.swo/**',\n'**/*.swn/**',\n'**/*.bak/**',\n'build/**',\n'out/**',\n'tmp/**',\n'temp/**',\n'**/repomix-output.*/**',\n'**/repopack-output.*/**',\n'**/package-lock.json/**',\n'**/yarn-error.log/**',\n'**/yarn.lock/**',\n'**/pnpm-lock.yaml/**',\n'**/bun.lockb/**',\n'**/bun.lock/**',\n'**/__pycache__/**',\n'**/*.py[cod]/**',\n'**/venv/**',\n'**/.venv/**',\n'**/.pytest_cache/**',\n'**/.mypy_cache/**',\n'**/.ipynb_checkpoints/**',\n'**/Pipfile.lock/**',\n'**/poetry.lock/**',\n'**/uv.lock/**',\n'**/Cargo.lock/**',\n'**/Cargo.toml.orig/**',\n'**/target/**',\n'**/*.rs.bk/**',\n'**/composer.lock/**',\n'**/Gemfile.lock/**',\n'**/go.sum/**',\n'**/mix.lock/**',\n'**/stack.yaml.lock/**',\n'**/cabal.project.freeze/**',\n'repomix-output.xml'\n]\nIgnore file patterns: [ '**/.gitignore', '**/.repomixignore' ]\nFiltered 141 files\nInitializing worker pool with min=1, max=2 threads. Worker path: file:///Users/<USER>/.bun/install/global/node_modules/repomix/lib/core/file/workers/fileCollectWorker.js\n\nExpected: Stack trace: Error: Not implemented\nat unknown\nat new ThreadPool (/Users/<USER>/.bun/install/global/node_modules/piscina/dist/index.js:95:50)\nat new Piscina (/Users/<USER>/.bun/install/global/node_modules/piscina/dist/index.js:570:57)\nat initPiscina (/Users/<USER>/.bun/install/global/node_modules/repomix/lib/shared/processConcurrency.js:20:16)\nat initTaskRunner (/Users/<USER>/.bun/install/global/node_modules/repomix/lib/core/file/fileCollect.js:14:18)\nat <anonymous> (/Users/<USER>/.bun/install/global/node_modules/repomix/lib/core/file/fileCollect.js:20:26)\nat <anonymous> (/Users/<USER>/.bun/install/global/node_modules/repomix/lib/core/file/fileCollect.js:7:71)\nat new Promise (native:1:11)\nat __awaiter (/Users/<USER>/.bun/install/global/node_modules/repomix/lib/core/file/fileCollect.js:3:27)\nat map (native:1:11)\nNeed help?\n• File an issue on GitHub: https://github.com/yamadashy/repomix/issues\n• Join our Discord community: https://discord.gg/wNYzTwZFku\n```\nI was able to pack successfully using a Docker container as a workaround:\n```\ndocker run -v .:/app -it --rm ghcr.io/yamadashy/repomix\n```\nI am using Bun version `1.2.14` (latest at the time of writing).\nSorry if you're not intending to support Bun. I have just gotten used to using it as a replacement and wasn't sure if you'd seen this bug before.\n### Usage Context\nRepomix CLI"}, {"issue_number": "582", "issue_url": "https://github.com/yamadashy/repomix/issues/582", "state": "open", "title": "Support SSE transport for `--mcp` mode", "content": "Repomix’s MCP currently only speaks stdio. We should add an HTTP/SSE endpoint (e.g. `/sse`) so clients can connect via EventSource. Internally, reuse the SDK’s `SSEServerTransport`, and expose a `--mcp-sse` flag (or enable by default) for configuration. Update docs and add a basic smoke test."}, {"issue_number": "561", "issue_url": "https://github.com/yamadashy/repomix/issues/561", "state": "open", "title": "Feature Request: Fine-tuned selection of including/excluding specific code block when `--compress` (i.e. Function Body code block)", "content": "Topic: `repomix --compress`, LLM-based code compression\n## Motivation\n### Generate richer context\nWhen I run `--compress` in Repomix, I’d like the output to capture not only symbol information but also the *implementation details* of functions.\nMy use-case is to bundle an entire source-code repository into a single file, feed it to an LLM, and let the model analyze the codebase holistically.\nBecause many insights depend on what happens *inside* a function, simply exporting the symbols wasn't enough for me—I need the function bodies in the compressed output as well.\nGiving users control over which code snippets get included would provide much greater flexibility.\n### Apply custom pre-processing to code blocks identified via tree-sitter\nWith tree-sitter we can pinpoint distinct AST nodes—such as a function’s name and its body. If we could:\n* **retain every function symbol untouched**, and\n* **pass each function body through an LLM for aggressive compression**,\nthen we’d keep the static-analysis benefits of full symbol visibility *and* supply the language model with far richer context.\nIdeally, each code block (e.g., function name, function body) would have its own configurable processing pipeline.\n## Related issues\n* #36 and #511 discuss LLM-based summarization.\n## More Notes\nI am currently developing [llmlingua-2-js](https://github.com/atjsh/llmlingua-2-js), a JavaScript port of LLMLingua-2. Once it is finished, developers will be able to run LLM-based code compression seamlessly in the same Node.js environment that Repomix uses.\nI plan to open a separate PR for llmlingua-2-js when it is ready."}, {"issue_number": "558", "issue_url": "https://github.com/yamadashy/repomix/issues/558", "state": "open", "title": "Error while importing repomix", "content": "```\n{\n\"errorType\": \"TypeError\",\n\"errorMessage\": \"The \\\"path\\\" argument must be of type string or an instance of URL. Received undefined\",\n\"trace\": [\n\"TypeError [ERR_INVALID_ARG_TYPE]: The \\\"path\\\" argument must be of type string or an instance of URL. Received undefined\",\n\"    at fileURLToPath (node:internal/url:1487:11)\",\n\"    at Object.<anonymous> (/var/task/index.js:87358:88)\",\n\"    at Module._compile (node:internal/modules/cjs/loader:1529:14)\",\n\"    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\",\n\"    at Module.load (node:internal/modules/cjs/loader:1275:32)\",\n\"    at Module._load (node:internal/modules/cjs/loader:1096:12)\",\n\"    at Module.require (node:internal/modules/cjs/loader:1298:19)\",\n\"    at require (node:internal/modules/helpers:182:18)\",\n\"    at _tryRequireFile (file:///var/runtime/index.mjs:1002:37)\",\n\"    at _tryRequire (file:///var/runtime/index.mjs:1052:25)\"\n]\n}\n```"}, {"issue_number": "557", "issue_url": "https://github.com/yamadashy/repomix/issues/557", "state": "open", "title": "How to import with commonjs", "content": "I am facing issues while working with my projects which uses tsc and commonjs.\nGetting the following error:\nthe current file is a commonjs module whose imports will produce 'require' calls; however, the referenced file is an ecmascript module and cannot be imported with 'require'."}, {"issue_number": "555", "issue_url": "https://github.com/yamadashy/repomix/issues/555", "state": "open", "title": "Missing tiktoken_bg.wasm", "content": "```\n{\n\"errorType\": \"Error\",\n\"errorMessage\": \"Missing tiktoken_bg.wasm\",\n\"trace\": [\n\"Error: Missing tiktoken_bg.wasm\",\n\"    at node_modules/tiktoken/tiktoken.cjs (/var/task/index.js:16383:30)\",\n\"    at __require (/var/task/index.js:12:51)\",\n\"    at node_modules/repomix/lib/core/metrics/TokenCounter.js (/var/task/index.js:16397:31)\",\n\"    at __init (/var/task/index.js:9:56)\",\n\"    at node_modules/repomix/lib/core/metrics/calculateMetrics.js (/var/task/index.js:16434:5)\",\n\"    at __init (/var/task/index.js:9:56)\",\n\"    at node_modules/repomix/lib/core/packager.js (/var/task/index.js:26483:5)\",\n\"    at __init (/var/task/index.js:9:56)\",\n\"    at node_modules/repomix/lib/index.js (/var/task/index.js:67205:5)\",\n\"    at __init (/var/task/index.js:9:56)\"\n]\n}\n```\nDeploying TS Lambda\ntsconfig.json\n```\n{\n\"compilerOptions\": {\n\"target\": \"ES2020\",\n\"module\": \"commonjs\",\n\"lib\": [\"es2016\", \"es2017.object\", \"es2017.string\", \"es2020\"],\n\"declaration\": true,\n\"outDir\": \"./dist\",\n\"declarationDir\": \"./dist\",\n\"strict\": true,\n\"noImplicitAny\": true,\n\"strictNullChecks\": true,\n\"noImplicitThis\": true,\n\"alwaysStrict\": true,\n\"noUnusedLocals\": false,\n\"noUnusedParameters\": false,\n\"noImplicitReturns\": true,\n\"noFallthroughCasesInSwitch\": false,\n\"inlineSourceMap\": true,\n\"inlineSources\": true,\n\"experimentalDecorators\": true,\n\"strictPropertyInitialization\": false,\n\"typeRoots\": [\"./node_modules/@types\"],\n\"rootDir\": \"./lib\"\n},\n\"include\": [\"lib/**/*.ts\"],\n\"exclude\": [\"cdk.out\", \"build\", \"node_modules\", \"dist\"]\n}\n```"}, {"issue_number": "554", "issue_url": "https://github.com/yamadashy/repomix/issues/554", "state": "open", "title": "Feature Request: `--no-file-summary` should also suppress `generationHeader`; should not suppress user-specified `headerText`", "content": "Repomix's output has a [summary/usage guidelines section](https://github.com/yamadashy/repomix/blob/fbad7c9ae364e52eb0cd153c451fc54f1606850a/src/core/output/outputGenerate.ts#L61), which can be suppressed with `--no-file-summary`,  but it also has a [generationHeader](https://github.com/yamadashy/repomix/blob/fbad7c9ae364e52eb0cd153c451fc54f1606850a/src/core/output/outputGenerate.ts#L60) section which cannot be suppressed.\nMy use case is for packing files other than source code (project documentation, code guidelines, etc), but the text in the unsuppressable [generationHeader](https://github.com/yamadashy/repomix/blob/fbad7c9ae364e52eb0cd153c451fc54f1606850a/src/core/output/outputStyleDecorate.ts#L50) has static references to \"the codebase\", which may mislead the reader of the file (especially since I'm lazy and just pass the directory name in argv instead of using `--include`, so the output says it's \"the entire codebase\").\nAdditionally, the `--header-text` config item adds its contents to the [summary/usage guidelines section](https://github.com/yamadashy/repomix/blob/7d1aa52697e2fe4342a21a4607254e5d84e043f8/src/core/output/outputStyleDecorate.ts#L118). When that section is suppressed with `--no-file-summary`, the custom `--header-text` is suppressed along with it.\nDesired behavior:\n- `--no-file-summary` should suppress both the summary/usage guidelines section and the `generationHeader`\n- `--header-text`, if provided, should be included in the output, regardless of the `--no-file-summary` flag"}, {"issue_number": "553", "issue_url": "https://github.com/yamadashy/repomix/issues/553", "state": "open", "title": "Feature Request: Add `--include-from-file` option", "content": "Add support for reading include patterns from a file to improve usability when dealing with many specific files or complex patterns.\n## Problem\nCurrently, when using the `--include` option with many specific file paths, the command line becomes unwieldy and difficult to manage. For example:\n```bash\nrepomix --include \"file1.ts,file2.ts,file3.ts,file4.ts,file5.ts,...\" # Very long command line\n```\nThis becomes problematic when:\n- Including many specific files (10+ files)\n- Files have long paths\n- You want to reuse the same set of files across multiple runs\n- Working with version control where command history becomes messy\n- Command line length limits are reached\n## Proposed Solution\nAdd a new option `--include-from-file` (or `--include-file`) that reads include patterns from a file, one per line.\n### Usage Example\n```bash\n# Create a file with patterns/files to include\necho \"src/components/**/*.tsx\nsrc/hooks/*.ts\nsrc/lib/utils/dateUtils.ts\nsrc/pages/dashboard/**/store.ts\" > include-patterns.txt\n# Use the file with repomix\nrepomix --include-from-file include-patterns.txt\n```\n### File Format\n- One pattern per line\n- Empty lines and lines starting with `#` are ignored (comments)\n- Both glob patterns and specific file paths are supported\n### Example include file:\n```\n# UI Components\nsrc/components/molecules/**/*.tsx\nsrc/components/ui/*.tsx\n# Utility functions\nsrc/lib/utils/common.ts\nsrc/lib/utils/dateUtils.ts\n# Dashboard hooks\nsrc/lib/hooks/dashboard/*.ts\n```\n## Benefits\n1. **Cleaner command line**: Avoid extremely long command lines\n2. **Reusability**: Save and reuse file lists across different runs\n3. **Version control friendly**: Include patterns can be committed to the repository\n4. **Better organization**: Group related files with comments\n5. **Easier maintenance**: Modify file lists without retyping entire commands\nWould love to see this feature added to make repomix even more flexible for complex project structures!"}, {"issue_number": "552", "issue_url": "https://github.com/yamadashy/repomix/issues/552", "state": "closed", "title": "Suspicious file no longer detected", "content": "Hello,\nI've been using the v0.1.43 for quite some time, as I didn't have a need to upgrade up until now.\nI downloaded the (latest) v0.2.7, and a file that contained my github token no longer got flagged, and it was included without a problem.\nthat's about all I know about the issue."}, {"issue_number": "542", "issue_url": "https://github.com/yamadashy/repomix/issues/542", "state": "closed", "title": "Add support for Node.js v24", "content": "Repomix currently tests and supports Node.js versions 16 through 23 in our CI workflows, devcontainer, and documentation. Node.js v24 is now officially released, and we’d like to ensure full compatibility so users can take advantage of the latest performance improvements and language features.\n## Proposed Changes\n* **CI**\n* Add `24.x` to the `node-version` matrix in `.github/workflows/ci.yml`.\n* Add `24` to the matrix in `.github/workflows/test-action.yml`.\n* **Devcontainer**\n* Update the base image in `.devcontainer/devcontainer.json` to one that supports Node.js 24 (e.g. `mcr.microsoft.com/devcontainers/typescript-node:1-24-bullseye`)."}, {"issue_number": "540", "issue_url": "https://github.com/yamadashy/repomix/issues/540", "state": "open", "title": "Add `--include-logs` option to include `git log --name-status` output", "content": "Repomix already supports pulling in diffs via `--include-diffs` (see PR [#533](https://github.com/yamadashy/repomix/pull/533)). This enhancement will let users bundle recent commit history—specifically the output of `git log --name-status`—into their archive with a simple flag.\n---\n**Motivation:**\n* **Change Visibility:** Shows which files changed in each commit, helping reviewers and LLMs trace code evolution.\n* **Context:** Complements low-level diffs with a concise, high-level history.\n* **Configurability:** Users can limit how many commits to include.\n---\n**Proposed Changes:**\n1. **CLI Definition**\n```ts\n// in src/cli/cliRun.ts (and types.ts)\n.option('--include-logs', 'include git log --name-status output in the archive')\n.option(\n'--include-logs-max-commits <number>',\n'maximum number of commits to include in git log (default: 100)',\n{ default: 100 }\n)\n```\n2. **Config Schema**\n```ts\ninterface RepomixConfigCli {\n// ...existing fields\noutput?: {\ngit?: {\nincludeLogs?: boolean;\nincludeLogsMaxCommits?: number;\n}\n}\n}\n```\n3. **Git Collection Logic**\n* In your git collector (e.g. `src/core/git/gitCollector.ts`), when `config.output.git.includeLogs` is `true`, run:\n```bash\ngit log --name-status -n ${config.output.git.includeLogsMaxCommits}\n```\n* Emit the result as a file (e.g. `repomix-git-log.txt`) inside the output bundle.\n4. **Documentation & Tests**\n* Update `command-line-options.md` and `README.md` to document `--include-logs` and `--include-logs-max-commits`.\n* Add tests under `tests/` to verify:\n* `repomix --include-logs` produces `repomix-git-log.txt`.\n* The `--include-logs-max-commits` override is respected.\n* Default behavior uses 100 commits.\n---"}, {"issue_number": "539", "issue_url": "https://github.com/yamadashy/repomix/issues/539", "state": "open", "title": "Remove Node.js 18 support (EOL April 30, 2025)", "content": "Node.js 18 reached end-of-life on April 30, 2025. Please update Repomix to require Node.js 20 or later:\n* Bump the devcontainer and GitHub Action workflows to use Node.js 20.\n* Remove Node.js 18 from the CI test matrix in `.github/workflows/ci.yml`.\n* Update any documentation or README references to reflect the new minimum version."}, {"issue_number": "535", "issue_url": "https://github.com/yamadashy/repomix/issues/535", "state": "open", "title": "Feature Request: Auto exclude library/weird files (node_modules, etc, .", "content": "By default, it auto excludes and shows user a toast like thing (detected blabla library files, that are excluded)  + enables some checkmarks for this.\nUser can opt in to remove that exclusion.\nit adds the related regexp patterns under the hood(not under the hood, user should see the added regexp at the input field field)"}, {"issue_number": "516", "issue_url": "https://github.com/yamadashy/repomix/issues/516", "state": "open", "title": "Add include/exclude options specific to `--compress`", "content": "Going to take a stab at making a PR for this myself, but in the meantime wanted to put an issue up to track.\nIt'd be really useful to allow for `--compress` to have its own exclusive parameters to define what files should be compressed. Right now, it is all or nothing. But oftentimes, I have a certain section of the code base that is most relevant and I want to send the whole section in the context, and then compress the rest as a bonus.\nNot entirely sure what the best CLI argument names would be, and which takes precedence. Right now there is `--include` and `--ignore`, so probably `--compress-include` and `--compress-ignore`.\nBut what happens if say, you put a file in `--ignore` and then also put it in `--compress-include`? Lots of open ended questions like that. Probably will be dictated by the structure of the code and what is easiest to implement once I dig in.\nIf anyone has thoughts please chime in."}, {"issue_number": "511", "issue_url": "https://github.com/yamadashy/repomix/issues/511", "state": "open", "title": "Add `--summary` CLI option for LLM-based code summarization", "content": "We are considering adding a `--summary` CLI option that would leverage an LLM to perform summarization of code. The approach would split target files into manageable chunks, have the LLM summarize each chunk, and then combine the results. This would provide a better understanding of large codebases where traditional `--compress` is often not sufficient.\nref:\n- https://discord.com/channels/1324644561959522314/1324644562429542425/1364257773214499047"}, {"issue_number": "508", "issue_url": "https://github.com/yamadashy/repomix/issues/508", "state": "closed", "title": "Support for GitHub Action", "content": "This feature request is for `repomix` to also provide a `GitHub Action` that projects can use on their workflows to generate LLM friendly files automatically.\nYou will need an `action.yml` file on this repo with something like this:\n```yaml\nname: \"Repomix Action\"\ndescription: \"Pack repository contents into a single file that is easy for LLMs to process\"\nauthor: \"<PERSON><PERSON> <<EMAIL>>\"\nbranding:\nicon: archive\ncolor: purple\ninputs:\ndirectories:\ndescription: \"Space-separated list of directories to process (defaults to '.')\"\nrequired: false\ndefault: \".\"\ninclude:\ndescription: \"Comma-separated glob patterns to include\"\nrequired: false\ndefault: \"\"\nignore:\ndescription: \"Comma-separated glob patterns to ignore\"\nrequired: false\ndefault: \"\"\noutput:\nrequired: false\ndefault: \"repomix.txt\"\ncompress:\ndescription: \"Set to 'false' to disable smart compression\"\nrequired: false\ndefault: \"true\"\nadditional-args:\ndescription: \"Any extra raw arguments to pass directly to the repomix CLI\"\nrequired: false\ndefault: \"\"\n\nSteps: - name: Setup Node.js\nuses: actions/setup-node@v4\nwith:\n```\nNote this yaml was generated using `OpenAI - o3`"}, {"issue_number": "506", "issue_url": "https://github.com/yamadashy/repomix/issues/506", "state": "closed", "title": "Glama listing is missing <PERSON>erfile", "content": "Your MCP server is currently listed on the [Glama MCP directory](https://glama.ai/mcp/servers/yamadashy/repomix), but it is not available for others to use because it does not have a Dockerfile.\nIt takes only a few minutes to fix this:\n1. Go to your server's listing: [yamadashy/repomix](https://glama.ai/mcp/servers/yamadashy/repomix)\n2. Click \"Claim\" to verify ownership.\n3. Once claimed, navigate to the [admin `Dockerfile` page](https://glama.ai/mcp/servers/yamadashy/repomix/admin/dockerfile) and add a `Dockerfile`.\n4. Ensure your server passes all the [checks](https://glama.ai/mcp/servers/yamadashy/repomix/score).\nOnce completed, your server will be available for anyone to use.\nFor context, there are about 60k people using Glama every month and I'd love to see more people using your server."}, {"issue_number": "505", "issue_url": "https://github.com/yamadashy/repomix/issues/505", "state": "closed", "title": "Add support for MCP tool annotations", "content": "The Model Context Protocol 2025-03-26 specification added support for tool annotations, which provide additional metadata about a tool's behavior. Once the MCP SDK is updated to support annotations, we should enhance our MCP tools implementation to include appropriate annotations.\nExample of future implementation in readRepomixOutputTool.ts:\n```typescript\nmcpServer.tool(\n'read_repomix_output',\n{\noutputId: z.string().describe('ID of the Repomix output file to read'),\n},\nasync ({ outputId }) => { /* implementation */ },\n{\ntitle: 'Read Repomix Output File',  // User-friendly title for UI display\nreadOnlyHint: true,                 // Indicates this tool does not modify its environment\nopenWorldHint: false                // Indicates this tool does not interact with external systems\n}\n);\n```\n### Tasks\n- Monitor updates to the MCP SDK (@modelcontextprotocol/sdk) for annotation support\n- Add appropriate annotations to each of our tool implementations:\n- read_repomix_output\n- file_system_read_file\n- file_system_read_directory\n- pack_codebase\n- pack_remote_repository\n### References\n- MCP Tool annotations spec: https://modelcontextprotocol.io/docs/concepts/tools/#tool-annotations\n- MCP SDK issue tracking annotation support: https://github.com/modelcontextprotocol/typescript-sdk/issues/276"}, {"issue_number": "479", "issue_url": "https://github.com/yamadashy/repomix/issues/479", "state": "open", "title": "Export core as a package", "content": "Hey,\nLove the repo! I'd like to propose exporting core as a package that i can use in my nodejs app or export the core functions as a part of the repomix package.\nThere's plenty of functions in the core folder that we can use during pre processing before running the cli.\nHappy to open a PR on a direction you agree on 🤞🏻"}, {"issue_number": "478", "issue_url": "https://github.com/yamadashy/repomix/issues/478", "state": "open", "title": "Idea: output only related files command", "content": "Problem:\ncode bases tend to grow enormously, especially frontend code.\nAnd sometimes you don't need the whole code base to send to LLMS\n200K context and even 1M context window aren't enough\nSolution:\nhave a repomix command that tells the repomix to read current directory files and imported files also and bundle them in one codebase\nI am expecting to have `server/actions/users.ts`\nThis file also imports several functions from `apps/utils/users.ts`, `apps/db/queries/users.ts`\nThe final output will have the code from actions and related files, from utils and associated files from queries\nAlso have the feature to set a list of files or directories to be always included, no matter what, like ai_instructions.md\nI know that this behaviour can be reached by tweaking the include and ignore in the repomix config. But it takes a lot of time to do it each time"}, {"issue_number": "477", "issue_url": "https://github.com/yamadashy/repomix/issues/477", "state": "open", "title": "Integrate MCP `roots` and dynamic tool discovery", "content": "It would be really great if repomix could leverage MCP features to create a more context-aware and adaptive experience. VS Code supports both of them and hopefully other MCP clients follow up as this can provide a much smoother tooling experience.\n- **MCP Roots Support:** https://modelcontextprotocol.io/docs/concepts/roots#why-use-roots%3F\n- Allow repomix to accept multiple roots (like local paths or URLs) so it can automatically determine which part of a workspace or repository to pack.\n- Automatically configure tool parameters based on the current VS Code workspace.\n- **Dynamic Tool Discovery:**  https://modelcontextprotocol.io/docs/concepts/tools#tool-discovery-and-updates\n- Listen for `list-change` events to update available repo packs and tool definitions in real time.\n- Re-scan the workspace when changes are detected, ensuring operations always match the current project state.\nLooking forward to your thoughts and suggestions on how to best implement these ideas!"}, {"issue_number": "476", "issue_url": "https://github.com/yamadashy/repomix/issues/476", "state": "open", "title": "Token count mismatch with Google AI Studio", "content": "Thanks for creating and maintaining repomix.\nWith o200k_base, repomix reports \"969,990 tokens\", but AI Studio claims it's \"1,156,615\". Is this a known issue with AI Studio? If not, is there a way to configure repomix to be compatible with it?\nBtw, cl100k_base and o200k_base returns same token counts, is this expected?"}, {"issue_number": "475", "issue_url": "https://github.com/yamadashy/repomix/issues/475", "state": "open", "title": "[Feature] Metadata-Only Output", "content": "An option (maybe --metadata-only) to output just the summary, directory structure, and file metrics without the content, useful for quick analysis."}, {"issue_number": "474", "issue_url": "https://github.com/yamadashy/repomix/issues/474", "state": "closed", "title": "Badge in README shows CI-FAILING", "content": "SCREENSHOT-\n<img width=\"1467\" alt=\"Image\" src=\"https://github.com/user-attachments/assets/8d193e4a-1482-4ee9-a2cc-1b5fbe12d670\" />"}, {"issue_number": "473", "issue_url": "https://github.com/yamadashy/repomix/issues/473", "state": "closed", "title": "BACK TO TOP- not working", "content": "SCREEN RECORDING-\nhttps://github.com/user-attachments/assets/5f941f7d-b609-4c6b-8259-86b73c4448c6"}, {"issue_number": "472", "issue_url": "https://github.com/yamadashy/repomix/issues/472", "state": "open", "title": "MCP Feature Request: generate output in project directory", "content": "I've somehow managed to chain different repomix mcp tools together, but anytime i try to access it, i get this Cursor error: `Content type \"resource\" not supported`\nwould be awesome if pack_codebase could have a `local` parameter so it woud generate the repomix output in the project directory\n@yamadashy wdyt?"}, {"issue_number": "468", "issue_url": "https://github.com/yamadashy/repomix/issues/468", "state": "open", "title": "Typescript library in order to use repomix not only through CLI", "content": "It would be awesome if a ts library was available in order to use repomix in other open-source projects (such as https://github.com/filopedraz/kosuke-core)."}, {"issue_number": "467", "issue_url": "https://github.com/yamadashy/repomix/issues/467", "state": "open", "title": "Support Brace Expansion in CLI `--include` `--ignore` Option", "content": "The current CLI `--include` `--ignore` option does not support brace expansion patterns like `{__tests__,theme}`. This requires users to specify each pattern separately, which is less efficient. Adding support for brace expansion would allow more concise and flexible pattern definitions."}, {"issue_number": "463", "issue_url": "https://github.com/yamadashy/repomix/issues/463", "state": "open", "title": "Security check failure when running Repomix CLI locally", "content": "A user on Discord reported a failure when attempting to run `repomix` locally via the CLI.\n**Error message:**\n```\nError checking security on packages/@n8n/benchmark/scripts/mock-api/mappings/mockApiData.json: Error: Unknown type:undefined\n```"}, {"issue_number": "454", "issue_url": "https://github.com/yamadashy/repomix/issues/454", "state": "closed", "title": ".js files showing as empty in output file", "content": "I have a Svelte website I'm developing and when I use repomix my *.js files are shown as empty in the output file. It shows that the files exist, but there is no content.\nFor example:\n## File: postcss.conifg.js\n```\n```\n\nActual: ## File: postcss.conifg.js\n```\nmodule.exports = {\nplugins: {\ntailwindcss: {},\nautoprefixer: {},\n},\n};\n```"}, {"issue_number": "450", "issue_url": "https://github.com/yamadashy/repomix/issues/450", "state": "open", "title": "CLI Usability Issues: Pattern Matching and Command Suggestions", "content": "The CLI has two significant usability issues that affect user experience:\n### 1. Pattern Matching Problems\nThe file exclusion functionality doesn't work intuitively with common glob patterns:\n- Simple patterns like `--ignore \"*.ts\"` don't exclude files as expected\n- Only more specific patterns like `--ignore \"**/*.ts\"` actually work\n- Users familiar with other CLI tools expect simpler patterns to work properly\n### 2. Command Suggestion Limitations\nThe current suggestion system (inherited from Commander.js) has significant limitations:\n- Only suggests options based on string similarity (edit distance)\n- Lacks semantic understanding of command alternatives\n- Users typing semantically similar options (like `--reject`, `--omit`, `--skip`) don't get suggestions for `--ignore` or `--exclude`\n- Frustrates users with different mental models or coming from other tools\n\nSteps: ### Pattern Matching Issue:\n1. Run `npm run repomix -- --ignore \"*.ts\" --verbose`\n2. Observe that TypeScript files are still processed despite the exclusion pattern\n### Command Suggestion Issue:\n1. Run `npm run repomix -- --reject \"*.ts\"`\n2. Observe that the error message suggests only string-similar options rather than semantically similar ones like `--ignore` or `--exclude`\n\nExpected: ### Pattern Matching:\n- Simple patterns like `*.ts` should work properly to exclude files\n- Behavior should align with common glob pattern implementations in other tools\n### Command Suggestions:\n- When users type synonymous commands (like `--reject`, `--omit`, `--skip`), they should receive suggestions for the correct options (`--ignore`, `--exclude`)\n- Error messages should be more helpful for users with different mental models\n## Current Behavior\n### Pattern Matching:\n- Only specific formats like `**/*.ts` work properly\n- Simple patterns fail silently, processing files that should be excluded\n### Command Suggestions:\n- Only string-similar suggestions are provided\n- No semantic understanding of command alternatives\n- Unhelpful error messages for users trying semantically equivalent commands"}, {"issue_number": "449", "issue_url": "https://github.com/yamadashy/repomix/issues/449", "state": "closed", "title": "did you guys do any special function or just record directory and fileContent?i am a little confused", "content": "did you guys do any special function or just record directory and fileContent?i am a little confused,cause i can not found any other useful info"}, {"issue_number": "445", "issue_url": "https://github.com/yamadashy/repomix/issues/445", "state": "open", "title": "Get txt output instead of xml", "content": "The xml format adds token count with the tags.\nAlso the xml format does not open properly in two common web browsers I tested.\nAnd an option to generate the .txt would be good.\nThe XML format is much harder to read and navigate in plaintext when the user's desire is to trim down the repomix output without having to duplicate and delete items in their codebase before running repomix.\nIt would also be well if there was 'npx repomix help' option so the user can have these commands presented to them (generate txt with 'npx repomix txt' etcetera for any other help info.)\n-T"}, {"issue_number": "444", "issue_url": "https://github.com/yamadashy/repomix/issues/444", "state": "closed", "title": "Feature Request: make --exclude a synonym of --ignore", "content": "The tool uses `--include` to include files and folders for processing. The antonym of `--include` is `--exclude`, but that is not recognised. The following is output instead:\n```\nerror: unknown option '--exclude'\n(Did you mean --include?)\n```\nThis is obviously pretty funny, but nevertheless it would be beginner-friendly to have `--exclude` as a synonym for `--ignore`.\nIf there is a reason why this shouldn't be done, then please change the error message to suggest `--ignore` instead of `--include`."}, {"issue_number": "443", "issue_url": "https://github.com/yamadashy/repomix/issues/443", "state": "closed", "title": "Feature Request: make ignore globbing patterns more intuitive for directories", "content": "Hi,\nLet's say I have the following repomix command, which both includes and excludes files:\n```\nrepomix\n--include \"MyProject.Common/**/*,MyProject.Models/**/*,MyProject.UI/**/*\"\n--ignore \"**/bin,**/obj,**/GeneratedFiles,**/Assets\"\n```\nThat is the correct syntax. However, if I amend the exclusion patterns to include trailing slashes, it does not work:\n```\nrepomix\n--include \"MyProject.Common/**/*,MyProject.Models/**/*,MyProject.UI/**/*\"\n--ignore \"**/bin/,**/obj/,**/GeneratedFiles/,**/Assets/\"\n```\nThe result of running this is that all files are still included in the folders we've marked to ignore. It is reasonable to assume that `**/Assets` and `**/Assets/` would mark all `Assets` folders under the include paths for exclusion.\nSimilarly, the following command will not work either:\n```\nrepomix\n--include \"MyProject.Common/**/*,MyProject.Models/**/*,MyProject.UI/**/*\"\n--ignore \"**/bin/**/*,**/obj/**/*,**/GeneratedFiles/**/*,**/Assets/**/*\"\n```\nThis uses the same globbing pattern to attempt to exclude files as it does to include files, but no files are excluded.\nThis caught me out today when using the tool for the first time.\n```\n**/folder\n**/folder/\n**/folder/**/*\n```\nIncidentally, the \"**/name\" syntax for exclusion patterns may be problematic, as it will match both folders and files with that name. It's rare that you have files lacking extensions, of course, but it may be worth noting.\nThank you for your useful tool!"}, {"issue_number": "440", "issue_url": "https://github.com/yamadashy/repomix/issues/440", "state": "open", "title": "Bug: MCP Server Mode (mcp serve) Outputs Non-JSON Text to Stdout, Breaking Protocol Communication", "content": "When running `repomix` in MCP server mode (`repomix mcp serve`), the process outputs various non-JSON text elements (startup banner, interactive prompts, spinners, logs) to standard output (`stdout`). This contaminates the JSON-RPC communication channel expected by the Model Context Protocol, causing MCP clients like <PERSON> to fail parsing the output, resulting in timeouts and disconnections.\n\nSteps: 1.  Configure <PERSON>'s MCP settings file (`claude_desktop_config.json` or similar) to run the Repomix MCP server using `npx`:\n```json\n{\n\"mcpServers\": {\n\"file-operations-server\": {\n\"command\": \"node\",\n\"args\": [\n\"/path/to/mcp-file-operations-server/build/index.js\"\n]\n},\n\"repomix\": {\n\"command\": \"npx\",\n\"args\": [\n// \"-y\", // Note: Including -y caused an \"unknown option\" error from repomix itself\n\"repomix\",\n\"mcp\",\n\"serve\"\n// \"--quiet\" // Adding --quiet here did not resolve the issue\n]\n}\n}\n}\n```\n2.  Start <PERSON>, ensuring it launches the configured MCP servers.\n3.  Attempt to use a Repomix tool via <PERSON>, for example, by asking it to analyze a local project directory (this should trigger the `pack_codebase` tool).\n4.  Observe the MCP server logs provided by Claude Desktop (usually accessible via Developer Tools or a log file).\n\nExpected: The `repomix mcp serve` process should strictly adhere to the Model Context Protocol's communication standard:\n*   Only valid JSON-RPC 2.0 messages should be printed to `stdout`.\n*   No startup banners, interactive prompts (like the migration check), spinners, ANSI escape codes, or general log messages should be output to `stdout`. Output intended for debugging or user visibility during normal CLI operation should be suppressed or redirected to `stderr` when in MCP mode.\n*   The MCP client (Claude Desktop) should maintain a stable connection and successfully parse responses from the `repomix` server process.\n\nActual: The `repomix` process outputs non-JSON text to `stdout`, causing the MCP client's JSON parser to fail. This leads to request timeouts and the server connection being dropped by the client.\nLog snippets demonstrating the invalid output on `stdout`:\n*   **Startup Banner:**\n```\n[error] [repomix] Unexpected token '�', \"📦 Repomix v0.3.0\" is not valid JSON\n```\n*   **Migration Check Prompt (from `@clack/prompts` via `src/cli/actions/migrationAction.ts`):**\n```\n[error] [repomix] Unexpected token '◆', \"◆  Found R\"... is not valid JSON\n[error] [repomix] Unexpected token '│', \"│  ● Yes / ○ No\" is not valid JSON\n[error] [repomix] Unexpected token '│', \"│  No\" is not valid JSON\n[error] [repomix] Unexpected token 'M', \"Migration cancelled.\" is not valid JSON\n```\n*   **Spinner/ANSI Codes (from `log-update` via `src/cli/cliSpinner.ts`):**\n```\n[error] [repomix] Unexpected token ' ', \" [?25l│\" is not valid JSON\n[error] [repomix] Unexpected token ' ', \" [999D [4A\"... is not valid JSON\n```\n*   **Standard Logger Output (from `src/shared/logger.ts`):**\n```\n[error] [repomix] Unexpected token 'N', \"No custom \"... is not valid JSON\n[error] [repomix] Unexpected token 'N', \"Need help?\" is not valid JSON\n[error] [repomix] Unexpected token '•', \"• Join our\"... is not valid JSON\n```\n*   **Error Handling Output:**\n```\n[error] [repomix] Unexpected token '✖', \"✖ Error du\"... is not valid JSON\n```\n*   **(Separate Issue Observed):** When using `args: [\"-y\", \"repomix\", \"mcp\", \"serve\"]`, the process immediately crashes with `error: unknown option '-y'`, indicating `-y` is incorrectly passed to `repomix` instead of being consumed by `npx`. Removing `-y` allows the server to start but reveals the stdout pollution issue.\n**Analysis:**\nThe core issue is that the MCP server mode does not adequately suppress or redirect output generated by various components:"}, {"issue_number": "438", "issue_url": "https://github.com/yamadashy/repomix/issues/438", "state": "open", "title": "Feature Request: Add a \"force include\" configuration/CLI option to override default and custom exclusion rules", "content": "I frequently need to quickly bundle just a few specific files from my repository using `repomix.config.json`—particularly when I'm repeatedly adjusting the `include` field. However, I'm running into an issue: I'd like to include a file located in `node_modules/`, but Repomix automatically excludes that folder by default. Even if I specify the exact path to the file inside `node_modules/`, it’s not included because of the default or custom exclusion rules.\nIt would be incredibly helpful to have a **\"force include\"** option (either in the configuration file or as a CLI flag) that explicitly overrides all exclusion rules—both the default `node_modules` exclusion and any patterns set by `.gitignore`, `.repomixignore`, or other custom rules. This way, if I really need to pull in a specific file from `node_modules/` (or any other traditionally excluded directory), I can do so without having to disable or modify all the exclude patterns.\n---\n**Use Case**\n- Quickly bundling only a few files for AI analysis without including the entire node_modules directory.\n- Ensuring that even if `node_modules` or similar directories are globally excluded, certain critical files can still be selectively included.\n- Reducing back-and-forth toggling of ignore patterns in `repomix.config.json`.\n---\n**Proposed Solution**\n1. **Configuration File Approach**:\n- Add a new `forceInclude` array (similar to `include` and `ignore`) in `repomix.config.json` that overrides all exclusions.\n- Example:\n```json\n{\n\"forceInclude\": [\n\"node_modules/some-package/dist/specific-file.js\"\n]\n// ... other config properties\n}\n```\n2. **CLI Flag Approach**:\n- Provide a `--force-include` option that can be used once or multiple times to specify paths.\n- Example:\n```bash\nrepomix --force-include \"node_modules/some-package/dist/specific-file.js\"\n```\n3. **Behavior**:\n- Any pattern or path listed under `forceInclude` would be packed regardless of any exclusion rules set by `.gitignore`, `.repomixignore`, default ignore patterns, or the `ignore.customPatterns` configuration.\n---\n**Benefits**\n- Simplifies the process when a single or handful of files from an otherwise excluded directory need to be included.\n- Makes the configuration more flexible by letting users override strict defaults without disabling them entirely.\n- Reduces repetitive manual toggling of ignore patterns.\n---\n**Additional Notes / Considerations**\n- This feature might also be useful for teams that store partial dependencies or patched versions inside `node_modules` or similarly excluded folders.\n- It would be consistent if any \"force include\" entries also appear in the directory structure output (even when excluded directories are generally omitted).\nThank you for considering this request! I believe this feature would significantly improve workflows where only a few files in traditionally excluded directories need to be included. Please let me know if I can provide more details or clarifications."}, {"issue_number": "437", "issue_url": "https://github.com/yamadashy/repomix/issues/437", "state": "open", "title": "Feature Request: Allow setting a maximum size (in MB) for files that will be packed", "content": "Sometimes repos contain unexpected massive files. Running stat before opening files and filtering out any that are too large helps with having a smooth experience.\nA config variable like `skipFilesLargerThan` that takes a string like \"500kB\" would be my suggestion but I really have no strong opinion on the implementation"}, {"issue_number": "433", "issue_url": "https://github.com/yamadashy/repomix/issues/433", "state": "open", "title": "Impossible to pack a remote with specific paths", "content": "> npx repomix --remote https://github.com/aws/aws-sdk-js-v3/tree/main/clients/client-ses\nThrows\n> ✖ Failed to clone repository: Command failed: git -C /var/folders/89/7j7fc2m16lg234rx3pz_2jnc0000gn/T/repomix-ev84t3A9WgSA fetch --depth 1 origin main/clients/client-ses\nfatal: couldn't find remote ref main/clients/client-ses\nUsing Repomix v0.3.0\n[As per doc:](https://repomix.com/#power-user-guide)\n>Using full URL (supports branches and specific paths)\n>npx repomix --remote https://github.com/yamadashy/repomix\n>npx repomix --remote https://github.com/yamadashy/repomix/tree/main\n<img width=\"576\" alt=\"Image\" src=\"https://github.com/user-attachments/assets/121db3a0-36f0-47e0-aaab-fdabf3c53377\" />"}, {"issue_number": "431", "issue_url": "https://github.com/yamadashy/repomix/issues/431", "state": "open", "title": "Export loadFileConfig so that when repomix is used as a js library the users can choose to load a repomix.config.json", "content": "This would allow library users the option to respect the repomix behaviours when using repomix programatically"}, {"issue_number": "430", "issue_url": "https://github.com/yamadashy/repomix/issues/430", "state": "closed", "title": "Feature Request: support solidity comment removal", "content": "Repomix supports removing comments from a range of languages but solidity isn't a support language currently\nI found the query for solidity's tree sitter definitions\nhttps://github.com/Aider-AI/aider/blob/main/aider/queries/tree-sitter-language-pack/solidity-tags.scm"}, {"issue_number": "426", "issue_url": "https://github.com/yamadashy/repomix/issues/426", "state": "closed", "title": "Pack Codebase response resource item doesn't include content", "content": "When using the `pack_codebase` tool, the server returns items of the format:\n```\n{\ntype: \"resource\",\nresource: {\nuri: \"file:///tmp/repomix/mcp-outputs/pqmRzP/repomix-output.xml\",\nmimeType: \"application/xml\",\ntext: \"Repomix output file\",\n},\n}\n```\nAccording to the [MCP docs](https://modelcontextprotocol.io/docs/concepts/resources) if I'm understanding correctly, this call should include the actual contents of the file and the URI would be for future reference or for subscribing to resource changes.\nThis means building to this MCP server in a generic way (fetching file URL on tool response) would not match expected handling of tool responses for other MCP endpoints.\nFor context I think repomix would be a fantastic integration into [the Continue IDE extension](https://github.com/continuedev/continue). Most of the repomix tools work but the resource ones don't.\n# Solution\nInclude the contents of the repomix output file contents under `text` in tool output"}, {"issue_number": "424", "issue_url": "https://github.com/yamadashy/repomix/issues/424", "state": "open", "title": "Feature Request: Automatic Chunking (a.k.a. “YOLO Divide”) for Oversized Codebases", "content": "It would be immensely helpful if Repomix could automatically split large codebases into multiple output files once a certain token threshold (e.g. 128k) is reached. This “YOLO divide” approach would allow users to simply specify a cutoff and let Repomix handle the heavy lifting. The resulting “chunked” files could then be fed sequentially into AI tools that have strict context size limits.\n---\n### Use Case\nI often want to provide my entire codebase to a large language model for analysis or troubleshooting, but the codebase exceeds the 128k token limit by a wide margin—sometimes up to ten times more. Manually chopping the output into smaller pieces is tedious. An automated chunking solution would streamline this process significantly.\n### Desired Behavior\n1. **Config Option**: A single setting, for example:\n```json\n{\n\"output\": {\n\"yoloDivideIntoChunksIfExceedToken\": 128000\n}\n}\n```\n2. **Automatic Chunk Generation**:\n- If the total token count surpasses the specified threshold, Repomix splits the output into separate files (e.g., `repomix-output-chunk-1.xml`, `repomix-output-chunk-2.xml`, etc.).\n- The exact method of splitting doesn’t matter much to me. Any reasonable approach (by file boundaries, lines, or token count) would be sufficient, as long as each chunk stays under the limit.\n3. **Outcome**:\n- Users can quickly copy and paste each chunk into their AI tool of choice (e.g., O1 Pro, ChatGPT, Claude, etc.) without worrying about hitting token limits.\n### Why This Matters\n- **Efficiency**: Eliminates manual slicing of the codebase output.\n- **Ease of Use**: Allows large repositories to be handled in one go, rather than requiring multiple runs or external scripts.\n- **Flexibility**: Users who just want “the whole codebase in the AI” can get a straightforward multi-file output to paste into their model in chunks.\n---\nThank you for considering this request! An automatic chunking feature would be a game-changer for those of us dealing with large codebases and strict LLM context limits."}, {"issue_number": "417", "issue_url": "https://github.com/yamadashy/repomix/issues/417", "state": "open", "title": "Improve ignore pattern parsing by trimming whitespace after `split(',')`", "content": "Currently, Repomix's ignore pattern parsing does not trim whitespace after splitting on `,`. This can lead to unexpected behavior, where patterns contain unintended leading spaces.\nI plan to improve the parser to trim whitespace after split(',') so the first pattern style works as expected. There should be very few cases where leading or trailing spaces are actually needed in patterns, and in those rare cases, \\s can be used.\nrelated\n- https://github.com/yamadashy/repomix/issues/397"}, {"issue_number": "416", "issue_url": "https://github.com/yamadashy/repomix/issues/416", "state": "open", "title": "Installation via nix", "content": "I love installing programs using nix rather than `npm install`. I create a flake, nix package and home-manager module which allows you to install this program using nix!\nhttps://github.com/m4dc4p/repomix-nix"}, {"issue_number": "412", "issue_url": "https://github.com/yamadashy/repomix/issues/412", "state": "open", "title": "Add support for analyzing npm packages directly", "content": "## Add support for analyzing npm packages directly\n### Feature Request\nAdd the ability to analyze npm packages directly using  their package name similar to the existing GitHub repository support.\n### Motivation\nMany developers use npm and know packages by name rather than the corresponding github repo. This would streamline the workflow for:\n- Quick analysis of dependencies\n- Evaluating potential packages before installation\n- Understanding package internals for debugging or learning purposes\n### Proposed Implementation\n1. **Primary Method**: Extract GitHub repository information from package.json\n```bash\nrepomix --npm-package express\n```\n- Parse the package's metadata to find the GitHub repository URL\n- Use existing GitHub repository analysis functionality\n2. **Fallback Method**: Direct package analysis from npm tarball\n```bash\nrepomix --npm-package express --use-tarball\n```\n- Download and extract the package tarball\n- Analyze contents directly when GitHub repository isn't available\n### Technical Details\n1. Use the npm registry API to fetch package metadata:\n```\nhttps://registry.npmjs.org/[package-name]\n```\n2. Extract repository information from:\n- `repository` field in package.json\n- `homepage` field (if it's a GitHub URL)\n- `bugs` field (if it points to GitHub issues)\n3. For tarball fallback:\n- Use the `dist.tarball` URL from the package metadata\n- Extract and analyze the contents locally\n### Example Usage\n```bash\n# Using package name\nrepomix --npm-package express"}, {"issue_number": "408", "issue_url": "https://github.com/yamadashy/repomix/issues/408", "state": "open", "title": "Repomix Run On Open Files doesn't work", "content": "Im using VS Code in windows clicking \"Repomix Run On Open Files\" command but output file is empty (however files are opened in VS Code tabs), please help"}, {"issue_number": "400", "issue_url": "https://github.com/yamadashy/repomix/issues/400", "state": "open", "title": "Bug: Negation patterns with `!` in ignore patterns don't work properly", "content": "I'm experiencing an issue where negation patterns using the ! prefix in `ignore.customPatterns` are not properly excluding files from being ignored.\nIt appears this is related to limitations in fast-glob's handling of negative ignore patterns:\n- https://github.com/mrmlnc/fast-glob/issues/86#issuecomment-*********\nrelated:\n- https://github.com/yamadashy/repomix/pull/396"}, {"issue_number": "397", "issue_url": "https://github.com/yamadashy/repomix/issues/397", "state": "closed", "title": "question / bug for file-globs", "content": "I am trying to get a simple fileglob to work.\nIt does not work on the command line, it does work in repomix.config.json\nCLI:\nI have tried both\n```\n--ignore \"**/__tests__/**, **/theme/**\"\nand\n--ignore \"**/{__tests__, theme}/**\"\n```\n(the second syntax is from the fastglob repo linked in the readme as an example)\n*Does not work*\nrepomix.config:\n```\n\"customPatterns\": [\"**/__tests__/**\", \"**/theme/**\"]\n```\n*Does work*\nWhat am I missing?"}, {"issue_number": "392", "issue_url": "https://github.com/yamadashy/repomix/issues/392", "state": "open", "title": "--remote from private repo via SSH", "content": "Hello,\nis it possible to clone the repo via ssh? i had a quick glance at the docs and code and couldn't find the feature!\nif it isn't already implemented, are PRs welcome?\nall the best"}, {"issue_number": "388", "issue_url": "https://github.com/yamadashy/repomix/issues/388", "state": "open", "title": "How can I remove Copyright messages in the output?", "content": "As they are redundant and useless."}, {"issue_number": "385", "issue_url": "https://github.com/yamadashy/repomix/issues/385", "state": "open", "title": "`\"fileSummary\": false` doesn't work", "content": "Despite setting `\"fileSummary\": false,` in `repomix.config.json` I'm still getting the file summary above the `# Directory Structure`.\nThe rest of the config seems to be working:\n```\n{\n\"output\": {\n\"filePath\": \"repomix-output.md\",\n\"style\": \"markdown\",\n\"parsableStyle\": false,\n\"directoryStructure\": true,\n\"removeComments\": false,\n\"removeEmptyLines\": false,\n\"compress\": false,\n\"topFilesLength\": 5,\n\"showLineNumbers\": false,\n\"copyToClipboard\": false\n},\n\"include\": [],\n\"ignore\": {\n\"useGitignore\": true,\n\"useDefaultPatterns\": true,\n\"customPatterns\": [\n\"**/*.{md,svg}\",\n\"eslint.config.js\",\n\"package.json\",\n\"repomix.config.json\",\n\"CODEOWNERS\",\n\"**/.*\"\n]\n},\n\"security\": {\n\"enableSecurityCheck\": true\n}\n}\n```"}, {"issue_number": "379", "issue_url": "https://github.com/yamadashy/repomix/issues/379", "state": "open", "title": "window11中，cursor中无法使用Repomix: Run on open Files的功能", "content": "window11中，cursor中无法使用Repomix: Run on open Files的功能"}, {"issue_number": "378", "issue_url": "https://github.com/yamadashy/repomix/issues/378", "state": "open", "title": "Targeting remote while specifying nested output location fails", "content": "Hello, it seems like using both --remote and --output fails at the following line of the `pack` function in the `packager` module.\n```ts\nyield deps.writeOutputToDisk(output, config);\n```\nconfig.cwd contains the full temp directory path instead of the actual working directory. If I pass an output location that is nested, it fails to find the file, presumably due to the parent directory not existing. That might need to be reworked to where the temp file output location is not dependent on the configured output location.\n`--output docs/nested/filename.xml` fails, but `--output docs/filename.xml` is fine."}, {"issue_number": "377", "issue_url": "https://github.com/yamadashy/repomix/issues/377", "state": "open", "title": "[Feature Request]: Upload folders on repomix website", "content": "@yamadashy I implemented the zip upload feature. I would also like to add the folder upload feature. My approach would be:\n- let the user upload the folder\n- we will zip it in client side\n- then pass it to the server side as we do it for the existing zip flow.\n- i wont change the ui. just in the file upload region, one can also upload a folder.\nWhat do you think?"}, {"issue_number": "375", "issue_url": "https://github.com/yamadashy/repomix/issues/375", "state": "open", "title": "Use the contents of .git/info/exclude when useGitignore is set to true.", "content": "I leave useGitignore set to the default value of true. When running repomix, the repomix-output.txt still contains files that git ignores if they are listed in .git/info/exclude rather than .gitignore."}, {"issue_number": "372", "issue_url": "https://github.com/yamadashy/repomix/issues/372", "state": "closed", "title": "Run without write access to source directory", "content": "New user to repomix, and was testing with the docker image. It appears the application cannot run without write access to the source directory, even if the output file is specified to go elsewhere.\nFor example:\n```\n# mkdir /tmp/source\n# echo hello > /tmp/source/world\n# mkdir /tmp/out\n# podman run -v /tmp/source:/app:ro -v /tmp/out:/out -t --rm ghcr.io/yamadashy/repomix -o /out/repomix\n📦 Repomix v0.2.29\nNo custom config found at repomix.config.json or global config at /root/.config/repomix/repomix.config.json.\nYou can add a config file for additional settings. Please check https://github.com/yamadashy/repomix for more information.\n✖ Error during packing\nUnexpected error: Cannot access directory /app: undefined\nFor more help, please visit: https://github.com/yamadashy/repomix/issues\n```\nIf you take the `:ro` flag off the volume mount it works, even though output has been redirected somewhere else. I'm not sure if it's trying to use the source directory as a temporary working directory or what. But IMHO it shouldn't be putting anything in it. Any temporary files should either go in the directory of the output file, `$XDG_RUNTIME_DIR`, or `/tmp/`.\nMight also help if the error message would a little clearer. It took me a minute to figure out what the issue was, when the directory was there, readable, and not being used for output."}, {"issue_number": "368", "issue_url": "https://github.com/yamadashy/repomix/issues/368", "state": "open", "title": "Support absolute path in \"include\"", "content": "Sometimes I need to reference files or directory outside current project, if I do this:\n```js\n{\n\"include\": [\n\"/Users/<USER>/Work/company/company-components/src/Inputs/Checkbox/\"\n]\n}\n```\nit outputs error:\n```\nError filtering files: path should be a `path.relative()`d string, but got\n```"}, {"issue_number": "361", "issue_url": "https://github.com/yamadashy/repomix/issues/361", "state": "closed", "title": "RFC: Change Default Output Style to XML", "content": "## Proposal\nChange the default output style from `plain` to `xml` in version 0.3.0.\n## Why?\n- XML output provides better structure for AI processing\n- I've been using XML as maintainer and found it more reliable\n- Community feedback supports this change (see [Discord discussion](https://discord.com/channels/1324644561959522314/1325020123655835709/1340540106875863190))\n## Impact\nThis is a breaking change that will require users who want plain text to explicitly specify:\n```bash\nrepomix --style plain\n```\nor in config:\n```json\n{\n\"output\": {\n\"style\": \"plain\"\n}\n}\n```"}, {"issue_number": "359", "issue_url": "https://github.com/yamadashy/repomix/issues/359", "state": "open", "title": "[Compression] Preserve type declarations (critical for LLM context) while allowing function compression", "content": "Type declarations are structural anchors for LLMs to understand code relationships, whereas function bodies can be safely compressed."}, {"issue_number": "356", "issue_url": "https://github.com/yamadashy/repomix/issues/356", "state": "open", "title": "Idea: Bring fourth most frequently modified files", "content": "I saw another [tool](https://github.com/foresturquhart/grimoire) on GitHub that someone posted on Hacker News. I was reading what it said, and one sentence caught my eye.\n`It's a Go tool that converts directories into structured Markdown while smartly prioritizing frequently modified files to help LLMs focus on what's most important.`\nIf not already implemented in RepoMix, this could be a cool addition."}, {"issue_number": "355", "issue_url": "https://github.com/yamadashy/repomix/issues/355", "state": "closed", "title": "Website fails for https://github.com/evanw/esbuild", "content": ""}, {"issue_number": "352", "issue_url": "https://github.com/yamadashy/repomix/issues/352", "state": "open", "title": "请问支持Java，Kotlin吗？任意编程语言都可以？", "content": "请问支持Java，Kotlin吗？任意编程语言都可以？"}, {"issue_number": "350", "issue_url": "https://github.com/yamadashy/repomix/issues/350", "state": "open", "title": "Migrate Repomix to browser-side execution", "content": "Following @huy-trn's suggestion in the [previous discussion](https://github.com/yamadashy/repomix/issues/349#issuecomment-2651443446), let's explore the possibility of migrating Repomix to browser-side execution. This would enable static site hosting and potentially reduce server costs.\nThe main challenge would be the git clone functionality. I've been thinking that [isomorphic-git](https://github.com/isomorphic-git/isomorphic-git) might be a solution, along with addressing any library incompatibilities."}, {"issue_number": "349", "issue_url": "https://github.com/yamadashy/repomix/issues/349", "state": "open", "title": "Idea: Make a ChatGPT GPT that interacts with repomix", "content": "I am the alt of @SpyC0der77 . Later today I will create a GPT  that has an action to integrate it with RepoMix. You will be able to enter a github URL and it will request repomix and give it the data of the repo."}, {"issue_number": "346", "issue_url": "https://github.com/yamadashy/repomix/issues/346", "state": "closed", "title": "Allow Trailing Commas in `repomix.config.json`", "content": "When working with the `\"includes\"` key, I often comment out file paths to quickly test or calculate final token counts. However, I have to manually remove the trailing comma every time. Since comments are already supported in `repomix.config.json`, it would be very helpful if trailing commas were supported as well.\nFor reference, you can see where this is handled in our code:\nhttps://github.com/yamadashy/repomix/blob/b55c59d0dae9ba0c5bc534318c2a414d848f5ded/src/config/configLoad.ts#L70\nAdditionally, the [json5](https://www.npmjs.com/package/json5) library, which we could consider as an alternative, supports trailing commas."}, {"issue_number": "341", "issue_url": "https://github.com/yamadashy/repomix/issues/341", "state": "closed", "title": "Remove generation timestamp to reduce AI API costs", "content": "The timestamp \"Generated by Repomix on: [date]\" at the beginning of output files causes AI systems (OpenAI/Anthropic) to mark the entire prompt as a \"cache miss\", increasing API costs by ~20-40%.\nReported by rooox on [Discord](https://discord.com/channels/1324644561959522314/1325020123655835709/1336999870799810561)\n# Proposal\nRemove the timestamp line from the output as it provides little value while causing significant cost implications for AI processing."}, {"issue_number": "339", "issue_url": "https://github.com/yamadashy/repomix/issues/339", "state": "open", "title": "Idea: Diff output mode", "content": "Hi Repomix Team!\nI’d like to propose an enhancement that could be very useful for code reviews and tracking changes.\nCurrently, Repomix outputs the whole repository as one file, which is great—but it would be even more powerful if there were an option to generate a hybrid output. In this mode, the full project is included as usual, but files that are “affixed” or flagged for change would be presented in a diff format (for example, showing the differences between branches or between the current state and a specific branch). Meanwhile, all other files would be displayed in their standard format.\nThis hybrid mode would allow _AI reviewers_ to easily see what has changed without duplicating the entire codebase for both states. It would combine a complete view of the repository with a clear, focused diff for files that have been modified, streamlining the review process and helping to quickly identify important changes.\nThanks!"}, {"issue_number": "328", "issue_url": "https://github.com/yamadashy/repomix/issues/328", "state": "open", "title": "Fix request: When a user includes files, repomix still says 'this is the entire codebase'", "content": "Example command:\n`repomix --include \"pages/_app.js,pages/_document.js,components/Header.js,lib/supabaseClient.js,pages/auth.js\" --copy`\nExample output:\n```\nThis file is a merged representation of the entire codebase, combining all repository files into a single document.\nGenerated by Repomix on: 2025-01-29T11:23:01.763Z\n================================================================\n================================================================\nPurpose:\n--------\nThis file contains a packed representation of the entire repository's contents.\nIt is designed to be easily consumable by AI systems for analysis, code review,\nor other automated processes.\nFile Format:\n------------\nThe content is organized as follows:\n2. Repository information\n3. Directory structure\n4. Multiple file entries, each consisting of:\na. A separator line (================)\nb. The file path (File: path/to/file)\nc. Another separator line\nd. The full contents of the file\ne. A blank line\nUsage Guidelines:\n-----------------\n- This file should be treated as read-only. Any changes should be made to the\noriginal repository files, not this packed version.\n- When processing this file, use the file path to distinguish\nbetween different files in the repository.\n- Be aware that this file may contain sensitive information. Handle it with\nthe same level of security as you would the original repository.\nNotes:\n------\n- Some files may have been excluded based on .gitignore rules and Repomix's\nconfiguration.\n- Binary files are not included in this packed representation. Please refer to\nthe Repository Structure section for a complete list of file paths, including\nbinary files.\nAdditional Info:\n----------------\n================================================================\nDirectory Structure\n================================================================\ncomponents/\nAuth.js\nHeader.js\nLayout.js\ncontext/\nAuthContext.js\nlib/\nsupabaseClient.js\npages/\nauth/\ncallback.js\nconfirm.js\n_app.js\n_document.js\nauth.js\n================================================================\nFiles\n================================================================\n```\nEven though repomix says that some files might be excluded, this is contradictory to its statement at the top that 'this is the entire codebase', which is not AI-friendly, substantiated by the fact that Deepseek-R1 still thinks that it's the full codebase:\n```\n\nSteps: Critical Missing Piece:\njavascript\nCopy\n// pages/auth/callback.js - MISSING IN YOUR CODEBASE\n// This is essential for handling OAuth callbacks\n...\netc\n```\nIt's not missing, I just forgot to include it! I hope that's clear.\nThe requested change is to have the pasted output say 'this is a merged representation of part of the codebase', or something similar, if only some files are included.\nThanks to all the contributors - awesome project."}, {"issue_number": "327", "issue_url": "https://github.com/yamadashy/repomix/issues/327", "state": "closed", "title": "Feature request: Allow option to specify a list of files", "content": "The --include option allows me to include blobs which is nice, but if I'd like to pack together a particular list of say 8 files, this would be very useful to just have an option to do something like\n`repomix --list lib/file.js components/Auth.js pages/index.js --copy`\nto allow more efficient and targeted file inclusions than using Regex which can be quite complex and perhaps unnecessary for most use-cases.\nThanks in advance - awesome project."}, {"issue_number": "325", "issue_url": "https://github.com/yamadashy/repomix/issues/325", "state": "open", "title": "Idea: Support multiple configurations within a single repomix.config.json", "content": "There are times when I'm workin non a large project which has multiple subdirectories within it.\nIf I'm only working inside of one of the subdirectories and I use repomix on the entire project it will be a massive file and a waste of tokens.\nI would like to be able to place a `repomix.config.json` file at the root of the project and define a default config which packs the entire project by running\n```\nnpx repomix\n```\nBut then I would like to be able to pack a specific subdirectory by specifying a configuration such as\n```\nnpx repomix my-ui-config\n```\nor\n```\nnpx repomix my-api-config\n```\nThe config file could look something like this:\n```\n{\n\"default\": {\n\"output\": {\n\"filePath\": \"my-whole-project.xml\",\n\"style\": \"xml\"\n},\n\"include\": [],\n\"ignore\": {\n\"useGitignore\": true,\n\"useDefaultPatterns\": true,\n\"customPatterns\": []\n}\n},\n\"my-ui-project\":{\n\"output\": {\n\"filePath\": \"my-ui-project.xml\",\n\"style\": \"xml\"\n},\n\"include\": [],\n\"ignore\": {\n\"useGitignore\": true,\n\"useDefaultPatterns\": true,\n\"customPatterns\": [\n\"API-Project/\"<======= Ignoring non UI related directories\n]\n}\n},\n\"my-api-project\":{\n\"output\": {\n\"filePath\": \"my-ui-project.xml\",\n\"style\": \"xml\"\n},\n\"include\": [],\n\"ignore\": {\n\"useGitignore\": true,\n\"useDefaultPatterns\": true,\n\"customPatterns\": [\n\"UI-Project/\" <======= Ignoring non API related directories\n]\n}\n}\n}\n```\nWhere if the `default` config is applied if `npx repomix` is called without any arguments.\nMany other projects support similar configs, for example when building an angular project you can specify the build configuration like this:\n```\nng build --configuration production\n```\nWhere the `production` configuration is defined inside the `angular.json` file."}, {"issue_number": "323", "issue_url": "https://github.com/yamadashy/repomix/issues/323", "state": "closed", "title": "Feature request: pack only currently open files in VSCode (and forks like CursorAI or Windsurf)", "content": "I would like to request a new feature that allows Repomix to pack only the files currently open in Visual Studio Code and its forks such as CursorAI or Windsurf.\n**Motivation:**\nOften, I already have the relevant files open in the editor, but I cannot efficiently use inclusion/exclusion patterns to specify them. Having an option to directly pack the files open in the current editor session would greatly streamline the workflow.\nFurthermore, most developers already use an IDE based on Visual Studio Code, if not VSCode itself. Therefore, adding this feature would likely see high adoption with minimal friction. This enhancement would allow users to pack only the currently opened files without the need to manually specify inclusion/exclusion patterns, improving efficiency and ease of use.\n**Proposed Solution:**\nImplement a command-line option that detects and packs only the files currently open in the editor, leveraging the API of VSCode and compatible forks or by reading the workspace state.\n**Benefits:**\n- Saves time by eliminating the need to manually define patterns.\n- Provides more accurate file selection.\n- Facilitates integration with customized development environments.\n\nExpected: ```sh\nrepomix --active-files\n```\nThis command should generate a packed file containing only the currently open files in the editor session.\n**Considered Alternatives:**\nCurrently, manually specifying the files or relying on include/exclude patterns is required, which can be tedious and imprecise in certain scenarios."}, {"issue_number": "320", "issue_url": "https://github.com/yamadashy/repomix/issues/320", "state": "open", "title": "[BUG] `.repomixignore` file ignored when `ignore.customPatterns` contains `\"**/.*\"`", "content": "First of all, thank you for creating and maintaining `repomix`! It's a fantastic tool.\nI've encountered an issue while using the `ignore.customPatterns` in `repomix.config.json`. When I add `\"**/.*\"` in `ignore.customPatterns`, the `.repomixignore` file is completely ignored, and the patterns defined in it are no longer taken into account.\n\nSteps: 1. Add `\"**/.*\"` to `ignore.customPatterns` in `repomix.config.json`:\n```json\n{\n\"ignore\": {\n\"customPatterns\": [\"**/.*\"]\n}\n}\n```\n2. Create a `.repomixignore` file with some patterns, e.g.:\n```ignore\n*.log\n/temp/\n```\n3. Run `repomix` and observe that the patterns in `.repomixignore` are not applied.\n\nExpected: The patterns in `.repomixignore` should still be respected and applied, even if `ignore.customPatterns` includes `\"**/.*\"`.\n\nActual: The `.repomixignore` file is ignored entirely, and its patterns are not applied.\n### Additional Context:\nThis behavior seems unintended, as the `ignore.customPatterns` and `.repomixignore` should ideally work together rather than one overriding the other.\nLet me know if you need further details or if there’s anything I can do to help debug this issue. Thanks again for your work on this project!"}, {"issue_number": "316", "issue_url": "https://github.com/yamadashy/repomix/issues/316", "state": "closed", "title": "Missing CLI flags for some configuration options", "content": "Hi @yamadashy ! 👋\nWhile analyzing the correspondence between CLI flags and configuration options, I noticed that some configuration fields don't have corresponding CLI flags. I'd like to know if this is intentional or if we should add them for consistency.\n### Current missing CLI flags for existing config options:\n- `output.headerText` -> add `--header-text` ?\n- `output.instructionFilePath` -> add`--instruction-file` ?\n- `output.includeEmptyDirectories` -> add `--include-empty-directories` ?\n- `ignore.useGitignore` -> add `--no-gitignore` ?\n- `ignore.useDefaultPatterns` -> add `--no-default-patterns` ?\n## Use Case\nI specifically need these CLI flags for the Repomix Runner VS Code extension. Currently, the extension passes VS Code configuration to Repomix through CLI flags.\nI'd be happy to submit a PR to add them if you don't have time."}, {"issue_number": "312", "issue_url": "https://github.com/yamadashy/repomix/issues/312", "state": "open", "title": "Improve handling of large repositories in website", "content": "Fix performance issues when processing large repositories through the web interface, specifically addressing request timeouts and UI hangs during rendering.\n- Large repositories generate very large output files\n- HTTP requests timeout during processing\n- <PERSON><PERSON><PERSON> hangs when trying to render large output\n- Particularly problematic on mobile devices\nIssue was requested by user @gaby\nref\nhttps://github.com/yamadashy/repomix/issues/219#issuecomment-2572280926"}, {"issue_number": "311", "issue_url": "https://github.com/yamadashy/repomix/issues/311", "state": "closed", "title": "Add URL parsing for branch/commit information", "content": "Add functionality to parse branch, tag, and commit information from repository URLs.\nDifferent repository URL formats need to be supported:\n- `owner/repo`\n- `https://github.com/owner/repo`\n- `https://github.com/owner/repo/tree/branch`\n- `https://github.com/owner/repo/commit/hash`\nFeature was requested by user @gaby\nref\nhttps://github.com/yamadashy/repomix/issues/219#issuecomment-2573359838"}, {"issue_number": "310", "issue_url": "https://github.com/yamadashy/repomix/issues/310", "state": "closed", "title": "Add zip/folder upload functionality to website", "content": "This feature will allow users to upload zip archives or folders directly through the Repomix website (repomix.com), providing an alternative to the CLI tool.\nFeature was requested by user @huy-trn\nref\nhttps://github.com/yamadashy/repomix/issues/219#issuecomment-2571282619"}, {"issue_number": "303", "issue_url": "https://github.com/yamadashy/repomix/issues/303", "state": "open", "title": "can it able to do the same with WIKI?", "content": "I am wondering if this amazing text file can be made of the wiki of a GitHub repo to pass them to the LLM?"}, {"issue_number": "297", "issue_url": "https://github.com/yamadashy/repomix/issues/297", "state": "closed", "title": "Call repomix from within a node script", "content": "Hi, I would like to use repomix in my application. Is it possible to call repomix as a node library, as opposed to a command line tool? If so, is there documentation on that somewhere? Thank you!"}, {"issue_number": "286", "issue_url": "https://github.com/yamadashy/repomix/issues/286", "state": "open", "title": "Would anyone be interested in me building an Electron option for drag and drop?", "content": "I'm pretty visual and sometimes I work with files across different directories, it would be great to be able to have a UI component like a \"Dropbox\" that you can drag and drop files into which then gets piped into Repomix CLI\nAnyone interested?"}, {"issue_number": "282", "issue_url": "https://github.com/yamadashy/repomix/issues/282", "state": "closed", "title": "XML Escaping", "content": "I like the feature of having a structured output via XML. However, the string concatenation as it is done in `xmlStyle.ts` doesn't really work. XML requires escaping of certain characters: https://stackoverflow.com/questions/1091945/what-characters-do-i-need-to-escape-in-xml-documents\nBasically, if the repo you are reading with repomix contains an XML file itself, it will be parsed as part of the repo tree, not as file contents. The \"&&\" symbol that is used for boolean conjunction in many languages is disallowed in XML files entirely and will make the output invalid.\nThe proper solution would be to use an XML serializer that takes care of any escaping that is needed."}, {"issue_number": "278", "issue_url": "https://github.com/yamadashy/repomix/issues/278", "state": "closed", "title": "GitHub repo name including dot doesn't work", "content": "Thank you for the great repo.\nGitHub URL validation doesn't work with repo names including dot.\nExample GitHub URLs.\n`https://github.com/needle-mirror/com.unity.visualscripting`\n<img width=\"785\" alt=\"Screenshot 2025-01-10 at 10 14 02\" src=\"https://github.com/user-attachments/assets/2cee6d91-90f7-41f6-93c9-b1ce37a3a60d\" />"}, {"issue_number": "274", "issue_url": "https://github.com/yamadashy/repomix/issues/274", "state": "closed", "title": "SyntaxError: Unexpected token 'with' when running repomix", "content": "When running repomix, the command fails with a SyntaxError related to the with keyword. This suggests a compatibility issue with newer Node.js versions or ESM modules.\n\nSteps: `npx repomix`\nError Output:\n```\nFatal Error: {\nname: 'SyntaxError',\n\nExpected: '    at ESMLoader.moduleStrategy (node:internal/modules/esm/translators:119:18)\\n' +\n'    at ESMLoader.moduleProvider (node:internal/modules/esm/loader:468:14)'\n}\n```"}, {"issue_number": "258", "issue_url": "https://github.com/yamadashy/repomix/issues/258", "state": "closed", "title": "G<PERSON><PERSON> gist is giving errors, how can we fix this?", "content": "Getting this error:\nnpx repomix --remote https://github.com/nikahmadz/4046cf69caf4ddc68ea5e293e6afdc0e\nnpm error code ERR_INVALID_URL\nnpm error Invalid URL\nnpm error A complete log of this run can be found in: C:\\Users\\<USER>\\AppData\\Local\\npm-cache\\_logs\\2025-01-05T08_00_23_247Z-debug-0.log\nLog:\n0 verbose cli C:\\Program Files\\nodejs\\node.exe C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\npm\\bin\\npm-cli.js\n1 info using npm@10.9.2\n2 info using node@v22.11.0\n3 silly config load:file:C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\npm\\npmrc\n4 silly config load:file:\\\\wsl.localhost\\Ubuntu\\home\\user123_789\\.npmrc\n5 silly config load:file:C:\\Users\\<USER>\\.npmrc\n6 silly config load:file:C:\\Users\\<USER>\\AppData\\Roaming\\npm\\etc\\npmrc\n7 verbose title npm exec repomix --remote https://github.com/nikahmadz/4046cf69caf4ddc68ea5e293e6afdc0e\n8 verbose argv \"exec\" \"--\" \"repomix\" \"--remote\" \"https://github.com/nikahmadz/4046cf69caf4ddc68ea5e293e6afdc0e\"\n9 verbose logfile logs-max:10 dir:C:\\Users\\<USER>\\AppData\\Local\\npm-cache\\_logs\\2025-01-05T08_00_23_247Z-\n10 verbose logfile C:\\Users\\<USER>\\AppData\\Local\\npm-cache\\_logs\\2025-01-05T08_00_23_247Z-debug-0.log\n11 silly logfile start cleaning logs, removing 3 files\n12 verbose stack TypeError: Invalid URL\n12 verbose stack     at new URL (node:internal/url:816:29)\n12 verbose stack     at fromFile (C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\npm\\node_modules\\npm-package-arg\\lib\\npa.js:263:15)\n12 verbose stack     at resolve (C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\npm\\node_modules\\npm-package-arg\\lib\\npa.js:71:12)\n12 verbose stack     at npa (C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\npm\\node_modules\\npm-package-arg\\lib\\npa.js:53:10)\n12 verbose stack     at FetcherBase.get (C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\npm\\node_modules\\pacote\\lib\\fetcher.js:474:16)\n12 verbose stack     at Object.manifest (C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\npm\\node_modules\\pacote\\lib\\index.js:20:29)\n12 verbose stack     at hasPkgBin (C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\npm\\node_modules\\libnpmexec\\lib\\index.js:82:10)\n12 verbose stack     at exec (C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\npm\\node_modules\\libnpmexec\\lib\\index.js:137:17)\n12 verbose stack     at Exec.callExec (C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\npm\\lib\\commands\\exec.js:79:12)\n12 verbose stack     at Exec.exec (C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\npm\\lib\\commands\\exec.js:28:17)\n13 error code ERR_INVALID_URL\n14 error Invalid URL\n15 verbose cwd \\\\wsl.localhost\\Ubuntu\\home\\user123_789\n16 verbose os Windows_NT 10.0.26100\n17 verbose node v22.11.0\n18 verbose npm  v10.9.2\n19 verbose exit 1\n20 verbose code 1\n21 error A complete log of this run can be found in: C:\\Users\\<USER>\\AppData\\Local\\npm-cache\\_logs\\2025-01-05T08_00_23_247Z-debug-0.log"}, {"issue_number": "257", "issue_url": "https://github.com/yamadashy/repomix/issues/257", "state": "open", "title": "Interested in using as a library and not using command line", "content": "Hello,\nI am working on my own project that dose AI assisted edits to existing files. https://github.com/mmiscool/aiCoder\nI would be interested in using this tool as a library with an API.\nsome thing like:\n```javascript\nimport {repomix} from 'repomix';\nconst generatedText = repomix.generateMarkdown(\"pathToFolder\",\n{\n\"output\": {\n\"style\": \"markdown\",\n\"removeComments\": true,\n\"showLineNumbers\": true,\n\"topFilesLength\": 10\n},\n\"ignore\": {\n\"customPatterns\": [\"*.test.ts\", \"docs/**\"]\n}\n});\n```\nIs there any plan to support doing this?"}, {"issue_number": "240", "issue_url": "https://github.com/yamadashy/repomix/issues/240", "state": "open", "title": "(node:23021) ExperimentalWarning: Importing JSON modules is an experimental feature and might change at any time (Use `node --trace-warnings ...` to show where the warning was created) 0.2.12", "content": "```\nwangzhiyong@wangzhiyongdeMacBook-Pro ~ % repomix --version\n(node:23021) ExperimentalWarning: Importing JSON modules is an experimental feature and might change at any time\n(Use `node --trace-warnings ...` to show where the warning was created)\n0.2.12\nwangzhiyong@wangzhiyongdeMacBook-Pro ~ %\n```\nEverytime I type `repomix --version` this annoying error message shows up."}, {"issue_number": "226", "issue_url": "https://github.com/yamadashy/repomix/issues/226", "state": "open", "title": "Support for unpack", "content": "Since this tool is to pack a repo (codebase) into a prompt, after asking ChatGPT or other LLM AI to review, the LLM should edit the `repomix-output.txt`, and `repomix` should support unpack to apply LLM edited changes in the repo."}, {"issue_number": "221", "issue_url": "https://github.com/yamadashy/repomix/issues/221", "state": "closed", "title": "Add support for <PERSON><PERSON>", "content": "Add support for running `repomix` using Docker."}, {"issue_number": "219", "issue_url": "https://github.com/yamadashy/repomix/issues/219", "state": "open", "title": "Idea: Add a demo website", "content": "I think adding a demo website using github pages would be nice.\nPeople can paste link to a public repo, or upload a repository's archive to get repomix's output that they can easily copy without installing anything."}, {"issue_number": "211", "issue_url": "https://github.com/yamadashy/repomix/issues/211", "state": "closed", "title": "repomix --remote https://unpkg.com/repomix", "content": "Although this project is lit, people may think it's dangerous to let thrid-party cli to scan whole codebase. repomix needs to provide a way to convince people this project is legit, so one thing is to pack it self, not the github repo, cause github repo and npm can be totally two different codebase, it's a really simple attack method, so, one ease solution is to let repomix support `repomix --remote https://unpkg.com/repomix` , this way it can easily pack itself, people can just post the whole thing to LLM to verify if it contains mailicious code.While this project is fantastic, some users might hesitate to allow a third-party CLI to scan their entire codebase. To build confidence and demonstrate the project's legitimacy, **Repomix** could offer a mechanism to validate itself as a trusted tool.\nOne potential concern is the discrepancy between the GitHub repository and the published npm package, as malicious actors could exploit this difference with a simple attack method.\n**Proposed Solution**\nIntroduce a feature to allow Repomix to package itself directly from a remote source. For example:\n```bash\nrepomix --remote https://unpkg.com/repomix\n```\nWith this feature:\n- Users can fetch and package Repomix directly from its deployed version.\n- They could then analyze the resulting package (e.g., with an LLM) to ensure no malicious code is present.\nThis would make it easier for users to trust and adopt Repomix while mitigating potential security concerns."}, {"issue_number": "210", "issue_url": "https://github.com/yamadashy/repomix/issues/210", "state": "closed", "title": "Ignored files can't be found in folder tree.", "content": "Add an option to keep it."}, {"issue_number": "209", "issue_url": "https://github.com/yamadashy/repomix/issues/209", "state": "closed", "title": "Support comments in `repomix.config.json`", "content": "When dealing with a large codebase, people need to rapidly select which file paths to ignore and which to **include** in order to achieve acceptable token counts. The ability to add comments to `repomix.config.json` is critical."}, {"issue_number": "208", "issue_url": "https://github.com/yamadashy/repomix/issues/208", "state": "open", "title": "Unstable `removeComments` and `removeEmptyLines`", "content": "My codebase has **15,382,523 tokens** , when I set the `removeComments` and `removeEmptyLines` `true` , the terminal stucks forever, even ctrl-c not working."}, {"issue_number": "207", "issue_url": "https://github.com/yamadashy/repomix/issues/207", "state": "open", "title": "Browser Extension", "content": "I've created the following:\nhttps://gist.github.com/kfrancis/bbb57d85306246b72130f4a62ae7b946\n![image](https://github.com/user-attachments/assets/7580dcdd-d2d9-4a13-8e3b-b369c657de00)\nSettings are there, but don't work yet - just so you can get an idea:\n![image](https://github.com/user-attachments/assets/de1cfe18-e75b-4e97-988a-690514ff1a8e)"}, {"issue_number": "206", "issue_url": "https://github.com/yamadashy/repomix/issues/206", "state": "open", "title": "Removing File Summary header", "content": "Idea: Being able to omit and/or customize File Summary. I just want the code in the output, not anything else.\nProposal: Adding `output.summaryText` or `output.removeFileSummary`would be great."}, {"issue_number": "202", "issue_url": "https://github.com/yamadashy/repomix/issues/202", "state": "open", "title": "Idea:  Add an option to make the output more friendly with RAG engines", "content": "Hello @yamadashy ,\nThe tool is already great, but larger repositories will never fit into an LLM's context window.\nI know enterprise-level RAG systems have been around for a while, but sticking to user-friendly solutions, here’s what I’m thinking:\n- What to do:  Make the output of Repomix easier to parse, and more meaningful when retrieved by any RAG engine, such as the \"chat with documents\" feature that’s common in most LLM applications.\n- How to do: Split large code files into smaller chunks using the AST, then merge them back into a single output. Also, add separators between chunks that are recognizable by most text splitters.\nI’ve recently tried Langchain’s [source code loader](https://python.langchain.com/docs/integrations/document_loaders/source_code/), and this approach should be easy to implement with a few additional dependencies.\nIf this sounds good, I’d be happy to open a PR for it! Let me know your thoughts."}, {"issue_number": "198", "issue_url": "https://github.com/yamadashy/repomix/issues/198", "state": "open", "title": "Repomap - A Repomix/Mermaid/GenAI repo visualisation tool", "content": "I've used Repomix to make Repomap.\nhttps://github.com/George5562/Repomap\nIt creates a Repomix XML, uses an LLM with Langchain to convert to mermaid so you can visualise your project as a flowchart.\nYou can specify the types of nodes (pages, components, functions etc) and edges/relationships (depends on, uses, renders etc).\nUsing the --add CLI you can add an enhancement \"add nextAuth\", and two additional LLM calls will create a suggested file structure, and then create an updated flowchart, now with possible future components added."}, {"issue_number": "197", "issue_url": "https://github.com/yamadashy/repomix/issues/197", "state": "open", "title": "npx repomix  kabooms with \"Cannot find module ... cliRun.js\"", "content": "Might be a user error and RTFM (not a JS fella here) but tried to follow README.md stating:\n> You can try Repomix instantly in your project directory without installation:\n>\n> ```bash\n> npx repomix\n> ```\nand got (tried two ways) while running within the fresh clone at v0.2.5-10-g05589f7 :\n```shell\n❯ npx repomix\nFatal Error: {\nname: 'Error',\nmessage: \"Cannot find module '/home/<USER>/proj/misc/repomix/lib/cli/cliRun.js' imported from /home/<USER>/proj/misc/repomix/bin/repomix.cjs\",\nstack: \"Error [ERR_MODULE_NOT_FOUND]: Cannot find module '/home/<USER>/proj/misc/repomix/lib/cli/cliRun.js' imported from /home/<USER>/proj/misc/repomix/bin/repomix.cjs\\n\" +\n'    at finalizeResolution (node:internal/modules/esm/resolve:265:11)\\n' +\n'    at moduleResolve (node:internal/modules/esm/resolve:933:10)\\n' +\n'    at defaultResolve (node:internal/modules/esm/resolve:1169:11)\\n' +\n'    at ModuleLoader.defaultResolve (node:internal/modules/esm/loader:542:12)\\n' +\n'    at ModuleLoader.resolve (node:internal/modules/esm/loader:510:25)\\n' +\n'    at ModuleLoader.getModuleJob (node:internal/modules/esm/loader:239:38)\\n' +\n'    at ModuleLoader.import (node:internal/modules/esm/loader:472:34)\\n' +\n'    at defaultImportModuleDynamicallyForScript (node:internal/modules/esm/utils:227:31)\\n' +\n'    at importModuleDynamicallyCallback (node:internal/modules/esm/utils:249:12)\\n' +\n'    at /home/<USER>/proj/misc/repomix/bin/repomix.cjs:42:21'\n}\n❯ find -iname cliRun.js\n❯ find -iname cliRun.*\n./src/cli/cliRun.ts\n./tests/cli/cliRun.test.ts\n❯ npx .\nFatal Error: {\nname: 'Error',\nmessage: \"Cannot find module '/home/<USER>/.npm/_npx/bc821935aa97a4d0/node_modules/repomix/lib/cli/cliRun.js' imported from /home/<USER>/.npm/_npx/bc821935aa97a4d0/node_modules/repomix/bin/repomix.cjs\",\nstack: \"Error [ERR_MODULE_NOT_FOUND]: Cannot find module '/home/<USER>/.npm/_npx/bc821935aa97a4d0/node_modules/repomix/lib/cli/cliRun.js' imported from /home/<USER>/.npm/_npx/bc821935aa97a4d0/node_modules/repomix/bin/repomix.cjs\\n\" +\n'    at finalizeResolution (node:internal/modules/esm/resolve:265:11)\\n' +\n'    at moduleResolve (node:internal/modules/esm/resolve:933:10)\\n' +\n'    at defaultResolve (node:internal/modules/esm/resolve:1169:11)\\n' +\n'    at ModuleLoader.defaultResolve (node:internal/modules/esm/loader:542:12)\\n' +\n'    at ModuleLoader.resolve (node:internal/modules/esm/loader:510:25)\\n' +\n'    at ModuleLoader.getModuleJob (node:internal/modules/esm/loader:239:38)\\n' +\n'    at ModuleLoader.import (node:internal/modules/esm/loader:472:34)\\n' +\n'    at defaultImportModuleDynamicallyForScript (node:internal/modules/esm/utils:227:31)\\n' +\n'    at importModuleDynamicallyCallback (node:internal/modules/esm/utils:249:12)\\n' +\n'    at /home/<USER>/.npm/_npx/bc821935aa97a4d0/node_modules/repomix/bin/repomix.cjs:42:21'\n}\n```"}, {"issue_number": "195", "issue_url": "https://github.com/yamadashy/repomix/issues/195", "state": "closed", "title": "Feature Request: Add Branch Option for Remote Repository Packing", "content": "Hello @yamadashy ,\nI'm new to this project but I'm willing to help creating a PR. I think it would be beneficial to add an option to select a specific commit ID or branch when packing a remote repository.\nCurrent Behavior:\nWhen packing a remote repository, the process uses the default branch.\nProposed Feature:\nAdd a branch option to the packing process, allowing users to select a specific commit ID or branch to pack.\nPotential Implementation:\n```\nrepomix --remote https://github.com/yamadashy/repomix --branch master\n```"}, {"issue_number": "193", "issue_url": "https://github.com/yamadashy/repomix/issues/193", "state": "open", "title": "Bug: <PERSON><PERSON> when running on MacOS", "content": "Steps: 1. `npm install -g repomix` or `npx repomix`"}, {"issue_number": "192", "issue_url": "https://github.com/yamadashy/repomix/issues/192", "state": "open", "title": "Idea: Community directory of popular libraries ", "content": "I put this idea together quickly with the idea of making a directory of some sorts of popular libraries. Or maybe someone uses a set of common packages that they want to always have AI friendly RAG files, but they need to customize the configs for each library to try to limit the token count to only the most important files.\nI imagine the more worked-out idea of this would be to be able to combine source code and docs of specific methods/interfaces/features of the libraries you're working with.\nAnyways, cool package @yamadashy !\nhttps://github.com/austinm911/prompts"}, {"issue_number": "191", "issue_url": "https://github.com/yamadashy/repomix/issues/191", "state": "closed", "title": "command line flag for turning off security check (reopens #74)", "content": "Add a command line flag to disable the security check. It is always a false positive when running repomix on documentation.\nhttps://github.com/yamadashy/repomix/issues/74"}, {"issue_number": "181", "issue_url": "https://github.com/yamadashy/repomix/issues/181", "state": "open", "title": "Add option to only exclude in \"Repository Files\", but not in \"Repository Structure\"", "content": "Hi @yamadashy , really nice work. I love this tool.\nBy the way, why don't you add option only exclude in \"Repository Files\"?\nCurrently, `--ignore` option both exclude in \"Repository Structure\" and \"Repository Files\".\nFor example, my target repository has this kind of file:\n```\n📈 Top 5 Files by Character Count and Token Count:\n──────────────────────────────────────────────────────\n1.  apps/frontend/src/app/fixtures/transcripts1.json (1145024 chars, 377136 tokens)\n2.  apps/frontend/public/locales/en/common.json (192476 chars, 82151 tokens)\n3.  apps/admin/public/assets/CompanySign.svg (185918 chars, 132207 tokens)\n...\n```\nIn this case, I don't want to store file content but want to include in repository structure.\nI think `--exclude` option is one good name for this purpose."}, {"issue_number": "175", "issue_url": "https://github.com/yamadashy/repomix/issues/175", "state": "open", "title": "Standardized markdown", "content": "The llms.txt initiative introduces a standardized markdown.\nI don't know if we should follow this format\n[LLM Txt](https://llmstxt.org/)"}, {"issue_number": "170", "issue_url": "https://github.com/yamadashy/repomix/issues/170", "state": "open", "title": "Share Your Experience with Repomix", "content": "Hi everyone! I’m new to coding and rely on <PERSON> for a lot of guidance, so I find Repomix super helpful for providing project context. However, I only want the project structure—not the actual code—because including everything fills up my context window too quickly. Specifically, I need just the directory and file names under project/frontend/ and project/backend/ without going too deep. Is there a command to display this structure at a high level, showing just the folder and file names?\nThanks for the help!"}, {"issue_number": "168", "issue_url": "https://github.com/yamadashy/repomix/issues/168", "state": "open", "title": "Showcase Video for this project will be truly appreciated", "content": "Please create small video to showcase this project functionality so that people can have generally idea of what to do and how to do. There is btw openwebui great open source project so maybe you can try to showcase with that project as they provide you one of the best frontend which has advanced functionalities like functions,tools,vector db etc etc with Litellm (another great open source project which can help you use 100+ LLM's easily) .\nThankyou very much for this great open source project :-))"}, {"issue_number": "164", "issue_url": "https://github.com/yamadashy/repomix/issues/164", "state": "open", "title": "Feature Request: Code Detail Level", "content": "If your project is sufficiently well documented, having a block of text describing the intent, intput and example outputs for each function, then would it be useful to have an option to only include the method signatures and associated documentation, and not the full code?\nThis might reduce the context a bit and still give the LLM what it actually needs to write the new item you are asking it for."}, {"issue_number": "163", "issue_url": "https://github.com/yamadashy/repomix/issues/163", "state": "open", "title": "Remove images from notebooks", "content": "Right now, the repomix generated file includes the images generated in `.ipynb`.\n```\n{\n\"cell_type\": \"code\",\n\"execution_count\": 7,\n\"metadata\": {},\n\"outputs\": [\n{\n\"name\": \"stdout\",\n\"output_type\": \"stream\",\n\"text\": [\n\"Max Training: 516.0\\n\",\n\"Min Training: 1.0\\n\",\n\"Mean Training: 58.099391480730226\\n\",\n\"Median Training: 39.0\\n\",\n\"\\n\",\n\"\\n\",\n\"Max Validation: 23.0\\n\",\n\"Min Validation: 1.0\\n\",\n\"Mean Validation: 1.8539553752535496\\n\",\n\"Median Validation: 1.0\\n\",\n\"\\n\",\n\"\\n\"\n]\n},\n{\n\"data\": {\n\"image/png\": \"iVBORw0KGgoAAAANSUhEUgAABIcAAAE/CAYAAADc0KMkAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAALEgAACxIB0t1+/AAAADl0RVh0U29mdHdhcmUAbWF0cGxvdGxpYiB2ZXJzaW9uIDI ...\n```\nI think there should be some preprocessing to remove them (or put a placeholder), since they don't add any value and take up many space (thus making it harder to retrieve the truly relevant information).\nMy dirty solution for now is to go and remove the lines:\n```python\ninput_file = 'repomix.txt'\noutput_file = 'repomix_modified.txt'\nwith open(input_file, 'r') as file:\nlines = file.readlines()\nwith open(output_file, 'w') as file:\nfor line in lines:\nif not line.startswith('      \"image/png\": \"'):\nfile.write(line)\n```\nThe generated repomix file went from 5MB to 300kB."}, {"issue_number": "162", "issue_url": "https://github.com/yamadashy/repomix/issues/162", "state": "open", "title": "illegal operation on folder?", "content": "I accidentally hit \"deny\" when macOS asked for the security/privacy permissions for Repomix, and now when I run repomix I get this error:\nUnexpected error: EISDIR: illegal operation on a directory, open '/users/myUserNameHere'\neven if I cd to another directory it won't let me run anything.\nI've tried uninstalling, reinstalling, and doing a full tccuntil reset."}, {"issue_number": "161", "issue_url": "https://github.com/yamadashy/repomix/issues/161", "state": "closed", "title": "How to include empty folders/files in the \"Repository Structure\" for more context?", "content": "First, thank you all for your work.\nI was expecting the whole project structure to be included in the output file (even for empty folders and files, to give context to the LLM on how the project should evolve or what it should exactly work on)."}, {"issue_number": "152", "issue_url": "https://github.com/yamadashy/repomix/issues/152", "state": "open", "title": "Feature Request: Co<PERSON> to clipboard after packing", "content": "Maybe a `repomix -x` or something to copy to clipboard after generating or a config option would be nice."}, {"issue_number": "149", "issue_url": "https://github.com/yamadashy/repomix/issues/149", "state": "open", "title": "Add file truncation", "content": "The use case is: I have multiple JSON data files. I want to include them in the LLM input, but only to show their structure, not the contents. I'd like to be able to specify that I just want to include the first N lines."}, {"issue_number": "146", "issue_url": "https://github.com/yamadashy/repomix/issues/146", "state": "open", "title": "Unable to use repopack in ubuntu 20.04", "content": "Unable to use repopack in ubuntu 20.04\nI install npm just by \"sudo apt install npm\", then either just run \"npx repopack\" or installed by npm then run \"repopack\", it get this error\n(node:52216) UnhandledPromiseRejectionWarning: Error: Not supported\nat /usr/local/lib/node_modules/repopack/bin/repopack.cjs:5:19\nat Object.<anonymous> (/usr/local/lib/node_modules/repopack/bin/repopack.cjs:7:3)\nat Module._compile (internal/modules/cjs/loader.js:778:30)\nat Object.Module._extensions..js (internal/modules/cjs/loader.js:789:10)\nat Module.load (internal/modules/cjs/loader.js:653:32)\nat tryModuleLoad (internal/modules/cjs/loader.js:593:12)\nat Function.Module._load (internal/modules/cjs/loader.js:585:3)\nat Function.Module.runMain (internal/modules/cjs/loader.js:831:12)\nat startup (internal/bootstrap/node.js:283:19)\nat bootstrapNodeJSCore (internal/bootstrap/node.js:623:3)\n(node:52216) UnhandledPromiseRejectionWarning: Unhandled promise rejection. This error originated either by throwing inside of an async function without a catch block, or by rejecting a promise which was not handled with .catch(). (rejection id: 2)\n(node:52216) [DEP0018] DeprecationWarning: Unhandled promise rejections are deprecated. In the future, promise rejections that are not handled will terminate the Node.js process with a non-zero exit code."}, {"issue_number": "140", "issue_url": "https://github.com/yamadashy/repomix/issues/140", "state": "closed", "title": "Broken link (LICENSE) in README", "content": "Current Behavior:\nLICENSE in README redirects to invalid link ( 404 )\n\nExpected: It should redirect to correct link\n![image](https://github.com/user-attachments/assets/e7b8a3f0-3114-42e0-8dcd-27e0b8916c88)\n![image](https://github.com/user-attachments/assets/d909ec95-3bac-4f0d-9099-e9e9284cdb7f)"}, {"issue_number": "133", "issue_url": "https://github.com/yamadashy/repomix/issues/133", "state": "open", "title": "Feature Request : erasing line gaps", "content": "if a file has unnecessary line gaps the they are not removed which makes the file more bulky.  is it possible to remove unnecessary line gaps to make the files more relevant ?"}, {"issue_number": "132", "issue_url": "https://github.com/yamadashy/repomix/issues/132", "state": "open", "title": "Streamline Logic and Error Handling in `configLoad.ts`", "content": "The code in `src/config/configLoad.ts` can be optimized for better efficiency. Suggested improvements include:\n1. **Parallelize File Checks:** Execute the local and global configuration file existence checks in parallel using `Promise.all` to reduce execution time.\n2. **Refactor `mergeConfigs` Function:** Simplify the merging logic by using helper functions and `Array.prototype.flat()` to avoid repetitive code.\n3. **Enhance Error Handling:** Consolidate error handling in `loadAndValidateConfig` to make the code more concise and maintainable.\nImplementing these changes will enhance code efficiency without affecting existing functionality."}, {"issue_number": "131", "issue_url": "https://github.com/yamadashy/repomix/issues/131", "state": "open", "title": "Improve Efficiency and Clarity in `fileSearch.ts`", "content": "The `fileSearch.ts` file can be optimized for better performance and readability:\n1. **`searchFiles` Function:**\n- Improve **error handling** with more detailed messages.\n- Run `getIgnorePatterns` and `getIgnoreFilePatterns` in parallel using **`Promise.all`**.\n2. **`getIgnorePatterns` Function:**\n- Use a **`Set`** to avoid duplicate entries.\n- Simplify the addition of default, output, and custom patterns.\n3. **`parseIgnoreContent` Function:**\n- Check for **empty content** before processing.\n- Use **`reduce`** for cleaner code.\n4. **`getIgnoreFilePatterns` Function:**\n- Refactor **conditional logic** to support future extensions easily.\nThese changes will enhance the code's efficiency and maintainability.\nPlease assign me this issue so that i can start work on it."}, {"issue_number": "125", "issue_url": "https://github.com/yamadashy/repomix/issues/125", "state": "closed", "title": "Refactor: Centralize renderContext & style generation logic for output styles", "content": "The renderContext was repeated in all three style generation files including the logic to generate\n<code>/src/core/output/outputStyles/plainStyle.ts</code>\n<code>/src/core/output/outputStyles/xmlStyle.ts</code>\n<code>/src/core/output/outputStyles/markdownStyle.ts</code>\n``` javascript\nexport const generateMarkdownStyle = (outputGeneratorContext: OutputGeneratorContext) => {\nconst template = Handlebars.compile(markdownTemplate);\nconst renderContext = {\ngenerationHeader: generateHeader(outputGeneratorContext.generationDate),\nsummaryUsageGuidelines: generateSummaryUsageGuidelines(\noutputGeneratorContext.config,\noutputGeneratorContext.instruction,\n),\nsummaryNotes: generateSummaryNotes(outputGeneratorContext.config),\nsummaryAdditionalInfo: generateSummaryAdditionalInfo(),\nheaderText: outputGeneratorContext.config.output.headerText,\ninstruction: outputGeneratorContext.instruction,\ntreeString: outputGeneratorContext.treeString,\nprocessedFiles: outputGeneratorContext.processedFiles,\n};\nreturn `${template(renderContext).trim()}\\n`;\n};\n```\n###  Proposed Solution\nMove the common logic in these files into a shared utility file. This will make it easier to maintain as you add new styles and also add new parameters for renderContext in the future"}, {"issue_number": "112", "issue_url": "https://github.com/yamadashy/repomix/issues/112", "state": "closed", "title": "Optimize File Manipulation Logic in `fileManipulate.ts`", "content": "The current implementation of the file manipulation logic in `repopack/src/core/file/fileManipulate.ts` can be optimized for performance. Several areas, such as regex handling, string trimming, and quote matching, can be made more efficient.\n**Proposed Optimizations**:\n1. **Improve Docstring Removal Regex**:\nSimplify and optimize the regex used for removing docstrings in `PythonManipulator`.\n2. **Optimize Quote Matching in `removeHashComments`**:\nEnhance the logic to reduce redundant `slice` operations and improve performance in detecting quoted strings.\n3. **Refactor `searchInPairs` Binary Search**:\nStreamline the binary search function for checking if a hash is inside a string literal.\n4. **Enhance `rtrimLines` Function**:\nUse a more efficient method to trim trailing spaces and tabs from lines.\n5. **Lazy Instantiate Manipulators**:\nInstantiate manipulators in the `manipulators` object only when needed to improve memory usage.\n---\n\nExpected: - Faster file manipulation for large files.\n- Better memory efficiency.\n- Cleaner, more maintainable code.\nI'd like to contribute to optimizing the file manipulation logic in `fileManipulate.ts` as described in the issue. Could you please assign it to me so I can start working on it?\nThanks!"}, {"issue_number": "106", "issue_url": "https://github.com/yamadashy/repomix/issues/106", "state": "closed", "title": "Minor Grammatical Issues in Repopack Contributor Covenant Code of Conduct", "content": "This pull request addresses a couple of minor grammatical issues found in the **Repopack Contributor Covenant Code of Conduct**. These changes aim to enhance the clarity and readability of the document, ensuring that the language is accurate and consistent.\n### Changes Made:\n1. **Verb Correction**\n2. **Clarification in \"Examples of Unacceptable Behavior\"**\n---\n### Examples:\n1. **Before the fix (Pledge Section)**:\n2. **Before the fix (Unacceptable Behavior Section)**:\n3. and many more..."}, {"issue_number": "105", "issue_url": "https://github.com/yamadashy/repomix/issues/105", "state": "closed", "title": "Glob ignore out of included folders does not work", "content": "First of all thanks for this tool! been looking for something like this for a while\nI noticed this issue:\n```sh\n# repo structure\n/src\nmocks/\nMockA.ts\n```\n```sh\n# MockA.ts and all files under mocks folder are still included in output file\nnpx repopack --include \"/src/**/*.ts\" --ignore \"/src/mocks/\"\n\nExpected: npx repopack --include \"/src/**/*.ts\" --ignore \"/src/mocks/**/*.ts\"\n```"}, {"issue_number": "97", "issue_url": "https://github.com/yamadashy/repomix/issues/97", "state": "closed", "title": "docstrings in python files are not removed", "content": "This section of doc (enclosed by \"\"\") can sometimes be out of sync from the current code during debugging process. As such when you feed it to the model, it can produce weird results. As a workaround, \"\"\"[\\s\\S]*?\"\"\" regex Find and Replace in IDE does the job."}, {"issue_number": "90", "issue_url": "https://github.com/yamadashy/repomix/issues/90", "state": "closed", "title": "exclude packager lock file by default", "content": "npm: package-lock.json\nYarn: yarn.lock\npnpm: pnpm-lock.yaml"}, {"issue_number": "89", "issue_url": "https://github.com/yamadashy/repomix/issues/89", "state": "closed", "title": "can't pack a repo due to presence of special token: <|endoftext|>", "content": "working with a repo which contains [hf model](https://huggingface.co/mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis). tried to ignore folders which might cause the problem, but still unable to pack.\n<img width=\"849\" alt=\"Screenshot 2024-09-28 at 19 50 58\" src=\"https://github.com/user-attachments/assets/ae34cbaf-cb15-42ea-9b6d-58106089f3ba\">"}, {"issue_number": "86", "issue_url": "https://github.com/yamadashy/repomix/issues/86", "state": "closed", "title": "add support output to markdown", "content": ""}, {"issue_number": "74", "issue_url": "https://github.com/yamadashy/repomix/issues/74", "state": "closed", "title": "Turn off security check?", "content": "It would be helpful to turn off security checks when we're working with developing cryptographic libraries... Can we have an argument flag to simply turn this function off when needed?"}, {"issue_number": "71", "issue_url": "https://github.com/yamadashy/repomix/issues/71", "state": "open", "title": "Splitting code into several files", "content": "Hi\nI've been using this for a while now (also replied to you on Reddit some time ago) and it's really saving me a lot of time. Would it be possible to add the ability to generate more than one repopack file for a specific codebase? At a certain number of lines of code, the LLMs tend not to be able to see the rest. It would be great if I can specify the number of files (or perhaps number of lines per file) it should split the complete codebase into. Best would be if the code of a specific file is not split when creating the separate files.\nThanks for your work on this."}, {"issue_number": "66", "issue_url": "https://github.com/yamadashy/repomix/issues/66", "state": "open", "title": "Feature request: Exclusion/Inclusion Tags", "content": "I've been using this tool for a few days and it's great. I've created a handful of config files for when I'm working on different parts of my app (UI, cloud functions, scripts, etc) and it works well but I've found myself wanting to only include specific parts of files in specific \"packs\".\nFor example, when working on an edge function feature, I want my AI to be aware of my button component so it can create a test page and I want to reference a few lines of a separate file that shows how the button is used (this is a very simple example but it gets the idea across).\nMy idea is to specify optional inclusion and Exclusion tags in the config file (ex. \"// KEEP\" and \"// OMIT\", respectively) and then when you run repopack, if it has those tags it will include code between the \"KEEP\" comments and exclude code between the \"OMIT\" comments. If a file has neither of the specific tags, all of it is included.\nThis would give users a lot of flexibility when creating repopacks that are specific to some domain within their apps:\n- UI-KEEP\n- UI-OMIT\n- DB-KEEP\n- DB-OMIT\n- ...\nThe idea needs little bit of ironing out but it has the potential to save on token counts and help in situations where your files are huge.\nThis is an awesome tool, especially combined with Claude projects!"}, {"issue_number": "63", "issue_url": "https://github.com/yamadashy/repomix/issues/63", "state": "closed", "title": "Infinite Loading and Memory Leak", "content": "---\n\nSteps: 1. Run Repopack with the following command:\n```\nnpx repopack\n```\n2. Wait for the packing process to complete.\n3. Observe that the process runs without completing and without producing content.\n4. When aborted with `CTRL + C`, an empty Repopack file is produced.\n\nExpected: Repopack should successfully complete the packaging process and generate the packed files.\n\nActual: - The process hangs indefinitely.\n- An empty Repopack file is generated when manually aborted.\n### Additional Information\nI ran the command with the suggested flag to trace warnings:\n```bash\nset NODE_OPTIONS=--trace-warnings && npx repopack\n```\nThis is the output I received:\n```\n📦 Repopack v0.1.31\nNo custom config found at repopack.config.json or global config at C:\\Users\\<USER>\\AppData\\Local\\Repopack\\repopack.config.json.\nYou can add a config file for additional settings. Please check https://github.com/yamadashy/repopack for more information.\n⠸ Packing files...\n(node:11652) MaxPerformanceEntryBufferExceededWarning: Possible perf_hooks memory leak detected. 1000001 mark entries added to the global performance entry buffer. Use performance.clearMarks to clear the buffer.\nat bufferUserTiming (node:internal/perf/observe:422:15)\nat mark (node:internal/perf/usertiming:160:3)\nat Performance.mark (node:internal/perf/performance:123:12)\nat SecretLintProfiler.mark (file:///C:/Users/<USER>/AppData/Local/npm-cache/_npx/843b8f871cabf3ea/node_modules/@secretlint/profiler/module/index.js:41:23)\nat file:///C:/Users/<USER>/AppData/Local/npm-cache/_npx/843b8f871cabf3ea/node_modules/@secretlint/core/module/RunningEvents.js:50:36\nat async Promise.all (index 8)\n```\n### System Information\n- **Repopack Version**: v0.1.31 (started happening with v0.1.26)"}, {"issue_number": "56", "issue_url": "https://github.com/yamadashy/repomix/issues/56", "state": "closed", "title": "V0.1.27 fails on Termux on Android", "content": "```\n$ repopack\n📦 Repopack v0.1.27\n✖ Error during packing\nUnexpected error: Expected `concurrency` to be an integer from 1 and up or `Infinity`, got `0` (number)\nFor more help, please visit: https://github.com/yamadashy/repopack/issues\n```\nReviewing releases, concurrency was introduced a few versions ago in v0.1.24. Installing v0.1.23 results in a successful repopack.\n```\n$ repopack\n📦 Repopack v0.1.23\n✔ Packing completed successfully!\n```"}, {"issue_number": "55", "issue_url": "https://github.com/yamadashy/repomix/issues/55", "state": "closed", "title": "bug: Large chunks of code missing with remove comments on", "content": "this was happening before update to 0.1.27.\nI don't really want to post the code here, so if you want to message me or something, then I can send you the files."}, {"issue_number": "51", "issue_url": "https://github.com/yamadashy/repomix/issues/51", "state": "closed", "title": "Feature request: global configuration equivalent of repopack.config.json", "content": "Hi,\nI have been using repopack and really like the configurability. However, I typically use the same repopack config settings for most projects and it would be nice if I could have a global `repopack.config.json` in `~/.config/` or something that would take effect if a local config file is not present.  It didn't seem like this was already possible, or at least I did not see anything to that effect. Any help would be appreciated. Thank you!"}, {"issue_number": "43", "issue_url": "https://github.com/yamadashy/repomix/issues/43", "state": "open", "title": "Feature request: AI pre process", "content": "Hi! Thanks for this awesome project.\nI have a great time using it, but as the project scales up, the output file for ai becomes lazy,\nI want to suggest that add a option for using own AI API key to introduce/pre-process the output files,\nand it can depend on a prompt file that was pre-defined to make the output file more understandable for ai.\nI know this request is a heavy job and it might have multiple potential issues, it's unnecessary to make this feature because this\nproject is already good!\nThank you for viewing this issue\nBest wishes,\n<PERSON>nn"}, {"issue_number": "40", "issue_url": "https://github.com/yamadashy/repomix/issues/40", "state": "open", "title": "For consideration: Suggest LLM_RULES.md file in your readme", "content": "Hey,\nGreat utility, many thanks!\nSomething I've been doing that you may want to include in the readme is the suggestion that people bake a set of repo specific rules into their repos.\nI have a LLM_RULES.md file that I have at the base of my repo.  It does things like prompt the LLM to use British English, UK date formats as well as other things like provision of some specifications on certain approaches for storybook, component structure etc.\nCheers\nJon"}, {"issue_number": "36", "issue_url": "https://github.com/yamadashy/repomix/issues/36", "state": "open", "title": "Various compression levels to reduce token count", "content": "First of all, love this project idea and have been using it very successfully with the new \"Projects\" feature in <PERSON>.\nGeneral idea for this enhancement would be flags to reduce token count/cost through minification or even obfuscation. It's quite easy to manipulate artifacts in <PERSON> etc - let folks try and push the limits easier. Going to work on this tomorrow/this week, but curious if anyone has considered/tried yet.\nOne option I'm considering: Build a built-in algo comparison method that runs popular \"compression\" algos against tokenizer measurements - retaining agnostic language support."}, {"issue_number": "34", "issue_url": "https://github.com/yamadashy/repomix/issues/34", "state": "closed", "title": "Honour .gitignore in subfolders as well", "content": "Seems like the script is only looking into a global gitignore (on root folder), but if there are specific gitignore files in the subfolders they're ignored."}, {"issue_number": "22", "issue_url": "https://github.com/yamadashy/repomix/issues/22", "state": "closed", "title": "feat: add `include` filters", "content": "Though `ignore` filters are useful, it would be helpful to have the opposite. For example pack only \"*.md\" files"}, {"issue_number": "18", "issue_url": "https://github.com/yamadashy/repomix/issues/18", "state": "closed", "title": "Dependency Dashboard", "content": "This issue lists Renovate updates and detected dependencies. Read the [Dependency Dashboard](https://docs.renovatebot.com/key-concepts/dashboard/) docs to learn more.\n## Awaiting Schedule\nThese updates are awaiting their schedule. Click on a checkbox to get an update now.\n- [ ] <!-- unschedule-branch=renovate/all-minor-patch -->chore(deps): update all non-major dependencies (`@eslint/js`, `@types/eslint`, `@types/node`, `@typescript-eslint/eslint-plugin`, `@typescript-eslint/parser`, `@vitest/coverage-v8`, `commander`, `eslint-plugin-prettier`, `ignore`, `log-update`, `rimraf`, `vite`, `vitest`, `yarn`)\n- [ ] <!-- unschedule-branch=renovate/eslint-9.x -->chore(deps): update dependency @types/eslint to v9\n- [ ] <!-- unschedule-branch=renovate/major-eslint-monorepo -->chore(deps): update dependency eslint to v9\n- [ ] <!-- unschedule-branch=renovate/rimraf-6.x -->chore(deps): update dependency rimraf to v6\n- [ ] <!-- unschedule-branch=renovate/typescript-5.x -->chore(deps): update dependency typescript to v5\n- [ ] <!-- unschedule-branch=renovate/vite-5.x -->chore(deps): update dependency vite to v5\n- [ ] <!-- unschedule-branch=renovate/cli-spinners-3.x -->fix(deps): update dependency cli-spinners to v3\n- [ ] <!-- unschedule-branch=renovate/commander-12.x -->fix(deps): update dependency commander to v12\n## Detected dependencies\n<blockquote>\n</details>\n</blockquote>\n</details>\n<blockquote>\n<details><summary>.github/workflows/test.yml</summary>\n- `actions/checkout v4`\n- `actions/setup-node v4`\n- `actions/checkout v4`\n- `actions/setup-node v4`\n- `ubuntu 22.04`\n</details>\n</blockquote>\n</details>\n<blockquote>\n</details>\n</blockquote>\n</details>\n<blockquote>\n- `@secretlint/core ^8.2.4`\n- `@secretlint/secretlint-rule-preset-recommend ^8.2.4`\n- `cli-spinners ^2.9.2`\n- `commander ^7.1.0`\n- `iconv-lite ^0.6.3`\n- `ignore ^5.2.0`\n- `istextorbinary ^9.5.0`\n- `jschardet ^3.1.3`\n- `log-update ^6.0.0`\n- `picocolors ^1.0.1`\n- `strip-comments ^2.0.1`\n- `@eslint/js ^9.7.0`\n- `@types/eslint ~8.56.10`\n- `@types/eslint__js ~8.42.3`\n- `@types/eslint-config-prettier ~6.11.3`\n- `@types/node ^20.14.10`\n- `@types/strip-comments ^2.0.4`\n- `@typescript-eslint/eslint-plugin ^7.16.0`\n- `@typescript-eslint/parser ^7.16.0`\n- `@vitest/coverage-v8 ^2.0.2`\n- `eslint ^8.57.0`\n- `eslint-config-prettier ^9.1.0`\n- `eslint-plugin-import ^2.29.1`\n- `eslint-plugin-prettier ^5.1.3`\n- `rimraf ^5.0.7`\n- `secretlint ^8.2.4`\n- `typescript ^4.9.5`\n- `vite ^4.1.4`\n- `vitest ^2.0.2`\n- `yarn >=1.0.0`\n</details>\n</blockquote>\n</details>\n---\n- [ ] <!-- manual job -->Check this box to trigger a request for Renovate to run again on this repository"}, {"issue_number": "17", "issue_url": "https://github.com/yamadashy/repomix/issues/17", "state": "closed", "title": "Action Required: Fix Renovate Configuration", "content": "There is an error with this repository's Renovate configuration that needs to be fixed. As a precaution, Renovate will stop PRs until it is resolved.\nError type: Cannot find preset's package (schedule:earlySaturdays)"}, {"issue_number": "12", "issue_url": "https://github.com/yamadashy/repomix/issues/12", "state": "closed", "title": "Feat: Propose .repopackignore file to override .gitignore for repopack bundles", "content": "Hello,\nFirst, thank you for this very useful project!\nI'd like to suggest a feature that could enhance repopack's flexibility.\n### Current situation:\nI have a file \"claude-custom-instructions.txt\" that I want to include in my repopack bundle, but it's listed in my .gitignore file. Currently, to include this file, I need to set `ignore.useDefaultPatterns = false` and then manually add all the patterns I want to ignore in `ignore.customPatterns`. This works, but it's a bit cumbersome.\n### Feature proposal:\nIntroduce a new file called `.repopackignore` that would override .gitignore settings specifically for repopack bundles when present. Here's how it could work:\n1. If a `.repopackignore` file exists in the repository, repopack would use it instead of .gitignore.\n2. The syntax would be similar to .gitignore, allowing users to easily specify which files to include or exclude from the bundle.\nBenefits:\n- Simpler management of bundle-specific ignores\n- Maintains separation between git ignore rules and repopack bundle rules\n- Allows users to easily include files in repopack bundles that are git-ignored\nI'd appreciate your thoughts on this idea and whether it aligns with the project's goals.\nThank you again !"}, {"issue_number": "8", "issue_url": "https://github.com/yamadashy/repomix/issues/8", "state": "closed", "title": "feat: Option to prexfix line number", "content": "Add option to prefix each line in the output file with the line number from the true file.\nI think this would be useful as you can add to your prompt something like: \"Show the code changes with the line number, and include one line before to easier locate the change\".\nI find this saves a lot of time on larger projects"}, {"issue_number": "2", "issue_url": "https://github.com/yamadashy/repomix/issues/2", "state": "closed", "title": "feat: <PERSON><PERSON><PERSON> comments", "content": "Would removing comments based on the language be beneficial?\nSo for Python remove anything that starts with `# `"}, {"issue_number": "1", "issue_url": "https://github.com/yamadashy/repomix/issues/1", "state": "closed", "title": "Issue with Ignore", "content": "Hello,\nThis seems like a really great start to the package, so nice work! Any suggestions on how to ignore all SVG, CSS, etc. files? That does not appear to be working as I would expect.\n![image](https://github.com/user-attachments/assets/a4fbacdc-0f9a-4e45-a952-17366a6d4473)\n![image](https://github.com/user-attachments/assets/df29e427-4837-4c17-9380-4e322d276a84)\nThanks,"}]
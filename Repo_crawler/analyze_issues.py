#!/usr/bin/env python3
"""
Issue分析脚本
分析哪些Issue有PR，哪些没有，以及原因
"""

import json
import sys
from collections import Counter

def analyze_issues(json_file):
    """分析Issues JSON文件"""
    try:
        with open(json_file, 'r', encoding='utf-8') as f:
            issues = json.load(f)
    except FileNotFoundError:
        print(f"❌ 文件不存在: {json_file}")
        return
    except json.JSONDecodeError:
        print(f"❌ JSON格式错误: {json_file}")
        return
    
    print(f"📊 Issue分析报告")
    print("=" * 50)
    print(f"总Issue数: {len(issues)}")
    
    # 统计有PR的Issue
    with_pr = [issue for issue in issues if issue['pr_number']]
    without_pr = [issue for issue in issues if not issue['pr_number']]
    
    print(f"有PR的Issue: {len(with_pr)} ({len(with_pr)/len(issues)*100:.1f}%)")
    print(f"无PR的Issue: {len(without_pr)} ({len(without_pr)/len(issues)*100:.1f}%)")
    
    # 分析标题关键词
    print(f"\n🔍 无PR的Issue标题分析:")
    print("-" * 30)
    
    # 常见的非bug关键词
    non_bug_keywords = [
        'question', 'help', 'how to', 'documentation', 'docs',
        'feature request', 'enhancement', 'suggestion',
        'duplicate', 'invalid', 'wontfix', 'by design',
        'discussion', 'feedback', 'opinion'
    ]
    
    # 分类无PR的Issue
    categories = {
        'question': [],
        'feature_request': [],
        'duplicate': [],
        'documentation': [],
        'other': []
    }
    
    for issue in without_pr:
        title = issue['title'].lower()
        content = issue['content'].lower()
        
        categorized = False
        
        # 问题咨询
        if any(keyword in title or keyword in content for keyword in ['question', 'help', 'how to', '?']):
            categories['question'].append(issue)
            categorized = True
        
        # 功能请求
        elif any(keyword in title or keyword in content for keyword in ['feature', 'enhancement', 'request', 'suggestion']):
            categories['feature_request'].append(issue)
            categorized = True
        
        # 重复/无效
        elif any(keyword in title or keyword in content for keyword in ['duplicate', 'invalid', 'wontfix']):
            categories['duplicate'].append(issue)
            categorized = True
        
        # 文档
        elif any(keyword in title or keyword in content for keyword in ['doc', 'documentation', 'readme']):
            categories['documentation'].append(issue)
            categorized = True
        
        if not categorized:
            categories['other'].append(issue)
    
    # 显示分类结果
    for category, issues_list in categories.items():
        if issues_list:
            print(f"\n📋 {category.replace('_', ' ').title()}: {len(issues_list)} 个")
            for issue in issues_list[:3]:  # 显示前3个例子
                print(f"   - #{issue['issue_number']}: {issue['title'][:60]}...")
            if len(issues_list) > 3:
                print(f"   ... 还有 {len(issues_list) - 3} 个")
    
    # 分析有PR的Issue
    if with_pr:
        print(f"\n✅ 有PR的Issue示例:")
        print("-" * 30)
        for issue in with_pr[:5]:
            print(f"#{issue['issue_number']}: {issue['title'][:50]}...")
            print(f"   PR: #{issue['pr_number']} | 合并时间: {issue['pr_merged_time']}")
            print()

def show_recommendations():
    """显示改进建议"""
    print(f"\n💡 提高PR匹配率的建议:")
    print("=" * 50)
    print("1. 扩展PR查找策略:")
    print("   - 搜索Issue标题中的关键词")
    print("   - 查找相似时间的PR")
    print("   - 分析commit message")
    
    print("\n2. 过滤策略:")
    print("   - 只分析bug类型的Issue")
    print("   - 排除question/feature request")
    print("   - 关注有具体错误信息的Issue")
    
    print("\n3. 数据质量:")
    print("   - 选择活跃维护的项目")
    print("   - 关注最近的Issue")
    print("   - 检查项目的Issue管理规范")

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("用法: python analyze_issues.py <json_file>")
        print("例如: python analyze_issues.py test_with_token.json")
        sys.exit(1)
    
    json_file = sys.argv[1]
    analyze_issues(json_file)
    show_recommendations()

[{"issue_number": "4117", "issue_url": "https://github.com/octocat/Hello-World/issues/4117", "state": "closed", "title": "새 이슈 등록하기", "content": "내 이름 추가하기", "created_at": "2025-07-13T13:47:36Z", "pr_merged_time": "", "pr_number": "4116", "pr_url": "https://github.com/octocat/Hello-World/pull/4116"}, {"issue_number": "4108", "issue_url": "https://github.com/octocat/Hello-World/issues/4108", "state": "closed", "title": "Exemple d'issue de démonstration", "content": "Ceci est une issue de démonstration générée automatiquement.\nElle sert à montrer comment appeler l’API GitHub via un assistant.\n### Objectifs à atteindre\n- Tester l’intégration API\n- Générer une issue sur un dépôt GitHub\n### Cas d'utilisation\nUn développeur souhaite créer une issue rapidement sans se connecter à GitHub.\n### Spécifications techniques\n- Utilisation de l’endpoint `createGithubIssue`\n- <PERSON><PERSON><PERSON><PERSON><PERSON> : `octocat`\n- <PERSON><PERSON><PERSON><PERSON><PERSON> : `Hello-World`", "created_at": "2025-07-09T12:25:30Z", "pr_merged_time": "", "pr_number": "", "pr_url": ""}, {"issue_number": "4107", "issue_url": "https://github.com/octocat/Hello-World/issues/4107", "state": "closed", "title": "Démo : exemple de création d'une issue", "content": "Ceci est une issue de démonstration pour tester l'opération `createGithubIssue` via l'API.\nL'objectif est de vérifier que la création automatique d'une issue fonctionne correctement dans un dépôt GitHub.\n### Objectifs à atteindre\n- Vérifier la connectivité avec l'API GitHub\n- Créer une issue avec un format Markdown structuré\n### Cas d'utilisation\nEn tant que développeur, je souhaite générer des issues automatiquement à partir d'un assistant intelligent pour gagner du temps et structurer mes tâches efficacement.\n### Spécifications techniques\n- Utilisation de l'API `api.github.com`\n- Utilisation de l'opération `createGithubIssue` du plugin", "created_at": "2025-07-09T12:14:45Z", "pr_merged_time": "", "pr_number": "", "pr_url": ""}, {"issue_number": "4087", "issue_url": "https://github.com/octocat/Hello-World/issues/4087", "state": "closed", "title": "Test Report - Image Embedding Validation", "content": "", "created_at": "2025-07-02T22:15:21Z", "pr_merged_time": "", "pr_number": "", "pr_url": ""}, {"issue_number": "4056", "issue_url": "https://github.com/octocat/Hello-World/issues/4056", "state": "closed", "title": "test", "content": "test", "created_at": "2025-06-18T18:29:26Z", "pr_merged_time": "", "pr_number": "", "pr_url": ""}]
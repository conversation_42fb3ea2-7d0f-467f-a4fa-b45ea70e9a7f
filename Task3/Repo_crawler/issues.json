[{"issue_number": "311", "issue_url": "https://github.com/yamadashy/repomix/issues/311", "state": "closed", "title": "Add URL parsing for branch/commit information", "content": "Add functionality to parse branch, tag, and commit information from repository URLs.\nDifferent repository URL formats need to be supported:\n- `owner/repo`\n- `https://github.com/owner/repo`\n- `https://github.com/owner/repo/tree/branch`\n- `https://github.com/owner/repo/commit/hash`\nFeature was requested by user @gaby\nref\nhttps://github.com/yamadashy/repomix/issues/219#issuecomment-2573359838", "created_at": "2025-01-25T06:46:23Z", "pr_merged_time": "2025-02-07T15:26:55Z", "pr_number": "335", "pr_url": "https://github.com/yamadashy/repomix/pull/335"}, {"issue_number": "310", "issue_url": "https://github.com/yamadashy/repomix/issues/310", "state": "closed", "title": "Add zip/folder upload functionality to website", "content": "This feature will allow users to upload zip archives or folders directly through the Repomix website (repomix.com), providing an alternative to the CLI tool.\nFeature was requested by user @huy-trn\nref\nhttps://github.com/yamadashy/repomix/issues/219#issuecomment-2571282619", "created_at": "2025-01-25T06:44:07Z", "pr_merged_time": "2025-02-21T15:27:05Z", "pr_number": "353", "pr_url": "https://github.com/yamadashy/repomix/pull/353"}, {"issue_number": "274", "issue_url": "https://github.com/yamadashy/repomix/issues/274", "state": "closed", "title": "SyntaxError: Unexpected token 'with' when running repomix", "content": "When running repomix, the command fails with a SyntaxError related to the with keyword. This suggests a compatibility issue with newer Node.js versions or ESM modules.\n\nSteps: `npx repomix`\nError Output:\n```\nFatal Error: {\nname: 'SyntaxError',\n\nExpected: '    at ESMLoader.moduleStrategy (node:internal/modules/esm/translators:119:18)\\n' +\n'    at ESMLoader.moduleProvider (node:internal/modules/esm/loader:468:14)'\n}\n```", "created_at": "2025-01-06T23:27:41Z", "pr_merged_time": "2024-12-31T13:38:06Z", "pr_number": "234", "pr_url": "https://github.com/yamadashy/repomix/pull/234"}, {"issue_number": "221", "issue_url": "https://github.com/yamadashy/repomix/issues/221", "state": "closed", "title": "Add support for <PERSON><PERSON>", "content": "Add support for running `repomix` using Docker.", "created_at": "2024-12-28T18:40:54Z", "pr_merged_time": "2024-12-31T11:40:21Z", "pr_number": "233", "pr_url": "https://github.com/yamadashy/repomix/pull/233"}, {"issue_number": "209", "issue_url": "https://github.com/yamadashy/repomix/issues/209", "state": "closed", "title": "Support comments in `repomix.config.json`", "content": "When dealing with a large codebase, people need to rapidly select which file paths to ignore and which to **include** in order to achieve acceptable token counts. The ability to add comments to `repomix.config.json` is critical.", "created_at": "2024-12-20T07:57:21Z", "pr_merged_time": "2024-12-29T12:30:44Z", "pr_number": "214", "pr_url": "https://github.com/yamadashy/repomix/pull/214"}, {"issue_number": "195", "issue_url": "https://github.com/yamadashy/repomix/issues/195", "state": "closed", "title": "Feature Request: Add Branch Option for Remote Repository Packing", "content": "Hello @yamadashy ,\nI'm new to this project but I'm willing to help creating a PR. I think it would be beneficial to add an option to select a specific commit ID or branch when packing a remote repository.\nCurrent Behavior:\nWhen packing a remote repository, the process uses the default branch.\nProposed Feature:\nAdd a branch option to the packing process, allowing users to select a specific commit ID or branch to pack.\nPotential Implementation:\n```\nrepomix --remote https://github.com/yamadashy/repomix --branch master\n```", "created_at": "2024-12-09T05:44:44Z", "pr_merged_time": "2024-12-31T07:24:38Z", "pr_number": "212", "pr_url": "https://github.com/yamadashy/repomix/pull/212"}, {"issue_number": "125", "issue_url": "https://github.com/yamadashy/repomix/issues/125", "state": "closed", "title": "Refactor: Centralize renderContext & style generation logic for output styles", "content": "The renderContext was repeated in all three style generation files including the logic to generate\n<code>/src/core/output/outputStyles/plainStyle.ts</code>\n<code>/src/core/output/outputStyles/xmlStyle.ts</code>\n<code>/src/core/output/outputStyles/markdownStyle.ts</code>\n``` javascript\nexport const generateMarkdownStyle = (outputGeneratorContext: OutputGeneratorContext) => {\nconst template = Handlebars.compile(markdownTemplate);\nconst renderContext = {\ngenerationHeader: generateHeader(outputGeneratorContext.generationDate),\nsummaryUsageGuidelines: generateSummaryUsageGuidelines(\noutputGeneratorContext.config,\noutputGeneratorContext.instruction,\n),\nsummaryNotes: generateSummaryNotes(outputGeneratorContext.config),\nsummaryAdditionalInfo: generateSummaryAdditionalInfo(),\nheaderText: outputGeneratorContext.config.output.headerText,\ninstruction: outputGeneratorContext.instruction,\ntreeString: outputGeneratorContext.treeString,\nprocessedFiles: outputGeneratorContext.processedFiles,\n};\nreturn `${template(renderContext).trim()}\\n`;\n};\n```\n###  Proposed Solution\nMove the common logic in these files into a shared utility file. This will make it easier to maintain as you add new styles and also add new parameters for renderContext in the future", "created_at": "2024-10-14T16:27:25Z", "pr_merged_time": "", "pr_number": "130", "pr_url": "https://github.com/yamadashy/repomix/pull/130"}, {"issue_number": "112", "issue_url": "https://github.com/yamadashy/repomix/issues/112", "state": "closed", "title": "Optimize File Manipulation Logic in `fileManipulate.ts`", "content": "The current implementation of the file manipulation logic in `repopack/src/core/file/fileManipulate.ts` can be optimized for performance. Several areas, such as regex handling, string trimming, and quote matching, can be made more efficient.\n**Proposed Optimizations**:\n1. **Improve Docstring Removal Regex**:\nSimplify and optimize the regex used for removing docstrings in `PythonManipulator`.\n2. **Optimize Quote Matching in `removeHashComments`**:\nEnhance the logic to reduce redundant `slice` operations and improve performance in detecting quoted strings.\n3. **Refactor `searchInPairs` Binary Search**:\nStreamline the binary search function for checking if a hash is inside a string literal.\n4. **Enhance `rtrimLines` Function**:\nUse a more efficient method to trim trailing spaces and tabs from lines.\n5. **Lazy Instantiate Manipulators**:\nInstantiate manipulators in the `manipulators` object only when needed to improve memory usage.\n---\n\nExpected: - Faster file manipulation for large files.\n- Better memory efficiency.\n- Cleaner, more maintainable code.\nI'd like to contribute to optimizing the file manipulation logic in `fileManipulate.ts` as described in the issue. Could you please assign it to me so I can start working on it?\nThanks!", "created_at": "2024-10-08T18:29:49Z", "pr_merged_time": "2024-09-22T15:14:49Z", "pr_number": "81", "pr_url": "https://github.com/yamadashy/repomix/pull/81"}, {"issue_number": "106", "issue_url": "https://github.com/yamadashy/repomix/issues/106", "state": "closed", "title": "Minor Grammatical Issues in Repopack Contributor Covenant Code of Conduct", "content": "This pull request addresses a couple of minor grammatical issues found in the **Repopack Contributor Covenant Code of Conduct**. These changes aim to enhance the clarity and readability of the document, ensuring that the language is accurate and consistent.\n### Changes Made:\n1. **Verb Correction**\n2. **Clarification in \"Examples of Unacceptable Behavior\"**\n---\n### Examples:\n1. **Before the fix (Pledge Section)**:\n2. **Before the fix (Unacceptable Behavior Section)**:\n3. and many more...", "created_at": "2024-10-07T12:37:07Z", "pr_merged_time": "2024-10-08T14:08:39Z", "pr_number": "109", "pr_url": "https://github.com/yamadashy/repomix/pull/109"}, {"issue_number": "63", "issue_url": "https://github.com/yamadashy/repomix/issues/63", "state": "closed", "title": "Infinite Loading and Memory Leak", "content": "---\n\nSteps: 1. Run Repopack with the following command:\n```\nnpx repopack\n```\n2. Wait for the packing process to complete.\n3. Observe that the process runs without completing and without producing content.\n4. When aborted with `CTRL + C`, an empty Repopack file is produced.\n\nExpected: Repopack should successfully complete the packaging process and generate the packed files.\n\nActual: - The process hangs indefinitely.\n- An empty Repopack file is generated when manually aborted.\n### Additional Information\nI ran the command with the suggested flag to trace warnings:\n```bash\nset NODE_OPTIONS=--trace-warnings && npx repopack\n```\nThis is the output I received:\n```\n📦 Repopack v0.1.31\nNo custom config found at repopack.config.json or global config at C:\\Users\\<USER>\\AppData\\Local\\Repopack\\repopack.config.json.\nYou can add a config file for additional settings. Please check https://github.com/yamadashy/repopack for more information.\n⠸ Packing files...\n(node:11652) MaxPerformanceEntryBufferExceededWarning: Possible perf_hooks memory leak detected. 1000001 mark entries added to the global performance entry buffer. Use performance.clearMarks to clear the buffer.\nat bufferUserTiming (node:internal/perf/observe:422:15)\nat mark (node:internal/perf/usertiming:160:3)\nat Performance.mark (node:internal/perf/performance:123:12)\nat SecretLintProfiler.mark (file:///C:/Users/<USER>/AppData/Local/npm-cache/_npx/843b8f871cabf3ea/node_modules/@secretlint/profiler/module/index.js:41:23)\nat file:///C:/Users/<USER>/AppData/Local/npm-cache/_npx/843b8f871cabf3ea/node_modules/@secretlint/core/module/RunningEvents.js:50:36\nat async Promise.all (index 8)\n```\n### System Information\n- **Repopack Version**: v0.1.31 (started happening with v0.1.26)", "created_at": "2024-08-28T09:51:24Z", "pr_merged_time": "2024-09-02T14:26:42Z", "pr_number": "68", "pr_url": "https://github.com/yamadashy/repomix/pull/68"}, {"issue_number": "55", "issue_url": "https://github.com/yamadashy/repomix/issues/55", "state": "closed", "title": "bug: Large chunks of code missing with remove comments on", "content": "this was happening before update to 0.1.27.\nI don't really want to post the code here, so if you want to message me or something, then I can send you the files.", "created_at": "2024-08-21T17:17:13Z", "pr_merged_time": "2024-09-22T15:14:49Z", "pr_number": "81", "pr_url": "https://github.com/yamadashy/repomix/pull/81"}, {"issue_number": "51", "issue_url": "https://github.com/yamadashy/repomix/issues/51", "state": "closed", "title": "Feature request: global configuration equivalent of repopack.config.json", "content": "Hi,\nI have been using repopack and really like the configurability. However, I typically use the same repopack config settings for most projects and it would be nice if I could have a global `repopack.config.json` in `~/.config/` or something that would take effect if a local config file is not present.  It didn't seem like this was already possible, or at least I did not see anything to that effect. Any help would be appreciated. Thank you!", "created_at": "2024-08-14T18:33:24Z", "pr_merged_time": "2024-08-16T16:41:24Z", "pr_number": "52", "pr_url": "https://github.com/yamadashy/repomix/pull/52"}, {"issue_number": "34", "issue_url": "https://github.com/yamadashy/repomix/issues/34", "state": "closed", "title": "Honour .gitignore in subfolders as well", "content": "Seems like the script is only looking into a global gitignore (on root folder), but if there are specific gitignore files in the subfolders they're ignored.", "created_at": "2024-08-04T10:32:24Z", "pr_merged_time": "2024-08-04T12:47:02Z", "pr_number": "35", "pr_url": "https://github.com/yamadashy/repomix/pull/35"}, {"issue_number": "22", "issue_url": "https://github.com/yamadashy/repomix/issues/22", "state": "closed", "title": "feat: add `include` filters", "content": "Though `ignore` filters are useful, it would be helpful to have the opposite. For example pack only \"*.md\" files", "created_at": "2024-07-31T14:56:02Z", "pr_merged_time": "2024-08-03T17:50:14Z", "pr_number": "30", "pr_url": "https://github.com/yamadashy/repomix/pull/30"}, {"issue_number": "12", "issue_url": "https://github.com/yamadashy/repomix/issues/12", "state": "closed", "title": "Feat: Propose .repopackignore file to override .gitignore for repopack bundles", "content": "Hello,\nFirst, thank you for this very useful project!\nI'd like to suggest a feature that could enhance repopack's flexibility.\n### Current situation:\nI have a file \"claude-custom-instructions.txt\" that I want to include in my repopack bundle, but it's listed in my .gitignore file. Currently, to include this file, I need to set `ignore.useDefaultPatterns = false` and then manually add all the patterns I want to ignore in `ignore.customPatterns`. This works, but it's a bit cumbersome.\n### Feature proposal:\nIntroduce a new file called `.repopackignore` that would override .gitignore settings specifically for repopack bundles when present. Here's how it could work:\n1. If a `.repopackignore` file exists in the repository, repopack would use it instead of .gitignore.\n2. The syntax would be similar to .gitignore, allowing users to easily specify which files to include or exclude from the bundle.\nBenefits:\n- Simpler management of bundle-specific ignores\n- Maintains separation between git ignore rules and repopack bundle rules\n- Allows users to easily include files in repopack bundles that are git-ignored\nI'd appreciate your thoughts on this idea and whether it aligns with the project's goals.\nThank you again !", "created_at": "2024-07-26T17:26:09Z", "pr_merged_time": "2024-07-27T14:13:32Z", "pr_number": "13", "pr_url": "https://github.com/yamadashy/repomix/pull/13"}, {"issue_number": "2", "issue_url": "https://github.com/yamadashy/repomix/issues/2", "state": "closed", "title": "feat: <PERSON><PERSON><PERSON> comments", "content": "Would removing comments based on the language be beneficial?\nSo for Python remove anything that starts with `# `", "created_at": "2024-07-20T16:41:45Z", "pr_merged_time": "2024-07-27T05:24:14Z", "pr_number": "14", "pr_url": "https://github.com/yamadashy/repomix/pull/14"}, {"issue_number": "1", "issue_url": "https://github.com/yamadashy/repomix/issues/1", "state": "closed", "title": "Issue with Ignore", "content": "Hello,\nThis seems like a really great start to the package, so nice work! Any suggestions on how to ignore all SVG, CSS, etc. files? That does not appear to be working as I would expect.\n![image](https://github.com/user-attachments/assets/a4fbacdc-0f9a-4e45-a952-17366a6d4473)\n![image](https://github.com/user-attachments/assets/df29e427-4837-4c17-9380-4e322d276a84)\nThanks,", "created_at": "2024-07-18T14:06:52Z", "pr_merged_time": "2024-07-21T05:41:41Z", "pr_number": "3", "pr_url": "https://github.com/yamadashy/repomix/pull/3"}]
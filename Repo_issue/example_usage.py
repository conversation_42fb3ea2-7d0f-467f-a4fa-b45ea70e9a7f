#!/usr/bin/env python3
"""
GitHub Issue主题爬虫使用示例
"""

from github_issue_crawler import GitHubIssueTopicCrawler
import json

def example_basic_usage():
    """基本使用示例"""
    print("=== 基本使用示例 ===")

    crawler = GitHubIssueTopicCrawler()

    # 爬取一个小型项目的Issue主题
    repo_url = "https://github.com/octocat/Hello-World"

    try:
        issues_topics = crawler.crawl_repository_topics(repo_url, "example_topics.csv", max_issues=10)
        print(f"成功爬取 {len(issues_topics)} 个Issue主题")

        # 显示前几个Issue的分割信息
        for i, topic in enumerate(issues_topics[:3]):
            print(f"\nIssue {i+1}: {topic['title']}")
            print(f"  描述: {topic['description'][:100]}...")
            print(f"  重现步骤: {topic['steps_to_reproduce'][:50]}...")
            print(f"  期望行为: {topic['expected_behavior'][:50]}...")
            print(f"  实际行为: {topic['actual_behavior'][:50]}...")
            print(f"  环境信息: {topic['environment'][:50]}...")

    except Exception as e:
        print(f"错误: {e}")

def example_with_token():
    """使用Token的示例"""
    print("\n=== 使用Token的示例 ===")
    
    # 从环境变量或配置文件读取Token
    import os
    token = os.getenv('GITHUB_TOKEN')
    
    if not token:
        print("请设置GITHUB_TOKEN环境变量")
        return
    
    crawler = GitHubIssueCrawler(token=token)
    
    # 爬取一个较大项目的Issues
    repo_url = "https://github.com/microsoft/vscode"
    
    try:
        issues = crawler.crawl_repository(repo_url, "vscode_issues.csv")
        print(f"成功爬取 {len(issues)} 个Issues")
        
    except Exception as e:
        print(f"错误: {e}")

def example_custom_processing():
    """自定义处理示例"""
    print("\n=== 自定义处理示例 ===")
    
    crawler = GitHubIssueCrawler()
    
    # 只获取Issues，不保存到文件
    repo_owner, repo_name = "octocat", "Hello-World"
    
    try:
        issues = crawler.get_issues(repo_owner, repo_name, state="closed")
        
        # 统计分析
        bug_issues = []
        feature_issues = []
        
        for issue in issues:
            title = issue['title'].lower()
            labels = [label['name'].lower() for label in issue.get('labels', [])]
            
            if 'bug' in labels or 'bug' in title:
                bug_issues.append(issue)
            elif 'feature' in labels or 'enhancement' in labels:
                feature_issues.append(issue)
        
        print(f"Bug Issues: {len(bug_issues)}")
        print(f"Feature Issues: {len(feature_issues)}")
        print(f"Other Issues: {len(issues) - len(bug_issues) - len(feature_issues)}")
        
        # 保存分类结果
        with open("issue_analysis.json", "w", encoding="utf-8") as f:
            json.dump({
                "total_issues": len(issues),
                "bug_count": len(bug_issues),
                "feature_count": len(feature_issues),
                "bug_titles": [issue['title'] for issue in bug_issues[:10]],
                "feature_titles": [issue['title'] for issue in feature_issues[:10]]
            }, f, indent=2, ensure_ascii=False)
        
        print("分析结果已保存到 issue_analysis.json")
        
    except Exception as e:
        print(f"错误: {e}")

def example_batch_crawling():
    """批量爬取多个仓库"""
    print("\n=== 批量爬取示例 ===")
    
    crawler = GitHubIssueCrawler()
    
    # 要爬取的仓库列表
    repositories = [
        "https://github.com/octocat/Hello-World",
        "https://github.com/github/gitignore",
        # 添加更多仓库...
    ]
    
    all_issues = []
    
    for repo_url in repositories:
        try:
            print(f"\n爬取仓库: {repo_url}")
            repo_owner, repo_name = crawler.get_repo_info(repo_url)
            output_file = f"{repo_owner}_{repo_name}_issues.csv"
            
            issues = crawler.crawl_repository(repo_url, output_file)
            all_issues.extend(issues)
            
        except Exception as e:
            print(f"爬取 {repo_url} 失败: {e}")
            continue
    
    print(f"\n总共爬取了 {len(all_issues)} 个Issues")
    
    # 保存汇总数据
    crawler.save_to_csv(all_issues, "all_repositories_issues.csv")

if __name__ == "__main__":
    print("GitHub Issue Crawler 使用示例")
    print("=" * 50)
    
    # 运行示例
    example_basic_usage()
    # example_with_token()  # 需要Token
    # example_custom_processing()
    # example_batch_crawling()
    
    print("\n" + "=" * 50)
    print("示例运行完成！")

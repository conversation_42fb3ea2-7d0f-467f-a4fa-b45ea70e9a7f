#!/usr/bin/env python3
"""
PR Diff分析器
分析每个Issue对应的PR的代码变更
"""

import json
import requests
import sys
import argparse
from typing import List, Dict, Optional

class PRDiffAnalyzer:
    def __init__(self, token: str):
        self.session = requests.Session()
        self.base_url = "https://api.github.com"
        
        if token:
            self.session.headers.update({
                'Authorization': f'token {token}',
                'Accept': 'application/vnd.github.v3+json'
            })
            print("✅ 使用GitHub Token")
        else:
            raise ValueError("需要GitHub Token来获取PR详细信息")
    
    def safe_api_call(self, url: str, params: dict = None) -> requests.Response:
        """安全的API调用"""
        try:
            response = self.session.get(url, params=params, timeout=30)
            if response.status_code == 200:
                return response
            elif response.status_code == 403:
                raise Exception(f"API限制: {response.status_code}")
            else:
                raise Exception(f"API错误: {response.status_code}")
        except requests.exceptions.RequestException as e:
            raise Exception(f"网络错误: {e}")
    
    def get_pr_commits(self, repo_owner: str, repo_name: str, pr_number: str) -> Dict:
        """获取PR的提交信息"""
        try:
            # 获取PR详细信息
            pr_url = f"{self.base_url}/repos/{repo_owner}/{repo_name}/pulls/{pr_number}"
            pr_response = self.safe_api_call(pr_url)
            pr_data = pr_response.json()
            
            # 获取PR的commits
            commits_url = f"{self.base_url}/repos/{repo_owner}/{repo_name}/pulls/{pr_number}/commits"
            commits_response = self.safe_api_call(commits_url)
            commits = commits_response.json()
            
            if not commits:
                return {
                    'before_commit': '',
                    'after_commit': '',
                    'base_sha': '',
                    'head_sha': '',
                    'merge_commit_sha': ''
                }
            
            # PR合并前的提交 (base)
            before_commit = pr_data['base']['sha']
            
            # PR合并后的提交 (merge commit 或 head)
            after_commit = pr_data.get('merge_commit_sha') or pr_data['head']['sha']
            
            return {
                'before_commit': before_commit,
                'after_commit': after_commit,
                'base_sha': pr_data['base']['sha'],
                'head_sha': pr_data['head']['sha'],
                'merge_commit_sha': pr_data.get('merge_commit_sha', '')
            }
            
        except Exception as e:
            print(f"⚠️  获取PR {pr_number} 提交信息失败: {e}")
            return {
                'before_commit': '',
                'after_commit': '',
                'base_sha': '',
                'head_sha': '',
                'merge_commit_sha': ''
            }
    
    def get_commit_diff(self, repo_owner: str, repo_name: str, base_sha: str, head_sha: str) -> Dict:
        """获取两个提交之间的diff"""
        try:
            # 使用GitHub API获取compare
            compare_url = f"{self.base_url}/repos/{repo_owner}/{repo_name}/compare/{base_sha}...{head_sha}"
            response = self.safe_api_call(compare_url)
            compare_data = response.json()
            
            # 分析文件变更
            files_changed = []
            total_additions = 0
            total_deletions = 0
            
            for file in compare_data.get('files', []):
                file_info = {
                    'filename': file['filename'],
                    'status': file['status'],  # added, modified, deleted, renamed
                    'additions': file['additions'],
                    'deletions': file['deletions'],
                    'changes': file['changes'],
                    'patch': file.get('patch', '')[:1000]  # 限制patch长度
                }
                files_changed.append(file_info)
                total_additions += file['additions']
                total_deletions += file['deletions']
            
            return {
                'total_commits': compare_data.get('total_commits', 0),
                'ahead_by': compare_data.get('ahead_by', 0),
                'behind_by': compare_data.get('behind_by', 0),
                'total_additions': total_additions,
                'total_deletions': total_deletions,
                'files_changed_count': len(files_changed),
                'files_changed': files_changed
            }
            
        except Exception as e:
            print(f"⚠️  获取diff失败: {e}")
            return {
                'total_commits': 0,
                'ahead_by': 0,
                'behind_by': 0,
                'total_additions': 0,
                'total_deletions': 0,
                'files_changed_count': 0,
                'files_changed': []
            }
    
    def analyze_issue_pr_diff(self, issue: Dict) -> Dict:
        """分析单个Issue的PR diff"""
        if not issue.get('pr_number'):
            return issue
        
        # 从URL提取仓库信息
        issue_url = issue['issue_url']
        parts = issue_url.split('/')
        repo_owner = parts[3]
        repo_name = parts[4]
        
        print(f"🔍 分析Issue #{issue['issue_number']} 的PR #{issue['pr_number']}")
        
        # 获取PR提交信息
        commits_info = self.get_pr_commits(repo_owner, repo_name, issue['pr_number'])
        
        # 获取代码diff
        if commits_info['before_commit'] and commits_info['after_commit']:
            diff_info = self.get_commit_diff(
                repo_owner, 
                repo_name, 
                commits_info['before_commit'], 
                commits_info['after_commit']
            )
        else:
            diff_info = {
                'total_commits': 0,
                'ahead_by': 0,
                'behind_by': 0,
                'total_additions': 0,
                'total_deletions': 0,
                'files_changed_count': 0,
                'files_changed': []
            }
        
        # 添加diff信息到issue
        issue_with_diff = issue.copy()
        issue_with_diff.update({
            'pr_commits': commits_info,
            'code_diff': diff_info
        })
        
        print(f"  ✅ 文件变更: {diff_info['files_changed_count']} 个")
        print(f"  📊 代码变更: +{diff_info['total_additions']} -{diff_info['total_deletions']}")
        
        return issue_with_diff
    
    def analyze_issues_file(self, input_file: str, output_file: str):
        """分析issues文件中的所有PR diff"""
        try:
            with open(input_file, 'r', encoding='utf-8') as f:
                issues = json.load(f)
        except FileNotFoundError:
            print(f"❌ 文件不存在: {input_file}")
            return
        except json.JSONDecodeError:
            print(f"❌ JSON格式错误: {input_file}")
            return
        
        print(f"📊 开始分析 {len(issues)} 个Issues的PR diff...")
        
        analyzed_issues = []
        for i, issue in enumerate(issues, 1):
            print(f"\n[{i}/{len(issues)}]", end=" ")
            analyzed_issue = self.analyze_issue_pr_diff(issue)
            analyzed_issues.append(analyzed_issue)
        
        # 保存结果
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(analyzed_issues, f, ensure_ascii=False, indent=2)
        
        print(f"\n✅ 分析完成，结果保存到: {output_file}")
        
        # 统计信息
        total_files_changed = sum(issue['code_diff']['files_changed_count'] for issue in analyzed_issues)
        total_additions = sum(issue['code_diff']['total_additions'] for issue in analyzed_issues)
        total_deletions = sum(issue['code_diff']['total_deletions'] for issue in analyzed_issues)
        
        print(f"\n📈 统计信息:")
        print(f"  总Issues: {len(analyzed_issues)}")
        print(f"  总文件变更: {total_files_changed}")
        print(f"  总代码行数: +{total_additions} -{total_deletions}")

def main():
    parser = argparse.ArgumentParser(description='PR Diff分析器')
    parser.add_argument('input_file', help='输入的issues JSON文件')
    parser.add_argument('-t', '--token', required=True, help='GitHub Token')
    parser.add_argument('-o', '--output', default='issues_with_diff.json', help='输出文件')
    
    args = parser.parse_args()
    
    try:
        analyzer = PRDiffAnalyzer(token=args.token)
        analyzer.analyze_issues_file(args.input_file, args.output)
        print("🎉 完成！")
    except Exception as e:
        print(f"❌ 错误: {e}")

if __name__ == "__main__":
    main()

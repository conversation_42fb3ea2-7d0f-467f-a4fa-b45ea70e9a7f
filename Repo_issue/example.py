#!/usr/bin/env python3
"""
使用示例
"""

from issue_crawler import GitHubIssueCrawler

def basic_example():
    """基本使用"""
    print("=== 基本使用示例 ===")
    
    crawler = GitHubIssueCrawler()
    
    # 爬取小型项目
    try:
        issues = crawler.crawl("https://github.com/octocat/Hello-World", "example.csv", max_issues=5)
        
        print(f"获取到 {len(issues)} 个Issues")
        
        # 显示前3个
        for i, issue in enumerate(issues[:3]):
            print(f"\nIssue {i+1}: {issue['title']}")
            print(f"  描述: {issue['description'][:50]}...")
            print(f"  内容: {issue['content'][:50]}...")
            
    except Exception as e:
        print(f"错误: {e}")

def analyze_example():
    """分析示例"""
    print("\n=== 分析示例 ===")
    
    crawler = GitHubIssueCrawler()
    
    try:
        issues = crawler.get_issues("microsoft", "vscode", max_issues=10)
        
        # 统计
        with_desc = sum(1 for issue in issues if issue['description'])
        with_content = sum(1 for issue in issues if issue['content'])

        print(f"总Issues: {len(issues)}")
        print(f"有描述: {with_desc}")
        print(f"有内容: {with_content}")
        
    except Exception as e:
        print(f"错误 (可能需要Token): {e}")

if __name__ == "__main__":
    basic_example()
    analyze_example()

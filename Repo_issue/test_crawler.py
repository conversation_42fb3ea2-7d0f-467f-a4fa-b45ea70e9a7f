#!/usr/bin/env python3
"""
GitHub Issue Crawler 测试脚本
"""

import os
import sys
from github_issue_crawler import GitHubIssueCrawler

def test_basic_functionality():
    """测试基本功能"""
    print("🧪 测试基本功能...")
    
    crawler = GitHubIssueCrawler()
    
    # 测试URL解析
    test_urls = [
        "https://github.com/octocat/Hello-World",
        "https://github.com/octocat/Hello-World.git",
        "https://github.com/octocat/Hello-World/",
        "https://github.com/octocat/Hello-World/issues"
    ]
    
    for url in test_urls:
        try:
            owner, name = crawler.get_repo_info(url)
            print(f"✅ {url} -> {owner}/{name}")
        except Exception as e:
            print(f"❌ {url} -> 错误: {e}")
    
    print()

def test_small_repository():
    """测试小型仓库爬取"""
    print("🧪 测试小型仓库爬取...")
    
    crawler = GitHubIssueCrawler()
    
    # 使用一个小型的测试仓库
    test_repo = "https://github.com/octocat/Hello-World"
    
    try:
        print(f"爬取仓库: {test_repo}")
        issues = crawler.crawl_repository(test_repo, "test_output.csv")
        
        print(f"✅ 成功爬取 {len(issues)} 个Issues")
        
        if issues:
            print("\n📋 前3个Issues:")
            for i, issue in enumerate(issues[:3]):
                print(f"  {i+1}. {issue['issue_title']}")
                print(f"     URL: {issue['issue_url']}")
                print(f"     修复: {issue['fixed_content'][:50]}...")
                print()
        
        # 验证CSV文件
        if os.path.exists("test_output.csv"):
            with open("test_output.csv", "r", encoding="utf-8") as f:
                lines = f.readlines()
                print(f"✅ CSV文件包含 {len(lines)-1} 行数据 (不含标题)")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    
    print()

def test_with_token():
    """测试使用Token的功能"""
    print("🧪 测试Token功能...")
    
    token = os.getenv('GITHUB_TOKEN')
    
    if not token:
        print("⚠️  未设置GITHUB_TOKEN环境变量，跳过Token测试")
        return
    
    crawler = GitHubIssueCrawler(token=token)
    
    try:
        # 测试获取仓库信息
        issues = crawler.get_issues("octocat", "Hello-World", state="all")
        print(f"✅ 使用Token成功获取 {len(issues)} 个Issues")
        
    except Exception as e:
        print(f"❌ Token测试失败: {e}")
    
    print()

def test_error_handling():
    """测试错误处理"""
    print("🧪 测试错误处理...")
    
    crawler = GitHubIssueCrawler()
    
    # 测试无效URL
    try:
        crawler.get_repo_info("invalid_url")
        print("❌ 应该抛出异常但没有")
    except ValueError:
        print("✅ 正确处理无效URL")
    except Exception as e:
        print(f"❌ 意外异常: {e}")
    
    # 测试不存在的仓库
    try:
        crawler.get_issues("nonexistent", "repository")
        print("❌ 应该抛出异常但没有")
    except Exception:
        print("✅ 正确处理不存在的仓库")
    
    print()

def cleanup_test_files():
    """清理测试文件"""
    test_files = [
        "test_output.csv",
        "example_issues.csv",
        "issue_analysis.json"
    ]
    
    for file in test_files:
        if os.path.exists(file):
            os.remove(file)
            print(f"🧹 清理文件: {file}")

def main():
    """运行所有测试"""
    print("GitHub Issue Crawler 测试套件")
    print("=" * 50)
    
    try:
        test_basic_functionality()
        test_small_repository()
        test_with_token()
        test_error_handling()
        
        print("🎉 所有测试完成！")
        
    except KeyboardInterrupt:
        print("\n⚠️  测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
    finally:
        print("\n🧹 清理测试文件...")
        cleanup_test_files()

if __name__ == "__main__":
    main()

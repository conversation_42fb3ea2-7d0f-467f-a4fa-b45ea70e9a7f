issue_number,issue_url,state,title,description,content
4117,https://github.com/octocat/Hello-World/issues/4117,closed,새 이슈 등록하기,내 이름 추가하기,
4108,https://github.com/octocat/Hello-World/issues/4108,closed,Exemple d'issue de démonstration,"Ceci est une issue de démonstration générée automatiquement.
Elle sert à montrer comment appeler l’API GitHub via un assistant.
### Objectifs à atteindre
- Tester l’intégration API
- Générer une issue sur un dépôt GitHub
### Cas d'utilisation
Un développeur souhaite créer une issue rapidement sans se connecter à GitHub.
### Spécifications techniques
- Utilisation de l’endpoint `createGithubIssue`
- Propriétaire : `octocat`
- Dépôt : `Hello-World`",
4107,https://github.com/octocat/Hello-World/issues/4107,closed,Démo : exemple de création d'une issue,"Ceci est une issue de démonstration pour tester l'opération `createGithubIssue` via l'API.
L'objectif est de vérifier que la création automatique d'une issue fonctionne correctement dans un dépôt GitHub.
### Objectifs à atteindre
- Vérifier la connectivité avec l'API GitHub
- Créer une issue avec un format Markdown structuré
### Cas d'utilisation
En tant que développeur, je souhaite générer des issues automatiquement à partir d'un assistant intelligent pour gagner du temps et structurer mes tâches efficacement.
### Spécifications techniques
- Utilisation de l'API `api.github.com`
- Utilisation de l'opération `createGithubIssue` du plugin",
4106,https://github.com/octocat/Hello-World/issues/4106,open,Enrollment form,,
4101,https://github.com/octocat/Hello-World/issues/4101,open,Dummy,Test,

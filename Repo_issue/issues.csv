issue_number,issue_url,state,title,description,content
725,https://github.com/yamadashy/repomix/issues/725,open,Run repomix and include CLI output of another tool in one step,"When vibe coding with non-agent tools (so directly with AI-Studio or chatgpt.com) it would be nice if repomix could automatically run a command line tool and add the output of this tool to the summary.
I would image something like
```
repomix -c npm install
```
This would run repomix as usual but also run the given command and include its output (preferably at the top). For example, this could generate:
I just ran `npm install` in my codebase in ${PWD}, but it `FAILED` with the following output:
```
...CLI_OUTPUT_HERE...
```
The rest of this file is a merged representation of the entire codebase, combined into a single document by Repomix.
The content has been processed where security check has been disabled.
...
This would really help to package error messages together with the latest status and current working directory.",
718,https://github.com/yamadashy/repomix/issues/718,closed,Please add to Open VSX https://open-vsx.org/,Support Cursor and other VSCode forks - deploy to the https://open-vsx.org/ marketplace please.,
714,https://github.com/yamadashy/repomix/issues/714,open,`.gitignore` is not respected,"1. Create MRE project like following:
```
$ tree
.
|-- logs
|   `-- some_logs.txt
|-- main.py
```
2. Install `uv` to the system if it's not already installed. I haven't checked without this, but I'm kinda sure it would be the same
3. Run `uvx repomix .`
```
$ uvx repomix .
📦 Repomix v0.2.9
────────────────
Total Files: 3 files
Total Characters: 20 characters
Token calculation: disabled
Output to: repomix-output.md
Security: ✔ No suspicious files detected
🔎 Security Check:
──────────────────
✔ No suspicious files detected
📈 Top 5 files by character and token count:
──────────────────────────────────────────────────
1.  main.py (11 characters)
2.  .gitignore (5 characters)
3.  logs/some_logs.txt (4 characters)
🎉 Done!
Your code repository has been successfully packaged.
```
How it should be:
```
📈 Top 5 files by character and token count:
──────────────────────────────────────────────────
1.  main.py (11 characters)
2.  .gitignore (5 characters)
```
### Usage Context
I did not configure `repomix` at any way",
713,https://github.com/yamadashy/repomix/issues/713,closed,1.0.0 not working on windows,"executing repomix leads to
Fatal Error: {
name: 'Error',
message: 'No such built-in module: node:readline/promises',
stack: 'Error [ERR_UNKNOWN_BUILTIN_MODULE]: No such built-in module: node:readline/promises\n' +
'    at new NodeError (node:internal/errors:372:5)\n' +
'    at ESMLoader.builtinStrategy (node:internal/modules/esm/translators:260:11)\n' +
'    at ESMLoader.moduleProvider (node:internal/modules/esm/loader:337:14)'
}
it was working before
installed via npm install -g repomix
### Usage Context
None",
696,https://github.com/yamadashy/repomix/issues/696,open,--compress option is not working,"Hi, I tried using the --compress option, but it threw an error with the following message. I'm currently using version 0.2.25. Is the --compress option actually supported?
```
error: unknown option '--compress'
```",
691,https://github.com/yamadashy/repomix/issues/691,open,Remote Server,"It would be great to see a remote MCP server for RepoMix in alignment with one of Anthropic's [latest announcements](https://docs.anthropic.com/en/docs/agents-and-tools/remote-mcp-servers)
Example remote server support:
```
""repomix"": {
""url"": ""https://mcp.repomix.com/mcp""
}
```
For inspiration, check out [context7](https://github.com/upstash/context) repo",
685,https://github.com/yamadashy/repomix/issues/685,open,Exclude variables in specific files,It'd be helpful to have the ability to ignore variable patterns in specific files as opposed to files wholesale.,
673,https://github.com/yamadashy/repomix/issues/673,open,False positive error with --stdout,"When running e.g.
```sh
npx -y repomix --stdout --remote https://github.com/JedWatson/classnames
```
If you capture stderr and stdout, if you check for `stderr`, it's present with ""command failed"" even if stdout is successful.
### Usage Context
None",
650,https://github.com/yamadashy/repomix/issues/650,open,simple way to only include git-tracked files,Maybe I missed this option but currently what I do is I create a custom include list that contains only files that show up in the in git tracking. And then add on some exclude patterns on top of that. It would be nice to have a way a more direct way to only include git tracked files.,
648,https://github.com/yamadashy/repomix/issues/648,closed,Read file list from stdin,"Add support for reading the file list from stdin
# Use Case
I'm using [fd](https://github.com/sharkdp/fd) and [ripgrep](https://github.com/BurntSushi/ripgrep) a lot in my day-to-day work.
It would have been nice to pipe the results of the file search to repomix:
```sh
# List all Markdown files
fd -t file -e md | repomix --stdin
# List files from ""tests"" directories
fd -t directory 'tests' | repomix --stdin
# Find TS files that contain 'MyClass' pattern using both fd and ripgrep
fd -t file -e ts -e tsx --exec-batch rg -l 'MyClass' | repomix --stdin
```
# Proposed Solution
Add a flag (`--stdin` or `--include-from-stdin`) that would enable repomix to read the list of files, directories, or globs from the piped input
# Related
#553",
636,https://github.com/yamadashy/repomix/issues/636,closed,Browser extension don't work in Edge,"Hi,
I just installed the repomix browser extension in Edge browser. I don't understand why, but when I click the icon, I get some Edge maintenance menu instead of the extension UI, see screenshot:
![Image](https://github.com/user-attachments/assets/a65f67ba-b45e-42d8-b350-634f2dbf974f)
If I click ""Repomix"" choice, it opens the extension page in chrome webstore.
### Usage Context
None",
631,https://github.com/yamadashy/repomix/issues/631,closed,VSCode extension,"The experience of AI coding tools (cursor or others) mentioning file/directory context is unsatisfactory, and it worsens in agent mode (long workflows, discarding content to save costs). Some tools cannot even use the mouse to drag files. If repomix could directly generate an XML prompt for the selected file using the right-click, it would be very convenient!
> There is already a repomix runner extension in the marketplace, but it is outdated. It seems to use a very early version.",
624,https://github.com/yamadashy/repomix/issues/624,open,"Behavior of --ignore ""**/.gitignore""","Thank you for the great project.
The behavior of --ignore ""**/.gitignore"" seems not correct.
I just want to exclude the content of .gitignore from the output, but I want to keep using filters listed in .gitignore
`repomix --ignore ""**/.gitignore""` excludes .gitignore, but also disable the filters described in the .gitignore
### Usage Context
None",
618,https://github.com/yamadashy/repomix/issues/618,open,Support JSONC extension for the config,"The `.jsonc` extension is helpful for adding comments in JSON files.
I know that a path to a custom config file can be passed via `-c` but I would prefer not to have to add it every time I run repomix.",
614,https://github.com/yamadashy/repomix/issues/614,open,Installation via brew command throws error,"2018 MacbookPro Sequoia 15.4.1
When attempting to install repomix via homebrew, the following error is thrown =>
```
brew install repomix
==> Downloading https://ghcr.io/v2/homebrew/core/repomix/manifests/0.3.7
Already downloaded: /Users/<USER>/Library/Caches/Homebrew/downloads/9d9f0fbb552fe93b7c6004d694d917135adeba80dff8e30f8465b4289cb2353f--repomix-0.3.7.bottle_manifest.json
==> Fetching dependencies for repomix: libuv, ca-certificates and openssl@3
==> Downloading https://ghcr.io/v2/homebrew/core/libuv/manifests/1.51.0
Already downloaded: /Users/<USER>/Library/Caches/Homebrew/downloads/25bd9f2f86d047011dc8ca247bea2e4e3e5a150e29648418d48dfca4c8c136ea--libuv-1.51.0.bottle_manifest.json
==> Fetching libuv
==> Downloading https://ghcr.io/v2/homebrew/core/libuv/blobs/sha256:fb199706c025af4c6160825de25f9220c8d571499c5dba71c4b93a3874ea7a03
Already downloaded: /Users/<USER>/Library/Caches/Homebrew/downloads/d53fecfc1f0449b8a29d78c7364f8effa7f77965da02e57b732ad70621f29112--libuv--1.51.0.sonoma.bottle.tar.gz
==> Downloading https://ghcr.io/v2/homebrew/core/ca-certificates/manifests/2025-05-20
Already downloaded: /Users/<USER>/Library/Caches/Homebrew/downloads/bc18acc15e0abddc102f828b57a29cfdbec1b6b002db37ad12bad9dbf0e9d12f--ca-certificates-2025-05-20.bottle_manifest.json
==> Fetching ca-certificates
==> Downloading https://ghcr.io/v2/homebrew/core/ca-certificates/blobs/sha256:dda1100e7f994081a593d6a5e422451bfa20037e29667ed2b79f011ffc9288a7
curl: (56) The requested URL returned error: 503    #           #            #
Error: repomix: Failed to download resource ""ca-certificates""
Download failed: https://ghcr.io/v2/homebrew/core/ca-certificates/blobs/sha256:dda1100e7f994081a593d6a5e422451bfa20037e29667ed2b79f011ffc9288a7
```
### Usage Context
None",
609,https://github.com/yamadashy/repomix/issues/609,open,Unexpected error: Failed to filter files in directory,"❯ repomix ./testtest
📦 Repomix v0.3.7
Error filtering files: ENOTDIR: not a directory, stat '/Users/<USER>/tmp/testtest/build/Release/**'
✖ Error during packing
✖ Unexpected error: Failed to filter files in directory /Users/<USER>/tmp/testtest. Reason: ENOTDIR: not a directory, stat '/Users/<USER>/tmp/testtest/build/Release/**'
Stack trace: Error: Failed to filter files in directory /Users/<USER>/tmp/testtest. Reason: ENOTDIR: not a directory, stat '/Users/<USER>/tmp/testtest/build/Release/**'
at file:///opt/homebrew/lib/node_modules/repomix/lib/core/file/fileSearch.js:158:19
at Generator.throw (<anonymous>)
at rejected (file:///opt/homebrew/lib/node_modules/repomix/lib/core/file/fileSearch.js:5:65)
For detailed debug information, use the --verbose flag
failed to filter files error with a non-existent directory within my repo testtest
### Usage Context
None",
608,https://github.com/yamadashy/repomix/issues/608,open,"Files ""inclusion level""","Create a way to provide the detalization level for packed files.
There are 3 levels:
1. **Full content** - for a set of the most important files.
2. **Compress** - for the less important ones, which still may be relevant.
3. **Directory-only** - mention in the directory structure.
One can think of it as nested sets which describe not just the files, but the level of their detalization:
[ File in directory structure] < [ Compressed file ] < [ Full file ]
Use case: we need to improve build system.
1. Full files: for all configs and build files.
2. Directory structure: full file structure.
Use case: we need to work on a module residing inside a certain folder:
1. Full files: the whole module, main project files, configuration
2. Compressed files: other modules which somewhat contribute to context, but details aren't important.
Then we'll be able to create packing scenarios with various such settings and invoke them via MCP.
The simplest syntax would be 3 settings, e.g.
```js
files: {
content: [...globs...],  // full content
compress: [...globs...], // compressed
},
directory: [...globs...] // only list in the file structure
```
1. If a file matches a glob in `content` - full content is included.
2. Otherwise, if it matches a glob in `compress` - it gets compressed
So we can have like `compress: **/*.ts, content: my.ts` - all ts will be compressed, but my.ts will be included fully.
Also, all mentioned files are included into the directory structure, together with `directory` globs.
The `directory` globs has precedence over all ignores.
Here, the `directory` setting is the least important one, but it still may matter for huge projects with many files and folders. I understand we already have some ignore settings, perhaps they can replace `directory` or somehow make it into the mix =)",
591,https://github.com/yamadashy/repomix/issues/591,closed,"""Not implemented"" error when using `bunx`","I use `bunx repomix` regularly on my Windows computer, but when trying it on my Mac (Apple Silicon M4 chip), I receive an error:
```
🫀  ➜  bunx repomix --verbose
directories: [ '.' ]
cwd: /Users/<USER>/Programming/project-folder
options: {
directoryStructure: true,
files: true,
gitSortByChanges: true,
gitignore: true,
defaultPatterns: true,
securityCheck: true,
verbose: true
}
📦 Repomix v0.3.6
Loaded CLI options: {
directoryStructure: true,
files: true,
gitSortByChanges: true,
gitignore: true,
defaultPatterns: true,
securityCheck: true,
verbose: true
}
No Repopack files found to migrate.
Loading local config from: /Users/<USER>/Programming/project-folder/repomix.config.json
Loading global config from: /Users/<USER>/.config/repomix/repomix.config.json
No custom config found at repomix.config.json or global config at /Users/<USER>/.config/repomix/repomix.config.json.
You can add a config file for additional settings. Please check https://github.com/yamadashy/repomix for more information.
Loaded file config: {}
CLI config: {}
Default config: {
input: { maxFileSize: 52428800 },
output: {
filePath: 'repomix-output.xml',
style: 'xml',
parsableStyle: false,
directoryStructure: true,
files: true,
removeComments: false,
removeEmptyLines: false,
compress: false,
topFilesLength: 5,
showLineNumbers: false,
copyToClipboard: false,
git: {
sortByChanges: true,
sortByChangesMaxCommits: 100,
includeDiffs: false
}
},
include: [],
ignore: { useGitignore: true, useDefaultPatterns: true, customPatterns: [] },
security: { enableSecurityCheck: true },
tokenCount: { encoding: 'o200k_base' }
}
Default output file path is set to: repomix-output.xml
Merged config: {
input: { maxFileSize: 52428800 },
output: {
filePath: 'repomix-output.xml',
style: 'xml',
parsableStyle: false,
directoryStructure: true,
files: true,
removeComments: false,
removeEmptyLines: false,
compress: false,
topFilesLength: 5,
showLineNumbers: false,
copyToClipboard: false,
git: {
sortByChanges: true,
sortByChangesMaxCommits: 100,
includeDiffs: false
}
},
include: [],
ignore: { useGitignore: true, useDefaultPatterns: true, customPatterns: [] },
security: { enableSecurityCheck: true },
tokenCount: { encoding: 'o200k_base' },
cwd: '/Users/<USER>/Programming/project-folder'
}
Adding default ignore patterns
Adding output file to ignore patterns: repomix-output.xml
Adding custom ignore patterns: []
Include patterns: [ '**/*' ]
Ignore patterns: [
'.git/**',
'.hg/**',
'.hgignore',
'.svn/**',
'**/node_modules/**',
'**/bower_components/**',
'**/jspm_packages/**',
'vendor/**',
'**/.bundle/**',
'**/.gradle/**',
'target/**',
'logs/**',
'**/*.log/**',
'**/npm-debug.log*/**',
'**/yarn-debug.log*/**',
'**/yarn-error.log*/**',
'pids/**',
'*.pid',
'*.seed',
'*.pid.lock',
'lib-cov/**',
'coverage/**',
'.nyc_output/**',
'.grunt/**',
'.lock-wscript',
'build/Release/**',
'typings/**',
'**/.npm/**',
'.eslintcache',
'.rollup.cache/**',
'.webpack.cache/**',
'.parcel-cache/**',
'.sass-cache/**',
'*.cache',
'.node_repl_history',
'*.tgz',
'**/.yarn/**',
'**/.yarn-integrity/**',
'.env',
'.next/**',
'.nuxt/**',
'.vuepress/dist/**',
'.serverless/**',
'.fusebox/**',
'.dynamodb/**',
'dist/**',
'**/.DS_Store/**',
'**/Thumbs.db/**',
'.idea/**',
'.vscode/**',
'**/*.swp/**',
'**/*.swo/**',
'**/*.swn/**',
'**/*.bak/**',
'build/**',
'out/**',
'tmp/**',
'temp/**',
'**/repomix-output.*/**',
'**/repopack-output.*/**',
'**/package-lock.json/**',
'**/yarn-error.log/**',
'**/yarn.lock/**',
'**/pnpm-lock.yaml/**',
'**/bun.lockb/**',
'**/bun.lock/**',
'**/__pycache__/**',
'**/*.py[cod]/**',
'**/venv/**',
'**/.venv/**',
'**/.pytest_cache/**',
'**/.mypy_cache/**',
'**/.ipynb_checkpoints/**',
'**/Pipfile.lock/**',
'**/poetry.lock/**',
'**/uv.lock/**',
'**/Cargo.lock/**',
'**/Cargo.toml.orig/**',
'**/target/**',
'**/*.rs.bk/**',
'**/composer.lock/**',
'**/Gemfile.lock/**',
'**/go.sum/**',
'**/mix.lock/**',
'**/stack.yaml.lock/**',
'**/cabal.project.freeze/**',
'repomix-output.xml'
]
Ignore file patterns: [ '**/.gitignore', '**/.repomixignore' ]
Filtered 141 files
Initializing worker pool with min=1, max=2 threads. Worker path: file:///Users/<USER>/.bun/install/global/node_modules/repomix/lib/core/file/workers/fileCollectWorker.js","Expected: Stack trace: Error: Not implemented
at unknown
at new ThreadPool (/Users/<USER>/.bun/install/global/node_modules/piscina/dist/index.js:95:50)
at new Piscina (/Users/<USER>/.bun/install/global/node_modules/piscina/dist/index.js:570:57)
at initPiscina (/Users/<USER>/.bun/install/global/node_modules/repomix/lib/shared/processConcurrency.js:20:16)
at initTaskRunner (/Users/<USER>/.bun/install/global/node_modules/repomix/lib/core/file/fileCollect.js:14:18)
at <anonymous> (/Users/<USER>/.bun/install/global/node_modules/repomix/lib/core/file/fileCollect.js:20:26)
at <anonymous> (/Users/<USER>/.bun/install/global/node_modules/repomix/lib/core/file/fileCollect.js:7:71)
at new Promise (native:1:11)
at __awaiter (/Users/<USER>/.bun/install/global/node_modules/repomix/lib/core/file/fileCollect.js:3:27)
at map (native:1:11)
Need help?
• File an issue on GitHub: https://github.com/yamadashy/repomix/issues
• Join our Discord community: https://discord.gg/wNYzTwZFku
```
I was able to pack successfully using a Docker container as a workaround:
```
docker run -v .:/app -it --rm ghcr.io/yamadashy/repomix
```
I am using Bun version `1.2.14` (latest at the time of writing).
Sorry if you're not intending to support Bun. I have just gotten used to using it as a replacement and wasn't sure if you'd seen this bug before.
### Usage Context
Repomix CLI"
582,https://github.com/yamadashy/repomix/issues/582,open,Support SSE transport for `--mcp` mode,"Repomix’s MCP currently only speaks stdio. We should add an HTTP/SSE endpoint (e.g. `/sse`) so clients can connect via EventSource. Internally, reuse the SDK’s `SSEServerTransport`, and expose a `--mcp-sse` flag (or enable by default) for configuration. Update docs and add a basic smoke test.",
561,https://github.com/yamadashy/repomix/issues/561,open,Feature Request: Fine-tuned selection of including/excluding specific code block when `--compress` (i.e. Function Body code block),"Topic: `repomix --compress`, LLM-based code compression
## Motivation
### Generate richer context
When I run `--compress` in Repomix, I’d like the output to capture not only symbol information but also the *implementation details* of functions.
My use-case is to bundle an entire source-code repository into a single file, feed it to an LLM, and let the model analyze the codebase holistically.
Because many insights depend on what happens *inside* a function, simply exporting the symbols wasn't enough for me—I need the function bodies in the compressed output as well.
Giving users control over which code snippets get included would provide much greater flexibility.
### Apply custom pre-processing to code blocks identified via tree-sitter
With tree-sitter we can pinpoint distinct AST nodes—such as a function’s name and its body. If we could:
* **retain every function symbol untouched**, and
* **pass each function body through an LLM for aggressive compression**,
then we’d keep the static-analysis benefits of full symbol visibility *and* supply the language model with far richer context.
Ideally, each code block (e.g., function name, function body) would have its own configurable processing pipeline.
## Related issues
* #36 and #511 discuss LLM-based summarization.
## More Notes
I am currently developing [llmlingua-2-js](https://github.com/atjsh/llmlingua-2-js), a JavaScript port of LLMLingua-2. Once it is finished, developers will be able to run LLM-based code compression seamlessly in the same Node.js environment that Repomix uses.
I plan to open a separate PR for llmlingua-2-js when it is ready.",
558,https://github.com/yamadashy/repomix/issues/558,open,Error while importing repomix,"```
{
""errorType"": ""TypeError"",
""errorMessage"": ""The \""path\"" argument must be of type string or an instance of URL. Received undefined"",
""trace"": [
""TypeError [ERR_INVALID_ARG_TYPE]: The \""path\"" argument must be of type string or an instance of URL. Received undefined"",
""    at fileURLToPath (node:internal/url:1487:11)"",
""    at Object.<anonymous> (/var/task/index.js:87358:88)"",
""    at Module._compile (node:internal/modules/cjs/loader:1529:14)"",
""    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)"",
""    at Module.load (node:internal/modules/cjs/loader:1275:32)"",
""    at Module._load (node:internal/modules/cjs/loader:1096:12)"",
""    at Module.require (node:internal/modules/cjs/loader:1298:19)"",
""    at require (node:internal/modules/helpers:182:18)"",
""    at _tryRequireFile (file:///var/runtime/index.mjs:1002:37)"",
""    at _tryRequire (file:///var/runtime/index.mjs:1052:25)""
]
}
```",
557,https://github.com/yamadashy/repomix/issues/557,open,How to import with commonjs,"I am facing issues while working with my projects which uses tsc and commonjs.
Getting the following error:
the current file is a commonjs module whose imports will produce 'require' calls; however, the referenced file is an ecmascript module and cannot be imported with 'require'.",
555,https://github.com/yamadashy/repomix/issues/555,open,Missing tiktoken_bg.wasm,"```
{
""errorType"": ""Error"",
""errorMessage"": ""Missing tiktoken_bg.wasm"",
""trace"": [
""Error: Missing tiktoken_bg.wasm"",
""    at node_modules/tiktoken/tiktoken.cjs (/var/task/index.js:16383:30)"",
""    at __require (/var/task/index.js:12:51)"",
""    at node_modules/repomix/lib/core/metrics/TokenCounter.js (/var/task/index.js:16397:31)"",
""    at __init (/var/task/index.js:9:56)"",
""    at node_modules/repomix/lib/core/metrics/calculateMetrics.js (/var/task/index.js:16434:5)"",
""    at __init (/var/task/index.js:9:56)"",
""    at node_modules/repomix/lib/core/packager.js (/var/task/index.js:26483:5)"",
""    at __init (/var/task/index.js:9:56)"",
""    at node_modules/repomix/lib/index.js (/var/task/index.js:67205:5)"",
""    at __init (/var/task/index.js:9:56)""
]
}
```
Deploying TS Lambda
tsconfig.json
```
{
""compilerOptions"": {
""target"": ""ES2020"",
""module"": ""commonjs"",
""lib"": [""es2016"", ""es2017.object"", ""es2017.string"", ""es2020""],
""declaration"": true,
""outDir"": ""./dist"",
""declarationDir"": ""./dist"",
""strict"": true,
""noImplicitAny"": true,
""strictNullChecks"": true,
""noImplicitThis"": true,
""alwaysStrict"": true,
""noUnusedLocals"": false,
""noUnusedParameters"": false,
""noImplicitReturns"": true,
""noFallthroughCasesInSwitch"": false,
""inlineSourceMap"": true,
""inlineSources"": true,
""experimentalDecorators"": true,
""strictPropertyInitialization"": false,
""typeRoots"": [""./node_modules/@types""],
""rootDir"": ""./lib""
},
""include"": [""lib/**/*.ts""],
""exclude"": [""cdk.out"", ""build"", ""node_modules"", ""dist""]
}
```",
554,https://github.com/yamadashy/repomix/issues/554,open,Feature Request: `--no-file-summary` should also suppress `generationHeader`; should not suppress user-specified `headerText`,"Repomix's output has a [summary/usage guidelines section](https://github.com/yamadashy/repomix/blob/fbad7c9ae364e52eb0cd153c451fc54f1606850a/src/core/output/outputGenerate.ts#L61), which can be suppressed with `--no-file-summary`,  but it also has a [generationHeader](https://github.com/yamadashy/repomix/blob/fbad7c9ae364e52eb0cd153c451fc54f1606850a/src/core/output/outputGenerate.ts#L60) section which cannot be suppressed.
My use case is for packing files other than source code (project documentation, code guidelines, etc), but the text in the unsuppressable [generationHeader](https://github.com/yamadashy/repomix/blob/fbad7c9ae364e52eb0cd153c451fc54f1606850a/src/core/output/outputStyleDecorate.ts#L50) has static references to ""the codebase"", which may mislead the reader of the file (especially since I'm lazy and just pass the directory name in argv instead of using `--include`, so the output says it's ""the entire codebase"").
Additionally, the `--header-text` config item adds its contents to the [summary/usage guidelines section](https://github.com/yamadashy/repomix/blob/7d1aa52697e2fe4342a21a4607254e5d84e043f8/src/core/output/outputStyleDecorate.ts#L118). When that section is suppressed with `--no-file-summary`, the custom `--header-text` is suppressed along with it.
Desired behavior:
- `--no-file-summary` should suppress both the summary/usage guidelines section and the `generationHeader`
- `--header-text`, if provided, should be included in the output, regardless of the `--no-file-summary` flag",
553,https://github.com/yamadashy/repomix/issues/553,open,Feature Request: Add `--include-from-file` option,"Add support for reading include patterns from a file to improve usability when dealing with many specific files or complex patterns.
## Problem
Currently, when using the `--include` option with many specific file paths, the command line becomes unwieldy and difficult to manage. For example:
```bash
repomix --include ""file1.ts,file2.ts,file3.ts,file4.ts,file5.ts,..."" # Very long command line
```
This becomes problematic when:
- Including many specific files (10+ files)
- Files have long paths
- You want to reuse the same set of files across multiple runs
- Working with version control where command history becomes messy
- Command line length limits are reached
## Proposed Solution
Add a new option `--include-from-file` (or `--include-file`) that reads include patterns from a file, one per line.
### Usage Example
```bash
# Create a file with patterns/files to include
echo ""src/components/**/*.tsx
src/hooks/*.ts
src/lib/utils/dateUtils.ts
src/pages/dashboard/**/store.ts"" > include-patterns.txt
# Use the file with repomix
repomix --include-from-file include-patterns.txt
```
### File Format
- One pattern per line
- Empty lines and lines starting with `#` are ignored (comments)
- Both glob patterns and specific file paths are supported
### Example include file:
```
# UI Components
src/components/molecules/**/*.tsx
src/components/ui/*.tsx
# Utility functions
src/lib/utils/common.ts
src/lib/utils/dateUtils.ts
# Dashboard hooks
src/lib/hooks/dashboard/*.ts
```
## Benefits
1. **Cleaner command line**: Avoid extremely long command lines
2. **Reusability**: Save and reuse file lists across different runs
3. **Version control friendly**: Include patterns can be committed to the repository
4. **Better organization**: Group related files with comments
5. **Easier maintenance**: Modify file lists without retyping entire commands
Would love to see this feature added to make repomix even more flexible for complex project structures!",
552,https://github.com/yamadashy/repomix/issues/552,closed,Suspicious file no longer detected,"Hello,
I've been using the v0.1.43 for quite some time, as I didn't have a need to upgrade up until now.
I downloaded the (latest) v0.2.7, and a file that contained my github token no longer got flagged, and it was included without a problem.
that's about all I know about the issue.",
542,https://github.com/yamadashy/repomix/issues/542,closed,Add support for Node.js v24,"Repomix currently tests and supports Node.js versions 16 through 23 in our CI workflows, devcontainer, and documentation. Node.js v24 is now officially released, and we’d like to ensure full compatibility so users can take advantage of the latest performance improvements and language features.
## Proposed Changes
* **CI**
* Add `24.x` to the `node-version` matrix in `.github/workflows/ci.yml`.
* Add `24` to the matrix in `.github/workflows/test-action.yml`.
* **Devcontainer**
* Update the base image in `.devcontainer/devcontainer.json` to one that supports Node.js 24 (e.g. `mcr.microsoft.com/devcontainers/typescript-node:1-24-bullseye`).",
540,https://github.com/yamadashy/repomix/issues/540,open,Add `--include-logs` option to include `git log --name-status` output,"Repomix already supports pulling in diffs via `--include-diffs` (see PR [#533](https://github.com/yamadashy/repomix/pull/533)). This enhancement will let users bundle recent commit history—specifically the output of `git log --name-status`—into their archive with a simple flag.
---
**Motivation:**
* **Change Visibility:** Shows which files changed in each commit, helping reviewers and LLMs trace code evolution.
* **Context:** Complements low-level diffs with a concise, high-level history.
* **Configurability:** Users can limit how many commits to include.
---
**Proposed Changes:**
1. **CLI Definition**
```ts
// in src/cli/cliRun.ts (and types.ts)
.option('--include-logs', 'include git log --name-status output in the archive')
.option(
'--include-logs-max-commits <number>',
'maximum number of commits to include in git log (default: 100)',
{ default: 100 }
)
```
2. **Config Schema**
```ts
interface RepomixConfigCli {
// ...existing fields
output?: {
git?: {
includeLogs?: boolean;
includeLogsMaxCommits?: number;
}
}
}
```
3. **Git Collection Logic**
* In your git collector (e.g. `src/core/git/gitCollector.ts`), when `config.output.git.includeLogs` is `true`, run:
```bash
git log --name-status -n ${config.output.git.includeLogsMaxCommits}
```
* Emit the result as a file (e.g. `repomix-git-log.txt`) inside the output bundle.
4. **Documentation & Tests**
* Update `command-line-options.md` and `README.md` to document `--include-logs` and `--include-logs-max-commits`.
* Add tests under `tests/` to verify:
* `repomix --include-logs` produces `repomix-git-log.txt`.
* The `--include-logs-max-commits` override is respected.
* Default behavior uses 100 commits.
---",
539,https://github.com/yamadashy/repomix/issues/539,open,"Remove Node.js 18 support (EOL April 30, 2025)","Node.js 18 reached end-of-life on April 30, 2025. Please update Repomix to require Node.js 20 or later:
* Bump the devcontainer and GitHub Action workflows to use Node.js 20.
* Remove Node.js 18 from the CI test matrix in `.github/workflows/ci.yml`.
* Update any documentation or README references to reflect the new minimum version.",
535,https://github.com/yamadashy/repomix/issues/535,open,"Feature Request: Auto exclude library/weird files (node_modules, etc, .","By default, it auto excludes and shows user a toast like thing (detected blabla library files, that are excluded)  + enables some checkmarks for this.
User can opt in to remove that exclusion.
it adds the related regexp patterns under the hood(not under the hood, user should see the added regexp at the input field field)",
516,https://github.com/yamadashy/repomix/issues/516,open,Add include/exclude options specific to `--compress`,"Going to take a stab at making a PR for this myself, but in the meantime wanted to put an issue up to track.
It'd be really useful to allow for `--compress` to have its own exclusive parameters to define what files should be compressed. Right now, it is all or nothing. But oftentimes, I have a certain section of the code base that is most relevant and I want to send the whole section in the context, and then compress the rest as a bonus.
Not entirely sure what the best CLI argument names would be, and which takes precedence. Right now there is `--include` and `--ignore`, so probably `--compress-include` and `--compress-ignore`.
But what happens if say, you put a file in `--ignore` and then also put it in `--compress-include`? Lots of open ended questions like that. Probably will be dictated by the structure of the code and what is easiest to implement once I dig in.
If anyone has thoughts please chime in.",
511,https://github.com/yamadashy/repomix/issues/511,open,Add `--summary` CLI option for LLM-based code summarization,"We are considering adding a `--summary` CLI option that would leverage an LLM to perform summarization of code. The approach would split target files into manageable chunks, have the LLM summarize each chunk, and then combine the results. This would provide a better understanding of large codebases where traditional `--compress` is often not sufficient.
ref:
- https://discord.com/channels/1324644561959522314/1324644562429542425/1364257773214499047",
508,https://github.com/yamadashy/repomix/issues/508,closed,Support for GitHub Action,"This feature request is for `repomix` to also provide a `GitHub Action` that projects can use on their workflows to generate LLM friendly files automatically.
You will need an `action.yml` file on this repo with something like this:
```yaml
name: ""Repomix Action""
description: ""Pack repository contents into a single file that is easy for LLMs to process""
author: ""Kazuki Yamada <<EMAIL>>""
branding:
icon: archive
color: purple
inputs:
directories:
description: ""Space-separated list of directories to process (defaults to '.')""
required: false
default: "".""
include:
description: ""Comma-separated glob patterns to include""
required: false
default: """"
ignore:
description: ""Comma-separated glob patterns to ignore""
required: false
default: """"
output:
required: false
default: ""repomix.txt""
compress:
description: ""Set to 'false' to disable smart compression""
required: false
default: ""true""
additional-args:
description: ""Any extra raw arguments to pass directly to the repomix CLI""
required: false
default: """"","Steps: - name: Setup Node.js
uses: actions/setup-node@v4
with:
```
Note this yaml was generated using `OpenAI - o3`"
506,https://github.com/yamadashy/repomix/issues/506,closed,Glama listing is missing Dockerfile,"Your MCP server is currently listed on the [Glama MCP directory](https://glama.ai/mcp/servers/yamadashy/repomix), but it is not available for others to use because it does not have a Dockerfile.
It takes only a few minutes to fix this:
1. Go to your server's listing: [yamadashy/repomix](https://glama.ai/mcp/servers/yamadashy/repomix)
2. Click ""Claim"" to verify ownership.
3. Once claimed, navigate to the [admin `Dockerfile` page](https://glama.ai/mcp/servers/yamadashy/repomix/admin/dockerfile) and add a `Dockerfile`.
4. Ensure your server passes all the [checks](https://glama.ai/mcp/servers/yamadashy/repomix/score).
Once completed, your server will be available for anyone to use.
For context, there are about 60k people using Glama every month and I'd love to see more people using your server.",
505,https://github.com/yamadashy/repomix/issues/505,closed,Add support for MCP tool annotations,"The Model Context Protocol 2025-03-26 specification added support for tool annotations, which provide additional metadata about a tool's behavior. Once the MCP SDK is updated to support annotations, we should enhance our MCP tools implementation to include appropriate annotations.
Example of future implementation in readRepomixOutputTool.ts:
```typescript
mcpServer.tool(
'read_repomix_output',
{
outputId: z.string().describe('ID of the Repomix output file to read'),
},
async ({ outputId }) => { /* implementation */ },
{
title: 'Read Repomix Output File',  // User-friendly title for UI display
readOnlyHint: true,                 // Indicates this tool does not modify its environment
openWorldHint: false                // Indicates this tool does not interact with external systems
}
);
```
### Tasks
- Monitor updates to the MCP SDK (@modelcontextprotocol/sdk) for annotation support
- Add appropriate annotations to each of our tool implementations:
- read_repomix_output
- file_system_read_file
- file_system_read_directory
- pack_codebase
- pack_remote_repository
### References
- MCP Tool annotations spec: https://modelcontextprotocol.io/docs/concepts/tools/#tool-annotations
- MCP SDK issue tracking annotation support: https://github.com/modelcontextprotocol/typescript-sdk/issues/276",
479,https://github.com/yamadashy/repomix/issues/479,open,Export core as a package,"Hey,
Love the repo! I'd like to propose exporting core as a package that i can use in my nodejs app or export the core functions as a part of the repomix package.
There's plenty of functions in the core folder that we can use during pre processing before running the cli.
Happy to open a PR on a direction you agree on 🤞🏻",
478,https://github.com/yamadashy/repomix/issues/478,open,Idea: output only related files command,"Problem:
code bases tend to grow enormously, especially frontend code.
And sometimes you don't need the whole code base to send to LLMS
200K context and even 1M context window aren't enough
Solution:
have a repomix command that tells the repomix to read current directory files and imported files also and bundle them in one codebase
I am expecting to have `server/actions/users.ts`
This file also imports several functions from `apps/utils/users.ts`, `apps/db/queries/users.ts`
The final output will have the code from actions and related files, from utils and associated files from queries
Also have the feature to set a list of files or directories to be always included, no matter what, like ai_instructions.md
I know that this behaviour can be reached by tweaking the include and ignore in the repomix config. But it takes a lot of time to do it each time",
477,https://github.com/yamadashy/repomix/issues/477,open,Integrate MCP `roots` and dynamic tool discovery,"It would be really great if repomix could leverage MCP features to create a more context-aware and adaptive experience. VS Code supports both of them and hopefully other MCP clients follow up as this can provide a much smoother tooling experience.
- **MCP Roots Support:** https://modelcontextprotocol.io/docs/concepts/roots#why-use-roots%3F
- Allow repomix to accept multiple roots (like local paths or URLs) so it can automatically determine which part of a workspace or repository to pack.
- Automatically configure tool parameters based on the current VS Code workspace.
- **Dynamic Tool Discovery:**  https://modelcontextprotocol.io/docs/concepts/tools#tool-discovery-and-updates
- Listen for `list-change` events to update available repo packs and tool definitions in real time.
- Re-scan the workspace when changes are detected, ensuring operations always match the current project state.
Looking forward to your thoughts and suggestions on how to best implement these ideas!",
476,https://github.com/yamadashy/repomix/issues/476,open,Token count mismatch with Google AI Studio,"Thanks for creating and maintaining repomix.
With o200k_base, repomix reports ""969,990 tokens"", but AI Studio claims it's ""1,156,615"". Is this a known issue with AI Studio? If not, is there a way to configure repomix to be compatible with it?
Btw, cl100k_base and o200k_base returns same token counts, is this expected?",
475,https://github.com/yamadashy/repomix/issues/475,open,[Feature] Metadata-Only Output,"An option (maybe --metadata-only) to output just the summary, directory structure, and file metrics without the content, useful for quick analysis.",
474,https://github.com/yamadashy/repomix/issues/474,closed,Badge in README shows CI-FAILING,"SCREENSHOT-
<img width=""1467"" alt=""Image"" src=""https://github.com/user-attachments/assets/8d193e4a-1482-4ee9-a2cc-1b5fbe12d670"" />",
473,https://github.com/yamadashy/repomix/issues/473,closed,BACK TO TOP- not working,"SCREEN RECORDING-
https://github.com/user-attachments/assets/5f941f7d-b609-4c6b-8259-86b73c4448c6",
472,https://github.com/yamadashy/repomix/issues/472,open,MCP Feature Request: generate output in project directory,"I've somehow managed to chain different repomix mcp tools together, but anytime i try to access it, i get this Cursor error: `Content type ""resource"" not supported`
would be awesome if pack_codebase could have a `local` parameter so it woud generate the repomix output in the project directory
@yamadashy wdyt?",
468,https://github.com/yamadashy/repomix/issues/468,open,Typescript library in order to use repomix not only through CLI,It would be awesome if a ts library was available in order to use repomix in other open-source projects (such as https://github.com/filopedraz/kosuke-core).,
467,https://github.com/yamadashy/repomix/issues/467,open,Support Brace Expansion in CLI `--include` `--ignore` Option,"The current CLI `--include` `--ignore` option does not support brace expansion patterns like `{__tests__,theme}`. This requires users to specify each pattern separately, which is less efficient. Adding support for brace expansion would allow more concise and flexible pattern definitions.",
463,https://github.com/yamadashy/repomix/issues/463,open,Security check failure when running Repomix CLI locally,"A user on Discord reported a failure when attempting to run `repomix` locally via the CLI.
**Error message:**
```
Error checking security on packages/@n8n/benchmark/scripts/mock-api/mappings/mockApiData.json: Error: Unknown type:undefined
```",
454,https://github.com/yamadashy/repomix/issues/454,closed,.js files showing as empty in output file,"I have a Svelte website I'm developing and when I use repomix my *.js files are shown as empty in the output file. It shows that the files exist, but there is no content.
For example:
## File: postcss.conifg.js
```
```","Actual: ## File: postcss.conifg.js
```
module.exports = {
plugins: {
tailwindcss: {},
autoprefixer: {},
},
};
```"
450,https://github.com/yamadashy/repomix/issues/450,open,CLI Usability Issues: Pattern Matching and Command Suggestions,"The CLI has two significant usability issues that affect user experience:
### 1. Pattern Matching Problems
The file exclusion functionality doesn't work intuitively with common glob patterns:
- Simple patterns like `--ignore ""*.ts""` don't exclude files as expected
- Only more specific patterns like `--ignore ""**/*.ts""` actually work
- Users familiar with other CLI tools expect simpler patterns to work properly
### 2. Command Suggestion Limitations
The current suggestion system (inherited from Commander.js) has significant limitations:
- Only suggests options based on string similarity (edit distance)
- Lacks semantic understanding of command alternatives
- Users typing semantically similar options (like `--reject`, `--omit`, `--skip`) don't get suggestions for `--ignore` or `--exclude`
- Frustrates users with different mental models or coming from other tools","Steps: ### Pattern Matching Issue:
1. Run `npm run repomix -- --ignore ""*.ts"" --verbose`
2. Observe that TypeScript files are still processed despite the exclusion pattern
### Command Suggestion Issue:
1. Run `npm run repomix -- --reject ""*.ts""`
2. Observe that the error message suggests only string-similar options rather than semantically similar ones like `--ignore` or `--exclude`

Expected: ### Pattern Matching:
- Simple patterns like `*.ts` should work properly to exclude files
- Behavior should align with common glob pattern implementations in other tools
### Command Suggestions:
- When users type synonymous commands (like `--reject`, `--omit`, `--skip`), they should receive suggestions for the correct options (`--ignore`, `--exclude`)
- Error messages should be more helpful for users with different mental models
## Current Behavior
### Pattern Matching:
- Only specific formats like `**/*.ts` work properly
- Simple patterns fail silently, processing files that should be excluded
### Command Suggestions:
- Only string-similar suggestions are provided
- No semantic understanding of command alternatives
- Unhelpful error messages for users trying semantically equivalent commands"
449,https://github.com/yamadashy/repomix/issues/449,closed,did you guys do any special function or just record directory and fileContent?i am a little confused,"did you guys do any special function or just record directory and fileContent?i am a little confused,cause i can not found any other useful info",
445,https://github.com/yamadashy/repomix/issues/445,open,Get txt output instead of xml,"The xml format adds token count with the tags.
Also the xml format does not open properly in two common web browsers I tested.
And an option to generate the .txt would be good.
The XML format is much harder to read and navigate in plaintext when the user's desire is to trim down the repomix output without having to duplicate and delete items in their codebase before running repomix.
It would also be well if there was 'npx repomix help' option so the user can have these commands presented to them (generate txt with 'npx repomix txt' etcetera for any other help info.)
-T",
444,https://github.com/yamadashy/repomix/issues/444,closed,Feature Request: make --exclude a synonym of --ignore,"The tool uses `--include` to include files and folders for processing. The antonym of `--include` is `--exclude`, but that is not recognised. The following is output instead:
```
error: unknown option '--exclude'
(Did you mean --include?)
```
This is obviously pretty funny, but nevertheless it would be beginner-friendly to have `--exclude` as a synonym for `--ignore`.
If there is a reason why this shouldn't be done, then please change the error message to suggest `--ignore` instead of `--include`.",
443,https://github.com/yamadashy/repomix/issues/443,closed,Feature Request: make ignore globbing patterns more intuitive for directories,"Hi,
Let's say I have the following repomix command, which both includes and excludes files:
```
repomix
--include ""MyProject.Common/**/*,MyProject.Models/**/*,MyProject.UI/**/*""
--ignore ""**/bin,**/obj,**/GeneratedFiles,**/Assets""
```
That is the correct syntax. However, if I amend the exclusion patterns to include trailing slashes, it does not work:
```
repomix
--include ""MyProject.Common/**/*,MyProject.Models/**/*,MyProject.UI/**/*""
--ignore ""**/bin/,**/obj/,**/GeneratedFiles/,**/Assets/""
```
The result of running this is that all files are still included in the folders we've marked to ignore. It is reasonable to assume that `**/Assets` and `**/Assets/` would mark all `Assets` folders under the include paths for exclusion.
Similarly, the following command will not work either:
```
repomix
--include ""MyProject.Common/**/*,MyProject.Models/**/*,MyProject.UI/**/*""
--ignore ""**/bin/**/*,**/obj/**/*,**/GeneratedFiles/**/*,**/Assets/**/*""
```
This uses the same globbing pattern to attempt to exclude files as it does to include files, but no files are excluded.
This caught me out today when using the tool for the first time.
```
**/folder
**/folder/
**/folder/**/*
```
Incidentally, the ""**/name"" syntax for exclusion patterns may be problematic, as it will match both folders and files with that name. It's rare that you have files lacking extensions, of course, but it may be worth noting.
Thank you for your useful tool!",
440,https://github.com/yamadashy/repomix/issues/440,open,"Bug: MCP Server Mode (mcp serve) Outputs Non-JSON Text to Stdout, Breaking Protocol Communication","When running `repomix` in MCP server mode (`repomix mcp serve`), the process outputs various non-JSON text elements (startup banner, interactive prompts, spinners, logs) to standard output (`stdout`). This contaminates the JSON-RPC communication channel expected by the Model Context Protocol, causing MCP clients like Claude Desktop to fail parsing the output, resulting in timeouts and disconnections.","Steps: 1.  Configure Claude Desktop's MCP settings file (`claude_desktop_config.json` or similar) to run the Repomix MCP server using `npx`:
```json
{
""mcpServers"": {
""file-operations-server"": {
""command"": ""node"",
""args"": [
""/path/to/mcp-file-operations-server/build/index.js""
]
},
""repomix"": {
""command"": ""npx"",
""args"": [
// ""-y"", // Note: Including -y caused an ""unknown option"" error from repomix itself
""repomix"",
""mcp"",
""serve""
// ""--quiet"" // Adding --quiet here did not resolve the issue
]
}
}
}
```
2.  Start Claude Desktop, ensuring it launches the configured MCP servers.
3.  Attempt to use a Repomix tool via Claude, for example, by asking it to analyze a local project directory (this should trigger the `pack_codebase` tool).
4.  Observe the MCP server logs provided by Claude Desktop (usually accessible via Developer Tools or a log file).

Expected: The `repomix mcp serve` process should strictly adhere to the Model Context Protocol's communication standard:
*   Only valid JSON-RPC 2.0 messages should be printed to `stdout`.
*   No startup banners, interactive prompts (like the migration check), spinners, ANSI escape codes, or general log messages should be output to `stdout`. Output intended for debugging or user visibility during normal CLI operation should be suppressed or redirected to `stderr` when in MCP mode.
*   The MCP client (Claude Desktop) should maintain a stable connection and successfully parse responses from the `repomix` server process.

Actual: The `repomix` process outputs non-JSON text to `stdout`, causing the MCP client's JSON parser to fail. This leads to request timeouts and the server connection being dropped by the client.
Log snippets demonstrating the invalid output on `stdout`:
*   **Startup Banner:**
```
[error] [repomix] Unexpected token '�', ""📦 Repomix v0.3.0"" is not valid JSON
```
*   **Migration Check Prompt (from `@clack/prompts` via `src/cli/actions/migrationAction.ts`):**
```
[error] [repomix] Unexpected token '◆', ""◆  Found R""... is not valid JSON
[error] [repomix] Unexpected token '│', ""│  ● Yes / ○ No"" is not valid JSON
[error] [repomix] Unexpected token '│', ""│  No"" is not valid JSON
[error] [repomix] Unexpected token 'M', ""Migration cancelled."" is not valid JSON
```
*   **Spinner/ANSI Codes (from `log-update` via `src/cli/cliSpinner.ts`):**
```
[error] [repomix] Unexpected token ' ', "" [?25l│"" is not valid JSON
[error] [repomix] Unexpected token ' ', "" [999D [4A""... is not valid JSON
```
*   **Standard Logger Output (from `src/shared/logger.ts`):**
```
[error] [repomix] Unexpected token 'N', ""No custom ""... is not valid JSON
[error] [repomix] Unexpected token 'N', ""Need help?"" is not valid JSON
[error] [repomix] Unexpected token '•', ""• Join our""... is not valid JSON
```
*   **Error Handling Output:**
```
[error] [repomix] Unexpected token '✖', ""✖ Error du""... is not valid JSON
```
*   **(Separate Issue Observed):** When using `args: [""-y"", ""repomix"", ""mcp"", ""serve""]`, the process immediately crashes with `error: unknown option '-y'`, indicating `-y` is incorrectly passed to `repomix` instead of being consumed by `npx`. Removing `-y` allows the server to start but reveals the stdout pollution issue.
**Analysis:**
The core issue is that the MCP server mode does not adequately suppress or redirect output generated by various components:"
438,https://github.com/yamadashy/repomix/issues/438,open,"Feature Request: Add a ""force include"" configuration/CLI option to override default and custom exclusion rules","I frequently need to quickly bundle just a few specific files from my repository using `repomix.config.json`—particularly when I'm repeatedly adjusting the `include` field. However, I'm running into an issue: I'd like to include a file located in `node_modules/`, but Repomix automatically excludes that folder by default. Even if I specify the exact path to the file inside `node_modules/`, it’s not included because of the default or custom exclusion rules.
It would be incredibly helpful to have a **""force include""** option (either in the configuration file or as a CLI flag) that explicitly overrides all exclusion rules—both the default `node_modules` exclusion and any patterns set by `.gitignore`, `.repomixignore`, or other custom rules. This way, if I really need to pull in a specific file from `node_modules/` (or any other traditionally excluded directory), I can do so without having to disable or modify all the exclude patterns.
---
**Use Case**
- Quickly bundling only a few files for AI analysis without including the entire node_modules directory.
- Ensuring that even if `node_modules` or similar directories are globally excluded, certain critical files can still be selectively included.
- Reducing back-and-forth toggling of ignore patterns in `repomix.config.json`.
---
**Proposed Solution**
1. **Configuration File Approach**:
- Add a new `forceInclude` array (similar to `include` and `ignore`) in `repomix.config.json` that overrides all exclusions.
- Example:
```json
{
""forceInclude"": [
""node_modules/some-package/dist/specific-file.js""
]
// ... other config properties
}
```
2. **CLI Flag Approach**:
- Provide a `--force-include` option that can be used once or multiple times to specify paths.
- Example:
```bash
repomix --force-include ""node_modules/some-package/dist/specific-file.js""
```
3. **Behavior**:
- Any pattern or path listed under `forceInclude` would be packed regardless of any exclusion rules set by `.gitignore`, `.repomixignore`, default ignore patterns, or the `ignore.customPatterns` configuration.
---
**Benefits**
- Simplifies the process when a single or handful of files from an otherwise excluded directory need to be included.
- Makes the configuration more flexible by letting users override strict defaults without disabling them entirely.
- Reduces repetitive manual toggling of ignore patterns.
---
**Additional Notes / Considerations**
- This feature might also be useful for teams that store partial dependencies or patched versions inside `node_modules` or similarly excluded folders.
- It would be consistent if any ""force include"" entries also appear in the directory structure output (even when excluded directories are generally omitted).
Thank you for considering this request! I believe this feature would significantly improve workflows where only a few files in traditionally excluded directories need to be included. Please let me know if I can provide more details or clarifications.",
437,https://github.com/yamadashy/repomix/issues/437,open,Feature Request: Allow setting a maximum size (in MB) for files that will be packed,"Sometimes repos contain unexpected massive files. Running stat before opening files and filtering out any that are too large helps with having a smooth experience.
A config variable like `skipFilesLargerThan` that takes a string like ""500kB"" would be my suggestion but I really have no strong opinion on the implementation",
433,https://github.com/yamadashy/repomix/issues/433,open,Impossible to pack a remote with specific paths,"> npx repomix --remote https://github.com/aws/aws-sdk-js-v3/tree/main/clients/client-ses
Throws
> ✖ Failed to clone repository: Command failed: git -C /var/folders/89/7j7fc2m16lg234rx3pz_2jnc0000gn/T/repomix-ev84t3A9WgSA fetch --depth 1 origin main/clients/client-ses
fatal: couldn't find remote ref main/clients/client-ses
Using Repomix v0.3.0
[As per doc:](https://repomix.com/#power-user-guide)
>Using full URL (supports branches and specific paths)
>npx repomix --remote https://github.com/yamadashy/repomix
>npx repomix --remote https://github.com/yamadashy/repomix/tree/main
<img width=""576"" alt=""Image"" src=""https://github.com/user-attachments/assets/121db3a0-36f0-47e0-aaab-fdabf3c53377"" />",
431,https://github.com/yamadashy/repomix/issues/431,open,Export loadFileConfig so that when repomix is used as a js library the users can choose to load a repomix.config.json,This would allow library users the option to respect the repomix behaviours when using repomix programatically,
430,https://github.com/yamadashy/repomix/issues/430,closed,Feature Request: support solidity comment removal,"Repomix supports removing comments from a range of languages but solidity isn't a support language currently
I found the query for solidity's tree sitter definitions
https://github.com/Aider-AI/aider/blob/main/aider/queries/tree-sitter-language-pack/solidity-tags.scm",
426,https://github.com/yamadashy/repomix/issues/426,closed,Pack Codebase response resource item doesn't include content,"When using the `pack_codebase` tool, the server returns items of the format:
```
{
type: ""resource"",
resource: {
uri: ""file:///tmp/repomix/mcp-outputs/pqmRzP/repomix-output.xml"",
mimeType: ""application/xml"",
text: ""Repomix output file"",
},
}
```
According to the [MCP docs](https://modelcontextprotocol.io/docs/concepts/resources) if I'm understanding correctly, this call should include the actual contents of the file and the URI would be for future reference or for subscribing to resource changes.
This means building to this MCP server in a generic way (fetching file URL on tool response) would not match expected handling of tool responses for other MCP endpoints.
For context I think repomix would be a fantastic integration into [the Continue IDE extension](https://github.com/continuedev/continue). Most of the repomix tools work but the resource ones don't.
# Solution
Include the contents of the repomix output file contents under `text` in tool output",
424,https://github.com/yamadashy/repomix/issues/424,open,Feature Request: Automatic Chunking (a.k.a. “YOLO Divide”) for Oversized Codebases,"It would be immensely helpful if Repomix could automatically split large codebases into multiple output files once a certain token threshold (e.g. 128k) is reached. This “YOLO divide” approach would allow users to simply specify a cutoff and let Repomix handle the heavy lifting. The resulting “chunked” files could then be fed sequentially into AI tools that have strict context size limits.
---
### Use Case
I often want to provide my entire codebase to a large language model for analysis or troubleshooting, but the codebase exceeds the 128k token limit by a wide margin—sometimes up to ten times more. Manually chopping the output into smaller pieces is tedious. An automated chunking solution would streamline this process significantly.
### Desired Behavior
1. **Config Option**: A single setting, for example:
```json
{
""output"": {
""yoloDivideIntoChunksIfExceedToken"": 128000
}
}
```
2. **Automatic Chunk Generation**:
- If the total token count surpasses the specified threshold, Repomix splits the output into separate files (e.g., `repomix-output-chunk-1.xml`, `repomix-output-chunk-2.xml`, etc.).
- The exact method of splitting doesn’t matter much to me. Any reasonable approach (by file boundaries, lines, or token count) would be sufficient, as long as each chunk stays under the limit.
3. **Outcome**:
- Users can quickly copy and paste each chunk into their AI tool of choice (e.g., O1 Pro, ChatGPT, Claude, etc.) without worrying about hitting token limits.
### Why This Matters
- **Efficiency**: Eliminates manual slicing of the codebase output.
- **Ease of Use**: Allows large repositories to be handled in one go, rather than requiring multiple runs or external scripts.
- **Flexibility**: Users who just want “the whole codebase in the AI” can get a straightforward multi-file output to paste into their model in chunks.
---
Thank you for considering this request! An automatic chunking feature would be a game-changer for those of us dealing with large codebases and strict LLM context limits.",
417,https://github.com/yamadashy/repomix/issues/417,open,"Improve ignore pattern parsing by trimming whitespace after `split(',')`","Currently, Repomix's ignore pattern parsing does not trim whitespace after splitting on `,`. This can lead to unexpected behavior, where patterns contain unintended leading spaces.
I plan to improve the parser to trim whitespace after split(',') so the first pattern style works as expected. There should be very few cases where leading or trailing spaces are actually needed in patterns, and in those rare cases, \s can be used.
related
- https://github.com/yamadashy/repomix/issues/397",
416,https://github.com/yamadashy/repomix/issues/416,open,Installation via nix,"I love installing programs using nix rather than `npm install`. I create a flake, nix package and home-manager module which allows you to install this program using nix!
https://github.com/m4dc4p/repomix-nix",
412,https://github.com/yamadashy/repomix/issues/412,open,Add support for analyzing npm packages directly,"## Add support for analyzing npm packages directly
### Feature Request
Add the ability to analyze npm packages directly using  their package name similar to the existing GitHub repository support.
### Motivation
Many developers use npm and know packages by name rather than the corresponding github repo. This would streamline the workflow for:
- Quick analysis of dependencies
- Evaluating potential packages before installation
- Understanding package internals for debugging or learning purposes
### Proposed Implementation
1. **Primary Method**: Extract GitHub repository information from package.json
```bash
repomix --npm-package express
```
- Parse the package's metadata to find the GitHub repository URL
- Use existing GitHub repository analysis functionality
2. **Fallback Method**: Direct package analysis from npm tarball
```bash
repomix --npm-package express --use-tarball
```
- Download and extract the package tarball
- Analyze contents directly when GitHub repository isn't available
### Technical Details
1. Use the npm registry API to fetch package metadata:
```
https://registry.npmjs.org/[package-name]
```
2. Extract repository information from:
- `repository` field in package.json
- `homepage` field (if it's a GitHub URL)
- `bugs` field (if it points to GitHub issues)
3. For tarball fallback:
- Use the `dist.tarball` URL from the package metadata
- Extract and analyze the contents locally
### Example Usage
```bash
# Using package name
repomix --npm-package express",
408,https://github.com/yamadashy/repomix/issues/408,open,Repomix Run On Open Files doesn't work,"Im using VS Code in windows clicking ""Repomix Run On Open Files"" command but output file is empty (however files are opened in VS Code tabs), please help",
400,https://github.com/yamadashy/repomix/issues/400,open,Bug: Negation patterns with `!` in ignore patterns don't work properly,"I'm experiencing an issue where negation patterns using the ! prefix in `ignore.customPatterns` are not properly excluding files from being ignored.
It appears this is related to limitations in fast-glob's handling of negative ignore patterns:
- https://github.com/mrmlnc/fast-glob/issues/86#issuecomment-*********
related:
- https://github.com/yamadashy/repomix/pull/396",
397,https://github.com/yamadashy/repomix/issues/397,closed,question / bug for file-globs,"I am trying to get a simple fileglob to work.
It does not work on the command line, it does work in repomix.config.json
CLI:
I have tried both
```
--ignore ""**/__tests__/**, **/theme/**""
and
--ignore ""**/{__tests__, theme}/**""
```
(the second syntax is from the fastglob repo linked in the readme as an example)
*Does not work*
repomix.config:
```
""customPatterns"": [""**/__tests__/**"", ""**/theme/**""]
```
*Does work*
What am I missing?",
392,https://github.com/yamadashy/repomix/issues/392,open,--remote from private repo via SSH,"Hello,
is it possible to clone the repo via ssh? i had a quick glance at the docs and code and couldn't find the feature!
if it isn't already implemented, are PRs welcome?
all the best",
388,https://github.com/yamadashy/repomix/issues/388,open,How can I remove Copyright messages in the output?,As they are redundant and useless.,
385,https://github.com/yamadashy/repomix/issues/385,open,"`""fileSummary"": false` doesn't work","Despite setting `""fileSummary"": false,` in `repomix.config.json` I'm still getting the file summary above the `# Directory Structure`.
The rest of the config seems to be working:
```
{
""output"": {
""filePath"": ""repomix-output.md"",
""style"": ""markdown"",
""parsableStyle"": false,
""directoryStructure"": true,
""removeComments"": false,
""removeEmptyLines"": false,
""compress"": false,
""topFilesLength"": 5,
""showLineNumbers"": false,
""copyToClipboard"": false
},
""include"": [],
""ignore"": {
""useGitignore"": true,
""useDefaultPatterns"": true,
""customPatterns"": [
""**/*.{md,svg}"",
""eslint.config.js"",
""package.json"",
""repomix.config.json"",
""CODEOWNERS"",
""**/.*""
]
},
""security"": {
""enableSecurityCheck"": true
}
}
```",
379,https://github.com/yamadashy/repomix/issues/379,open,window11中，cursor中无法使用Repomix: Run on open Files的功能,window11中，cursor中无法使用Repomix: Run on open Files的功能,
378,https://github.com/yamadashy/repomix/issues/378,open,Targeting remote while specifying nested output location fails,"Hello, it seems like using both --remote and --output fails at the following line of the `pack` function in the `packager` module.
```ts
yield deps.writeOutputToDisk(output, config);
```
config.cwd contains the full temp directory path instead of the actual working directory. If I pass an output location that is nested, it fails to find the file, presumably due to the parent directory not existing. That might need to be reworked to where the temp file output location is not dependent on the configured output location.
`--output docs/nested/filename.xml` fails, but `--output docs/filename.xml` is fine.",
377,https://github.com/yamadashy/repomix/issues/377,open,[Feature Request]: Upload folders on repomix website,"@yamadashy I implemented the zip upload feature. I would also like to add the folder upload feature. My approach would be:
- let the user upload the folder
- we will zip it in client side
- then pass it to the server side as we do it for the existing zip flow.
- i wont change the ui. just in the file upload region, one can also upload a folder.
What do you think?",
375,https://github.com/yamadashy/repomix/issues/375,open,Use the contents of .git/info/exclude when useGitignore is set to true.,"I leave useGitignore set to the default value of true. When running repomix, the repomix-output.txt still contains files that git ignores if they are listed in .git/info/exclude rather than .gitignore.",
372,https://github.com/yamadashy/repomix/issues/372,closed,Run without write access to source directory,"New user to repomix, and was testing with the docker image. It appears the application cannot run without write access to the source directory, even if the output file is specified to go elsewhere.
For example:
```
# mkdir /tmp/source
# echo hello > /tmp/source/world
# mkdir /tmp/out
# podman run -v /tmp/source:/app:ro -v /tmp/out:/out -t --rm ghcr.io/yamadashy/repomix -o /out/repomix
📦 Repomix v0.2.29
No custom config found at repomix.config.json or global config at /root/.config/repomix/repomix.config.json.
You can add a config file for additional settings. Please check https://github.com/yamadashy/repomix for more information.
✖ Error during packing
Unexpected error: Cannot access directory /app: undefined
For more help, please visit: https://github.com/yamadashy/repomix/issues
```
If you take the `:ro` flag off the volume mount it works, even though output has been redirected somewhere else. I'm not sure if it's trying to use the source directory as a temporary working directory or what. But IMHO it shouldn't be putting anything in it. Any temporary files should either go in the directory of the output file, `$XDG_RUNTIME_DIR`, or `/tmp/`.
Might also help if the error message would a little clearer. It took me a minute to figure out what the issue was, when the directory was there, readable, and not being used for output.",
368,https://github.com/yamadashy/repomix/issues/368,open,"Support absolute path in ""include""","Sometimes I need to reference files or directory outside current project, if I do this:
```js
{
""include"": [
""/Users/<USER>/Work/company/company-components/src/Inputs/Checkbox/""
]
}
```
it outputs error:
```
Error filtering files: path should be a `path.relative()`d string, but got
```",
361,https://github.com/yamadashy/repomix/issues/361,closed,RFC: Change Default Output Style to XML,"## Proposal
Change the default output style from `plain` to `xml` in version 0.3.0.
## Why?
- XML output provides better structure for AI processing
- I've been using XML as maintainer and found it more reliable
- Community feedback supports this change (see [Discord discussion](https://discord.com/channels/1324644561959522314/1325020123655835709/1340540106875863190))
## Impact
This is a breaking change that will require users who want plain text to explicitly specify:
```bash
repomix --style plain
```
or in config:
```json
{
""output"": {
""style"": ""plain""
}
}
```",
359,https://github.com/yamadashy/repomix/issues/359,open,[Compression] Preserve type declarations (critical for LLM context) while allowing function compression,"Type declarations are structural anchors for LLMs to understand code relationships, whereas function bodies can be safely compressed.",
356,https://github.com/yamadashy/repomix/issues/356,open,Idea: Bring fourth most frequently modified files,"I saw another [tool](https://github.com/foresturquhart/grimoire) on GitHub that someone posted on Hacker News. I was reading what it said, and one sentence caught my eye.
`It's a Go tool that converts directories into structured Markdown while smartly prioritizing frequently modified files to help LLMs focus on what's most important.`
If not already implemented in RepoMix, this could be a cool addition.",
355,https://github.com/yamadashy/repomix/issues/355,closed,Website fails for https://github.com/evanw/esbuild,,
352,https://github.com/yamadashy/repomix/issues/352,open,请问支持Java，Kotlin吗？任意编程语言都可以？,请问支持Java，Kotlin吗？任意编程语言都可以？,
350,https://github.com/yamadashy/repomix/issues/350,open,Migrate Repomix to browser-side execution,"Following @huy-trn's suggestion in the [previous discussion](https://github.com/yamadashy/repomix/issues/349#issuecomment-2651443446), let's explore the possibility of migrating Repomix to browser-side execution. This would enable static site hosting and potentially reduce server costs.
The main challenge would be the git clone functionality. I've been thinking that [isomorphic-git](https://github.com/isomorphic-git/isomorphic-git) might be a solution, along with addressing any library incompatibilities.",
349,https://github.com/yamadashy/repomix/issues/349,open,Idea: Make a ChatGPT GPT that interacts with repomix,I am the alt of @SpyC0der77 . Later today I will create a GPT  that has an action to integrate it with RepoMix. You will be able to enter a github URL and it will request repomix and give it the data of the repo.,
346,https://github.com/yamadashy/repomix/issues/346,closed,Allow Trailing Commas in `repomix.config.json`,"When working with the `""includes""` key, I often comment out file paths to quickly test or calculate final token counts. However, I have to manually remove the trailing comma every time. Since comments are already supported in `repomix.config.json`, it would be very helpful if trailing commas were supported as well.
For reference, you can see where this is handled in our code:
https://github.com/yamadashy/repomix/blob/b55c59d0dae9ba0c5bc534318c2a414d848f5ded/src/config/configLoad.ts#L70
Additionally, the [json5](https://www.npmjs.com/package/json5) library, which we could consider as an alternative, supports trailing commas.",
341,https://github.com/yamadashy/repomix/issues/341,closed,Remove generation timestamp to reduce AI API costs,"The timestamp ""Generated by Repomix on: [date]"" at the beginning of output files causes AI systems (OpenAI/Anthropic) to mark the entire prompt as a ""cache miss"", increasing API costs by ~20-40%.
Reported by rooox on [Discord](https://discord.com/channels/1324644561959522314/1325020123655835709/1336999870799810561)
# Proposal
Remove the timestamp line from the output as it provides little value while causing significant cost implications for AI processing.",
339,https://github.com/yamadashy/repomix/issues/339,open,Idea: Diff output mode,"Hi Repomix Team!
I’d like to propose an enhancement that could be very useful for code reviews and tracking changes.
Currently, Repomix outputs the whole repository as one file, which is great—but it would be even more powerful if there were an option to generate a hybrid output. In this mode, the full project is included as usual, but files that are “affixed” or flagged for change would be presented in a diff format (for example, showing the differences between branches or between the current state and a specific branch). Meanwhile, all other files would be displayed in their standard format.
This hybrid mode would allow _AI reviewers_ to easily see what has changed without duplicating the entire codebase for both states. It would combine a complete view of the repository with a clear, focused diff for files that have been modified, streamlining the review process and helping to quickly identify important changes.
Thanks!",
328,https://github.com/yamadashy/repomix/issues/328,open,"Fix request: When a user includes files, repomix still says 'this is the entire codebase'","Example command:
`repomix --include ""pages/_app.js,pages/_document.js,components/Header.js,lib/supabaseClient.js,pages/auth.js"" --copy`
Example output:
```
This file is a merged representation of the entire codebase, combining all repository files into a single document.
Generated by Repomix on: 2025-01-29T11:23:01.763Z
================================================================
================================================================
Purpose:
--------
This file contains a packed representation of the entire repository's contents.
It is designed to be easily consumable by AI systems for analysis, code review,
or other automated processes.
File Format:
------------
The content is organized as follows:
2. Repository information
3. Directory structure
4. Multiple file entries, each consisting of:
a. A separator line (================)
b. The file path (File: path/to/file)
c. Another separator line
d. The full contents of the file
e. A blank line
Usage Guidelines:
-----------------
- This file should be treated as read-only. Any changes should be made to the
original repository files, not this packed version.
- When processing this file, use the file path to distinguish
between different files in the repository.
- Be aware that this file may contain sensitive information. Handle it with
the same level of security as you would the original repository.
Notes:
------
- Some files may have been excluded based on .gitignore rules and Repomix's
configuration.
- Binary files are not included in this packed representation. Please refer to
the Repository Structure section for a complete list of file paths, including
binary files.
Additional Info:
----------------
================================================================
Directory Structure
================================================================
components/
Auth.js
Header.js
Layout.js
context/
AuthContext.js
lib/
supabaseClient.js
pages/
auth/
callback.js
confirm.js
_app.js
_document.js
auth.js
================================================================
Files
================================================================
```
Even though repomix says that some files might be excluded, this is contradictory to its statement at the top that 'this is the entire codebase', which is not AI-friendly, substantiated by the fact that Deepseek-R1 still thinks that it's the full codebase:
```","Steps: Critical Missing Piece:
javascript
Copy
// pages/auth/callback.js - MISSING IN YOUR CODEBASE
// This is essential for handling OAuth callbacks
...
etc
```
It's not missing, I just forgot to include it! I hope that's clear.
The requested change is to have the pasted output say 'this is a merged representation of part of the codebase', or something similar, if only some files are included.
Thanks to all the contributors - awesome project."
327,https://github.com/yamadashy/repomix/issues/327,closed,Feature request: Allow option to specify a list of files,"The --include option allows me to include blobs which is nice, but if I'd like to pack together a particular list of say 8 files, this would be very useful to just have an option to do something like
`repomix --list lib/file.js components/Auth.js pages/index.js --copy`
to allow more efficient and targeted file inclusions than using Regex which can be quite complex and perhaps unnecessary for most use-cases.
Thanks in advance - awesome project.",
325,https://github.com/yamadashy/repomix/issues/325,open,Idea: Support multiple configurations within a single repomix.config.json,"There are times when I'm workin non a large project which has multiple subdirectories within it.
If I'm only working inside of one of the subdirectories and I use repomix on the entire project it will be a massive file and a waste of tokens.
I would like to be able to place a `repomix.config.json` file at the root of the project and define a default config which packs the entire project by running
```
npx repomix
```
But then I would like to be able to pack a specific subdirectory by specifying a configuration such as
```
npx repomix my-ui-config
```
or
```
npx repomix my-api-config
```
The config file could look something like this:
```
{
""default"": {
""output"": {
""filePath"": ""my-whole-project.xml"",
""style"": ""xml""
},
""include"": [],
""ignore"": {
""useGitignore"": true,
""useDefaultPatterns"": true,
""customPatterns"": []
}
},
""my-ui-project"":{
""output"": {
""filePath"": ""my-ui-project.xml"",
""style"": ""xml""
},
""include"": [],
""ignore"": {
""useGitignore"": true,
""useDefaultPatterns"": true,
""customPatterns"": [
""API-Project/""<======= Ignoring non UI related directories
]
}
},
""my-api-project"":{
""output"": {
""filePath"": ""my-ui-project.xml"",
""style"": ""xml""
},
""include"": [],
""ignore"": {
""useGitignore"": true,
""useDefaultPatterns"": true,
""customPatterns"": [
""UI-Project/"" <======= Ignoring non API related directories
]
}
}
}
```
Where if the `default` config is applied if `npx repomix` is called without any arguments.
Many other projects support similar configs, for example when building an angular project you can specify the build configuration like this:
```
ng build --configuration production
```
Where the `production` configuration is defined inside the `angular.json` file.",
323,https://github.com/yamadashy/repomix/issues/323,closed,Feature request: pack only currently open files in VSCode (and forks like CursorAI or Windsurf),"I would like to request a new feature that allows Repomix to pack only the files currently open in Visual Studio Code and its forks such as CursorAI or Windsurf.
**Motivation:**
Often, I already have the relevant files open in the editor, but I cannot efficiently use inclusion/exclusion patterns to specify them. Having an option to directly pack the files open in the current editor session would greatly streamline the workflow.
Furthermore, most developers already use an IDE based on Visual Studio Code, if not VSCode itself. Therefore, adding this feature would likely see high adoption with minimal friction. This enhancement would allow users to pack only the currently opened files without the need to manually specify inclusion/exclusion patterns, improving efficiency and ease of use.
**Proposed Solution:**
Implement a command-line option that detects and packs only the files currently open in the editor, leveraging the API of VSCode and compatible forks or by reading the workspace state.
**Benefits:**
- Saves time by eliminating the need to manually define patterns.
- Provides more accurate file selection.
- Facilitates integration with customized development environments.","Expected: ```sh
repomix --active-files
```
This command should generate a packed file containing only the currently open files in the editor session.
**Considered Alternatives:**
Currently, manually specifying the files or relying on include/exclude patterns is required, which can be tedious and imprecise in certain scenarios."
320,https://github.com/yamadashy/repomix/issues/320,open,"[BUG] `.repomixignore` file ignored when `ignore.customPatterns` contains `""**/.*""`","First of all, thank you for creating and maintaining `repomix`! It's a fantastic tool.
I've encountered an issue while using the `ignore.customPatterns` in `repomix.config.json`. When I add `""**/.*""` in `ignore.customPatterns`, the `.repomixignore` file is completely ignored, and the patterns defined in it are no longer taken into account.","Steps: 1. Add `""**/.*""` to `ignore.customPatterns` in `repomix.config.json`:
```json
{
""ignore"": {
""customPatterns"": [""**/.*""]
}
}
```
2. Create a `.repomixignore` file with some patterns, e.g.:
```ignore
*.log
/temp/
```
3. Run `repomix` and observe that the patterns in `.repomixignore` are not applied.

Expected: The patterns in `.repomixignore` should still be respected and applied, even if `ignore.customPatterns` includes `""**/.*""`.

Actual: The `.repomixignore` file is ignored entirely, and its patterns are not applied.
### Additional Context:
This behavior seems unintended, as the `ignore.customPatterns` and `.repomixignore` should ideally work together rather than one overriding the other.
Let me know if you need further details or if there’s anything I can do to help debug this issue. Thanks again for your work on this project!"
316,https://github.com/yamadashy/repomix/issues/316,closed,Missing CLI flags for some configuration options,"Hi @yamadashy ! 👋
While analyzing the correspondence between CLI flags and configuration options, I noticed that some configuration fields don't have corresponding CLI flags. I'd like to know if this is intentional or if we should add them for consistency.
### Current missing CLI flags for existing config options:
- `output.headerText` -> add `--header-text` ?
- `output.instructionFilePath` -> add`--instruction-file` ?
- `output.includeEmptyDirectories` -> add `--include-empty-directories` ?
- `ignore.useGitignore` -> add `--no-gitignore` ?
- `ignore.useDefaultPatterns` -> add `--no-default-patterns` ?
## Use Case
I specifically need these CLI flags for the Repomix Runner VS Code extension. Currently, the extension passes VS Code configuration to Repomix through CLI flags.
I'd be happy to submit a PR to add them if you don't have time.",
312,https://github.com/yamadashy/repomix/issues/312,open,Improve handling of large repositories in website,"Fix performance issues when processing large repositories through the web interface, specifically addressing request timeouts and UI hangs during rendering.
- Large repositories generate very large output files
- HTTP requests timeout during processing
- Browser hangs when trying to render large output
- Particularly problematic on mobile devices
Issue was requested by user @gaby
ref
https://github.com/yamadashy/repomix/issues/219#issuecomment-2572280926",
311,https://github.com/yamadashy/repomix/issues/311,closed,Add URL parsing for branch/commit information,"Add functionality to parse branch, tag, and commit information from repository URLs.
Different repository URL formats need to be supported:
- `owner/repo`
- `https://github.com/owner/repo`
- `https://github.com/owner/repo/tree/branch`
- `https://github.com/owner/repo/commit/hash`
Feature was requested by user @gaby
ref
https://github.com/yamadashy/repomix/issues/219#issuecomment-2573359838",
310,https://github.com/yamadashy/repomix/issues/310,closed,Add zip/folder upload functionality to website,"This feature will allow users to upload zip archives or folders directly through the Repomix website (repomix.com), providing an alternative to the CLI tool.
Feature was requested by user @huy-trn
ref
https://github.com/yamadashy/repomix/issues/219#issuecomment-2571282619",
303,https://github.com/yamadashy/repomix/issues/303,open,can it able to do the same with WIKI?,I am wondering if this amazing text file can be made of the wiki of a GitHub repo to pass them to the LLM?,
297,https://github.com/yamadashy/repomix/issues/297,closed,Call repomix from within a node script,"Hi, I would like to use repomix in my application. Is it possible to call repomix as a node library, as opposed to a command line tool? If so, is there documentation on that somewhere? Thank you!",
286,https://github.com/yamadashy/repomix/issues/286,open,Would anyone be interested in me building an Electron option for drag and drop?,"I'm pretty visual and sometimes I work with files across different directories, it would be great to be able to have a UI component like a ""Dropbox"" that you can drag and drop files into which then gets piped into Repomix CLI
Anyone interested?",
282,https://github.com/yamadashy/repomix/issues/282,closed,XML Escaping,"I like the feature of having a structured output via XML. However, the string concatenation as it is done in `xmlStyle.ts` doesn't really work. XML requires escaping of certain characters: https://stackoverflow.com/questions/1091945/what-characters-do-i-need-to-escape-in-xml-documents
Basically, if the repo you are reading with repomix contains an XML file itself, it will be parsed as part of the repo tree, not as file contents. The ""&&"" symbol that is used for boolean conjunction in many languages is disallowed in XML files entirely and will make the output invalid.
The proper solution would be to use an XML serializer that takes care of any escaping that is needed.",
278,https://github.com/yamadashy/repomix/issues/278,closed,GitHub repo name including dot doesn't work,"Thank you for the great repo.
GitHub URL validation doesn't work with repo names including dot.
Example GitHub URLs.
`https://github.com/needle-mirror/com.unity.visualscripting`
<img width=""785"" alt=""Screenshot 2025-01-10 at 10 14 02"" src=""https://github.com/user-attachments/assets/2cee6d91-90f7-41f6-93c9-b1ce37a3a60d"" />",
274,https://github.com/yamadashy/repomix/issues/274,closed,SyntaxError: Unexpected token 'with' when running repomix,"When running repomix, the command fails with a SyntaxError related to the with keyword. This suggests a compatibility issue with newer Node.js versions or ESM modules.","Steps: `npx repomix`
Error Output:
```
Fatal Error: {
name: 'SyntaxError',

Expected: '    at ESMLoader.moduleStrategy (node:internal/modules/esm/translators:119:18)\n' +
'    at ESMLoader.moduleProvider (node:internal/modules/esm/loader:468:14)'
}
```"
258,https://github.com/yamadashy/repomix/issues/258,closed,"Github gist is giving errors, how can we fix this?","Getting this error:
npx repomix --remote https://github.com/nikahmadz/4046cf69caf4ddc68ea5e293e6afdc0e
npm error code ERR_INVALID_URL
npm error Invalid URL
npm error A complete log of this run can be found in: C:\Users\<USER>\AppData\Local\npm-cache\_logs\2025-01-05T08_00_23_247Z-debug-0.log
Log:
0 verbose cli C:\Program Files\nodejs\node.exe C:\Users\<USER>\AppData\Roaming\npm\node_modules\npm\bin\npm-cli.js
1 info using npm@10.9.2
2 info using node@v22.11.0
3 silly config load:file:C:\Users\<USER>\AppData\Roaming\npm\node_modules\npm\npmrc
4 silly config load:file:\\wsl.localhost\Ubuntu\home\user123_789\.npmrc
5 silly config load:file:C:\Users\<USER>\.npmrc
6 silly config load:file:C:\Users\<USER>\AppData\Roaming\npm\etc\npmrc
7 verbose title npm exec repomix --remote https://github.com/nikahmadz/4046cf69caf4ddc68ea5e293e6afdc0e
8 verbose argv ""exec"" ""--"" ""repomix"" ""--remote"" ""https://github.com/nikahmadz/4046cf69caf4ddc68ea5e293e6afdc0e""
9 verbose logfile logs-max:10 dir:C:\Users\<USER>\AppData\Local\npm-cache\_logs\2025-01-05T08_00_23_247Z-
10 verbose logfile C:\Users\<USER>\AppData\Local\npm-cache\_logs\2025-01-05T08_00_23_247Z-debug-0.log
11 silly logfile start cleaning logs, removing 3 files
12 verbose stack TypeError: Invalid URL
12 verbose stack     at new URL (node:internal/url:816:29)
12 verbose stack     at fromFile (C:\Users\<USER>\AppData\Roaming\npm\node_modules\npm\node_modules\npm-package-arg\lib\npa.js:263:15)
12 verbose stack     at resolve (C:\Users\<USER>\AppData\Roaming\npm\node_modules\npm\node_modules\npm-package-arg\lib\npa.js:71:12)
12 verbose stack     at npa (C:\Users\<USER>\AppData\Roaming\npm\node_modules\npm\node_modules\npm-package-arg\lib\npa.js:53:10)
12 verbose stack     at FetcherBase.get (C:\Users\<USER>\AppData\Roaming\npm\node_modules\npm\node_modules\pacote\lib\fetcher.js:474:16)
12 verbose stack     at Object.manifest (C:\Users\<USER>\AppData\Roaming\npm\node_modules\npm\node_modules\pacote\lib\index.js:20:29)
12 verbose stack     at hasPkgBin (C:\Users\<USER>\AppData\Roaming\npm\node_modules\npm\node_modules\libnpmexec\lib\index.js:82:10)
12 verbose stack     at exec (C:\Users\<USER>\AppData\Roaming\npm\node_modules\npm\node_modules\libnpmexec\lib\index.js:137:17)
12 verbose stack     at Exec.callExec (C:\Users\<USER>\AppData\Roaming\npm\node_modules\npm\lib\commands\exec.js:79:12)
12 verbose stack     at Exec.exec (C:\Users\<USER>\AppData\Roaming\npm\node_modules\npm\lib\commands\exec.js:28:17)
13 error code ERR_INVALID_URL
14 error Invalid URL
15 verbose cwd \\wsl.localhost\Ubuntu\home\user123_789
16 verbose os Windows_NT 10.0.26100
17 verbose node v22.11.0
18 verbose npm  v10.9.2
19 verbose exit 1
20 verbose code 1
21 error A complete log of this run can be found in: C:\Users\<USER>\AppData\Local\npm-cache\_logs\2025-01-05T08_00_23_247Z-debug-0.log",
257,https://github.com/yamadashy/repomix/issues/257,open,Interested in using as a library and not using command line,"Hello,
I am working on my own project that dose AI assisted edits to existing files. https://github.com/mmiscool/aiCoder
I would be interested in using this tool as a library with an API.
some thing like:
```javascript
import {repomix} from 'repomix';
const generatedText = repomix.generateMarkdown(""pathToFolder"",
{
""output"": {
""style"": ""markdown"",
""removeComments"": true,
""showLineNumbers"": true,
""topFilesLength"": 10
},
""ignore"": {
""customPatterns"": [""*.test.ts"", ""docs/**""]
}
});
```
Is there any plan to support doing this?",
240,https://github.com/yamadashy/repomix/issues/240,open,(node:23021) ExperimentalWarning: Importing JSON modules is an experimental feature and might change at any time (Use `node --trace-warnings ...` to show where the warning was created) 0.2.12,"```
wangzhiyong@wangzhiyongdeMacBook-Pro ~ % repomix --version
(node:23021) ExperimentalWarning: Importing JSON modules is an experimental feature and might change at any time
(Use `node --trace-warnings ...` to show where the warning was created)
0.2.12
wangzhiyong@wangzhiyongdeMacBook-Pro ~ %
```
Everytime I type `repomix --version` this annoying error message shows up.",
226,https://github.com/yamadashy/repomix/issues/226,open,Support for unpack,"Since this tool is to pack a repo (codebase) into a prompt, after asking ChatGPT or other LLM AI to review, the LLM should edit the `repomix-output.txt`, and `repomix` should support unpack to apply LLM edited changes in the repo.",
221,https://github.com/yamadashy/repomix/issues/221,closed,Add support for Docker,Add support for running `repomix` using Docker.,
219,https://github.com/yamadashy/repomix/issues/219,open,Idea: Add a demo website,"I think adding a demo website using github pages would be nice.
People can paste link to a public repo, or upload a repository's archive to get repomix's output that they can easily copy without installing anything.",
211,https://github.com/yamadashy/repomix/issues/211,closed,repomix --remote https://unpkg.com/repomix,"Although this project is lit, people may think it's dangerous to let thrid-party cli to scan whole codebase. repomix needs to provide a way to convince people this project is legit, so one thing is to pack it self, not the github repo, cause github repo and npm can be totally two different codebase, it's a really simple attack method, so, one ease solution is to let repomix support `repomix --remote https://unpkg.com/repomix` , this way it can easily pack itself, people can just post the whole thing to LLM to verify if it contains mailicious code.While this project is fantastic, some users might hesitate to allow a third-party CLI to scan their entire codebase. To build confidence and demonstrate the project's legitimacy, **Repomix** could offer a mechanism to validate itself as a trusted tool.
One potential concern is the discrepancy between the GitHub repository and the published npm package, as malicious actors could exploit this difference with a simple attack method.
**Proposed Solution**
Introduce a feature to allow Repomix to package itself directly from a remote source. For example:
```bash
repomix --remote https://unpkg.com/repomix
```
With this feature:
- Users can fetch and package Repomix directly from its deployed version.
- They could then analyze the resulting package (e.g., with an LLM) to ensure no malicious code is present.
This would make it easier for users to trust and adopt Repomix while mitigating potential security concerns.",
210,https://github.com/yamadashy/repomix/issues/210,closed,Ignored files can't be found in folder tree.,Add an option to keep it.,
209,https://github.com/yamadashy/repomix/issues/209,closed,Support comments in `repomix.config.json`,"When dealing with a large codebase, people need to rapidly select which file paths to ignore and which to **include** in order to achieve acceptable token counts. The ability to add comments to `repomix.config.json` is critical.",
208,https://github.com/yamadashy/repomix/issues/208,open,Unstable `removeComments` and `removeEmptyLines`,"My codebase has **15,382,523 tokens** , when I set the `removeComments` and `removeEmptyLines` `true` , the terminal stucks forever, even ctrl-c not working.",
207,https://github.com/yamadashy/repomix/issues/207,open,Browser Extension,"I've created the following:
https://gist.github.com/kfrancis/bbb57d85306246b72130f4a62ae7b946
![image](https://github.com/user-attachments/assets/7580dcdd-d2d9-4a13-8e3b-b369c657de00)
Settings are there, but don't work yet - just so you can get an idea:
![image](https://github.com/user-attachments/assets/de1cfe18-e75b-4e97-988a-690514ff1a8e)",
206,https://github.com/yamadashy/repomix/issues/206,open,Removing File Summary header,"Idea: Being able to omit and/or customize File Summary. I just want the code in the output, not anything else.
Proposal: Adding `output.summaryText` or `output.removeFileSummary`would be great.",
202,https://github.com/yamadashy/repomix/issues/202,open,Idea:  Add an option to make the output more friendly with RAG engines,"Hello @yamadashy ,
The tool is already great, but larger repositories will never fit into an LLM's context window.
I know enterprise-level RAG systems have been around for a while, but sticking to user-friendly solutions, here’s what I’m thinking:
- What to do:  Make the output of Repomix easier to parse, and more meaningful when retrieved by any RAG engine, such as the ""chat with documents"" feature that’s common in most LLM applications.
- How to do: Split large code files into smaller chunks using the AST, then merge them back into a single output. Also, add separators between chunks that are recognizable by most text splitters.
I’ve recently tried Langchain’s [source code loader](https://python.langchain.com/docs/integrations/document_loaders/source_code/), and this approach should be easy to implement with a few additional dependencies.
If this sounds good, I’d be happy to open a PR for it! Let me know your thoughts.",
198,https://github.com/yamadashy/repomix/issues/198,open,Repomap - A Repomix/Mermaid/GenAI repo visualisation tool,"I've used Repomix to make Repomap.
https://github.com/George5562/Repomap
It creates a Repomix XML, uses an LLM with Langchain to convert to mermaid so you can visualise your project as a flowchart.
You can specify the types of nodes (pages, components, functions etc) and edges/relationships (depends on, uses, renders etc).
Using the --add CLI you can add an enhancement ""add nextAuth"", and two additional LLM calls will create a suggested file structure, and then create an updated flowchart, now with possible future components added.",
197,https://github.com/yamadashy/repomix/issues/197,open,"npx repomix  kabooms with ""Cannot find module ... cliRun.js""","Might be a user error and RTFM (not a JS fella here) but tried to follow README.md stating:
> You can try Repomix instantly in your project directory without installation:
>
> ```bash
> npx repomix
> ```
and got (tried two ways) while running within the fresh clone at v0.2.5-10-g05589f7 :
```shell
❯ npx repomix
Fatal Error: {
name: 'Error',
message: ""Cannot find module '/home/<USER>/proj/misc/repomix/lib/cli/cliRun.js' imported from /home/<USER>/proj/misc/repomix/bin/repomix.cjs"",
stack: ""Error [ERR_MODULE_NOT_FOUND]: Cannot find module '/home/<USER>/proj/misc/repomix/lib/cli/cliRun.js' imported from /home/<USER>/proj/misc/repomix/bin/repomix.cjs\n"" +
'    at finalizeResolution (node:internal/modules/esm/resolve:265:11)\n' +
'    at moduleResolve (node:internal/modules/esm/resolve:933:10)\n' +
'    at defaultResolve (node:internal/modules/esm/resolve:1169:11)\n' +
'    at ModuleLoader.defaultResolve (node:internal/modules/esm/loader:542:12)\n' +
'    at ModuleLoader.resolve (node:internal/modules/esm/loader:510:25)\n' +
'    at ModuleLoader.getModuleJob (node:internal/modules/esm/loader:239:38)\n' +
'    at ModuleLoader.import (node:internal/modules/esm/loader:472:34)\n' +
'    at defaultImportModuleDynamicallyForScript (node:internal/modules/esm/utils:227:31)\n' +
'    at importModuleDynamicallyCallback (node:internal/modules/esm/utils:249:12)\n' +
'    at /home/<USER>/proj/misc/repomix/bin/repomix.cjs:42:21'
}
❯ find -iname cliRun.js
❯ find -iname cliRun.*
./src/cli/cliRun.ts
./tests/cli/cliRun.test.ts
❯ npx .
Fatal Error: {
name: 'Error',
message: ""Cannot find module '/home/<USER>/.npm/_npx/bc821935aa97a4d0/node_modules/repomix/lib/cli/cliRun.js' imported from /home/<USER>/.npm/_npx/bc821935aa97a4d0/node_modules/repomix/bin/repomix.cjs"",
stack: ""Error [ERR_MODULE_NOT_FOUND]: Cannot find module '/home/<USER>/.npm/_npx/bc821935aa97a4d0/node_modules/repomix/lib/cli/cliRun.js' imported from /home/<USER>/.npm/_npx/bc821935aa97a4d0/node_modules/repomix/bin/repomix.cjs\n"" +
'    at finalizeResolution (node:internal/modules/esm/resolve:265:11)\n' +
'    at moduleResolve (node:internal/modules/esm/resolve:933:10)\n' +
'    at defaultResolve (node:internal/modules/esm/resolve:1169:11)\n' +
'    at ModuleLoader.defaultResolve (node:internal/modules/esm/loader:542:12)\n' +
'    at ModuleLoader.resolve (node:internal/modules/esm/loader:510:25)\n' +
'    at ModuleLoader.getModuleJob (node:internal/modules/esm/loader:239:38)\n' +
'    at ModuleLoader.import (node:internal/modules/esm/loader:472:34)\n' +
'    at defaultImportModuleDynamicallyForScript (node:internal/modules/esm/utils:227:31)\n' +
'    at importModuleDynamicallyCallback (node:internal/modules/esm/utils:249:12)\n' +
'    at /home/<USER>/.npm/_npx/bc821935aa97a4d0/node_modules/repomix/bin/repomix.cjs:42:21'
}
```",
195,https://github.com/yamadashy/repomix/issues/195,closed,Feature Request: Add Branch Option for Remote Repository Packing,"Hello @yamadashy ,
I'm new to this project but I'm willing to help creating a PR. I think it would be beneficial to add an option to select a specific commit ID or branch when packing a remote repository.
Current Behavior:
When packing a remote repository, the process uses the default branch.
Proposed Feature:
Add a branch option to the packing process, allowing users to select a specific commit ID or branch to pack.
Potential Implementation:
```
repomix --remote https://github.com/yamadashy/repomix --branch master
```",
193,https://github.com/yamadashy/repomix/issues/193,open,Bug: Fatal Error when running on MacOS,,Steps: 1. `npm install -g repomix` or `npx repomix`
192,https://github.com/yamadashy/repomix/issues/192,open,Idea: Community directory of popular libraries ,"I put this idea together quickly with the idea of making a directory of some sorts of popular libraries. Or maybe someone uses a set of common packages that they want to always have AI friendly RAG files, but they need to customize the configs for each library to try to limit the token count to only the most important files.
I imagine the more worked-out idea of this would be to be able to combine source code and docs of specific methods/interfaces/features of the libraries you're working with.
Anyways, cool package @yamadashy !
https://github.com/austinm911/prompts",
191,https://github.com/yamadashy/repomix/issues/191,closed,command line flag for turning off security check (reopens #74),"Add a command line flag to disable the security check. It is always a false positive when running repomix on documentation.
https://github.com/yamadashy/repomix/issues/74",
181,https://github.com/yamadashy/repomix/issues/181,open,"Add option to only exclude in ""Repository Files"", but not in ""Repository Structure""","Hi @yamadashy , really nice work. I love this tool.
By the way, why don't you add option only exclude in ""Repository Files""?
Currently, `--ignore` option both exclude in ""Repository Structure"" and ""Repository Files"".
For example, my target repository has this kind of file:
```
📈 Top 5 Files by Character Count and Token Count:
──────────────────────────────────────────────────────
1.  apps/frontend/src/app/fixtures/transcripts1.json (1145024 chars, 377136 tokens)
2.  apps/frontend/public/locales/en/common.json (192476 chars, 82151 tokens)
3.  apps/admin/public/assets/CompanySign.svg (185918 chars, 132207 tokens)
...
```
In this case, I don't want to store file content but want to include in repository structure.
I think `--exclude` option is one good name for this purpose.",
175,https://github.com/yamadashy/repomix/issues/175,open,Standardized markdown,"The llms.txt initiative introduces a standardized markdown.
I don't know if we should follow this format
[LLM Txt](https://llmstxt.org/)",
170,https://github.com/yamadashy/repomix/issues/170,open,Share Your Experience with Repomix,"Hi everyone! I’m new to coding and rely on Claude for a lot of guidance, so I find Repomix super helpful for providing project context. However, I only want the project structure—not the actual code—because including everything fills up my context window too quickly. Specifically, I need just the directory and file names under project/frontend/ and project/backend/ without going too deep. Is there a command to display this structure at a high level, showing just the folder and file names?
Thanks for the help!",
168,https://github.com/yamadashy/repomix/issues/168,open,Showcase Video for this project will be truly appreciated,"Please create small video to showcase this project functionality so that people can have generally idea of what to do and how to do. There is btw openwebui great open source project so maybe you can try to showcase with that project as they provide you one of the best frontend which has advanced functionalities like functions,tools,vector db etc etc with Litellm (another great open source project which can help you use 100+ LLM's easily) .
Thankyou very much for this great open source project :-))",
164,https://github.com/yamadashy/repomix/issues/164,open,Feature Request: Code Detail Level,"If your project is sufficiently well documented, having a block of text describing the intent, intput and example outputs for each function, then would it be useful to have an option to only include the method signatures and associated documentation, and not the full code?
This might reduce the context a bit and still give the LLM what it actually needs to write the new item you are asking it for.",
163,https://github.com/yamadashy/repomix/issues/163,open,Remove images from notebooks,"Right now, the repomix generated file includes the images generated in `.ipynb`.
```
{
""cell_type"": ""code"",
""execution_count"": 7,
""metadata"": {},
""outputs"": [
{
""name"": ""stdout"",
""output_type"": ""stream"",
""text"": [
""Max Training: 516.0\n"",
""Min Training: 1.0\n"",
""Mean Training: 58.099391480730226\n"",
""Median Training: 39.0\n"",
""\n"",
""\n"",
""Max Validation: 23.0\n"",
""Min Validation: 1.0\n"",
""Mean Validation: 1.8539553752535496\n"",
""Median Validation: 1.0\n"",
""\n"",
""\n""
]
},
{
""data"": {
""image/png"": ""iVBORw0KGgoAAAANSUhEUgAABIcAAAE/CAYAAADc0KMkAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAALEgAACxIB0t1+/AAAADl0RVh0U29mdHdhcmUAbWF0cGxvdGxpYiB2ZXJzaW9uIDI ...
```
I think there should be some preprocessing to remove them (or put a placeholder), since they don't add any value and take up many space (thus making it harder to retrieve the truly relevant information).
My dirty solution for now is to go and remove the lines:
```python
input_file = 'repomix.txt'
output_file = 'repomix_modified.txt'
with open(input_file, 'r') as file:
lines = file.readlines()
with open(output_file, 'w') as file:
for line in lines:
if not line.startswith('      ""image/png"": ""'):
file.write(line)
```
The generated repomix file went from 5MB to 300kB.",
162,https://github.com/yamadashy/repomix/issues/162,open,illegal operation on folder?,"I accidentally hit ""deny"" when macOS asked for the security/privacy permissions for Repomix, and now when I run repomix I get this error:
Unexpected error: EISDIR: illegal operation on a directory, open '/users/myUserNameHere'
even if I cd to another directory it won't let me run anything.
I've tried uninstalling, reinstalling, and doing a full tccuntil reset.",
161,https://github.com/yamadashy/repomix/issues/161,closed,"How to include empty folders/files in the ""Repository Structure"" for more context?","First, thank you all for your work.
I was expecting the whole project structure to be included in the output file (even for empty folders and files, to give context to the LLM on how the project should evolve or what it should exactly work on).",
152,https://github.com/yamadashy/repomix/issues/152,open,Feature Request: Copy to clipboard after packing,Maybe a `repomix -x` or something to copy to clipboard after generating or a config option would be nice.,
149,https://github.com/yamadashy/repomix/issues/149,open,Add file truncation,"The use case is: I have multiple JSON data files. I want to include them in the LLM input, but only to show their structure, not the contents. I'd like to be able to specify that I just want to include the first N lines.",
146,https://github.com/yamadashy/repomix/issues/146,open,Unable to use repopack in ubuntu 20.04,"Unable to use repopack in ubuntu 20.04
I install npm just by ""sudo apt install npm"", then either just run ""npx repopack"" or installed by npm then run ""repopack"", it get this error
(node:52216) UnhandledPromiseRejectionWarning: Error: Not supported
at /usr/local/lib/node_modules/repopack/bin/repopack.cjs:5:19
at Object.<anonymous> (/usr/local/lib/node_modules/repopack/bin/repopack.cjs:7:3)
at Module._compile (internal/modules/cjs/loader.js:778:30)
at Object.Module._extensions..js (internal/modules/cjs/loader.js:789:10)
at Module.load (internal/modules/cjs/loader.js:653:32)
at tryModuleLoad (internal/modules/cjs/loader.js:593:12)
at Function.Module._load (internal/modules/cjs/loader.js:585:3)
at Function.Module.runMain (internal/modules/cjs/loader.js:831:12)
at startup (internal/bootstrap/node.js:283:19)
at bootstrapNodeJSCore (internal/bootstrap/node.js:623:3)
(node:52216) UnhandledPromiseRejectionWarning: Unhandled promise rejection. This error originated either by throwing inside of an async function without a catch block, or by rejecting a promise which was not handled with .catch(). (rejection id: 2)
(node:52216) [DEP0018] DeprecationWarning: Unhandled promise rejections are deprecated. In the future, promise rejections that are not handled will terminate the Node.js process with a non-zero exit code.",
140,https://github.com/yamadashy/repomix/issues/140,closed,Broken link (LICENSE) in README,"Current Behavior:
LICENSE in README redirects to invalid link ( 404 )","Expected: It should redirect to correct link
![image](https://github.com/user-attachments/assets/e7b8a3f0-3114-42e0-8dcd-27e0b8916c88)
![image](https://github.com/user-attachments/assets/d909ec95-3bac-4f0d-9099-e9e9284cdb7f)"
133,https://github.com/yamadashy/repomix/issues/133,open,Feature Request : erasing line gaps,if a file has unnecessary line gaps the they are not removed which makes the file more bulky.  is it possible to remove unnecessary line gaps to make the files more relevant ?,
132,https://github.com/yamadashy/repomix/issues/132,open,Streamline Logic and Error Handling in `configLoad.ts`,"The code in `src/config/configLoad.ts` can be optimized for better efficiency. Suggested improvements include:
1. **Parallelize File Checks:** Execute the local and global configuration file existence checks in parallel using `Promise.all` to reduce execution time.
2. **Refactor `mergeConfigs` Function:** Simplify the merging logic by using helper functions and `Array.prototype.flat()` to avoid repetitive code.
3. **Enhance Error Handling:** Consolidate error handling in `loadAndValidateConfig` to make the code more concise and maintainable.
Implementing these changes will enhance code efficiency without affecting existing functionality.",
131,https://github.com/yamadashy/repomix/issues/131,open,Improve Efficiency and Clarity in `fileSearch.ts`,"The `fileSearch.ts` file can be optimized for better performance and readability:
1. **`searchFiles` Function:**
- Improve **error handling** with more detailed messages.
- Run `getIgnorePatterns` and `getIgnoreFilePatterns` in parallel using **`Promise.all`**.
2. **`getIgnorePatterns` Function:**
- Use a **`Set`** to avoid duplicate entries.
- Simplify the addition of default, output, and custom patterns.
3. **`parseIgnoreContent` Function:**
- Check for **empty content** before processing.
- Use **`reduce`** for cleaner code.
4. **`getIgnoreFilePatterns` Function:**
- Refactor **conditional logic** to support future extensions easily.
These changes will enhance the code's efficiency and maintainability.
Please assign me this issue so that i can start work on it.",
125,https://github.com/yamadashy/repomix/issues/125,closed,Refactor: Centralize renderContext & style generation logic for output styles,"The renderContext was repeated in all three style generation files including the logic to generate
<code>/src/core/output/outputStyles/plainStyle.ts</code>
<code>/src/core/output/outputStyles/xmlStyle.ts</code>
<code>/src/core/output/outputStyles/markdownStyle.ts</code>
``` javascript
export const generateMarkdownStyle = (outputGeneratorContext: OutputGeneratorContext) => {
const template = Handlebars.compile(markdownTemplate);
const renderContext = {
generationHeader: generateHeader(outputGeneratorContext.generationDate),
summaryUsageGuidelines: generateSummaryUsageGuidelines(
outputGeneratorContext.config,
outputGeneratorContext.instruction,
),
summaryNotes: generateSummaryNotes(outputGeneratorContext.config),
summaryAdditionalInfo: generateSummaryAdditionalInfo(),
headerText: outputGeneratorContext.config.output.headerText,
instruction: outputGeneratorContext.instruction,
treeString: outputGeneratorContext.treeString,
processedFiles: outputGeneratorContext.processedFiles,
};
return `${template(renderContext).trim()}\n`;
};
```
###  Proposed Solution
Move the common logic in these files into a shared utility file. This will make it easier to maintain as you add new styles and also add new parameters for renderContext in the future",
112,https://github.com/yamadashy/repomix/issues/112,closed,Optimize File Manipulation Logic in `fileManipulate.ts`,"The current implementation of the file manipulation logic in `repopack/src/core/file/fileManipulate.ts` can be optimized for performance. Several areas, such as regex handling, string trimming, and quote matching, can be made more efficient.
**Proposed Optimizations**:
1. **Improve Docstring Removal Regex**:
Simplify and optimize the regex used for removing docstrings in `PythonManipulator`.
2. **Optimize Quote Matching in `removeHashComments`**:
Enhance the logic to reduce redundant `slice` operations and improve performance in detecting quoted strings.
3. **Refactor `searchInPairs` Binary Search**:
Streamline the binary search function for checking if a hash is inside a string literal.
4. **Enhance `rtrimLines` Function**:
Use a more efficient method to trim trailing spaces and tabs from lines.
5. **Lazy Instantiate Manipulators**:
Instantiate manipulators in the `manipulators` object only when needed to improve memory usage.
---","Expected: - Faster file manipulation for large files.
- Better memory efficiency.
- Cleaner, more maintainable code.
I'd like to contribute to optimizing the file manipulation logic in `fileManipulate.ts` as described in the issue. Could you please assign it to me so I can start working on it?
Thanks!"
106,https://github.com/yamadashy/repomix/issues/106,closed,Minor Grammatical Issues in Repopack Contributor Covenant Code of Conduct,"This pull request addresses a couple of minor grammatical issues found in the **Repopack Contributor Covenant Code of Conduct**. These changes aim to enhance the clarity and readability of the document, ensuring that the language is accurate and consistent.
### Changes Made:
1. **Verb Correction**
2. **Clarification in ""Examples of Unacceptable Behavior""**
---
### Examples:
1. **Before the fix (Pledge Section)**:
2. **Before the fix (Unacceptable Behavior Section)**:
3. and many more...",
105,https://github.com/yamadashy/repomix/issues/105,closed,Glob ignore out of included folders does not work,"First of all thanks for this tool! been looking for something like this for a while
I noticed this issue:
```sh
# repo structure
/src
mocks/
MockA.ts
```
```sh
# MockA.ts and all files under mocks folder are still included in output file
npx repopack --include ""/src/**/*.ts"" --ignore ""/src/mocks/""","Expected: npx repopack --include ""/src/**/*.ts"" --ignore ""/src/mocks/**/*.ts""
```"
97,https://github.com/yamadashy/repomix/issues/97,closed,docstrings in python files are not removed,"This section of doc (enclosed by """""") can sometimes be out of sync from the current code during debugging process. As such when you feed it to the model, it can produce weird results. As a workaround, """"""[\s\S]*?"""""" regex Find and Replace in IDE does the job.",
90,https://github.com/yamadashy/repomix/issues/90,closed,exclude packager lock file by default,"npm: package-lock.json
Yarn: yarn.lock
pnpm: pnpm-lock.yaml",
89,https://github.com/yamadashy/repomix/issues/89,closed,can't pack a repo due to presence of special token: <|endoftext|>,"working with a repo which contains [hf model](https://huggingface.co/mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis). tried to ignore folders which might cause the problem, but still unable to pack.
<img width=""849"" alt=""Screenshot 2024-09-28 at 19 50 58"" src=""https://github.com/user-attachments/assets/ae34cbaf-cb15-42ea-9b6d-58106089f3ba"">",
86,https://github.com/yamadashy/repomix/issues/86,closed,add support output to markdown,,
74,https://github.com/yamadashy/repomix/issues/74,closed,Turn off security check?,It would be helpful to turn off security checks when we're working with developing cryptographic libraries... Can we have an argument flag to simply turn this function off when needed?,
71,https://github.com/yamadashy/repomix/issues/71,open,Splitting code into several files,"Hi
I've been using this for a while now (also replied to you on Reddit some time ago) and it's really saving me a lot of time. Would it be possible to add the ability to generate more than one repopack file for a specific codebase? At a certain number of lines of code, the LLMs tend not to be able to see the rest. It would be great if I can specify the number of files (or perhaps number of lines per file) it should split the complete codebase into. Best would be if the code of a specific file is not split when creating the separate files.
Thanks for your work on this.",
66,https://github.com/yamadashy/repomix/issues/66,open,Feature request: Exclusion/Inclusion Tags,"I've been using this tool for a few days and it's great. I've created a handful of config files for when I'm working on different parts of my app (UI, cloud functions, scripts, etc) and it works well but I've found myself wanting to only include specific parts of files in specific ""packs"".
For example, when working on an edge function feature, I want my AI to be aware of my button component so it can create a test page and I want to reference a few lines of a separate file that shows how the button is used (this is a very simple example but it gets the idea across).
My idea is to specify optional inclusion and Exclusion tags in the config file (ex. ""// KEEP"" and ""// OMIT"", respectively) and then when you run repopack, if it has those tags it will include code between the ""KEEP"" comments and exclude code between the ""OMIT"" comments. If a file has neither of the specific tags, all of it is included.
This would give users a lot of flexibility when creating repopacks that are specific to some domain within their apps:
- UI-KEEP
- UI-OMIT
- DB-KEEP
- DB-OMIT
- ...
The idea needs little bit of ironing out but it has the potential to save on token counts and help in situations where your files are huge.
This is an awesome tool, especially combined with Claude projects!",
63,https://github.com/yamadashy/repomix/issues/63,closed,Infinite Loading and Memory Leak,---,"Steps: 1. Run Repopack with the following command:
```
npx repopack
```
2. Wait for the packing process to complete.
3. Observe that the process runs without completing and without producing content.
4. When aborted with `CTRL + C`, an empty Repopack file is produced.

Expected: Repopack should successfully complete the packaging process and generate the packed files.

Actual: - The process hangs indefinitely.
- An empty Repopack file is generated when manually aborted.
### Additional Information
I ran the command with the suggested flag to trace warnings:
```bash
set NODE_OPTIONS=--trace-warnings && npx repopack
```
This is the output I received:
```
📦 Repopack v0.1.31
No custom config found at repopack.config.json or global config at C:\Users\<USER>\AppData\Local\Repopack\repopack.config.json.
You can add a config file for additional settings. Please check https://github.com/yamadashy/repopack for more information.
⠸ Packing files...
(node:11652) MaxPerformanceEntryBufferExceededWarning: Possible perf_hooks memory leak detected. 1000001 mark entries added to the global performance entry buffer. Use performance.clearMarks to clear the buffer.
at bufferUserTiming (node:internal/perf/observe:422:15)
at mark (node:internal/perf/usertiming:160:3)
at Performance.mark (node:internal/perf/performance:123:12)
at SecretLintProfiler.mark (file:///C:/Users/<USER>/AppData/Local/npm-cache/_npx/843b8f871cabf3ea/node_modules/@secretlint/profiler/module/index.js:41:23)
at file:///C:/Users/<USER>/AppData/Local/npm-cache/_npx/843b8f871cabf3ea/node_modules/@secretlint/core/module/RunningEvents.js:50:36
at async Promise.all (index 8)
```
### System Information
- **Repopack Version**: v0.1.31 (started happening with v0.1.26)"
56,https://github.com/yamadashy/repomix/issues/56,closed,V0.1.27 fails on Termux on Android,"```
$ repopack
📦 Repopack v0.1.27
✖ Error during packing
Unexpected error: Expected `concurrency` to be an integer from 1 and up or `Infinity`, got `0` (number)
For more help, please visit: https://github.com/yamadashy/repopack/issues
```
Reviewing releases, concurrency was introduced a few versions ago in v0.1.24. Installing v0.1.23 results in a successful repopack.
```
$ repopack
📦 Repopack v0.1.23
✔ Packing completed successfully!
```",
55,https://github.com/yamadashy/repomix/issues/55,closed,bug: Large chunks of code missing with remove comments on,"this was happening before update to 0.1.27.
I don't really want to post the code here, so if you want to message me or something, then I can send you the files.",
51,https://github.com/yamadashy/repomix/issues/51,closed,Feature request: global configuration equivalent of repopack.config.json,"Hi,
I have been using repopack and really like the configurability. However, I typically use the same repopack config settings for most projects and it would be nice if I could have a global `repopack.config.json` in `~/.config/` or something that would take effect if a local config file is not present.  It didn't seem like this was already possible, or at least I did not see anything to that effect. Any help would be appreciated. Thank you!",
43,https://github.com/yamadashy/repomix/issues/43,open,Feature request: AI pre process,"Hi! Thanks for this awesome project.
I have a great time using it, but as the project scales up, the output file for ai becomes lazy,
I want to suggest that add a option for using own AI API key to introduce/pre-process the output files,
and it can depend on a prompt file that was pre-defined to make the output file more understandable for ai.
I know this request is a heavy job and it might have multiple potential issues, it's unnecessary to make this feature because this
project is already good!
Thank you for viewing this issue
Best wishes,
Xinn",
40,https://github.com/yamadashy/repomix/issues/40,open,For consideration: Suggest LLM_RULES.md file in your readme,"Hey,
Great utility, many thanks!
Something I've been doing that you may want to include in the readme is the suggestion that people bake a set of repo specific rules into their repos.
I have a LLM_RULES.md file that I have at the base of my repo.  It does things like prompt the LLM to use British English, UK date formats as well as other things like provision of some specifications on certain approaches for storybook, component structure etc.
Cheers
Jon",
36,https://github.com/yamadashy/repomix/issues/36,open,Various compression levels to reduce token count,"First of all, love this project idea and have been using it very successfully with the new ""Projects"" feature in Claude.
General idea for this enhancement would be flags to reduce token count/cost through minification or even obfuscation. It's quite easy to manipulate artifacts in Claude etc - let folks try and push the limits easier. Going to work on this tomorrow/this week, but curious if anyone has considered/tried yet.
One option I'm considering: Build a built-in algo comparison method that runs popular ""compression"" algos against tokenizer measurements - retaining agnostic language support.",
34,https://github.com/yamadashy/repomix/issues/34,closed,Honour .gitignore in subfolders as well,"Seems like the script is only looking into a global gitignore (on root folder), but if there are specific gitignore files in the subfolders they're ignored.",
22,https://github.com/yamadashy/repomix/issues/22,closed,feat: add `include` filters,"Though `ignore` filters are useful, it would be helpful to have the opposite. For example pack only ""*.md"" files",
18,https://github.com/yamadashy/repomix/issues/18,closed,Dependency Dashboard,"This issue lists Renovate updates and detected dependencies. Read the [Dependency Dashboard](https://docs.renovatebot.com/key-concepts/dashboard/) docs to learn more.
## Awaiting Schedule
These updates are awaiting their schedule. Click on a checkbox to get an update now.
- [ ] <!-- unschedule-branch=renovate/all-minor-patch -->chore(deps): update all non-major dependencies (`@eslint/js`, `@types/eslint`, `@types/node`, `@typescript-eslint/eslint-plugin`, `@typescript-eslint/parser`, `@vitest/coverage-v8`, `commander`, `eslint-plugin-prettier`, `ignore`, `log-update`, `rimraf`, `vite`, `vitest`, `yarn`)
- [ ] <!-- unschedule-branch=renovate/eslint-9.x -->chore(deps): update dependency @types/eslint to v9
- [ ] <!-- unschedule-branch=renovate/major-eslint-monorepo -->chore(deps): update dependency eslint to v9
- [ ] <!-- unschedule-branch=renovate/rimraf-6.x -->chore(deps): update dependency rimraf to v6
- [ ] <!-- unschedule-branch=renovate/typescript-5.x -->chore(deps): update dependency typescript to v5
- [ ] <!-- unschedule-branch=renovate/vite-5.x -->chore(deps): update dependency vite to v5
- [ ] <!-- unschedule-branch=renovate/cli-spinners-3.x -->fix(deps): update dependency cli-spinners to v3
- [ ] <!-- unschedule-branch=renovate/commander-12.x -->fix(deps): update dependency commander to v12
## Detected dependencies
<blockquote>
</details>
</blockquote>
</details>
<blockquote>
<details><summary>.github/workflows/test.yml</summary>
- `actions/checkout v4`
- `actions/setup-node v4`
- `actions/checkout v4`
- `actions/setup-node v4`
- `ubuntu 22.04`
</details>
</blockquote>
</details>
<blockquote>
</details>
</blockquote>
</details>
<blockquote>
- `@secretlint/core ^8.2.4`
- `@secretlint/secretlint-rule-preset-recommend ^8.2.4`
- `cli-spinners ^2.9.2`
- `commander ^7.1.0`
- `iconv-lite ^0.6.3`
- `ignore ^5.2.0`
- `istextorbinary ^9.5.0`
- `jschardet ^3.1.3`
- `log-update ^6.0.0`
- `picocolors ^1.0.1`
- `strip-comments ^2.0.1`
- `@eslint/js ^9.7.0`
- `@types/eslint ~8.56.10`
- `@types/eslint__js ~8.42.3`
- `@types/eslint-config-prettier ~6.11.3`
- `@types/node ^20.14.10`
- `@types/strip-comments ^2.0.4`
- `@typescript-eslint/eslint-plugin ^7.16.0`
- `@typescript-eslint/parser ^7.16.0`
- `@vitest/coverage-v8 ^2.0.2`
- `eslint ^8.57.0`
- `eslint-config-prettier ^9.1.0`
- `eslint-plugin-import ^2.29.1`
- `eslint-plugin-prettier ^5.1.3`
- `rimraf ^5.0.7`
- `secretlint ^8.2.4`
- `typescript ^4.9.5`
- `vite ^4.1.4`
- `vitest ^2.0.2`
- `yarn >=1.0.0`
</details>
</blockquote>
</details>
---
- [ ] <!-- manual job -->Check this box to trigger a request for Renovate to run again on this repository",
17,https://github.com/yamadashy/repomix/issues/17,closed,Action Required: Fix Renovate Configuration,"There is an error with this repository's Renovate configuration that needs to be fixed. As a precaution, Renovate will stop PRs until it is resolved.
Error type: Cannot find preset's package (schedule:earlySaturdays)",
12,https://github.com/yamadashy/repomix/issues/12,closed,Feat: Propose .repopackignore file to override .gitignore for repopack bundles,"Hello,
First, thank you for this very useful project!
I'd like to suggest a feature that could enhance repopack's flexibility.
### Current situation:
I have a file ""claude-custom-instructions.txt"" that I want to include in my repopack bundle, but it's listed in my .gitignore file. Currently, to include this file, I need to set `ignore.useDefaultPatterns = false` and then manually add all the patterns I want to ignore in `ignore.customPatterns`. This works, but it's a bit cumbersome.
### Feature proposal:
Introduce a new file called `.repopackignore` that would override .gitignore settings specifically for repopack bundles when present. Here's how it could work:
1. If a `.repopackignore` file exists in the repository, repopack would use it instead of .gitignore.
2. The syntax would be similar to .gitignore, allowing users to easily specify which files to include or exclude from the bundle.
Benefits:
- Simpler management of bundle-specific ignores
- Maintains separation between git ignore rules and repopack bundle rules
- Allows users to easily include files in repopack bundles that are git-ignored
I'd appreciate your thoughts on this idea and whether it aligns with the project's goals.
Thank you again !",
8,https://github.com/yamadashy/repomix/issues/8,closed,feat: Option to prexfix line number,"Add option to prefix each line in the output file with the line number from the true file.
I think this would be useful as you can add to your prompt something like: ""Show the code changes with the line number, and include one line before to easier locate the change"".
I find this saves a lot of time on larger projects",
2,https://github.com/yamadashy/repomix/issues/2,closed,feat: Remove comments,"Would removing comments based on the language be beneficial?
So for Python remove anything that starts with `# `",
1,https://github.com/yamadashy/repomix/issues/1,closed,Issue with Ignore,"Hello,
This seems like a really great start to the package, so nice work! Any suggestions on how to ignore all SVG, CSS, etc. files? That does not appear to be working as I would expect.
![image](https://github.com/user-attachments/assets/a4fbacdc-0f9a-4e45-a952-17366a6d4473)
![image](https://github.com/user-attachments/assets/df29e427-4837-4c17-9380-4e322d276a84)
Thanks,",

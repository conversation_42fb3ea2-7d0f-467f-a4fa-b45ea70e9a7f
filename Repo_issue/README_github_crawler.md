# GitHub Issue Crawler

一个用于爬取GitHub项目所有Issue信息的Python工具。

## 功能特性

- 🔍 爬取指定GitHub仓库的所有Issues
- 📊 按照指定格式输出数据
- 🔧 自动查找Issue的修复信息
- 💾 支持CSV格式导出
- 🚀 支持批量处理多个仓库
- 🔑 支持GitHub Token认证（避免API限制）

## 输出格式

| 字段 | 描述 |
|------|------|
| `repo_owner` | 仓库所有者 |
| `repo_name` | 仓库名称 |
| `issue_url` | Issue的GitHub链接 |
| `comment_url` | 包含修复引用的评论链接 |
| `issue_title` | Issue标题 |
| `issue_body` | Issue内容 |
| `fixed_content` | 修复方式/内容 |

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 1. 命令行使用

```bash
# 基本用法
python github_issue_crawler.py https://github.com/owner/repo

# 使用GitHub Token (推荐)
python github_issue_crawler.py https://github.com/owner/repo -t YOUR_GITHUB_TOKEN

# 指定输出文件
python github_issue_crawler.py https://github.com/owner/repo -o custom_output.csv
```

### 2. 编程使用

```python
from github_issue_crawler import GitHubIssueCrawler

# 创建爬虫实例
crawler = GitHubIssueCrawler(token="your_github_token")

# 爬取仓库Issues
issues = crawler.crawl_repository(
    "https://github.com/owner/repo",
    "output.csv"
)

# 处理数据
for issue in issues:
    print(f"Issue: {issue['issue_title']}")
    print(f"URL: {issue['issue_url']}")
    print(f"Fixed: {issue['fixed_content']}")
```

### 3. 批量处理

```python
repositories = [
    "https://github.com/microsoft/vscode",
    "https://github.com/facebook/react",
    "https://github.com/google/tensorflow"
]

crawler = GitHubIssueCrawler(token="your_token")

for repo_url in repositories:
    try:
        issues = crawler.crawl_repository(repo_url)
        print(f"成功爬取 {len(issues)} 个Issues")
    except Exception as e:
        print(f"爬取失败: {e}")
```

## GitHub Token 设置

为了避免API调用限制，强烈建议使用GitHub Personal Access Token：

1. 访问 [GitHub Settings > Developer settings > Personal access tokens](https://github.com/settings/tokens)
2. 点击 "Generate new token"
3. 选择适当的权限（至少需要 `public_repo` 权限）
4. 复制生成的Token

### 环境变量设置

```bash
# Linux/Mac
export GITHUB_TOKEN="your_token_here"

# Windows
set GITHUB_TOKEN=your_token_here
```

## API限制说明

- **无Token**: 每小时60次请求
- **有Token**: 每小时5000次请求

对于大型项目，建议使用Token以避免限制。

## 示例输出

```csv
repo_owner,repo_name,issue_url,comment_url,issue_title,issue_body,fixed_content
microsoft,vscode,https://github.com/microsoft/vscode/issues/123,,Bug in editor,Editor crashes when...,Fixed by commit: abc123
facebook,react,https://github.com/facebook/react/issues/456,https://github.com/facebook/react/issues/456#issuecomment-789,Performance issue,React is slow...,Resolved in PR #789
```

## 错误处理

脚本包含完整的错误处理：

- 🔍 仓库不存在或无权限访问
- 🚫 API调用限制
- 🌐 网络连接问题
- 📝 数据格式错误

## 高级功能

### 自定义过滤

```python
# 只获取Bug相关的Issues
issues = crawler.get_issues("owner", "repo", state="all")
bug_issues = [
    issue for issue in issues 
    if 'bug' in issue['title'].lower() or 
       any('bug' in label['name'].lower() for label in issue.get('labels', []))
]
```

### 数据分析

```python
# 统计Issue状态
open_count = len([i for i in issues if i['state'] == 'open'])
closed_count = len([i for i in issues if i['state'] == 'closed'])

print(f"Open Issues: {open_count}")
print(f"Closed Issues: {closed_count}")
```

## 注意事项

1. **尊重API限制**: 脚本已内置延时，请勿过于频繁调用
2. **大型项目**: 对于有数千个Issues的项目，爬取可能需要较长时间
3. **数据准确性**: 修复信息的识别基于关键词匹配，可能不完全准确
4. **权限要求**: 私有仓库需要相应的访问权限

## 故障排除

### 常见问题

1. **403 Forbidden**: API调用限制，请使用Token或稍后重试
2. **404 Not Found**: 仓库不存在或无权限访问
3. **网络超时**: 检查网络连接，脚本会自动重试

### 调试模式

```python
import logging
logging.basicConfig(level=logging.DEBUG)

crawler = GitHubIssueCrawler(token="your_token")
# 现在会显示详细的调试信息
```

## 许可证

MIT License

#!/usr/bin/env python3
"""
GitHub Issue爬虫 - 只获取有PR的closed Issues
输出：issues.json (只包含有修复PR的Issue)
"""

import requests
import json
import re
import argparse
import time
from typing import List, Dict, Optional

class GitHubIssueCrawler:
    def __init__(self, token: Optional[str] = None):
        self.session = requests.Session()
        self.base_url = "https://api.github.com"
        
        if token:
            self.session.headers.update({
                'Authorization': f'token {token}',
                'Accept': 'application/vnd.github.v3+json'
            })
            print("✅ 使用GitHub Token")
        else:
            print("⚠️  未使用Token，API调用限制为每小时60次")
    
    def get_repo_info(self, repo_url: str) -> tuple:
        """从GitHub URL提取仓库信息"""
        match = re.search(r'github\.com/([^/]+)/([^/]+)', repo_url)
        if match:
            return match.group(1), match.group(2)
        raise ValueError(f"无效的GitHub URL: {repo_url}")
    
    def split_issue_content(self, title: str, body: str) -> str:
        """分割并合并Issue内容"""
        if not body:
            return title
        
        # 简单合并标题和内容
        content_parts = [title]
        if body:
            content_parts.append(body)
        
        return '\n\n'.join(content_parts)
    
    def safe_api_call(self, url: str, params: dict = None) -> requests.Response:
        """安全的API调用"""
        try:
            response = self.session.get(url, params=params, timeout=30)
            if response.status_code == 200:
                return response
            elif response.status_code == 403:
                if 'X-RateLimit-Remaining' in response.headers:
                    remaining = int(response.headers['X-RateLimit-Remaining'])
                    if remaining == 0:
                        print("⏰ API限制，请稍后重试或使用Token")
                raise Exception(f"API限制: {response.status_code}")
            else:
                raise Exception(f"API错误: {response.status_code}")
        except requests.exceptions.RequestException as e:
            raise Exception(f"网络错误: {e}")
    
    def find_pr_for_issue(self, repo_owner: str, repo_name: str, issue_number: int) -> Dict:
        """查找Issue对应的PR"""
        # 1. 获取Issue事件
        try:
            events_url = f"{self.base_url}/repos/{repo_owner}/{repo_name}/issues/{issue_number}/events"
            response = self.safe_api_call(events_url)
            events = response.json()
            
            for event in events:
                if event.get('event') == 'closed' and event.get('commit_id'):
                    pr_number = self.find_pr_by_commit(repo_owner, repo_name, event['commit_id'])
                    if pr_number:
                        return self.get_pr_info(repo_owner, repo_name, pr_number)
        except:
            pass
        
        # 2. 在评论中查找PR引用
        try:
            comments_url = f"{self.base_url}/repos/{repo_owner}/{repo_name}/issues/{issue_number}/comments"
            response = self.safe_api_call(comments_url)
            comments = response.json()
            
            for comment in comments:
                body = comment.get('body', '')
                pr_patterns = [r'#(\d+)', r'fixes #(\d+)', r'closes #(\d+)', r'PR #(\d+)']
                
                for pattern in pr_patterns:
                    matches = re.findall(pattern, body, re.IGNORECASE)
                    for match in matches:
                        if self.is_pull_request(repo_owner, repo_name, match):
                            return self.get_pr_info(repo_owner, repo_name, match)
        except:
            pass
        
        return {'pr_merged_time': '', 'pr_number': '', 'pr_url': ''}
    
    def find_pr_by_commit(self, repo_owner: str, repo_name: str, commit_id: str) -> str:
        """通过commit查找PR"""
        try:
            url = f"{self.base_url}/repos/{repo_owner}/{repo_name}/pulls"
            params = {'state': 'closed', 'per_page': 30}
            response = self.safe_api_call(url, params)
            pulls = response.json()
            
            for pr in pulls:
                if pr.get('merge_commit_sha') and pr['merge_commit_sha'].startswith(commit_id[:7]):
                    return str(pr['number'])
        except:
            pass
        return None
    
    def is_pull_request(self, repo_owner: str, repo_name: str, number: str) -> bool:
        """检查是否是PR"""
        try:
            url = f"{self.base_url}/repos/{repo_owner}/{repo_name}/pulls/{number}"
            response = self.safe_api_call(url)
            return True
        except:
            return False
    
    def get_pr_info(self, repo_owner: str, repo_name: str, pr_number: str) -> Dict:
        """获取PR信息"""
        try:
            url = f"{self.base_url}/repos/{repo_owner}/{repo_name}/pulls/{pr_number}"
            response = self.safe_api_call(url)
            pr_data = response.json()
            
            return {
                'pr_merged_time': pr_data['merged_at'] or '',
                'pr_number': pr_number,
                'pr_url': pr_data['html_url']
            }
        except:
            return {'pr_merged_time': '', 'pr_number': pr_number, 'pr_url': ''}
    
    def crawl(self, repo_url: str, output_file: str = "issues.json", max_issues: int = None) -> List[Dict]:
        """爬取有PR的Issues"""
        repo_owner, repo_name = self.get_repo_info(repo_url)
        issues_with_pr = []
        page = 1
        processed_count = 0
        
        print(f"🔍 获取 {repo_owner}/{repo_name} 的closed Issues (只保存有PR的)...")
        
        while True:
            url = f"{self.base_url}/repos/{repo_owner}/{repo_name}/issues"
            params = {'page': page, 'per_page': 100, 'state': 'closed'}
            
            try:
                response = self.safe_api_call(url, params)
                page_issues = response.json()
                
                if not page_issues:
                    break
                
                # 过滤PR，只要真正的Issues
                real_issues = [issue for issue in page_issues if not issue.get('pull_request')]
                
                for issue in real_issues:
                    processed_count += 1
                    print(f"  处理Issue #{issue['number']}: {issue['title'][:50]}...")
                    
                    # 查找PR
                    pr_info = self.find_pr_for_issue(repo_owner, repo_name, issue['number'])
                    
                    # 只保存有PR的Issue
                    if pr_info['pr_number']:
                        content = self.split_issue_content(issue['title'], issue['body'] or '')
                        
                        issue_data = {
                            'issue_number': str(issue['number']),
                            'issue_url': issue['html_url'],
                            'state': issue['state'],
                            'title': issue['title'],
                            'content': content,
                            'created_at': issue['created_at'],
                            'pr_merged_time': pr_info['pr_merged_time'],
                            'pr_number': pr_info['pr_number'],
                            'pr_url': pr_info['pr_url']
                        }
                        
                        issues_with_pr.append(issue_data)
                        print(f"    ✅ 找到PR #{pr_info['pr_number']}")
                        
                        if max_issues and len(issues_with_pr) >= max_issues:
                            break
                    else:
                        print(f"    ❌ 未找到PR，跳过")
                
                if max_issues and len(issues_with_pr) >= max_issues:
                    break
                
                print(f"📄 第 {page} 页: {len(real_issues)} 个Issues")
                page += 1
                
            except Exception as e:
                print(f"❌ 错误: {e}")
                break
        
        print(f"✅ 总共处理 {processed_count} 个Issues，保存 {len(issues_with_pr)} 个有PR的Issues")
        if processed_count > 0:
            print(f"📊 PR匹配率: {len(issues_with_pr)/processed_count*100:.1f}%")
        
        # 保存到JSON
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(issues_with_pr, f, ensure_ascii=False, indent=2)
        
        print(f"💾 已保存到: {output_file}")
        return issues_with_pr

def main():
    parser = argparse.ArgumentParser(description='GitHub Issue爬虫 - 只获取有PR的Issues')
    parser.add_argument('repo_url', help='GitHub仓库URL')
    parser.add_argument('-t', '--token', help='GitHub Token')
    parser.add_argument('-o', '--output', default='issues.json', help='输出文件')
    parser.add_argument('-m', '--max', type=int, help='最大Issue数量')
    
    args = parser.parse_args()
    
    try:
        crawler = GitHubIssueCrawler(token=args.token)
        crawler.crawl(args.repo_url, args.output, max_issues=args.max)
        print("🎉 完成！")
    except Exception as e:
        print(f"❌ 错误: {e}")

if __name__ == "__main__":
    main()

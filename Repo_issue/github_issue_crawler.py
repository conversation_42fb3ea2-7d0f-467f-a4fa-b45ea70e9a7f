#!/usr/bin/env python3
"""
GitHub Issue主题爬虫
专门用于获取和分割Issue主题内容
"""

import requests
import json
import csv
import time
import argparse
from typing import List, Dict, Optional
from urllib.parse import urlparse
import re

class GitHubIssueTopicCrawler:
    def __init__(self, token: Optional[str] = None):
        """
        初始化GitHub Issue主题爬虫

        Args:
            token: GitHub Personal Access Token
        """
        self.session = requests.Session()
        self.base_url = "https://api.github.com"

        if token:
            self.session.headers.update({
                'Authorization': f'token {token}',
                'Accept': 'application/vnd.github.v3+json'
            })
        else:
            print("⚠️  未提供GitHub Token，API调用可能受限制")
    
    def get_repo_info(self, repo_url: str) -> tuple:
        """
        从GitHub URL中提取仓库信息
        
        Args:
            repo_url: GitHub仓库URL
            
        Returns:
            tuple: (repo_owner, repo_name)
        """
        # 支持多种URL格式
        patterns = [
            r'github\.com/([^/]+)/([^/]+?)(?:\.git)?/?$',
            r'github\.com/([^/]+)/([^/]+)/.*',
        ]
        
        for pattern in patterns:
            match = re.search(pattern, repo_url)
            if match:
                return match.group(1), match.group(2)
        
        raise ValueError(f"无法解析GitHub URL: {repo_url}")

    def split_issue_content(self, title: str, body: str) -> Dict[str, str]:
        """
        分割Issue内容为不同部分

        Args:
            title: Issue标题
            body: Issue内容

        Returns:
            Dict: 分割后的内容
        """
        result = {
            'title': title.strip(),
            'description': '',
            'steps_to_reproduce': '',
            'expected_behavior': '',
            'actual_behavior': '',
            'environment': '',
            'additional_info': '',
            'raw_body': body
        }

        if not body:
            return result

        # 常见的Issue模板关键词
        sections = {
            'description': [
                'description', 'summary', 'overview', '描述', '概述', '问题描述'
            ],
            'steps_to_reproduce': [
                'steps to reproduce', 'reproduction steps', 'how to reproduce',
                'reproduce', '重现步骤', '复现步骤', '如何重现'
            ],
            'expected_behavior': [
                'expected behavior', 'expected result', 'expected',
                '期望行为', '期望结果', '预期行为'
            ],
            'actual_behavior': [
                'actual behavior', 'actual result', 'actual',
                '实际行为', '实际结果', '当前行为'
            ],
            'environment': [
                'environment', 'system info', 'version', 'platform',
                '环境信息', '系统信息', '版本信息', '环境'
            ]
        }

        # 按行分割内容
        lines = body.split('\n')
        current_section = 'description'
        section_content = {key: [] for key in sections.keys()}
        section_content['additional_info'] = []

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # 检查是否是新的章节标题
            line_lower = line.lower()
            found_section = None

            for section_key, keywords in sections.items():
                for keyword in keywords:
                    if keyword in line_lower and (
                        line.startswith('#') or
                        line.startswith('**') or
                        line.endswith(':') or
                        len(line) < 50  # 短行更可能是标题
                    ):
                        found_section = section_key
                        break
                if found_section:
                    break

            if found_section:
                current_section = found_section
            else:
                # 将内容添加到当前章节
                if current_section in section_content:
                    section_content[current_section].append(line)
                else:
                    section_content['additional_info'].append(line)

        # 合并每个章节的内容
        for key in section_content:
            if section_content[key]:
                result[key] = '\n'.join(section_content[key]).strip()

        # 如果没有明确的描述，使用前几行作为描述
        if not result['description'] and result['additional_info']:
            lines = result['additional_info'].split('\n')
            if len(lines) > 0:
                result['description'] = '\n'.join(lines[:3])  # 前3行作为描述
                result['additional_info'] = '\n'.join(lines[3:])

        return result

    def get_issues_topics(self, repo_owner: str, repo_name: str, state: str = "all", max_issues: int = None) -> List[Dict]:
        """
        获取仓库Issues的主题内容

        Args:
            repo_owner: 仓库所有者
            repo_name: 仓库名称
            state: Issue状态 (open, closed, all)
            max_issues: 最大获取数量

        Returns:
            List[Dict]: Issue主题列表
        """
        issues_topics = []
        page = 1
        per_page = 100

        print(f"🔍 开始获取 {repo_owner}/{repo_name} 的Issue主题...")

        while True:
            url = f"{self.base_url}/repos/{repo_owner}/{repo_name}/issues"
            params = {
                'state': state,
                'page': page,
                'per_page': per_page,
                'sort': 'created',
                'direction': 'desc'
            }

            print(f"📄 获取第 {page} 页...")
            response = self.session.get(url, params=params)

            if response.status_code == 404:
                raise Exception(f"仓库 {repo_owner}/{repo_name} 不存在或无权限访问")
            elif response.status_code == 403:
                raise Exception("API调用限制，请提供GitHub Token或稍后重试")
            elif response.status_code != 200:
                raise Exception(f"API调用失败: {response.status_code} - {response.text}")

            page_issues = response.json()

            if not page_issues:
                break

            # 过滤掉Pull Request
            real_issues = [issue for issue in page_issues if not issue.get('pull_request')]

            for issue in real_issues:
                # 分割Issue内容
                topic_data = self.split_issue_content(
                    issue['title'],
                    issue['body'] or ''
                )

                # 添加基本信息
                topic_data.update({
                    'repo_owner': repo_owner,
                    'repo_name': repo_name,
                    'issue_number': issue['number'],
                    'issue_url': issue['html_url'],
                    'state': issue['state'],
                    'created_at': issue['created_at'],
                    'labels': [label['name'] for label in issue.get('labels', [])]
                })

                issues_topics.append(topic_data)

                # 检查是否达到最大数量
                if max_issues and len(issues_topics) >= max_issues:
                    print(f"✅ 已达到最大获取数量: {max_issues}")
                    return issues_topics

            print(f"✅ 第 {page} 页获取到 {len(real_issues)} 个Issues")

            # 检查是否还有更多页面
            if len(page_issues) < per_page:
                break

            page += 1
            time.sleep(0.1)  # 避免API限制

        print(f"🎉 总共获取到 {len(issues_topics)} 个Issue主题")
        return issues_topics

    def crawl_repository_topics(self, repo_url: str, output_file: str = "github_issue_topics.csv", max_issues: int = None) -> List[Dict]:
        """
        爬取整个仓库的Issue主题信息

        Args:
            repo_url: GitHub仓库URL
            output_file: 输出CSV文件名
            max_issues: 最大获取数量

        Returns:
            List[Dict]: 处理后的Issue主题数据
        """
        repo_owner, repo_name = self.get_repo_info(repo_url)
        issues_topics = self.get_issues_topics(repo_owner, repo_name, max_issues=max_issues)

        # 保存到CSV文件
        self.save_topics_to_csv(issues_topics, output_file)

        return issues_topics

    def save_topics_to_csv(self, issues_topics: List[Dict], filename: str):
        """
        将Issue主题数据保存到CSV文件

        Args:
            issues_topics: Issue主题数据列表
            filename: 输出文件名
        """
        fieldnames = [
            'repo_owner', 'repo_name', 'issue_number', 'issue_url', 'state', 'created_at',
            'title', 'description', 'steps_to_reproduce', 'expected_behavior',
            'actual_behavior', 'environment', 'additional_info', 'labels'
        ]

        # 处理数据格式
        processed_data = []
        for topic in issues_topics:
            processed_topic = {
                'repo_owner': topic['repo_owner'],
                'repo_name': topic['repo_name'],
                'issue_number': topic['issue_number'],
                'issue_url': topic['issue_url'],
                'state': topic['state'],
                'created_at': topic['created_at'],
                'title': topic['title'],
                'description': topic['description'],
                'steps_to_reproduce': topic['steps_to_reproduce'],
                'expected_behavior': topic['expected_behavior'],
                'actual_behavior': topic['actual_behavior'],
                'environment': topic['environment'],
                'additional_info': topic['additional_info'],
                'labels': '; '.join(topic['labels'])  # 将标签列表转为字符串
            }
            processed_data.append(processed_topic)

        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(processed_data)

        print(f"💾 Issue主题数据已保存到: {filename}")

def main():
    parser = argparse.ArgumentParser(description='GitHub Issue主题爬虫')
    parser.add_argument('repo_url', help='GitHub仓库URL')
    parser.add_argument('-t', '--token', help='GitHub Personal Access Token')
    parser.add_argument('-o', '--output', default='github_issue_topics.csv', help='输出CSV文件名')
    parser.add_argument('-m', '--max', type=int, help='最大获取Issue数量')

    args = parser.parse_args()

    try:
        crawler = GitHubIssueTopicCrawler(token=args.token)
        crawler.crawl_repository_topics(args.repo_url, args.output, max_issues=args.max)
        print("🎉 Issue主题爬取完成！")

    except Exception as e:
        print(f"❌ 错误: {e}")

if __name__ == "__main__":
    main()

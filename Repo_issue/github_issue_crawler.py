#!/usr/bin/env python3
"""
GitHub Issue Crawler
爬取指定GitHub项目的所有Issue信息
"""

import requests
import json
import csv
import time
import argparse
from typing import List, Dict, Optional
from urllib.parse import urlparse
import re

class GitHubIssueCrawler:
    def __init__(self, token: Optional[str] = None):
        """
        初始化GitHub Issue爬虫
        
        Args:
            token: GitHub Personal Access Token (可选，但建议使用以避免API限制)
        """
        self.session = requests.Session()
        self.base_url = "https://api.github.com"
        
        if token:
            self.session.headers.update({
                'Authorization': f'token {token}',
                'Accept': 'application/vnd.github.v3+json'
            })
        else:
            print("⚠️  未提供GitHub Token，API调用可能受限制")
    
    def get_repo_info(self, repo_url: str) -> tuple:
        """
        从GitHub URL中提取仓库信息
        
        Args:
            repo_url: GitHub仓库URL
            
        Returns:
            tuple: (repo_owner, repo_name)
        """
        # 支持多种URL格式
        patterns = [
            r'github\.com/([^/]+)/([^/]+?)(?:\.git)?/?$',
            r'github\.com/([^/]+)/([^/]+)/.*',
        ]
        
        for pattern in patterns:
            match = re.search(pattern, repo_url)
            if match:
                return match.group(1), match.group(2)
        
        raise ValueError(f"无法解析GitHub URL: {repo_url}")
    
    def get_issues(self, repo_owner: str, repo_name: str, state: str = "all") -> List[Dict]:
        """
        获取仓库的所有Issue
        
        Args:
            repo_owner: 仓库所有者
            repo_name: 仓库名称
            state: Issue状态 (open, closed, all)
            
        Returns:
            List[Dict]: Issue列表
        """
        issues = []
        page = 1
        per_page = 100
        
        print(f"🔍 开始爬取 {repo_owner}/{repo_name} 的Issues...")
        
        while True:
            url = f"{self.base_url}/repos/{repo_owner}/{repo_name}/issues"
            params = {
                'state': state,
                'page': page,
                'per_page': per_page,
                'sort': 'created',
                'direction': 'desc'
            }
            
            print(f"📄 获取第 {page} 页...")
            response = self.session.get(url, params=params)
            
            if response.status_code == 404:
                raise Exception(f"仓库 {repo_owner}/{repo_name} 不存在或无权限访问")
            elif response.status_code == 403:
                raise Exception("API调用限制，请提供GitHub Token或稍后重试")
            elif response.status_code != 200:
                raise Exception(f"API调用失败: {response.status_code} - {response.text}")
            
            page_issues = response.json()
            
            if not page_issues:
                break
            
            # 过滤掉Pull Request (GitHub API中PR也被当作Issue)
            real_issues = [issue for issue in page_issues if not issue.get('pull_request')]
            issues.extend(real_issues)
            
            print(f"✅ 第 {page} 页获取到 {len(real_issues)} 个Issues")
            
            # 检查是否还有更多页面
            if len(page_issues) < per_page:
                break
            
            page += 1
            time.sleep(0.1)  # 避免API限制
        
        print(f"🎉 总共获取到 {len(issues)} 个Issues")
        return issues
    
    def get_issue_comments(self, repo_owner: str, repo_name: str, issue_number: int) -> List[Dict]:
        """
        获取Issue的所有评论
        
        Args:
            repo_owner: 仓库所有者
            repo_name: 仓库名称
            issue_number: Issue编号
            
        Returns:
            List[Dict]: 评论列表
        """
        url = f"{self.base_url}/repos/{repo_owner}/{repo_name}/issues/{issue_number}/comments"
        response = self.session.get(url)
        
        if response.status_code == 200:
            return response.json()
        else:
            print(f"⚠️  获取Issue #{issue_number} 评论失败: {response.status_code}")
            return []
    
    def find_fix_references(self, repo_owner: str, repo_name: str, issue_number: int) -> Dict:
        """
        查找Issue的修复信息
        
        Args:
            repo_owner: 仓库所有者
            repo_name: 仓库名称
            issue_number: Issue编号
            
        Returns:
            Dict: 修复信息
        """
        fix_info = {
            'comment_url': '',
            'fixed_content': ''
        }
        
        # 获取Issue的事件 (关闭事件、引用等)
        events_url = f"{self.base_url}/repos/{repo_owner}/{repo_name}/issues/{issue_number}/events"
        response = self.session.get(events_url)
        
        if response.status_code == 200:
            events = response.json()
            
            for event in events:
                # 查找关闭事件
                if event.get('event') == 'closed' and event.get('commit_id'):
                    commit_id = event['commit_id']
                    fix_info['fixed_content'] = f"Fixed by commit: {commit_id}"
                    break
                
                # 查找引用的PR
                elif event.get('event') == 'referenced' and event.get('commit_id'):
                    commit_id = event['commit_id']
                    fix_info['fixed_content'] = f"Referenced in commit: {commit_id}"
        
        # 获取评论中的修复信息
        comments = self.get_issue_comments(repo_owner, repo_name, issue_number)
        for comment in comments:
            comment_body = comment.get('body', '').lower()
            
            # 查找修复相关的关键词
            fix_keywords = ['fix', 'fixed', 'resolve', 'resolved', 'close', 'closed']
            if any(keyword in comment_body for keyword in fix_keywords):
                comment_id = comment['id']
                fix_info['comment_url'] = f"https://github.com/{repo_owner}/{repo_name}/issues/{issue_number}#issuecomment-{comment_id}"
                fix_info['fixed_content'] = comment['body'][:500]  # 限制长度
                break
        
        return fix_info
    
    def crawl_repository(self, repo_url: str, output_file: str = "github_issues.csv") -> List[Dict]:
        """
        爬取整个仓库的Issue信息
        
        Args:
            repo_url: GitHub仓库URL
            output_file: 输出CSV文件名
            
        Returns:
            List[Dict]: 处理后的Issue数据
        """
        repo_owner, repo_name = self.get_repo_info(repo_url)
        issues = self.get_issues(repo_owner, repo_name)
        
        processed_issues = []
        
        print(f"🔧 开始处理Issues...")
        
        for i, issue in enumerate(issues, 1):
            print(f"📝 处理Issue {i}/{len(issues)}: #{issue['number']}")
            
            issue_data = {
                'repo_owner': repo_owner,
                'repo_name': repo_name,
                'issue_url': issue['html_url'],
                'comment_url': '',
                'issue_title': issue['title'],
                'issue_body': issue['body'] or '',
                'fixed_content': ''
            }
            
            # 查找修复信息
            if issue['state'] == 'closed':
                fix_info = self.find_fix_references(repo_owner, repo_name, issue['number'])
                issue_data.update(fix_info)
            
            processed_issues.append(issue_data)
            time.sleep(0.1)  # 避免API限制
        
        # 保存到CSV文件
        self.save_to_csv(processed_issues, output_file)
        
        return processed_issues
    
    def save_to_csv(self, issues: List[Dict], filename: str):
        """
        将Issue数据保存到CSV文件
        
        Args:
            issues: Issue数据列表
            filename: 输出文件名
        """
        fieldnames = [
            'repo_owner', 'repo_name', 'issue_url', 'comment_url',
            'issue_title', 'issue_body', 'fixed_content'
        ]
        
        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(issues)
        
        print(f"💾 数据已保存到: {filename}")

def main():
    parser = argparse.ArgumentParser(description='GitHub Issue爬虫')
    parser.add_argument('repo_url', help='GitHub仓库URL')
    parser.add_argument('-t', '--token', help='GitHub Personal Access Token')
    parser.add_argument('-o', '--output', default='github_issues.csv', help='输出CSV文件名')
    
    args = parser.parse_args()
    
    try:
        crawler = GitHubIssueCrawler(token=args.token)
        crawler.crawl_repository(args.repo_url, args.output)
        print("🎉 爬取完成！")
        
    except Exception as e:
        print(f"❌ 错误: {e}")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
测试Issue主题分割功能
"""

from github_issue_crawler import GitHubIssueTopicCrawler

def test_issue_splitting():
    """测试Issue内容分割功能"""
    print("🧪 测试Issue内容分割功能")
    print("=" * 50)
    
    crawler = GitHubIssueTopicCrawler()
    
    # 模拟一个典型的Bug报告
    test_title = "Application crashes when opening large files"
    test_body = """
## Description
The application crashes unexpectedly when trying to open files larger than 100MB. This happens consistently across different file types.

## Steps to Reproduce
1. Launch the application
2. Click on File > Open
3. Select a file larger than 100MB
4. Click "Open"
5. Application crashes immediately

## Expected Behavior
The application should open the file successfully, possibly with a loading indicator for large files.

## Actual Behavior
The application crashes with no error message and closes completely.

## Environment
- OS: Windows 10 Pro (Build 19042)
- Application Version: 2.1.3
- RAM: 16GB
- Available Disk Space: 500GB

## Additional Information
This issue started appearing after the latest update. Smaller files (< 50MB) work fine.
Error logs show "OutOfMemoryException" in the crash dump.
"""
    
    print("📝 原始Issue内容:")
    print(f"标题: {test_title}")
    print(f"内容长度: {len(test_body)} 字符")
    print()
    
    # 测试分割功能
    result = crawler.split_issue_content(test_title, test_body)
    
    print("✂️ 分割结果:")
    print("-" * 30)
    
    sections = [
        ('标题', 'title'),
        ('描述', 'description'),
        ('重现步骤', 'steps_to_reproduce'),
        ('期望行为', 'expected_behavior'),
        ('实际行为', 'actual_behavior'),
        ('环境信息', 'environment'),
        ('附加信息', 'additional_info')
    ]
    
    for chinese_name, english_key in sections:
        content = result[english_key]
        if content:
            print(f"📋 {chinese_name}:")
            print(f"   {content[:100]}{'...' if len(content) > 100 else ''}")
            print()
        else:
            print(f"📋 {chinese_name}: (空)")
            print()

def test_chinese_issue():
    """测试中文Issue分割"""
    print("🧪 测试中文Issue分割")
    print("=" * 50)
    
    crawler = GitHubIssueTopicCrawler()
    
    test_title = "登录功能在移动端无法正常工作"
    test_body = """
## 问题描述
在移动端浏览器中，用户无法正常登录系统。点击登录按钮后没有任何反应。

## 重现步骤
1. 使用手机浏览器打开网站
2. 输入正确的用户名和密码
3. 点击"登录"按钮
4. 页面没有跳转，也没有错误提示

## 期望行为
用户应该能够成功登录并跳转到主页面。

## 实际行为
点击登录按钮后没有任何反应，用户仍然停留在登录页面。

## 环境信息
- 设备: iPhone 12, Samsung Galaxy S21
- 浏览器: Safari 14.1, Chrome Mobile 91
- 网络: 4G/WiFi
- 系统版本: iOS 14.6, Android 11

## 附加信息
桌面端浏览器工作正常，只有移动端有此问题。
开发者工具显示JavaScript错误："Uncaught TypeError: Cannot read property 'submit' of null"
"""
    
    result = crawler.split_issue_content(test_title, test_body)
    
    print("✂️ 中文Issue分割结果:")
    print("-" * 30)
    
    for key, value in result.items():
        if key != 'raw_body' and value:
            print(f"📋 {key}: {value[:80]}{'...' if len(value) > 80 else ''}")
            print()

def test_minimal_issue():
    """测试最简Issue分割"""
    print("🧪 测试最简Issue分割")
    print("=" * 50)
    
    crawler = GitHubIssueTopicCrawler()
    
    test_title = "Button not working"
    test_body = "The submit button doesn't work when I click it."
    
    result = crawler.split_issue_content(test_title, test_body)
    
    print("✂️ 最简Issue分割结果:")
    print("-" * 30)
    
    for key, value in result.items():
        if key != 'raw_body' and value:
            print(f"📋 {key}: {value}")
            print()

def test_real_repository():
    """测试真实仓库的Issue分割"""
    print("🧪 测试真实仓库Issue分割")
    print("=" * 50)
    
    crawler = GitHubIssueTopicCrawler()
    
    try:
        # 获取一个真实仓库的Issues
        issues_topics = crawler.get_issues_topics("microsoft", "vscode", max_issues=3)
        
        print(f"📊 获取到 {len(issues_topics)} 个真实Issue")
        print()
        
        for i, topic in enumerate(issues_topics, 1):
            print(f"🔍 Issue {i}: {topic['title']}")
            print(f"   URL: {topic['issue_url']}")
            print(f"   状态: {topic['state']}")
            print(f"   标签: {', '.join(topic['labels'])}")
            
            # 显示分割结果
            sections_with_content = 0
            for key in ['description', 'steps_to_reproduce', 'expected_behavior', 'actual_behavior', 'environment']:
                if topic[key]:
                    sections_with_content += 1
            
            print(f"   分割章节数: {sections_with_content}/5")
            
            if topic['description']:
                print(f"   描述预览: {topic['description'][:100]}...")
            
            print("-" * 40)
        
    except Exception as e:
        print(f"❌ 测试失败 (可能需要GitHub Token): {e}")

if __name__ == "__main__":
    print("GitHub Issue主题分割测试")
    print("=" * 60)
    
    test_issue_splitting()
    print("\n" + "=" * 60)
    
    test_chinese_issue()
    print("\n" + "=" * 60)
    
    test_minimal_issue()
    print("\n" + "=" * 60)
    
    # test_real_repository()  # 需要网络连接
    
    print("\n🎉 所有测试完成！")

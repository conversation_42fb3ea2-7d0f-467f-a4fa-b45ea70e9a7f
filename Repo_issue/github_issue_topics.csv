repo_owner,repo_name,issue_number,issue_url,state,created_at,title,description,steps_to_reproduce,expected_behavior,actual_behavior,environment,additional_info,labels
octocat,Hello-World,4117,https://github.com/octocat/Hello-World/issues/4117,closed,2025-07-13T13:47:36Z,새 이슈 등록하기,내 이름 추가하기,,,,,,
octocat,Hello-World,4108,https://github.com/octocat/Hello-World/issues/4108,closed,2025-07-09T12:25:30Z,Exemple d'issue de démonstration,"Ceci est une issue de démonstration générée automatiquement.
Elle sert à montrer comment appeler l’API GitHub via un assistant.
### Objectifs à atteindre
- Tester l’intégration API
- Générer une issue sur un dépôt GitHub
### Cas d'utilisation
Un développeur souhaite créer une issue rapidement sans se connecter à GitHub.
### Spécifications techniques
- Utilisation de l’endpoint `createGithubIssue`
- <PERSON><PERSON><PERSON><PERSON><PERSON> : `octocat`
- Dépôt : `Hello-World`",,,,,,
octocat,Hello-World,4107,https://github.com/octocat/Hello-World/issues/4107,closed,2025-07-09T12:14:45Z,Démo : exemple de création d'une issue,"Ceci est une issue de démonstration pour tester l'opération `createGithubIssue` via l'API.
L'objectif est de vérifier que la création automatique d'une issue fonctionne correctement dans un dépôt GitHub.
### Objectifs à atteindre
- Vérifier la connectivité avec l'API GitHub
- Créer une issue avec un format Markdown structuré
### Cas d'utilisation
En tant que développeur, je souhaite générer des issues automatiquement à partir d'un assistant intelligent pour gagner du temps et structurer mes tâches efficacement.
### Spécifications techniques
- Utilisation de l'API `api.github.com`
- Utilisation de l'opération `createGithubIssue` du plugin",,,,,,
octocat,Hello-World,4106,https://github.com/octocat/Hello-World/issues/4106,open,2025-07-09T02:46:41Z,Enrollment form,,,,,,,
octocat,Hello-World,4101,https://github.com/octocat/Hello-World/issues/4101,open,2025-07-07T12:02:11Z,Dummy,Test,,,,,,

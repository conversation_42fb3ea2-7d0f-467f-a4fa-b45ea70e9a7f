# GitHub Issue爬虫

获取GitHub项目Issue标题并进行内容分割的简洁工具。

## 功能

- 🔍 爬取GitHub仓库的Issues
- ✂️ 分割Issue内容为：标题、描述、步骤、期望、实际、环境、其他
- 📊 输出CSV格式

## 文件说明

- `issue_crawler.py` - 主要爬虫（150行）
- `example.py` - 使用示例（50行）
- `requirements.txt` - 依赖包（2行）
- `README.md` - 说明文档

## 输出格式

| 字段 | 描述 |
|------|------|
| `issue_number` | Issue编号 |
| `issue_url` | Issue链接 |
| `state` | 状态 (open/closed) |
| `title` | Issue标题 |
| `description` | 问题描述 |
| `content` | 合并的内容 (步骤+期望+实际) |

## 使用方法

### 命令行
```bash
# 安装依赖
pip install requests

# 基本使用
python issue_crawler.py https://github.com/owner/repo

# 限制数量
python issue_crawler.py https://github.com/owner/repo -m 50

# 使用Token
python issue_crawler.py https://github.com/owner/repo -t YOUR_TOKEN
```

### 编程使用
```python
from issue_crawler import GitHubIssueCrawler

crawler = GitHubIssueCrawler(token="your_token")
issues = crawler.crawl("https://github.com/owner/repo", "output.csv", max_issues=100)

# 查看分割结果
for issue in issues[:3]:
    print(f"标题: {issue['title']}")
    print(f"描述: {issue['description']}")
    print(f"步骤: {issue['steps']}")
```

## 分割算法

基于关键词识别：
- **描述**: description, summary, 描述
- **步骤**: steps, reproduce, 重现
- **期望**: expected, 期望
- **实际**: actual, 实际
- **环境**: environment, version, 环境

# GitHub Issue主题爬虫

专门用于获取和分割GitHub项目Issue主题内容的Python工具。

## 功能特性

- 🔍 爬取指定GitHub仓库的所有Issues
- ✂️ **智能分割Issue内容**为不同部分
- 📊 按照结构化格式输出数据
- 💾 支持CSV格式导出
- 🔑 支持GitHub Token认证

## 主要改进

与传统Issue爬虫不同，本工具专注于**Issue内容的智能分割**：

- **标题提取**：Issue标题
- **描述分离**：问题描述部分
- **重现步骤**：如何重现问题的步骤
- **期望行为**：用户期望的行为
- **实际行为**：实际发生的行为
- **环境信息**：系统环境、版本信息等
- **附加信息**：其他相关信息

## 输出格式

| 字段 | 描述 |
|------|------|
| `repo_owner` | 仓库所有者 |
| `repo_name` | 仓库名称 |
| `issue_number` | Issue编号 |
| `issue_url` | Issue的GitHub链接 |
| `state` | Issue状态 (open/closed) |
| `created_at` | 创建时间 |
| `title` | Issue标题 |
| `description` | 问题描述 |
| `steps_to_reproduce` | 重现步骤 |
| `expected_behavior` | 期望行为 |
| `actual_behavior` | 实际行为 |
| `environment` | 环境信息 |
| `additional_info` | 附加信息 |
| `labels` | Issue标签 |

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 1. 命令行使用

```bash
# 基本用法
python github_issue_crawler.py https://github.com/owner/repo

# 使用GitHub Token (推荐)
python github_issue_crawler.py https://github.com/owner/repo -t YOUR_GITHUB_TOKEN

# 限制获取数量
python github_issue_crawler.py https://github.com/owner/repo -m 100

# 指定输出文件
python github_issue_crawler.py https://github.com/owner/repo -o custom_topics.csv
```

### 2. 编程使用

```python
from github_issue_crawler import GitHubIssueTopicCrawler

# 创建爬虫实例
crawler = GitHubIssueTopicCrawler(token="your_github_token")

# 爬取Issue主题
topics = crawler.crawl_repository_topics(
    "https://github.com/owner/repo",
    "output.csv",
    max_issues=50
)

# 查看分割结果
for topic in topics[:3]:
    print(f"标题: {topic['title']}")
    print(f"描述: {topic['description']}")
    print(f"重现步骤: {topic['steps_to_reproduce']}")
    print(f"期望行为: {topic['expected_behavior']}")
    print("---")
```

### 3. 高级用法

```python
# 只获取Issue数据，不保存文件
issues_topics = crawler.get_issues_topics("owner", "repo", max_issues=100)

# 过滤特定类型的Issues
bug_reports = [
    topic for topic in issues_topics 
    if 'bug' in topic['title'].lower() and topic['steps_to_reproduce']
]

# 保存过滤结果
crawler.save_topics_to_csv(bug_reports, "bug_reports.csv")
```

## 智能分割算法

脚本使用以下策略来分割Issue内容：

### 关键词识别
- **描述**: description, summary, overview, 描述, 概述
- **重现步骤**: steps to reproduce, reproduction steps, 重现步骤, 复现步骤
- **期望行为**: expected behavior, expected result, 期望行为, 期望结果
- **实际行为**: actual behavior, actual result, 实际行为, 当前行为
- **环境信息**: environment, system info, version, 环境信息, 系统信息

### 格式识别
- Markdown标题 (`#`, `##`, `###`)
- 粗体标记 (`**text**`)
- 冒号结尾的行 (`Description:`)
- 短行标题 (长度 < 50字符)

### 智能回退
- 如果没有明确的章节，使用前几行作为描述
- 未分类的内容归入"附加信息"

## 示例输出

```csv
repo_owner,repo_name,issue_number,title,description,steps_to_reproduce,expected_behavior,actual_behavior,environment
microsoft,vscode,123,Editor crashes on large files,The editor becomes unresponsive...,1. Open a file > 10MB...,Editor should remain responsive,Editor freezes,Windows 10 VS Code 1.60
```

## GitHub Token设置

```bash
# 设置环境变量
export GITHUB_TOKEN="your_token_here"

# 或在代码中直接使用
crawler = GitHubIssueTopicCrawler(token="your_token_here")
```

## 注意事项

1. **API限制**: 无Token每小时60次请求，有Token每小时5000次
2. **分割准确性**: 基于关键词和格式识别，可能不完全准确
3. **语言支持**: 支持中英文关键词识别
4. **大型项目**: 建议使用`max_issues`参数限制数量

## 许可证

MIT License

{"github_token": "your_github_token_here", "default_output_dir": "./output", "batch_repositories": ["https://github.com/microsoft/vscode", "https://github.com/facebook/react", "https://github.com/google/tensorflow", "https://github.com/nodejs/node"], "crawl_settings": {"include_closed_issues": true, "include_pull_requests": false, "max_issues_per_repo": 1000, "delay_between_requests": 0.1}, "filter_settings": {"bug_keywords": ["bug", "error", "crash", "issue", "problem"], "feature_keywords": ["feature", "enhancement", "improvement"], "exclude_labels": ["duplicate", "invalid", "wontfix"]}}
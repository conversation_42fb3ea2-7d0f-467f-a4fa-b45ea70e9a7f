from tree_sitter import Parser, Language
import tree_sitter_python as tspython
import tree_sitter_cpp as tscpp
from config import FILE_EXTENSIONS
import os

class ASTParser:
    def __init__(self):
        self.parsers = {}
        self.language_map = {}
        self.counter = 1  # 全局唯一自增ID

    def next_id(self):
        val = self.counter
        self.counter += 1
        return val

    def get_parser(self, file_extension):
        lang_name = FILE_EXTENSIONS.get(file_extension)
        if not lang_name:
            return None
        if lang_name not in self.parsers:
            try:
                if lang_name == "python":
                    language = tspython.language()
                elif lang_name == "cpp":
                    language = tscpp.language()
                else:
                    raise Exception(f"Unsupported language: {lang_name}")
            except ImportError as e:
                raise Exception(f"Failed to load language module for {lang_name}: {e}")
            PY_LANGUAGE = Language(language)
            parser = Parser(PY_LANGUAGE)
            self.parsers[lang_name] = parser
            self.language_map[file_extension] = lang_name
        return self.parsers[lang_name]

    def parse_file(self, file_path):
        with open(file_path, "rb") as f:
            source_code = f.read()
        ext = os.path.splitext(file_path)[1].lower()
        parser = self.get_parser(ext)
        if not parser:
            print(f"No parser for {file_path}")
            return None
        tree = parser.parse(source_code)
        return tree

    def analyze_ast(self, tree, filepath=None):
        root_node = tree.root_node
        classes, interfaces, structs = [], [], []
        functions, methods = [], []
        variables, constants = [], []
        code_blocks = []
        calls = []
        class_refs = []
        variable_refs = []
        inherits = []
        implements = []
        file_deps = []

        # 用于临时存储类id到方法id的映射
        class_methods_map = {}

        def traverse(node, parent_id=None, cur_func=None, cur_class=None, cur_struct=None, cur_interface=None, filepath=filepath):
            # 类定义
            if node.type == "class_specifier":
                class_id = self.next_id()
                class_name = node.child_by_field_name("name")
                fqname = class_name.text.decode() if class_name else ""
                base = None
                for child in node.children:
                    if child.type == "base_class_clause":
                        base = child.child_by_field_name("name")
                        if base:
                            inherits.append({
                                "subclass": class_id,
                                "superclass": base.text.decode()
                            })
                classes.append({
                    "id": class_id,
                    "name": fqname,
                    "fully_qualified_name": fqname,
                    "filepath": filepath,
                    "start_line": node.start_point[0] + 1,
                    "end_line": node.end_point[0] + 1,
                    "methods": [],
                    "attributes": []
                })
                class_methods_map[class_id] = []
                for child in node.children:
                    traverse(child, parent_id=class_id, cur_class=class_id, filepath=filepath)
                # 记录方法id到类
                for m in class_methods_map[class_id]:
                    for c in classes:
                        if c["id"] == class_id:
                            c["methods"].append(m)
            # 结构体
            elif node.type == "struct_specifier":
                struct_id = self.next_id()
                struct_name = node.child_by_field_name("name")
                fqname = struct_name.text.decode() if struct_name else ""
                structs.append({
                    "id": struct_id,
                    "name": fqname,
                    "fully_qualified_name": fqname,
                    "filepath": filepath,
                    "start_line": node.start_point[0] + 1,
                    "end_line": node.end_point[0] + 1,
                    "attributes": []
                })
                for child in node.children:
                    traverse(child, parent_id=struct_id, cur_struct=struct_id, filepath=filepath)
            # 接口
            elif node.type == "interface_specifier":
                interface_id = self.next_id()
                interface_name = node.child_by_field_name("name")
                fqname = interface_name.text.decode() if interface_name else ""
                interfaces.append({
                    "id": interface_id,
                    "name": fqname,
                    "fully_qualified_name": fqname,
                    "filepath": filepath,
                    "start_line": node.start_point[0] + 1,
                    "end_line": node.end_point[0] + 1,
                    "methods": []
                })
                for child in node.children:
                    traverse(child, parent_id=interface_id, cur_interface=interface_id, filepath=filepath)
            # 函数/方法
            elif node.type == "function_definition":
                declarator = node.child_by_field_name("declarator")
                func_name = None
                parameters = None
                if declarator:
                    for child in declarator.children:
                        if child.type == "qualified_identifier":
                            func_name = child.children[-1]
                        elif child.type == "identifier":
                            func_name = child
                        elif child.type == "parameter_list":
                            parameters = child
                if func_name:
                    func_id = self.next_id()
                    func_info = {
                        "id": func_id,
                        "name": func_name.text.decode(),
                        "signature": func_name.text.decode() + (parameters.text.decode() if parameters else ""),
                        "parameters": parameters.text.decode() if parameters else None,
                        "return_type": None,
                        "class_id": cur_class,  # 方法的class_id指向所属类，否则为None
                        "filepath": filepath,
                        "start_line": node.start_point[0] + 1,
                        "end_line": node.end_point[0] + 1
                    }
                    if cur_class:
                        methods.append(func_info)
                        class_methods_map[cur_class].append(func_id)
                    else:
                        functions.append(func_info)
                    for child in node.children:
                        if child.type == "compound_statement":
                            traverse(child, parent_id=func_id, cur_func=func_id, cur_class=cur_class, filepath=filepath)
            # 变量/常量
            elif node.type in ("field_declaration", "declaration"):
                var_name = node.child_by_field_name("declarator")
                var_type = node.child_by_field_name("type")
                var_id = self.next_id()
                if var_name:
                    var_info = {
                        "id": var_id,
                        "name": var_name.text.decode(),
                        "type": var_type.text.decode() if var_type else None,
                        "parent_id": parent_id,
                        "filepath": filepath,
                        "start_line": node.start_point[0] + 1,
                        "end_line": node.end_point[0] + 1
                    }
                    if node.type == "field_declaration":
                        variables.append(var_info)
                        # 类成员变量引用类型
                        if cur_class and var_type:
                            class_refs.append({"from": cur_class, "to": var_type.text.decode()})
                    else:
                        constants.append(var_info)
            # 代码块
            elif node.type in ("if_statement", "for_statement", "while_statement", "compound_statement"):
                block_id = self.next_id()
                code_blocks.append({
                    "id": block_id,
                    "type": node.type,
                    "function_id": cur_func,
                    "filepath": filepath,
                    "parent_id": parent_id,
                    "start_line": node.start_point[0] + 1,
                    "end_line": node.end_point[0] + 1
                })
                for child in node.children:
                    traverse(child, parent_id=block_id, cur_func=cur_func, cur_class=cur_class, filepath=filepath)
            # 函数调用
            elif node.type == "call_expression":
                func_node = node.child_by_field_name("function")
                if func_node:
                    calls.append({
                        "caller": cur_func,
                        "callee": func_node.text.decode()
                    })
                for child in node.children:
                    traverse(child, parent_id, cur_func, cur_class, filepath=filepath)
            # 文件依赖
            elif node.type == "preproc_include":
                included = node.text.decode()
                file_deps.append(included)
            else:
                for child in node.children:
                    traverse(child, parent_id, cur_func, cur_class, filepath=filepath)

        traverse(root_node, filepath=filepath)

        return {
            "classes": classes,
            "interfaces": interfaces,
            "structs": structs,
            "functions": functions,  # 全局函数
            "methods": methods,      # 类方法
            "variables": variables,
            "constants": constants,
            "code_blocks": code_blocks,
            "calls": calls,
            "class_refs": class_refs,
            "variable_refs": variable_refs,
            "inherits": inherits,
            "implements": implements,
            "file_deps": file_deps
        }
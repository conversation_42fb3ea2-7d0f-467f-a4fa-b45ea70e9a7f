import os
import shutil
import stat
import time
from pathlib import Path
import pygit2

def remove_readonly(func, path, excinfo):
    """清除文件的只读属性后重试删除"""
    try:
        os.chmod(path, stat.S_IWRITE)
        func(path)
    except Exception as e:
        print(f"Failed to remove readonly attribute for {path}: {e}")

def clone_repo(repo_url, target_dir, branch=None):
    """克隆仓库并切换到指定分支/tag"""
    # 确保目标目录存在
    os.makedirs(os.path.dirname(target_dir), exist_ok=True)
    
    if os.path.exists(target_dir):
        print(f"Removing existing directory: {target_dir}")
        try:
            # 多次尝试删除，处理Windows文件锁问题
            for _ in range(3):
                try:
                    shutil.rmtree(target_dir, onerror=remove_readonly)  # 添加 onerror 参数
                    break
                except PermissionError as e:
                    print(f"PermissionError: {e}. Retrying after short delay...")
                    time.sleep(0.5)
        except Exception as e:
            print(f"Failed to remove directory: {e}")
            return False
    
    print(f"Cloning {repo_url}...")
    try:
        repo = pygit2.clone_repository(repo_url, target_dir)
    except Exception as e:
        print(f"Clone failed: {e}")
        return False
    
    if branch:
        # 检查是分支还是tag
        branch_ref = f"refs/heads/{branch}"
        tag_ref = f"refs/tags/{branch}"
        
        if branch_ref in repo.references:
            ref = repo.references[branch_ref]
            repo.checkout(ref)
            print(f"Checked out branch: {branch}")
        elif tag_ref in repo.references:
            repo.checkout(repo.references[tag_ref])
            print(f"Checked out tag: {branch}")
        else:
            print(f"Warning: Branch/Tag '{branch}' not found. Using default branch.")
    
    return True

def get_all_code_files(target_dir, skip_dirs, extensions):
    """
    获取目标目录中所有符合条件的代码文件。
    """
    code_files = []
    for root, dirs, files in os.walk(target_dir):
        dirs[:] = [d for d in dirs if os.path.join(root, d) not in skip_dirs]
        for file in files:
            if any(file.endswith(ext) for ext in extensions):
                code_files.append(os.path.join(root, file))
    return code_files
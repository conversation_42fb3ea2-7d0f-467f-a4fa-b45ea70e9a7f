import requests
import json

def test_ollama_connection():
    """测试 Ollama API 连接"""
    print("开始测试 Ollama API 连接...")
    
    # 1. 测试基本连接
    url = "http://localhost:11434/api/generate"
    print(f"\n1. 测试连接到: {url}")
    try:
        response = requests.get("http://localhost:11434")
        print(f"基本连接状态码: {response.status_code}")
    except Exception as e:
        print(f"连接失败: {str(e)}")
    
    # 2. 测试简单生成请求
    print("\n2. 测试简单生成请求")
    try:
        payload = {
            "model": "gemma3:12b",
            "prompt": "Hello, how are you?",
            "stream": False
        }
        print("发送请求...")
        print(f"请求内容: {json.dumps(payload, indent=2)}")
        
        response = requests.post(url, json=payload)
        print(f"响应状态码: {response.status_code}")
        print(f"响应内容: {response.text if response.status_code == 200 else 'N/A'}")
    except Exception as e:
        print(f"请求失败: {str(e)}")
    
    # 3. 测试可用模型列表
    print("\n3. 测试获取可用模型列表")
    try:
        response = requests.get("http://localhost:11434/api/tags")
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            models = response.json()
            print("可用模型:")
            print(json.dumps(models, indent=2))
    except Exception as e:
        print(f"获取模型列表失败: {str(e)}")

if __name__ == "__main__":
    test_ollama_connection()
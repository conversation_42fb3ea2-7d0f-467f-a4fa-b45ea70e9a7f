from ast_parser import git_utils, parser
from config import REPO_URL, TARGET_BRANCH, SKIP_DIRS, FILE_EXTENSIONS
import time
import os
from graphdb import GraphDB
import logging

# 修改目标目录到上一层
TARGET_DIR = os.path.join(os.path.dirname(os.path.dirname(__file__)), "repository")

class CodeAnalyzer:
    def __init__(self):
        self.ast_parser = parser.ASTParser()
        
    def analyze_file(self, file_path):
        """分析单个文件"""
        tree = self.ast_parser.parse_file(file_path)
        if not tree:
            return None
        return self.ast_parser.analyze_ast(tree, file_path)
    
    def print_file_analysis(self, result, file_path):
        """打印文件分析结果"""
        if not result:
            return
            
        print(f"\nFile Analysis: {file_path}")
        print(f"Found {len(result['functions'])} functions, {len(result['classes'])} classes, "
              f"{len(result['variables'])} variables, {len(result['code_blocks'])} code blocks")
        
        self._print_classes(result, file_path)
        self._print_functions(result, file_path)
        self._print_variables(result)
        self._print_code_blocks(result, file_path)
        self._print_relationships(result)
    
    def _print_classes(self, result, file_path):
        if result["classes"]:
            print("\n📚 Classes:")
            for cls in result["classes"]:
                print(f"\n  📘 {cls['name']} (Lines {cls['start_line']}-{cls['end_line']})")
                # 打印方法
                methods = [m for m in result["methods"] if m["class_id"] == cls["id"]]
                if methods:
                    print("    Methods:")
                    for method in methods:
                        print(f"      📝 {method['name']} (Lines {method['start_line']}-{method['end_line']})")
                        print(f"         Signature: {method['signature']}")
                # 打印成员变量
                variables = [v for v in result["variables"] if v.get("parent_id") == cls["id"]]
                if variables:
                    print("    Variables:")
                    for var in variables:
                        print(f"      🔹 {var['name']} ({var.get('type', 'unknown type')})")

    def _print_functions(self, result, file_path):
        if result["functions"]:
            print("\n🔧 Functions:")
            for func in result["functions"]:
                print(f"  📝 {func['name']} (Lines {func['start_line']}-{func['end_line']})")
                print(f"    Signature: {func['signature']}")

    def _print_variables(self, result):
        globals = [v for v in result["variables"] if not v.get("parent_id")]
        if globals:
            print("\n🌍 Global Variables:")
            for var in globals:
                print(f"  🔸 {var['name']} ({var.get('type', 'unknown type')})")

    def _print_code_blocks(self, result, file_path):
        if result["code_blocks"]:
            print("\n📦 Code Blocks:")
            for block in result["code_blocks"]:
                print(f"  ▫️ {block['type']} (Lines {block['start_line']}-{block['end_line']})")

    def _print_relationships(self, result):
        if result.get("calls"):
            print("\n🔄 Function Calls:")
            for call in result["calls"]:
                print(f"  {call['caller']} ➜ {call['callee']}")

class DatabaseWriter:
    def __init__(self, url="neo4j://localhost:7687", user="neo4j", password="password"):
        self.db = GraphDB(url, user, password)
        
    def write_analysis_results(self, analysis_results):
        """将分析结果写入数据库"""
        self.db.create_indexes()
        repo_name = os.path.basename(TARGET_DIR)
        self.db.create_repository(REPO_URL, repo_name)
        
        for file_path, result in analysis_results.items():
            self._write_file_data(file_path, result)
            
        self.db.close()
        
    def _write_file_data(self, file_path, result):
        """写入单个文件的数据"""
        ext = os.path.splitext(file_path)[1].lower()
        language = FILE_EXTENSIONS.get(ext, "unknown")
        self.db.create_file(file_path, language)
        self.db.create_relationship("Repository", ("url", REPO_URL), "File", ("path", file_path), "CONTAINS_FILE")
        
        # 写入所有实体和关系
        self._write_classes(file_path, result)
        self._write_methods(file_path, result)
        self._write_variables(file_path, result)
        self._write_functions(file_path, result)
        self._write_code_blocks(file_path, result)
        self._write_relationships(file_path, result)
        
    def _write_classes(self, file_path, result):
        for cls in result["classes"]:
            self.db.create_class(cls["id"], cls["name"], cls["fully_qualified_name"], file_path)
            self.db.create_relationship("File", ("path", file_path), "Class", ("id", cls["id"]), "DEFINES_CLASS")

    def _write_methods(self, file_path, result):
        for method in result.get("methods", []):
            if method["class_id"]:
                self.db.create_method(method["id"], method["name"], method["signature"], 
                                   method["class_id"], file_path, method["start_line"], method["end_line"])
                self.db.create_relationship("Class", ("id", method["class_id"]), 
                                         "Method", ("id", method["id"]), "DEFINES_FUNCTION")

    def _write_variables(self, file_path, result):
        # 处理类成员变量
        for var in [v for v in result["variables"] if v.get("parent_id")]:
            self.db.create_variable(var["id"], var["name"], var.get("type"), 
                                  var["parent_id"], file_path, var["start_line"], var["end_line"])
            self.db.create_relationship("Class", ("id", var["parent_id"]), 
                                     "Variable", ("id", var["id"]), "DEFINES_VARIABLE")
        
        # 处理全局变量和实例变量
        for var in [v for v in result["variables"] if not v.get("parent_id")]:
            self.db.create_variable(var["id"], var["name"], var.get("type"), 
                                  None, file_path, var["start_line"], var["end_line"])
            self.db.create_relationship("File", ("path", file_path), 
                                     "Variable", ("id", var["id"]), "DEFINES_VARIABLE")
            # 处理实例变量
            if var.get("type") and any(cls["name"] == var.get("type") for cls in result["classes"]):
                self.db.create_relationship("Variable", ("id", var["id"]), 
                                         "Class", ("name", var["type"]), "INSTANCE_OF")

    def _write_functions(self, file_path, result):
        for func in result["functions"]:
            self.db.create_function(func["id"], func["name"], func["signature"], 
                                  func.get("class_id"), file_path, func["start_line"], func["end_line"])
            self.db.create_relationship("File", ("path", file_path), 
                                     "Function", ("id", func["id"]), "DEFINES_FUNCTION")

    def _write_code_blocks(self, file_path, result):
        for block in result["code_blocks"]:
            self.db.create_codeblock(block["id"], block.get("function_id"), 
                                   file_path, block["start_line"], block["end_line"], block["type"])
            if block.get("function_id"):
                self.db.create_relationship("Function", ("id", block["function_id"]), 
                                         "CodeBlock", ("id", block["id"]), "CONTAINS_BLOCK")

    def _write_relationships(self, file_path, result):
        # 写入各种关系
        for call in result.get("calls", []):
            self.db.create_relationship("Variable", ("id", call["caller"]), 
                                     "Method", ("name", call["callee"]), "CALLS")
        
        for ref in result.get("class_refs", []):
            self.db.create_relationship("Class", ("id", ref["from"]), 
                                     "Class", ("name", ref["to"]), "REFERENCES_CLASS")
        
        for inh in result.get("inherits", []):
            self.db.create_relationship("Class", ("id", inh["subclass"]), 
                                     "Class", ("name", inh["superclass"]), "INHERITS_FROM")

def main():
    print(f"Target directory: {TARGET_DIR}")
    
    # 1. 克隆仓库
    print("Starting repository cloning process...")
    if not git_utils.clone_repo(REPO_URL, TARGET_DIR, TARGET_BRANCH):
        print("Failed to clone repository. Exiting.")
        return
    
    # 2. 获取代码文件
    print("Scanning for code files...")
    code_files = git_utils.get_all_code_files(TARGET_DIR, SKIP_DIRS, list(FILE_EXTENSIONS.keys()))
    if not code_files:
        print("No code files found. Exiting.")
        return
    
    print(f"Found {len(code_files)} code files")
    
    # 3. 分析文件
    analyzer = CodeAnalyzer()
    analysis_results = {}
    start_time = time.time()
    
    for i, file_path in enumerate(code_files, 1):
        print(f"\n[{i}/{len(code_files)}] Analyzing: {os.path.basename(file_path)}")
        result = analyzer.analyze_file(file_path)
        if result:
            analysis_results[file_path] = result
            analyzer.print_file_analysis(result, file_path)
    
    # 4. 输出统计
    print("\n=== Analysis Summary ===")
    total_functions = sum(len(r["functions"]) for r in analysis_results.values())
    total_classes = sum(len(r["classes"]) for r in analysis_results.values())
    total_variables = sum(len(r["variables"]) for r in analysis_results.values())
    total_code_blocks = sum(len(r["code_blocks"]) for r in analysis_results.values())
    
    print(f"Analyzed {len(analysis_results)}/{len(code_files)} files")
    print(f"Total functions: {total_functions}")
    print(f"Total classes: {total_classes}")
    print(f"Total variables: {total_variables}")
    print(f"Total code blocks: {total_code_blocks}")
    print(f"Time taken: {time.time()-start_time:.2f} seconds")
    
    # 5. 写入数据库
    print("\n=== Writing to Database ===")
    db_writer = DatabaseWriter()
    db_writer.write_analysis_results(analysis_results)
    print("Database writing completed")

if __name__ == "__main__":
    main()
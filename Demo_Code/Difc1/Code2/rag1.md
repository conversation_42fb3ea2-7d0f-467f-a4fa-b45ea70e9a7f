This file is a merged representation of the entire codebase, combined into a single document by Repomix.

# File Summary

## Purpose
This file contains a packed representation of the entire repository's contents.
It is designed to be easily consumable by AI systems for analysis, code review,
or other automated processes.

## File Format
The content is organized as follows:
1. This summary section
2. Repository information
3. Directory structure
4. Repository files (if enabled)
5. Multiple file entries, each consisting of:
  a. A header with the file path (## File: path/to/file)
  b. The full contents of the file in a code block

## Usage Guidelines
- This file should be treated as read-only. Any changes should be made to the
  original repository files, not this packed version.
- When processing this file, use the file path to distinguish
  between different files in the repository.
- Be aware that this file may contain sensitive information. Handle it with
  the same level of security as you would the original repository.

## Notes
- Some files may have been excluded based on .gitignore rules and Repomix's configuration
- Binary files are not included in this packed representation. Please refer to the Repository Structure section for a complete list of file paths, including binary files
- Files matching patterns in .gitignore are excluded
- Files matching default ignore patterns are excluded
- Files are sorted by Git change count (files with more changes are at the bottom)

# Directory Structure
```
.github/
  workflows/
    node.js.yml
    npm-publish.yml
  FUNDING.yml
.husky/
  pre-commit
fixtures/
  db.json
  db.json5
public/
  test.html
src/
  app.test.ts
  app.ts
  bin.ts
  observer.ts
  service.test.ts
  service.ts
views/
  index.html
.gitattributes
.gitignore
.prettier.config.js
CONTRIBUTING.md
eslint.config.js
LICENSE
package.json
README.md
tsconfig.json
```

# Files

## File: .github/FUNDING.yml
````yaml
github: typicode
````

## File: fixtures/db.json
````json
{
  "posts": [
    { "id": "1", "title": "a title" },
    { "id": "2", "title": "another title" }
  ],
  "comments": [
    { "id": "1", "text": "a comment about post 1", "postId": "1" },
    { "id": "2", "text": "another comment about post 1", "postId": "1" }
  ],
  "profile": {
    "name": "typicode"
  }
}
````

## File: public/test.html
````html
<!-- Testing automatic serving of files from the 'public/' directory -->
````

## File: .gitattributes
````
* text=auto eol=lf
*.ts linguist-language=JavaScript
````

## File: .prettier.config.js
````javascript
export default {
  semi: false,
  singleQuote: true,
  trailingComma: 'all',
}
````

## File: CONTRIBUTING.md
````markdown
## Agreement

Thanks for your interest in contributing!

By contributing to this project, you agree to the following:

1. **Relicensing:** to support the project's sustainability and ensure it's longevity, your contributions can be relicensed to any license.

2. **Ownership Rights:** You confirm you own the rights to your contributed code.

3. **Disagreement:** If you disagree with these terms, please create an issue instead. I'll handle the bug or feature request.

4. **Benefits for Contributors:** If your contribution is merged, you'll enjoy the same benefits as a sponsor for one year. This includes using the project in a company context, free of charge.

## Fair Source License

This project uses the Fair Source License, which is neither purely open-source nor closed-source. It allows visibility of the source code and free usage for a limited number of two users within an organization. Beyond this limit (three or more users), a small licensing fee via [GitHub Sponsors](https://github.com/sponsors/typicode) applies.

This doesn't apply to individuals, students, teachers, small teams, ...

Got questions or need support? Feel free to reach <NAME_EMAIL>.
````

## File: eslint.config.js
````javascript
import globals from "globals";
import pluginJs from "@eslint/js";
import tseslint from "typescript-eslint";


export default [
  {files: ["**/*.{js,mjs,cjs,ts}"]},
  {languageOptions: { globals: globals.node }},
  pluginJs.configs.recommended,
  ...tseslint.configs.recommended,
];
````

## File: .github/workflows/node.js.yml
````yaml
# This workflow will do a clean installation of node dependencies, cache/restore them, build the source code and run tests across different versions of node
# For more information see: https://docs.github.com/en/actions/automating-builds-and-tests/building-and-testing-nodejs

name: Node.js CI

on:
  push:
    branches: [ "main" ]
  pull_request:
    branches: [ "main" ]

jobs:
  build:

    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: [18.x, 20.x]
        # See supported Node.js release schedule at https://nodejs.org/en/about/releases/

    steps:
    - uses: actions/checkout@v3
    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v3
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
    - run: npm ci
    - run: npm run build --if-present
    - run: npm test
````

## File: .github/workflows/npm-publish.yml
````yaml
# This workflow will run tests using node and then publish a package to GitHub Packages when a release is created
# For more information see: https://docs.github.com/en/actions/publishing-packages/publishing-nodejs-packages

name: Node.js Package

on:
  release:
    types: [published]

permissions:
  contents: read

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: 20
      - run: npm ci
      - run: npm test

  publish-npm:
    needs: build
    runs-on: ubuntu-latest
    permissions:
      id-token: write
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: 20
          registry-url: https://registry.npmjs.org/
      - run: npm ci
      - run: npm publish --provenance --access public
        env:
          NODE_AUTH_TOKEN: ${{secrets.NPM_TOKEN}}
````

## File: .husky/pre-commit
````
npm test
````

## File: fixtures/db.json5
````json5
{
  posts: [
    {
      id: '1',
      title: 'a title',
    },
    {
      id: '2',
      title: 'another title',
    },
  ],
  comments: [
    {
      id: '1',
      text: 'a comment about post 1',
      postId: '1',
    },
    {
      id: '2',
      text: 'another comment about post 1',
      postId: '1',
    },
  ],
  profile: {
    name: 'typicode',
  },
}
````

## File: src/observer.ts
````typescript
import { Adapter } from 'lowdb'

// Lowdb adapter to observe read/write events
export class Observer<T> {
  #adapter

  onReadStart = function () {
    return
  }
  onReadEnd: (data: T | null) => void = function () {
    return
  }
  onWriteStart = function () {
    return
  }
  onWriteEnd = function () {
    return
  }

  constructor(adapter: Adapter<T>) {
    this.#adapter = adapter
  }

  async read() {
    this.onReadStart()
    const data = await this.#adapter.read()
    this.onReadEnd(data)
    return data
  }

  async write(arg: T) {
    this.onWriteStart()
    await this.#adapter.write(arg)
    this.onWriteEnd()
  }
}
````

## File: .gitignore
````
**/*.log
.DS_Store
.idea
lib
node_modules
public/output.css
tmp
````

## File: LICENSE
````
Fair Source License, version 0.9

Copyright (C) 2023-present typicode

Licensor: typicode

Software: json-server

Use Limitation: 2 users

License Grant. Licensor hereby grants to each recipient of the
Software ("you") a non-exclusive, non-transferable, royalty-free and
fully-paid-up license, under all of the Licensor's copyright and
patent rights, to use, copy, distribute, prepare derivative works of,
publicly perform and display the Software, subject to the Use
Limitation and the conditions set forth below.

Use Limitation. The license granted above allows use by up to the
number of users per entity set forth above (the "Use Limitation"). For
determining the number of users, "you" includes all affiliates,
meaning legal entities controlling, controlled by, or under common
control with you. If you exceed the Use Limitation, your use is
subject to payment of Licensor's then-current list price for licenses.

Conditions. Redistribution in source code or other forms must include
a copy of this license document to be provided in a reasonable
manner. Any redistribution of the Software is only allowed subject to
this license.

Trademarks. This license does not grant you any right in the
trademarks, service marks, brand names or logos of Licensor.

DISCLAIMER. THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OR
CONDITION, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO WARRANTIES
OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
NONINFRINGEMENT. LICENSORS HEREBY DISCLAIM ALL LIABILITY, WHETHER IN
AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN
CONNECTION WITH THE SOFTWARE.

Termination. If you violate the terms of this license, your rights
will terminate automatically and will not be reinstated without the
prior written consent of Licensor. Any such termination will not
affect the right of others who may have received copies of the
Software from you.
````

## File: tsconfig.json
````json
{
  "extends": "@sindresorhus/tsconfig",
  "exclude": ["src/**/*.test.ts"],
  "compilerOptions": {
    "outDir": "./lib"
  }
}
````

## File: src/app.test.ts
````typescript
import assert from 'node:assert/strict'
import { writeFileSync } from 'node:fs'
import { join } from 'node:path'
import test from 'node:test'

import getPort from 'get-port'
import { Low, Memory } from 'lowdb'
import { temporaryDirectory } from 'tempy'

import { createApp } from './app.js'
import { Data } from './service.js'

type Test = {
   
  method: HTTPMethods
  url: string
  statusCode: number
}

type HTTPMethods =
  | 'DELETE'
  | 'GET'
  | 'HEAD'
  | 'PATCH'
  | 'POST'
  | 'PUT'
  | 'OPTIONS'

const port = await getPort()

// Create custom static dir with an html file
const tmpDir = temporaryDirectory()
const file = 'file.html'
writeFileSync(join(tmpDir, file), 'utf-8')

// Create app
const db = new Low<Data>(new Memory<Data>(), {})
db.data = {
  posts: [{ id: '1', title: 'foo' }],
  comments: [{ id: '1', postId: '1' }],
  object: { f1: 'foo' },
}
const app = createApp(db, { static: [tmpDir] })

await new Promise<void>((resolve, reject) => {
  try {
    const server = app.listen(port, () => resolve())
    test.after(() => server.close())
  } catch (err) {
    reject(err)
  }
})

await test('createApp', async (t) => {
  // URLs
  const POSTS = '/posts'
  const POSTS_WITH_COMMENTS = '/posts?_embed=comments'
  const POST_1 = '/posts/1'
  const POST_NOT_FOUND = '/posts/-1'
  const POST_WITH_COMMENTS = '/posts/1?_embed=comments'
  const COMMENTS = '/comments'
  const POST_COMMENTS = '/comments?postId=1'
  const NOT_FOUND = '/not-found'
  const OBJECT = '/object'
  const OBJECT_1 = '/object/1'

  const arr: Test[] = [
    // Static
    { method: 'GET', url: '/', statusCode: 200 },
    { method: 'GET', url: '/test.html', statusCode: 200 },
    { method: 'GET', url: `/${file}`, statusCode: 200 },

    // CORS
    { method: 'OPTIONS', url: POSTS, statusCode: 204 },

    // API
    { method: 'GET', url: POSTS, statusCode: 200 },
    { method: 'GET', url: POSTS_WITH_COMMENTS, statusCode: 200 },
    { method: 'GET', url: POST_1, statusCode: 200 },
    { method: 'GET', url: POST_NOT_FOUND, statusCode: 404 },
    { method: 'GET', url: POST_WITH_COMMENTS, statusCode: 200 },
    { method: 'GET', url: COMMENTS, statusCode: 200 },
    { method: 'GET', url: POST_COMMENTS, statusCode: 200 },
    { method: 'GET', url: OBJECT, statusCode: 200 },
    { method: 'GET', url: OBJECT_1, statusCode: 404 },
    { method: 'GET', url: NOT_FOUND, statusCode: 404 },

    { method: 'POST', url: POSTS, statusCode: 201 },
    { method: 'POST', url: POST_1, statusCode: 404 },
    { method: 'POST', url: POST_NOT_FOUND, statusCode: 404 },
    { method: 'POST', url: OBJECT, statusCode: 404 },
    { method: 'POST', url: OBJECT_1, statusCode: 404 },
    { method: 'POST', url: NOT_FOUND, statusCode: 404 },

    { method: 'PUT', url: POSTS, statusCode: 404 },
    { method: 'PUT', url: POST_1, statusCode: 200 },
    { method: 'PUT', url: OBJECT, statusCode: 200 },
    { method: 'PUT', url: OBJECT_1, statusCode: 404 },
    { method: 'PUT', url: POST_NOT_FOUND, statusCode: 404 },
    { method: 'PUT', url: NOT_FOUND, statusCode: 404 },

    { method: 'PATCH', url: POSTS, statusCode: 404 },
    { method: 'PATCH', url: POST_1, statusCode: 200 },
    { method: 'PATCH', url: OBJECT, statusCode: 200 },
    { method: 'PATCH', url: OBJECT_1, statusCode: 404 },
    { method: 'PATCH', url: POST_NOT_FOUND, statusCode: 404 },
    { method: 'PATCH', url: NOT_FOUND, statusCode: 404 },

    { method: 'DELETE', url: POSTS, statusCode: 404 },
    { method: 'DELETE', url: POST_1, statusCode: 200 },
    { method: 'DELETE', url: OBJECT, statusCode: 404 },
    { method: 'DELETE', url: OBJECT_1, statusCode: 404 },
    { method: 'DELETE', url: POST_NOT_FOUND, statusCode: 404 },
    { method: 'DELETE', url: NOT_FOUND, statusCode: 404 },
  ]

  for (const tc of arr) {
    await t.test(`${tc.method} ${tc.url}`, async () => {
      const response = await fetch(`http://localhost:${port}${tc.url}`, {
        method: tc.method,
      })
      assert.equal(
        response.status,
        tc.statusCode,
        `${response.status} !== ${tc.statusCode} ${tc.method} ${tc.url} failed`,
      )
    })
  }
})
````

## File: views/index.html
````html
<!DOCTYPE html>
<html>

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <style>
    html {
      font-size: 16px;
      line-height: 1.5;
      background-color: #fff;
      color: #000;
    }

    body {
      margin: 0 auto;
      max-width: 720px;
      padding: 0 16px;
      font-family: sans-serif;
    }

    a {
      color: #db2777;
      text-decoration: none;
    }

    header {
      margin-bottom: 32px;
      padding: 16px 0;
    }

    nav {
      display: flex;
      justify-content: space-between;
    }

    nav div a {
      margin-left: 16px;
    }

    ul {
      margin: 0;
      padding: 0;
      list-style: none;
    }

    li {
      margin-bottom: 8px;
    }

    /* Dark mode styles */
    @media (prefers-color-scheme: dark) {
      html {
        background-color: #1e293b;
        color: #fff;
      }

      a {

      }
    }

  </style>
</head>

<body>
  <header>
    <nav>
      <strong>JSON Server</strong>
      <div>
        <a href="https://github.com/typicode/json-server">Docs</a>
        <a href="https://github.com/sponsors/typicode">♡ Sponsor</a>
      </div>
    </nav>
  </header>
  <main class="my-12">
    <p class="bg-gradient-to-r from-purple-500 via-pink-500 to-red-500 text-transparent bg-clip-text">✧*｡٩(ˊᗜˋ*)و✧*｡</p>
    <% if (Object.keys(it.data).length===0) { %>
      <p>No resources found in JSON file</p>
    <% } %>
    <% Object.entries(it.data).forEach(function([name]) { %>
      <ul>
        <li>
          <a href="<%= name %>">/<%= name %></a>
          <span>
            <% if (Array.isArray(it.data[name])) { %>
              - <%= it.data[name].length %>
                <%= it.data[name].length> 1 ? 'items' : 'item' %>
          </span>
          <% } %>
        </li>
      </ul>
    <% }) %>
  </main>
</body>

</html>
````

## File: src/bin.ts
````typescript
#!/usr/bin/env node
import { existsSync, readFileSync, writeFileSync } from 'node:fs'
import { extname } from 'node:path'
import { parseArgs } from 'node:util'

import chalk from 'chalk'
import { watch } from 'chokidar'
import JSON5 from 'json5'
import { Adapter, Low } from 'lowdb'
import { DataFile, JSONFile } from 'lowdb/node'
import { PackageJson } from 'type-fest'

import { fileURLToPath } from 'node:url'
import { createApp } from './app.js'
import { Observer } from './observer.js'
import { Data } from './service.js'

function help() {
  console.log(`Usage: json-server [options] <file>

Options:
  -p, --port <port>  Port (default: 3000)
  -h, --host <host>  Host (default: localhost)
  -s, --static <dir> Static files directory (multiple allowed)
  --help             Show this message
  --version          Show version number
`)
}

// Parse args
function args(): {
  file: string
  port: number
  host: string
  static: string[]
} {
  try {
    const { values, positionals } = parseArgs({
      options: {
        port: {
          type: 'string',
          short: 'p',
          default: process.env['PORT'] ?? '3000',
        },
        host: {
          type: 'string',
          short: 'h',
          default: process.env['HOST'] ?? 'localhost',
        },
        static: {
          type: 'string',
          short: 's',
          multiple: true,
          default: [],
        },
        help: {
          type: 'boolean',
        },
        version: {
          type: 'boolean',
        },
        // Deprecated
        watch: {
          type: 'boolean',
          short: 'w',
        },
      },
      allowPositionals: true,
    })

    // --version
    if (values.version) {
      const pkg = JSON.parse(
        readFileSync(
          fileURLToPath(new URL('../package.json', import.meta.url)),
          'utf-8',
        ),
      ) as PackageJson
      console.log(pkg.version)
      process.exit()
    }

    // Handle --watch
    if (values.watch) {
      console.log(
        chalk.yellow(
          '--watch/-w can be omitted, JSON Server 1+ watches for file changes by default',
        ),
      )
    }

    if (values.help || positionals.length === 0) {
      help()
      process.exit()
    }

    // App args and options
    return {
      file: positionals[0] ?? '',
      port: parseInt(values.port as string),
      host: values.host as string,
      static: values.static as string[],
    }
  } catch (e) {
    if ((e as NodeJS.ErrnoException).code === 'ERR_PARSE_ARGS_UNKNOWN_OPTION') {
      console.log(chalk.red((e as NodeJS.ErrnoException).message.split('.')[0]))
      help()
      process.exit(1)
    } else {
      throw e
    }
  }
}

const { file, port, host, static: staticArr } = args()

if (!existsSync(file)) {
  console.log(chalk.red(`File ${file} not found`))
  process.exit(1)
}

// Handle empty string JSON file
if (readFileSync(file, 'utf-8').trim() === '') {
  writeFileSync(file, '{}')
}

// Set up database
let adapter: Adapter<Data>
if (extname(file) === '.json5') {
  adapter = new DataFile<Data>(file, {
    parse: JSON5.parse,
    stringify: JSON5.stringify,
  })
} else {
  adapter = new JSONFile<Data>(file)
}
const observer = new Observer(adapter)

const db = new Low<Data>(observer, {})
await db.read()

// Create app
const app = createApp(db, { logger: false, static: staticArr })

function logRoutes(data: Data) {
  console.log(chalk.bold('Endpoints:'))
  if (Object.keys(data).length === 0) {
    console.log(
      chalk.gray(`No endpoints found, try adding some data to ${file}`),
    )
    return
  }
  console.log(
    Object.keys(data)
      .map(
        (key) => `${chalk.gray(`http://${host}:${port}/`)}${chalk.blue(key)}`,
      )
      .join('\n'),
  )
}

const kaomojis = ['♡⸜(˶˃ ᵕ ˂˶)⸝♡', '♡( ◡‿◡ )', '( ˶ˆ ᗜ ˆ˵ )', '(˶ᵔ ᵕ ᵔ˶)']

function randomItem(items: string[]): string {
  const index = Math.floor(Math.random() * items.length)
  return items.at(index) ?? ''
}

app.listen(port, () => {
  console.log(
    [
      chalk.bold(`JSON Server started on PORT :${port}`),
      chalk.gray('Press CTRL-C to stop'),
      chalk.gray(`Watching ${file}...`),
      '',
      chalk.magenta(randomItem(kaomojis)),
      '',
      chalk.bold('Index:'),
      chalk.gray(`http://localhost:${port}/`),
      '',
      chalk.bold('Static files:'),
      chalk.gray('Serving ./public directory if it exists'),
      '',
    ].join('\n'),
  )
  logRoutes(db.data)
})

// Watch file for changes
if (process.env['NODE_ENV'] !== 'production') {
  let writing = false // true if the file is being written to by the app
  let prevEndpoints = ''

  observer.onWriteStart = () => {
    writing = true
  }
  observer.onWriteEnd = () => {
    writing = false
  }
  observer.onReadStart = () => {
    prevEndpoints = JSON.stringify(Object.keys(db.data).sort())
  }
  observer.onReadEnd = (data) => {
    if (data === null) {
      return
    }

    const nextEndpoints = JSON.stringify(Object.keys(data).sort())
    if (prevEndpoints !== nextEndpoints) {
      console.log()
      logRoutes(data)
    }
  }
  watch(file).on('change', () => {
    // Do no reload if the file is being written to by the app
    if (!writing) {
      db.read().catch((e) => {
        if (e instanceof SyntaxError) {
          return console.log(
            chalk.red(['', `Error parsing ${file}`, e.message].join('\n')),
          )
        }
        console.log(e)
      })
    }
  })
}
````

## File: src/service.test.ts
````typescript
import assert from 'node:assert/strict'
import test from 'node:test'

import { Low, Memory } from 'lowdb'

import { Data, Item, PaginatedItems, Service } from './service.js'

const defaultData = { posts: [], comments: [], object: {} }
const adapter = new Memory<Data>()
const db = new Low<Data>(adapter, defaultData)
const service = new Service(db)

const POSTS = 'posts'
const COMMENTS = 'comments'
const OBJECT = 'object'

const UNKNOWN_RESOURCE = 'xxx'
const UNKNOWN_ID = 'xxx'

const post1 = {
  id: '1',
  title: 'a',
  views: 100,
  published: true,
  author: { name: 'foo' },
  tags: ['foo', 'bar'],
}
const post2 = {
  id: '2',
  title: 'b',
  views: 200,
  published: false,
  author: { name: 'bar' },
  tags: ['bar'],
}
const post3 = {
  id: '3',
  title: 'c',
  views: 300,
  published: false,
  author: { name: 'baz' },
  tags: ['foo'],
}
const comment1 = { id: '1', title: 'a', postId: '1' }
const items = 3

const obj = {
  f1: 'foo',
}

function reset() {
  db.data = structuredClone({
    posts: [post1, post2, post3],
    comments: [comment1],
    object: obj,
  })
}

await test('constructor', () => {
  const defaultData = { posts: [{ id: '1' }, {}], object: {} } satisfies Data
  const db = new Low<Data>(adapter, defaultData)
  new Service(db)
  if (Array.isArray(db.data['posts'])) {
    const id0 = db.data['posts']?.at(0)?.['id']
    const id1 = db.data['posts']?.at(1)?.['id']
    assert.ok(
      typeof id1 === 'string' && id1.length > 0,
      `id should be a non empty string but was: ${String(id1)}`,
    )
    assert.ok(
      typeof id0 === 'string' && id0 === '1',
      `id should not change if already set but was: ${String(id0)}`,
    )
  }
})

await test('findById', () => {
  reset()
  if (!Array.isArray(db.data?.[POSTS]))
    throw new Error('posts should be an array')
  assert.deepEqual(service.findById(POSTS, '1', {}), db.data?.[POSTS]?.[0])
  assert.equal(service.findById(POSTS, UNKNOWN_ID, {}), undefined)
  assert.deepEqual(service.findById(POSTS, '1', { _embed: ['comments'] }), {
    ...post1,
    comments: [comment1],
  })
  assert.deepEqual(service.findById(COMMENTS, '1', { _embed: ['post'] }), {
    ...comment1,
    post: post1,
  })
  assert.equal(service.findById(UNKNOWN_RESOURCE, '1', {}), undefined)
})

await test('find', async (t) => {
  const arr: {
    data?: Data
    name: string
    params?: Parameters<Service['find']>[1]
    res: Item | Item[] | PaginatedItems | undefined
    error?: Error
  }[] = [
    {
      name: POSTS,
      res: [post1, post2, post3],
    },
    {
      name: POSTS,
      params: { id: post1.id },
      res: [post1],
    },
    {
      name: POSTS,
      params: { id: UNKNOWN_ID },
      res: [],
    },
    {
      name: POSTS,
      params: { views: post1.views.toString() },
      res: [post1],
    },
    {
      name: POSTS,
      params: { 'author.name': post1.author.name },
      res: [post1],
    },
    {
      name: POSTS,
      params: { 'tags[0]': 'foo' },
      res: [post1, post3],
    },
    {
      name: POSTS,
      params: { id: UNKNOWN_ID, views: post1.views.toString() },
      res: [],
    },
    {
      name: POSTS,
      params: { views_ne: post1.views.toString() },
      res: [post2, post3],
    },
    {
      name: POSTS,
      params: { views_lt: (post1.views + 1).toString() },
      res: [post1],
    },
    {
      name: POSTS,
      params: { views_lt: post1.views.toString() },
      res: [],
    },
    {
      name: POSTS,
      params: { views_lte: post1.views.toString() },
      res: [post1],
    },
    {
      name: POSTS,
      params: { views_gt: post1.views.toString() },
      res: [post2, post3],
    },
    {
      name: POSTS,
      params: { views_gt: (post1.views - 1).toString() },
      res: [post1, post2, post3],
    },
    {
      name: POSTS,
      params: { views_gte: post1.views.toString() },
      res: [post1, post2, post3],
    },
    {
      name: POSTS,
      params: {
        views_gt: post1.views.toString(),
        views_lt: post3.views.toString(),
      },
      res: [post2],
    },
    {
      data: { posts: [post3, post1, post2] },
      name: POSTS,
      params: { _sort: 'views' },
      res: [post1, post2, post3],
    },
    {
      data: { posts: [post3, post1, post2] },
      name: POSTS,
      params: { _sort: '-views' },
      res: [post3, post2, post1],
    },
    {
      data: { posts: [post3, post1, post2] },
      name: POSTS,
      params: { _sort: '-views,id' },
      res: [post3, post2, post1],
    },
    {
      name: POSTS,
      params: { published: 'true' },
      res: [post1],
    },
    {
      name: POSTS,
      params: { published: 'false' },
      res: [post2, post3],
    },
    {
      name: POSTS,
      params: { views_lt: post3.views.toString(), published: 'false' },
      res: [post2],
    },
    {
      name: POSTS,
      params: { _start: 0, _end: 2 },
      res: [post1, post2],
    },
    {
      name: POSTS,
      params: { _start: 1, _end: 3 },
      res: [post2, post3],
    },
    {
      name: POSTS,
      params: { _start: 0, _limit: 2 },
      res: [post1, post2],
    },
    {
      name: POSTS,
      params: { _start: 1, _limit: 2 },
      res: [post2, post3],
    },
    {
      name: POSTS,
      params: { _page: 1, _per_page: 2 },
      res: {
        first: 1,
        last: 2,
        prev: null,
        next: 2,
        pages: 2,
        items,
        data: [post1, post2],
      },
    },
    {
      name: POSTS,
      params: { _page: 2, _per_page: 2 },
      res: {
        first: 1,
        last: 2,
        prev: 1,
        next: null,
        pages: 2,
        items,
        data: [post3],
      },
    },
    {
      name: POSTS,
      params: { _page: 3, _per_page: 2 },
      res: {
        first: 1,
        last: 2,
        prev: 1,
        next: null,
        pages: 2,
        items,
        data: [post3],
      },
    },
    {
      name: POSTS,
      params: { _page: 2, _per_page: 1 },
      res: {
        first: 1,
        last: 3,
        prev: 1,
        next: 3,
        pages: 3,
        items,
        data: [post2],
      },
    },
    {
      name: POSTS,
      params: { _embed: ['comments'] },
      res: [
        { ...post1, comments: [comment1] },
        { ...post2, comments: [] },
        { ...post3, comments: [] },
      ],
    },
    {
      name: COMMENTS,
      params: { _embed: ['post'] },
      res: [{ ...comment1, post: post1 }],
    },
    {
      name: UNKNOWN_RESOURCE,
      res: undefined,
    },
    {
      name: OBJECT,
      res: obj,
    },
  ]
  for (const tc of arr) {
    await t.test(`${tc.name} ${JSON.stringify(tc.params)}`, () => {
      if (tc.data) {
        db.data = tc.data
      } else {
        reset()
      }

      assert.deepEqual(service.find(tc.name, tc.params), tc.res)
    })
  }
})

await test('create', async () => {
  reset()
  const post = { title: 'new post' }
  const res = await service.create(POSTS, post)
  assert.equal(res?.['title'], post.title)
  assert.equal(typeof res?.['id'], 'string', 'id should be a string')

  assert.equal(await service.create(UNKNOWN_RESOURCE, post), undefined)
})

await test('update', async () => {
  reset()
  const obj = { f1: 'bar' }
  const res = await service.update(OBJECT, obj)
  assert.equal(res, obj)

  assert.equal(
    await service.update(UNKNOWN_RESOURCE, obj),
    undefined,
    'should ignore unknown resources',
  )
  assert.equal(
    await service.update(POSTS, {}),
    undefined,
    'should ignore arrays',
  )
})

await test('updateById', async () => {
  reset()
  const post = { id: 'xxx', title: 'updated post' }
  const res = await service.updateById(POSTS, post1.id, post)
  assert.equal(res?.['id'], post1.id, 'id should not change')
  assert.equal(res?.['title'], post.title)

  assert.equal(
    await service.updateById(UNKNOWN_RESOURCE, post1.id, post),
    undefined,
  )
  assert.equal(await service.updateById(POSTS, UNKNOWN_ID, post), undefined)
})

await test('patchById', async () => {
  reset()
  const post = { id: 'xxx', title: 'updated post' }
  const res = await service.patchById(POSTS, post1.id, post)
  assert.notEqual(res, undefined)
  assert.equal(res?.['id'], post1.id)
  assert.equal(res?.['title'], post.title)

  assert.equal(
    await service.patchById(UNKNOWN_RESOURCE, post1.id, post),
    undefined,
  )
  assert.equal(await service.patchById(POSTS, UNKNOWN_ID, post), undefined)
})

await test('destroy', async () => {
  reset()
  let prevLength = Number(db.data?.[POSTS]?.length) || 0
  await service.destroyById(POSTS, post1.id)
  assert.equal(db.data?.[POSTS]?.length, prevLength - 1)
  assert.deepEqual(db.data?.[COMMENTS], [{ ...comment1, postId: null }])

  reset()
  prevLength = db.data?.[POSTS]?.length || 0
  await service.destroyById(POSTS, post1.id, [COMMENTS])
  assert.equal(db.data[POSTS].length, prevLength - 1)
  assert.equal(db.data[COMMENTS].length, 0)

  assert.equal(await service.destroyById(UNKNOWN_RESOURCE, post1.id), undefined)
  assert.equal(await service.destroyById(POSTS, UNKNOWN_ID), undefined)
})
````

## File: src/app.ts
````typescript
import { dirname, isAbsolute, join } from 'node:path'
import { fileURLToPath } from 'node:url'

import { App } from '@tinyhttp/app'
import { cors } from '@tinyhttp/cors'
import { Eta } from 'eta'
import { Low } from 'lowdb'
import { json } from 'milliparsec'
import sirv from 'sirv'

import { Data, isItem, Service } from './service.js'

const __dirname = dirname(fileURLToPath(import.meta.url))
const isProduction = process.env['NODE_ENV'] === 'production'

export type AppOptions = {
  logger?: boolean
  static?: string[]
}

const eta = new Eta({
  views: join(__dirname, '../views'),
  cache: isProduction,
})

export function createApp(db: Low<Data>, options: AppOptions = {}) {
  // Create service
  const service = new Service(db)

  // Create app
  const app = new App()

  // Static files
  app.use(sirv('public', { dev: !isProduction }))
  options.static
    ?.map((path) => (isAbsolute(path) ? path : join(process.cwd(), path)))
    .forEach((dir) => app.use(sirv(dir, { dev: !isProduction })))

  // CORS
  app
    .use((req, res, next) => {
      return cors({
        allowedHeaders: req.headers['access-control-request-headers']
          ?.split(',')
          .map((h) => h.trim()),
      })(req, res, next)
    })
    .options('*', cors())

  // Body parser
  // @ts-expect-error expected
  app.use(json())

  app.get('/', (_req, res) =>
    res.send(eta.render('index.html', { data: db.data })),
  )

  app.get('/:name', (req, res, next) => {
    const { name = '' } = req.params
    const query = Object.fromEntries(
      Object.entries(req.query)
        .map(([key, value]) => {
          if (
            ['_start', '_end', '_limit', '_page', '_per_page'].includes(key) &&
            typeof value === 'string'
          ) {
            return [key, parseInt(value)]
          } else {
            return [key, value]
          }
        })
        .filter(([, value]) => !Number.isNaN(value)),
    )
    res.locals['data'] = service.find(name, query)
    next?.()
  })

  app.get('/:name/:id', (req, res, next) => {
    const { name = '', id = '' } = req.params
    res.locals['data'] = service.findById(name, id, req.query)
    next?.()
  })

  app.post('/:name', async (req, res, next) => {
    const { name = '' } = req.params
    if (isItem(req.body)) {
      res.locals['data'] = await service.create(name, req.body)
    }
    next?.()
  })

  app.put('/:name', async (req, res, next) => {
    const { name = '' } = req.params
    if (isItem(req.body)) {
      res.locals['data'] = await service.update(name, req.body)
    }
    next?.()
  })

  app.put('/:name/:id', async (req, res, next) => {
    const { name = '', id = '' } = req.params
    if (isItem(req.body)) {
      res.locals['data'] = await service.updateById(name, id, req.body)
    }
    next?.()
  })

  app.patch('/:name', async (req, res, next) => {
    const { name = '' } = req.params
    if (isItem(req.body)) {
      res.locals['data'] = await service.patch(name, req.body)
    }
    next?.()
  })

  app.patch('/:name/:id', async (req, res, next) => {
    const { name = '', id = '' } = req.params
    if (isItem(req.body)) {
      res.locals['data'] = await service.patchById(name, id, req.body)
    }
    next?.()
  })

  app.delete('/:name/:id', async (req, res, next) => {
    const { name = '', id = '' } = req.params
    res.locals['data'] = await service.destroyById(
      name,
      id,
      req.query['_dependent'],
    )
    next?.()
  })

  app.use('/:name', (req, res) => {
    const { data } = res.locals
    if (data === undefined) {
      res.sendStatus(404)
    } else {
      if (req.method === 'POST') res.status(201)
      res.json(data)
    }
  })

  return app
}
````

## File: src/service.ts
````typescript
import { randomBytes } from 'node:crypto'

import { getProperty } from 'dot-prop'
import inflection from 'inflection'
import { Low } from 'lowdb'
import sortOn from 'sort-on'

export type Item = Record<string, unknown>

export type Data = Record<string, Item[] | Item>

export function isItem(obj: unknown): obj is Item {
  return typeof obj === 'object' && obj !== null
}

export function isData(obj: unknown): obj is Record<string, Item[]> {
  if (typeof obj !== 'object' || obj === null) {
    return false
  }

  const data = obj as Record<string, unknown>
  return Object.values(data).every(
    (value) => Array.isArray(value) && value.every(isItem),
  )
}

enum Condition {
  lt = 'lt',
  lte = 'lte',
  gt = 'gt',
  gte = 'gte',
  ne = 'ne',
  default = '',
}

function isCondition(value: string): value is Condition {
  return Object.values<string>(Condition).includes(value)
}

export type PaginatedItems = {
  first: number
  prev: number | null
  next: number | null
  last: number
  pages: number
  items: number
  data: Item[]
}

function ensureArray(arg: string | string[] = []): string[] {
  return Array.isArray(arg) ? arg : [arg]
}

function embed(db: Low<Data>, name: string, item: Item, related: string): Item {
  if (inflection.singularize(related) === related) {
    const relatedData = db.data[inflection.pluralize(related)] as Item[]
    if (!relatedData) {
      return item
    }
    const foreignKey = `${related}Id`
    const relatedItem = relatedData.find((relatedItem: Item) => {
      return relatedItem['id'] === item[foreignKey]
    })
    return { ...item, [related]: relatedItem }
  }
  const relatedData: Item[] = db.data[related] as Item[]

  if (!relatedData) {
    return item
  }

  const foreignKey = `${inflection.singularize(name)}Id`
  const relatedItems = relatedData.filter(
    (relatedItem: Item) => relatedItem[foreignKey] === item['id'],
  )

  return { ...item, [related]: relatedItems }
}

function nullifyForeignKey(db: Low<Data>, name: string, id: string) {
  const foreignKey = `${inflection.singularize(name)}Id`

  Object.entries(db.data).forEach(([key, items]) => {
    // Skip
    if (key === name) return

    // Nullify
    if (Array.isArray(items)) {
      items.forEach((item) => {
        if (item[foreignKey] === id) {
          item[foreignKey] = null
        }
      })
    }
  })
}

function deleteDependents(db: Low<Data>, name: string, dependents: string[]) {
  const foreignKey = `${inflection.singularize(name)}Id`

  Object.entries(db.data).forEach(([key, items]) => {
    // Skip
    if (key === name || !dependents.includes(key)) return

    // Delete if foreign key is null
    if (Array.isArray(items)) {
      db.data[key] = items.filter((item) => item[foreignKey] !== null)
    }
  })
}

function randomId(): string {
  return randomBytes(2).toString('hex')
}

function fixItemsIds(items: Item[]) {
  items.forEach((item) => {
    if (typeof item['id'] === 'number') {
      item['id'] = item['id'].toString()
    }
    if (item['id'] === undefined) {
      item['id'] = randomId()
    }
  })
}

// Ensure all items have an id
function fixAllItemsIds(data: Data) {
  Object.values(data).forEach((value) => {
    if (Array.isArray(value)) {
      fixItemsIds(value)
    }
  })
}

export class Service {
  #db: Low<Data>

  constructor(db: Low<Data>) {
    fixAllItemsIds(db.data)
    this.#db = db
  }

  #get(name: string): Item[] | Item | undefined {
    return this.#db.data[name]
  }

  has(name: string): boolean {
    return Object.prototype.hasOwnProperty.call(this.#db?.data, name)
  }

  findById(
    name: string,
    id: string,
    query: { _embed?: string[] | string },
  ): Item | undefined {
    const value = this.#get(name)

    if (Array.isArray(value)) {
      let item = value.find((item) => item['id'] === id)
      ensureArray(query._embed).forEach((related) => {
        if (item !== undefined) item = embed(this.#db, name, item, related)
      })
      return item
    }

    return
  }

  find(
    name: string,
    query: {
      [key: string]: unknown
      _embed?: string | string[]
      _sort?: string
      _start?: number
      _end?: number
      _limit?: number
      _page?: number
      _per_page?: number
    } = {},
  ): Item[] | PaginatedItems | Item | undefined {
    let items = this.#get(name)

    if (!Array.isArray(items)) {
      return items
    }

    // Include
    ensureArray(query._embed).forEach((related) => {
      if (items !== undefined && Array.isArray(items)) {
        items = items.map((item) => embed(this.#db, name, item, related))
      }
    })

    // Return list if no query params
    if (Object.keys(query).length === 0) {
      return items
    }

    // Convert query params to conditions
    const conds: [string, Condition, string | string[]][] = []
    for (const [key, value] of Object.entries(query)) {
      if (value === undefined || typeof value !== 'string') {
        continue
      }
      const re = /_(lt|lte|gt|gte|ne)$/
      const reArr = re.exec(key)
      const op = reArr?.at(1)
      if (op && isCondition(op)) {
        const field = key.replace(re, '')
        conds.push([field, op, value])
        continue
      }
      if (
        [
          '_embed',
          '_sort',
          '_start',
          '_end',
          '_limit',
          '_page',
          '_per_page',
        ].includes(key)
      ) {
        continue
      }
      conds.push([key, Condition.default, value])
    }

    // Loop through conditions and filter items
    let filtered = items
    for (const [key, op, paramValue] of conds) {
      filtered = filtered.filter((item: Item) => {
        if (paramValue && !Array.isArray(paramValue)) {
          // https://github.com/sindresorhus/dot-prop/issues/95
          const itemValue: unknown = getProperty(item, key)
          switch (op) {
            // item_gt=value
            case Condition.gt: {
              if (
                !(
                  typeof itemValue === 'number' &&
                  itemValue > parseInt(paramValue)
                )
              ) {
                return false
              }
              break
            }
            // item_gte=value
            case Condition.gte: {
              if (
                !(
                  typeof itemValue === 'number' &&
                  itemValue >= parseInt(paramValue)
                )
              ) {
                return false
              }
              break
            }
            // item_lt=value
            case Condition.lt: {
              if (
                !(
                  typeof itemValue === 'number' &&
                  itemValue < parseInt(paramValue)
                )
              ) {
                return false
              }
              break
            }
            // item_lte=value
            case Condition.lte: {
              if (
                !(
                  typeof itemValue === 'number' &&
                  itemValue <= parseInt(paramValue)
                )
              ) {
                return false
              }
              break
            }
            // item_ne=value
            case Condition.ne: {
              switch (typeof itemValue) {
                case 'number':
                  return itemValue !== parseInt(paramValue)
                case 'string':
                  return itemValue !== paramValue
                case 'boolean':
                  return itemValue !== (paramValue === 'true')
              }
              break
            }
            // item=value
            case Condition.default: {
              switch (typeof itemValue) {
                case 'number':
                  return itemValue === parseInt(paramValue)
                case 'string':
                  return itemValue === paramValue
                case 'boolean':
                  return itemValue === (paramValue === 'true')
              }
            }
          }
        }
        return true
      })
    }

    // Sort
    const sort = query._sort || ''
    const sorted = sortOn(filtered, sort.split(','))

    // Slice
    const start = query._start
    const end = query._end
    const limit = query._limit
    if (start !== undefined) {
      if (end !== undefined) {
        return sorted.slice(start, end)
      }
      return sorted.slice(start, start + (limit || 0))
    }
    if (limit !== undefined) {
      return sorted.slice(0, limit)
    }

    // Paginate
    let page = query._page
    const perPage = query._per_page || 10
    if (page) {
      const items = sorted.length
      const pages = Math.ceil(items / perPage)

      // Ensure page is within the valid range
      page = Math.max(1, Math.min(page, pages))

      const first = 1
      const prev = page > 1 ? page - 1 : null
      const next = page < pages ? page + 1 : null
      const last = pages

      const start = (page - 1) * perPage
      const end = start + perPage
      const data = sorted.slice(start, end)

      return {
        first,
        prev,
        next,
        last,
        pages,
        items,
        data,
      }
    }

    return sorted.slice(start, end)
  }

  async create(
    name: string,
    data: Omit<Item, 'id'> = {},
  ): Promise<Item | undefined> {
    const items = this.#get(name)
    if (items === undefined || !Array.isArray(items)) return

    const item = { id: randomId(), ...data }
    items.push(item)

    await this.#db.write()
    return item
  }

  async #updateOrPatch(
    name: string,
    body: Item = {},
    isPatch: boolean,
  ): Promise<Item | undefined> {
    const item = this.#get(name)
    if (item === undefined || Array.isArray(item)) return

    const nextItem = (this.#db.data[name] = isPatch ? { item, ...body } : body)

    await this.#db.write()
    return nextItem
  }

  async #updateOrPatchById(
    name: string,
    id: string,
    body: Item = {},
    isPatch: boolean,
  ): Promise<Item | undefined> {
    const items = this.#get(name)
    if (items === undefined || !Array.isArray(items)) return

    const item = items.find((item) => item['id'] === id)
    if (!item) return

    const nextItem = isPatch ? { ...item, ...body, id } : { ...body, id }
    const index = items.indexOf(item)
    items.splice(index, 1, nextItem)

    await this.#db.write()
    return nextItem
  }

  async update(name: string, body: Item = {}): Promise<Item | undefined> {
    return this.#updateOrPatch(name, body, false)
  }

  async patch(name: string, body: Item = {}): Promise<Item | undefined> {
    return this.#updateOrPatch(name, body, true)
  }

  async updateById(
    name: string,
    id: string,
    body: Item = {},
  ): Promise<Item | undefined> {
    return this.#updateOrPatchById(name, id, body, false)
  }

  async patchById(
    name: string,
    id: string,
    body: Item = {},
  ): Promise<Item | undefined> {
    return this.#updateOrPatchById(name, id, body, true)
  }

  async destroyById(
    name: string,
    id: string,
    dependent?: string | string[],
  ): Promise<Item | undefined> {
    const items = this.#get(name)
    if (items === undefined || !Array.isArray(items)) return

    const item = items.find((item) => item['id'] === id)
    if (item === undefined) return
    const index = items.indexOf(item)
    items.splice(index, 1)

    nullifyForeignKey(this.#db, name, id)
    const dependents = ensureArray(dependent)
    deleteDependents(this.#db, name, dependents)

    await this.#db.write()
    return item
  }
}
````

## File: package.json
````json
{
  "name": "json-server",
  "version": "1.0.0-beta.3",
  "description": "",
  "type": "module",
  "bin": {
    "json-server": "lib/bin.js"
  },
  "types": "lib",
  "files": [
    "lib",
    "views"
  ],
  "engines": {
    "node": ">=18.3"
  },
  "scripts": {
    "dev": "tsx watch src/bin.ts fixtures/db.json",
    "build": "rm -rf lib && tsc",
    "test": "node --import tsx/esm --test src/*.test.ts",
    "lint": "eslint src",
    "prepare": "husky",
    "prepublishOnly": "npm run build"
  },
  "keywords": [],
  "author": "typicode <<EMAIL>>",
  "license": "SEE LICENSE IN ./LICENSE",
  "repository": {
    "type": "git",
    "url": "git+https://github.com/typicode/json-server.git"
  },
  "devDependencies": {
    "@eslint/js": "^9.11.0",
    "@sindresorhus/tsconfig": "^6.0.0",
    "@tailwindcss/typography": "^0.5.15",
    "@types/node": "^22.5.5",
    "concurrently": "^9.0.1",
    "eslint": "^9.11.0",
    "get-port": "^7.1.0",
    "globals": "^15.9.0",
    "husky": "^9.1.6",
    "tempy": "^3.1.0",
    "tsx": "^4.19.1",
    "type-fest": "^4.26.1",
    "typescript": "^5.6.2",
    "typescript-eslint": "^8.6.0"
  },
  "dependencies": {
    "@tinyhttp/app": "^2.4.0",
    "@tinyhttp/cors": "^2.0.1",
    "@tinyhttp/logger": "^2.0.0",
    "chalk": "^5.3.0",
    "chokidar": "^4.0.1",
    "dot-prop": "^9.0.0",
    "eta": "^3.5.0",
    "inflection": "^3.0.0",
    "json5": "^2.2.3",
    "lowdb": "^7.0.1",
    "milliparsec": "^4.0.0",
    "sirv": "^2.0.4",
    "sort-on": "^6.1.0"
  }
}
````

## File: README.md
````markdown
# json-server

[![Node.js CI](https://github.com/typicode/json-server/actions/workflows/node.js.yml/badge.svg)](https://github.com/typicode/json-server/actions/workflows/node.js.yml)

> [!IMPORTANT]
> Viewing beta v1 documentation – usable but expect breaking changes. For stable version, see [here](https://github.com/typicode/json-server/tree/v0)

👋 _Hey! Using React, Vue or Astro? Check my new project [MistCSS](https://github.com/typicode/mistcss) to write 50% less code._

## Install

```shell
npm install json-server
```

## Usage

Create a `db.json` or `db.json5` file

```json
{
  "posts": [
    { "id": "1", "title": "a title", "views": 100 },
    { "id": "2", "title": "another title", "views": 200 }
  ],
  "comments": [
    { "id": "1", "text": "a comment about post 1", "postId": "1" },
    { "id": "2", "text": "another comment about post 1", "postId": "1" }
  ],
  "profile": {
    "name": "typicode"
  }
}
```

<details>

<summary>View db.json5 example</summary>

```json5
{
  posts: [
    { id: '1', title: 'a title', views: 100 },
    { id: '2', title: 'another title', views: 200 },
  ],
  comments: [
    { id: '1', text: 'a comment about post 1', postId: '1' },
    { id: '2', text: 'another comment about post 1', postId: '1' },
  ],
  profile: {
    name: 'typicode',
  },
}
```

You can read more about JSON5 format [here](https://github.com/json5/json5).

</details>

Pass it to JSON Server CLI

```shell
$ npx json-server db.json
```

Get a REST API

```shell
$ curl http://localhost:3000/posts/1
{
  "id": "1",
  "title": "a title",
  "views": 100
}
```

Run `json-server --help` for a list of options

## Sponsors ✨

| Sponsors |
| :---: |
| <a href="https://mockend.com/" target="_blank"><img src="https://jsonplaceholder.typicode.com/mockend.svg" height="100px"></a> |
| <a href="https://zuplo.link/json-server-gh"><img src="https://github.com/user-attachments/assets/adfee31f-a8b6-4684-9a9b-af4f03ac5b75" height="100px"></a> |

| Sponsors |
| :---: |
| <a href="https://konghq.com/products/kong-konnect?utm_medium=referral&utm_source=github&utm_campaign=platform&utm_content=json-server"><img src="https://github.com/typicode/json-server/assets/5502029/e8d8ecb2-3c45-4f60-92d0-a060b820fa7f" height="75px"></a> |

| Sponsors | |
| :---: | :---: |
| <a href="https://www.storyblok.com/" target="_blank"><img src="https://github.com/typicode/json-server/assets/5502029/c6b10674-4ada-4616-91b8-59d30046b45a" height="35px"></a> | <a href="https://betterstack.com/" target="_blank"><img src="https://github.com/typicode/json-server/assets/5502029/44679f8f-9671-470d-b77e-26d90b90cbdc" height="35px"></a> |
| <a href="https://route4me.com"><img src="https://github.com/user-attachments/assets/4eab0bac-119e-4b27-8183-8b136190b776" height="35px" alt="Delivery Routing Software and Route Optimization Software"></a> | <a href="https://www.speechanddebate.org"><img src="https://github.com/user-attachments/assets/cc7980e4-2147-4499-8de4-4d0c265d0c07" height="35px"></a> |


[Become a sponsor and have your company logo here](https://github.com/users/typicode/sponsorship)

## Sponsorware

> [!NOTE]
> This project uses the [Fair Source License](https://fair.io/). Only organizations with 3+ users are kindly asked to contribute a small amount through sponsorship [sponsor](https://github.com/sponsors/typicode) for usage. __This license helps keep the project sustainable and healthy, benefiting everyone.__
>
> For more information, FAQs, and the rationale behind this, visit [https://fair.io/](https://fair.io/).

## Routes

Based on the example `db.json`, you'll get the following routes:

```
GET    /posts
GET    /posts/:id
POST   /posts
PUT    /posts/:id
PATCH  /posts/:id
DELETE /posts/:id

# Same for comments
```

```
GET   /profile
PUT   /profile
PATCH /profile
```

## Params

### Conditions

- ` ` → `==`
- `lt` → `<`
- `lte` → `<=`
- `gt` → `>`
- `gte` → `>=`
- `ne` → `!=`

```
GET /posts?views_gt=9000
```

### Range

- `start`
- `end`
- `limit`

```
GET /posts?_start=10&_end=20
GET /posts?_start=10&_limit=10
```

### Paginate

- `page`
- `per_page` (default = 10)

```
GET /posts?_page=1&_per_page=25
```

### Sort

- `_sort=f1,f2`

```
GET /posts?_sort=id,-views
```

### Nested and array fields

- `x.y.z...`
- `x.y.z[i]...`

```
GET /foo?a.b=bar
GET /foo?x.y_lt=100
GET /foo?arr[0]=bar
```

### Embed

```
GET /posts?_embed=comments
GET /comments?_embed=post
```

## Delete

```
DELETE /posts/1
DELETE /posts/1?_dependent=comments
```

## Serving static files

If you create a `./public` directory, JSON Server will serve its content in addition to the REST API.

You can also add custom directories using `-s/--static` option.

```sh
json-server -s ./static
json-server -s ./static -s ./node_modules
```

## Notable differences with v0.17

- `id` is always a string and will be generated for you if missing
- use `_per_page` with `_page` instead of `_limit`for pagination
- use Chrome's `Network tab > throtling` to delay requests instead of `--delay` CLI option
````
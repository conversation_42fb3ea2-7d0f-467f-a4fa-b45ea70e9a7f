[{"id": "Q1", "text": "该项目的主要用途是什么？"}, {"id": "Q2", "text": "该项目支持哪些主要 REST 操作？"}, {"id": "Q3", "text": "项目默认使用哪个端口？是否可以自定义？"}, {"id": "Q4", "text": "GET /posts?_embed=comments 的作用是什么？"}, {"id": "Q5", "text": "该项目如何支持静态文件托管？"}, {"id": "Q6", "text": "项目中如何确保所有数据条目具有字符串形式的 id？"}, {"id": "Q7", "text": "Service 类中的条件查询是如何实现的？支持哪些操作符？"}, {"id": "Q8", "text": "项目如何进行测试？是否有自动化覆盖？"}, {"id": "Q9", "text": "src/app.test.ts 中模拟了哪些类型的路由访问？"}, {"id": "Q10", "text": "最近一次 commit 是什么时候？做了什么改动？"}, {"id": "Q11", "text": "项目从 v1.0.0-beta.2 升级到 beta.3 的核心变更有哪些？"}, {"id": "Q12", "text": "该项目使用的 License 是什么？对组织和个人用户有什么限制？"}, {"id": "Q13", "text": "Logs not logging in terminal;npx json-server --watch data/db.json --port 3500\n$ npx json-server --watch data/db.json --port 3500\n--watch/-w can be omitted, JSON Server 1+ watches for file changes by default\nJSON Server started on PORT :3500\nPress CTRL-C to stop\nWatching data/db.json...\n\n(˶ᵔ ᵕ ᵔ˶)\n\nIndex:\nhttp://localhost:3500/\n\nStatic files:\nServing ./public directory if it exists\n\nEndpoints:\nhttp://localhost:3500/bruh\nhttp://localhost:3500/items\nExpect printed logs when URL is accessed or when update request is made, but terminal is silent.\n\nDid not install npm -i json-server, using json-server out of box via npx."}, {"id": "Q14", "text": "query parameter does not working i try many times this \"http://localhost:3000/Products?q=mobile\" but this not working so anyone pls help and my db.json file this \"Products\": [ { \"productName\": \"Boat \", \"price\": \"2999\", \"color\": \"black\", \"category\": \"watch\", \"description\": \"good watch for personal use\", \"productUrl\": \"https://images-na.ssl-images-amazon.com/images/I/418liDrUObL.SX320_QL100_AC_SCLZZZZZZZ.jpg\", \"id\": \"4169\" }, { \"id\": \"be25\", \"productName\": \"iphone 15\", \"price\": \"79999\", \"color\": \"white\", \"category\": \"mobile\", \"description\": \"good smart mobile\", \"productUrl\": \"https://m.media-amazon.com/images/W/MEDIAX_849526-T2/images/I/71PjpS59XLL.AC_SR360,240_QL70.jpg\" }, { \"productName\": \"samsung galaxy\", \"price\": \"2000\", \"color\": \"green\", \"category\": \"mobile\", \"description\": \"good mobile phone\", \"productUrl\": \"https://m.media-amazon.com/images/W/MEDIAX_849526-T2/images/I/818VqDSKpCL.AC_UY436_FMwebp_QL65.jpg\", \"id\": \"fe0b\" } ]"}, {"id": "Q15", "text": "cant find package json-server within node_modules. PNPM and NPM are reporting that it cannot find json-server from import statement within calling server.js file. server.js file: import jsonServer from 'json-server'; console.log('Starting JSON Server'); package.json file: { \"name\": \"crazy\", \"version\": \"1.0.0\", \"description\": \"\", \"main\": \"index.js\", \"type\": \"module\", \"scripts\": { \"test\": \"echo \\\"Error: no test specified\\\" && exit 1\", \"start\": \"node server.js\" }, \"author\": \"\", \"license\": \"ISC\", \"devDependencies\": { \"json-server\": \"^1.0.0-beta.1\" } }. The error: ajackman@MarsVenus:~/projects/ep/crazy$ npm run start crazy@1.0.0 start node server.js node:internal/modules/esm/resolve:210 const resolvedOption = FSLegacyMainResolve(packageJsonUrlString, packageConfig.main, baseStringified); ^ Error: Cannot find package '/home/<USER>/projects/ep/crazy/node_modules/json-server/package.json' imported from /home/<USER>/projects/ep/crazy/server.js at legacyMainResolve (node:internal/modules/esm/resolve:210:26) at packageResolve (node:internal/modules/esm/resolve:828:14) at moduleResolve (node:internal/modules/esm/resolve:914:18) at defaultResolve (node:internal/modules/esm/resolve:1119:11) at ModuleLoader.defaultResolve (node:internal/modules/esm/loader:542:12) at ModuleLoader.resolve (node:internal/modules/esm/loader:511:25) at ModuleLoader.getModuleJob (node:internal/modules/esm/loader:241:38) at ModuleJob._link (node:internal/modules/esm/module_job:126:49) { code: 'ERR_MODULE_NOT_FOUND' }. Node.js v22.2.0, npm version 9.5.1, pnpm version 9.1.0, node version 22.2.0, WSL2 / Debian 11 (Guest) / Windows 11 (host)"}]
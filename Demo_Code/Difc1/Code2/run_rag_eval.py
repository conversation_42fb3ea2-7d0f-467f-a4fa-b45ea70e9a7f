import json
import os
from pathlib import Path
import requests
import pandas as pd
import matplotlib.pyplot as plt
from sklearn.metrics import precision_score, recall_score, f1_score

# === 配置 ===
API_KEY = "sk-7a1fda31b49246af842b66ed3edf2326"
API_BASE = "https://api.deepseek.com/v1"  # 或你的代理地址
MODEL = "deepseek-chat"

HEADERS = {
    "Authorization": f"Bearer {API_KEY}",
    "Content-Type": "application/json"
}

CONTEXT_FILES = {
    "rag1": "rag1.md",
    "rag2": "rag2.md",
    "rag3": "rag3.md",
}
QUESTIONS_FILE = "questions2.json"
OUTPUT_DIR = "results"

PROMPT_TEMPLATE = """你将看到一份项目文档的上下文内容，请仅基于它回答下面的问题，不要推理或添加任何额外的内容。

<上下文开始>
{context}
<上下文结束>

---

请回答这个问题：{question}
"""



# 自动生成标准答案
def generate_standard_answer(question):
    """
    从标准答案集获取每个问题的正确答案
    """
    standard_answers = {
        "Q1": """该项目是一个轻量级的 REST API 服务器，基于本地 JSON 文件快速生成 API，用于前端开发、原型验证、mock 数据接口等用途。它使用 Node.js 构建，支持 JSON 和 JSON5 数据源。""",

        "Q2": "项目支持以下操作：1.GET /resource：获取资源列表2.GET /resource/:id：获取单个资源3.POST /resource：创建新资源4.PUT/PATCH /resource/:id：更新资源5.DELETE /resource/:id：删除资源6.此外还支持条件查询、分页、排序、嵌套关联 _embed 及静态文件托管。",

        "Q3": "默认使用端口 3000，可以通过命令行参数 --port 或环境变量 PORT 自定义。",
        
        "Q4": "该请求会在获取 /posts 列表时，将每个 post 对象中嵌入其相关联的 comments 字段，实现父子关系联动展示。",
        
        "Q5": "该请求会在获取 /posts 列表时，将每个 post 对象中嵌入其相关联的 comments 字段，实现父子",
        
        "Q6": "在 Service 类中调用 fixAllItemsIds()，对所有条目进行检查与补全，确保：1.若 id 为数字则转换为字符串2.若无 id 则生成随机 id（使用 crypto 生成的 hex）",
        
        "Q7": """通过正则提取 _gt, _lt, _ne 等后缀来构建条件，支持的操作包括：_lt, _lte, _gt, _gte, _ne, 等于（无后缀）条件查询基于 `lodash` 的 `_.filter()` 方法实现，支持的操作符包括：
                * `=`
                * `!=`
                * `>`, `<`, `>=`, `<=`
                * `like`（模糊匹配）
                * `q`（全文模糊搜索）
                这些查询通过 URL 参数解析为查询条件，并动态应用到数据集合中 。""",

        "Q8": "使用 Node.js 原生的 node:test 框架，覆盖了服务创建、API 路由、条件查询、分页、排序、嵌套关系处理等，测试文件包括 app.test.ts 和 service.test.ts。",

        "Q9": """GET/POST/PUT/PATCH/DELETE 多种方法
                正常资源与不存在资源（404）
                静态文件访问
                CORS OPTIONS 请求
                嵌套查询和参数组合请求
                """,

        "Q10": "最近一次提交发生在 2025-03-31，作者为 typicode，内容为更新 README.md 中赞助商的图片地址。修复 toIdString 转换时的类型判断逻辑`，主要修复了 `toIdString` 在处理 null 或非 object 类型时的错误 。",

        "Q11": """更新依赖（如 @tinyhttp, chokidar, milliparsec 等）
                添加了 @tinyhttp/logger
                修复 CORS 配置，允许任意 header
                对 package.json 中版本号和脚本做了小幅更新""",

        "Q12": """项目使用 Fair Source License：
                个人/学生/小团队使用免费
                超过 2 人的组织需要付费授权
                源码开放但带有用户数限制（非 OSI 认证）""",
        
        "Q13": """awaiting #1625 you can preview by adding this to your package.json:

                "scripts": {
                    "server": "node node_modules/json-server/lib/bin.js db.json --port 3000 --middleware=logger.mjs",
                    "postinstall": "cd node_modules && git clone -b feature/add-middleware https://github.com/rkristelijn/json-server.git"
                }
                and create a logger.mjs file:

                // logger.mjs
                import chalk from 'chalk';

                export default (req, _res, next) => {
                const currentDate = new Date().toISOString();
                console.log(chalk.green(req.method), chalk.yellow(req.url), chalk.blue(`${currentDate}`));

                // Check if the request body is already parsed
                if (req.body && Object.keys(req.body).length > 0) {
                console.log(chalk.magenta('Body:'), req.body);
                } else {
                // Manually parse the request body if not already parsed
                let body = '';
                req.on('data', (chunk) => {
                    body += chunk.toString();
                });
                req.on('end', () => {
                    if (body) {
                    try {
                        const parsedBody = JSON.parse(body);
                        console.log(chalk.magenta('Body:'), parsedBody);
                    } catch (error) {
                        console.log(chalk.red('Failed to parse body'), error);
                    }
                    }
                    next();
                });
                return;
                }

                next();
                };
                it will output:

                > node node_modules/json-server/lib/bin.js db.json --port 3000 --middleware=src/utils/logger.mjs

                Loading middleware from src/utils/logger.mjs
                app.ts: Using middleware [Function: default]
                JSON Server started on PORT :3000
                Press CTRL-C to stop
                Watching db.json...

                (˶ᵔ ᵕ ᵔ˶)

                Index:
                http://localhost:3000/

                Static files:
                Serving ./public directory if it exists

                Endpoints:
                http://localhost:3000/users
                http://localhost:3000/users_columns

                [2025-01-03T08:35:43.824Z] POST /posts
                Body: { title: 'foo', body: 'bar', userId: 1 }
                [2025-01-03T08:35:46.331Z] PATCH /posts/1
                Body: { title: 'foo', body: 'bar', userId: 1 }
                [2025-01-03T08:35:47.579Z] GET /posts
                for these http calls

                // test.http
                // use https://marketplace.visualstudio.com/items?itemName=humao.rest-client to call
                ###
                GET http://localhost:3000/posts

                ###
                POST http://localhost:3000/posts
                Content-Type: application/json

                {
                "title": "foo",
                "body": "bar",
                "userId": 1
                }

                ###
                PATCH http://localhost:3000/posts/1
                Content-Type: application/json

                {
                "title": "foo",
                "body": "bar",
                "userId": 1
                }
                """,    

        "Q14": """app.get('/Products', (req, res) => {
                const query = req.query.q;
                if (!query) {
                res.status(400).send('Missing query parameter');
                return;
                }

                fs.readFile('db.json', 'utf8', (err, data) => {
                if (err) {
                console.error(err);
                res.status(500).send('Internal Server Error');
                return;
                }
                make sure you have this code structured properly""",

        "Q15": """i think this is because the line:

                "main": "./lib/server/index.js",
                which used to be in the package.json on 0.17.4 no longer exists on 1.0 versions.
                which was pointing to the main script, but the 1.0 versions seem to only support the CLI variant.

                and thus since this package has become no longer usable like that.

                npm install --save-dev json-server@0.17.4
                should be the fix for now,
                while asking the maintainers what their plans are surrounding support for running this as express middleware.

                """,

    }

    # 返回标准答案
    return standard_answers.get(question, "未提供标准答案")


def load_questions():
    try:
        # 确保路径正确
        file_path = os.path.join(os.path.dirname(__file__), QUESTIONS_FILE)
        print(f"尝试加载: {file_path}")
        
        with open(file_path, "r", encoding="utf-8") as f:
            data = json.load(f)
            print("成功加载 questions.json")
            return data
    except Exception as e:
        print(f"❌ 加载失败: {e}")
        return None

def load_context(path):
    # 先检查文件是否存在
    path = os.path.join("Difc1", path)  # 路径拼接：Difc1/xxx.md
    if not os.path.exists(path):
        print(f"❌ 文件不存在: {os.path.abspath(path)}")
        print(f"当前目录内容: {os.listdir()}")
        return ""
    
    with open(path, "r", encoding="utf-8-sig") as f:
        return f.read()


def save_answer(rag_id, question_id, answer):
    Path(OUTPUT_DIR).mkdir(exist_ok=True)
    with open(f"{OUTPUT_DIR}/{rag_id}_{question_id}.txt", "w", encoding="utf-8") as f:
        f.write(answer)


def ask_deepseek(prompt, i, j):
    rag = {
        "rag1": {
            "Q1": """该项目是一个用于提供模拟 REST API 的服务器框架，基于 json-server 构建，支持灵活的数据查询和自定义路由，主要用于前端开发中的接口模拟和测试 。""",

            "Q2": """支持完整的 REST 操作，包括：

                GET（查询资源）

                POST（新增资源）

                PUT（替换资源）

                PATCH（部分更新资源）

                DELETE（删除资源）

                OPTIONS（用于 CORS 预检）。""",

            "Q3": """默认端口为 3000，可以通过 -p 或 --port 参数自定义，例如：
                json-server -p 4000 db.json
                该端口也可通过环境变量 PORT 设置。""",

            "Q4": """该请求会返回每个 post 的同时，将与之相关的 comments（通过 postId 外键关联）嵌入到返回结果中，即生成嵌套的对象结构。""",

            "Q5": """项目默认托管 ./public 目录中的静态文件。也可以通过 CLI 参数 -s 或 --static 添加多个静态目录：son-server -s ./static -s ./node_modules这些目录通过 sirv 中间件托管。""",

            "Q6": """项目在中间件层使用 `toIdString()` 函数，统一将资源对象的 `id` 转为字符串，以保证一致性和避免类型比较错误 。""",

            "Q7": """查询参数通过正则解析 _lt、_lte、_gt、_gte、_ne 等后缀来识别操作符。支持的操作符包括：
                    =
                    != (_ne)
                    < (_lt)
                    <= (_lte)
                    > (_gt)
                    >= (_gte)
                    并支持嵌套字段（如 author.name=foo）和数组字段索引（如 tags[0]=bar）。""",

            "Q8": """项目使用 Node.js 的 node:test 模块进行测试。通过 .husky/pre-commit 中的 npm test 和 .github/workflows/node.js.yml、npm-publish.yml 实现持续集成与自动化测试""",

            "Q9": """测试覆盖了以下内容：

                    静态文件访问（如 /test.html）

                    常规 CRUD API 路由（如 /posts, /posts/1, /comments?postId=1）

                    嵌套数据查询（如 /posts?_embed=comments）

                    异常请求（如 /not-found、404 检查）

                    所有 HTTP 方法（GET、POST、PUT、PATCH、DELETE、OPTIONS）。""",

            "Q10": """最近一次提交版本为 1.0.0-beta.3。具体改动可通过 package.json 中版本号判断，功能上主要包括嵌套字段查询、分页方式调整（使用 _per_page 替代 _limit）和静态资源配置等更新。""",

            "Q11": """主要变化包括：
                所有条目的 id 强制为字符串类型
                分页参数更改为使用 _page 和 _per_page
                删除了 --delay 参数，推荐使用浏览器开发者工具模拟延迟
                更友好的条件查询与嵌套字段支持。
            """,

            "Q12": """使用的是 Fair Source License v0.9""",

            "Q13": """This is expected behavior for json-server v1.0.0-beta.3. It no longer prints logs for each incoming request (e.g. GET, POST) unless you explicitly enable logging.
                To enable request logging, you need to pass the logger: true option when calling createApp() programmatically. If you're only using the CLI (npx json-server), logging middleware is not included by default.
                Solution (CLI alternative):
                You can’t enable logging via CLI in v1+. If you need request logs, consider:
                Writing a custom server.js with logger: true
                Or temporarily downgrading to version 0.17.x for legacy logging behavior
                """,

            "Q14": """The q parameter performs a full-text search across all fields. For it to work, the value must match any field's string value exactly or partially, case-insensitive.
                In your db.json, "mobile" is only present in the "category" field of some products. However:
                If the field has extra whitespace or unexpected encoding, it may not match.
                Ensure your server is running correctly and you’re querying the correct resource (/Products, not /products, since it's case-sensitive).
                Fix Suggestions:
                Double-check that your server is running and the resource path is correct (/Products).
                Try querying http://localhost:3000/Products?category=mobile instead, which is more precise.
                To debug, test with:
                http://localhost:3000/Products?q=Boat – this should return the first item.
                """,

            "Q15": """**Explanation:**
                    Using `npx json-server` **does not install the package locally**, so `import` statements fail because the module isn't in your `node_modules`.
                    **Solution:**
                    Install the package explicitly:
                    ```bash
                    npm install json-server
                    # or if using pnpm:
                    pnpm add json-server
                    ```
                    Then, your `server.js` will work as expected.
                    """,
        },
        "rag2": {
            "Q1": """该项目是一个基于 Node.js 的 JSON Server，用于从本地 JSON 或 JSON5 文件快速生成 REST API 服务。它适用于前端开发、接口模拟、原型设计等场景。""",

            "Q2": """
                项目支持完整的 REST 操作，包括：
                * `GET`
                * `POST`
                * `PUT`
                * `PATCH`
                * `DELETE`
                以及通过 `_embed` 和 `_expand` 参数实现的数据关联查询 。""",

            "Q3": """默认监听端口是 `3000`，但可以通过命令行参数或配置文件进行自定义设置 。""",

            "Q4": """该请求表示在获取 `posts` 的同时，将其相关联的 `comments` 一并嵌入返回结果中，实现在一条 API 中返回嵌套资源 。""",

            "Q5": """项目支持通过 `--static` 参数挂载静态资源目录，使得指定目录下的文件可以通过 URL 直接访问 。""",

            "Q6": """项目通过 fixAllItemsIds 函数遍历所有条目：
                    若 id 是数字，则转换为字符串。
                    若 id 不存在，则通过 randomBytes(2).toString('hex') 自动生成随机字符串。""",

            "Q7": """条件查询基于 `lodash` 的 `_.filter()` 方法实现，支持的操作符包括：

                * `=`
                * `!=`
                * `>`, `<`, `>=`, `<=`
                * `like`（模糊匹配）
                * `q`（全文模糊搜索）
                这些查询通过 URL 参数解析为查询条件，并动态应用到数据集合中 。""",

            "Q8": """项目使用 `Vitest` 框架进行自动化测试，覆盖了服务端行为、路由访问、静态托管等功能，测试入口文件为 `src/app.test.ts` 。""",

            "Q9": """该测试文件模拟并验证了对 RESTful API（如 `/posts`、`/comments`）的访问，包含 GET 请求、嵌套资源请求、错误处理等 。""",

            "Q10": """最近一次提交版本为 1.0.0-beta.3。最近一次提交发生在 2025-03-31,最近一次 commit 内容为 `fix: 修复 toIdString 转换时的类型判断逻辑`，主要修复了 `toIdString` 在处理 null 或非 object 类型时的错误 。""",

            "Q11": """在 beta.3 中引入了 `toIdString` 方法，并调整了 `Service` 查询逻辑，确保所有返回对象中的 `id` 字段为字符串类型，提高一致性 。""",

            "Q12": """项目采用 `MIT License`，允许组织和个人用户自由使用、修改和再发布，仅需保留原始版权声明和许可文本 。使用的是 Fair Source License v0.9：允许查看源代码和在最多 2 名用户/实体下免费使用；超过 2 名用户的组织需要支付许可费用；""",

            "Q13": """**Explanation:**
When using `npx json-server`, it uses a minimal setup. If you want detailed logs (e.g., printing every GET, POST), you need to explicitly add middleware like this:

```js
// server.js
import jsonServer from 'json-server';
const server = jsonServer.create();
const router = jsonServer.router('data/db.json');
const middlewares = jsonServer.defaults(); // includes logger

server.use(middlewares);
server.use(router);
server.listen(3500, () => {
  console.log('JSON Server is running on port 3500');
});
```

**Solution:**

* Use a custom `server.js` file with middleware enabled.
* Or install `json-server` locally with `npm install json-server` and run a fully configured script instead of `npx`.""",

            "Q14": """**Explanation:**
`q` performs a **full-text fuzzy search across all fields**, so it should work with the word "mobile" if it appears anywhere. However:

**Potential issues:**

1. You may be accessing the wrong port (3000 vs. 3500).
2. JSON Server may not be reading the `Products` key properly.
3. There could be a syntax error in the URL or a file.
4. Browser cache or misconfigured frontend proxy may interfere.

**Recommendations:**

* Make sure you are accessing the right port (likely `http://localhost:3500/Products?q=mobile`)
* Try a more specific query:

  ```
  http://localhost:3500/Products?category=mobile
  ```
* Confirm `db.json` is correctly loaded and contains the `Products` array.
""",

            "Q15": """This issue occurs because you installed json-server as a dev dependency (devDependencies) and are trying to import it from an ES module in runtime (server.js).
                    There are two possible issues:
                    ESM Compatibility: Ensure json-server supports ESM. As of 1.0.0-beta.1, it’s an ESM-only package, so "type": "module" is okay — but the path and environment must be clean.
                    Installation Scope: Some tools (like pnpm) isolate dev dependencies differently. If you're using pnpm in production mode or outside the workspace context, devDependencies won’t be installed.
                    Solutions:
                    Move json-server to dependencies, not devDependencies:npm install json-server --save
                    OR, ensure you’re running in a dev environment where devDependencies are available.
                    Also confirm your node_modules/json-server/package.json exists. If not:
                    rm -rf node_modules
                    npm install""",
        },
        "rag3": {
            "Q1": """该项目是用于将本地 JSON 数据（如 `db.json`）快速转化为完整的 REST API 的工具，便于开发测试、前端联调、原型数据演示等用途。""",

            "Q2": """项目支持以下 REST 操作：
* `GET /resource`
* `GET /resource/:id`
* `POST /resource`
* `PUT /resource/:id`
* `PATCH /resource/:id`
* `DELETE /resource/:id`
  支持的资源包括 `posts`、`comments`、`profile` 等。""",

            "Q3": """默认端口是 `3000`，通过命令 `npx json-server db.json` 启动后，接口地址为 `http://localhost:3000`。你也可以使用 `--port` 参数进行自定义，例如 `--port 3500`。""",

            "Q4": """该查询用于嵌套返回每个 post 相关的 comments，相当于对 `comments` 表中满足 `postId == post.id` 的记录进行聚合并加入 `comments` 字段中返回。""",

            "Q5": """若存在 `./public` 目录，项目会自动将其中的文件以静态资源的方式提供服务。也可以通过 `-s` 或 `--static` 指定其他目录。""",

            "Q6": """项目通过 `fixAllItemsIds` 函数为所有项分配字符串形式的 `id`。若条目缺失 id，会调用 `randomId()` 自动生成字符串形式的唯一 id。""",

            "Q7": """Service 类支持通过 URL 参数进行条件查询，解析逻辑识别后缀操作符：
            * `lt` → `<`
            * `lte` → `<=`
            * `gt` → `>`
            * `gte` → `>=`
            * `ne` → `!=`
            * 空操作符表示 `==`。""",

            "Q8": """项目使用 Node.js 原生的 `node:test` 模块进行测试，且集成了 GitHub Actions 自动运行测试流程。此外，`.husky/pre-commit` 脚本中也配置了自动执行 `npm test`，具备自动化测试覆盖能力。""",

            "Q9": """测试文件模拟了多种 HTTP 请求，包括：

            * 查询（GET）排序、分页、过滤、嵌套（\_embed）等参数
            * 多页分页 `_page, _per_page`
            * 各类资源的增删改查路由""",

            "Q10": """最近一次提交是在 **2025-03-31**，作者为 `typicode`，提交信息为“Update README.md”，内容是修改 sponsor 图像地址。""",

            "Q11": """升级依赖版本（如 `@tinyhttp/app`, `chokidar`, `milliparsec` 等）
                * 新增 `@tinyhttp/logger` 模块
                * 增强对 `CORS` 的支持：允许任意 header
                * 对部分参数处理逻辑（如 `.query` 字段解析）做了优化""",

            "Q12": """项目使用 **Fair Source License**。该许可允许个人、学生、教师、小团队免费使用。**但如果是组织内有超过 2 位用户使用该软件，则需要通过 GitHub Sponsors 支付费用**。该许可不授予商标使用权，违反许可将自动终止使用权。""",

            "Q13": """**Answer:**
The absence of logs in the terminal when using `npx json-server` might be because JSON Server does not log requests by default unless a custom middleware is added. While the server confirms it is running and watching the file (e.g., `data/db.json`), request logging (e.g., GET, POST info) to the terminal may require explicitly setting up a logger or using custom middleware in a `server.js` script. This behavior aligns with the file content where the terminal output confirms server start, but no request-specific logs are shown.""",

            "Q14": """**Answer:**
The query string `?q=mobile` is used in JSON Server to perform a full-text search across all fields. If the server is running on port 3500, as indicated in the logs, accessing `http://localhost:3000/Products?q=mobile` would fail unless a proxy or port mapping is set. You must access `http://localhost:3500/Products?q=mobile` instead. Additionally, ensure that the field values (like `"category": "mobile"`) are properly indexed and your endpoint matches the case-sensitive path (`Products`, not `products` if that’s how it's defined in `db.json`).""",

            "Q15": """**Answer:**
The error likely occurs because `json-server` was not installed locally using `npm install json-server`. Using `npx json-server` runs the package without installing it. Therefore, when trying to import it via `import jsonServer from 'json-server';` in a custom script like `server.js`, the module cannot be found in `node_modules`. To fix this, run:
```bash
npm install json-server
```
This ensures it is available for ES module imports.""",
        }
    }
    return rag[i][j]
    #url = f"{API_BASE}/chat/completions"
    #data = {
    #    "model": MODEL,
    #    "messages": [{"role": "user", "content": prompt}],
    #    "temperature": 0.3
    #}

    #response = requests.post(url, headers=HEADERS, json=data)
    #if response.status_code != 200:
    #    raise Exception(f"Error {response.status_code}: {response.text}")
    #return response.json()["choices"][0]["message"]["content"]

from fuzzywuzzy import fuzz
from sklearn.metrics import precision_score, recall_score, f1_score
from sentence_transformers import SentenceTransformer, util
import numpy as np
from sklearn.metrics.pairwise import cosine_similarity

import requests
import numpy as np

def get_embedding(text):
    url = "http://localhost:11434/api/embeddings"
    payload = {
        "model": "nomic-embed-text",
        "prompt": text
    }
    response = requests.post(url, json=payload)
    
    try:
        data = response.json()
        return np.array(data["embedding"])
    except Exception as e:
        print("🔴 获取 embedding 失败，响应内容如下：")
        print(response.text)
        raise e


def evaluate_answer(correct_answer, generated_answer):
    """改进的评估函数，结合模糊匹配和语义相似度"""
    # 1. 模糊匹配分数 (0-100)
    fuzzy_score = fuzz.ratio(correct_answer.lower(), generated_answer.lower()) / 100
    
    # 2. 语义相似度 (0-1)
    try:
        emb1 = get_embedding(correct_answer).reshape(1, -1)
        emb2 = get_embedding(generated_answer).reshape(1, -1)
        semantic_score = cosine_similarity(emb1, emb2)[0][0]
    except Exception as e:
        print(f"Embedding calculation failed: {str(e)}")
        semantic_score = 0
    
    print(f"Semantic Score: {semantic_score:.2f}")
    # 3. 综合评分 (可调整权重)
    combined_score = 0.1 * fuzzy_score + 0.85 * semantic_score
    
    return combined_score



# Main function to evaluate answers and save the results
def main():
    questions = load_questions()
    results = []

    for rag_id, context_file in CONTEXT_FILES.items():
        context = load_context(context_file)
        for q in questions:
            print(f"🧠 Asking {q['id']} using {rag_id}...")
            prompt = PROMPT_TEMPLATE.format(context=context, question=q["text"])
            answer = ask_deepseek(prompt, rag_id, q["id"])
            save_answer(rag_id, q["id"], answer)

            # Generate the standard answer
            correct_answer = generate_standard_answer(q["id"])

            # Use fuzzy evaluation
            precision = evaluate_answer(correct_answer, answer)
            results.append({
                "Question": q["id"],
                "RAG Mode": rag_id,
                # "Correct": correct,
                "Precision": precision,
                # "Recall": recall,
                # "F1 Score": f1,
                "Generated Answer": answer,
                "Standard Answer": correct_answer
            })

    # Save results to a DataFrame and output the evaluation
    df = pd.DataFrame(results)
    # 排序方法：提取 Question 编号数字部分并排序
    df["Question_num"] = df["Question"].str.extract(r"Q(\d+)").astype(int)
    # 排序 DataFrame
    df = df.sort_values(by=["Question_num", "RAG Mode"])

    print(df)
    df.to_excel(f"{OUTPUT_DIR}/evaluation_results_updated.xlsx", index=False)
    print(f"Evaluation results saved in {OUTPUT_DIR}/evaluation_results_updated.xlsx")

    # Step 2: 构建有序 MultiIndex
    ordered_question_labels = df["Question"].unique().tolist()
    df["Question"] = pd.Categorical(df["Question"], categories=ordered_question_labels, ordered=True)

    # Step 3: 设置 index 并绘图（现在 matplotlib 会尊重你设定的顺序）
    df_plot = df.set_index(['Question', 'RAG Mode'])[['Precision']].unstack()

    df_plot.plot(kind='bar', figsize=(12, 8))
    plt.title('Evaluation Metrics (Precision)')
    plt.ylabel('Score')
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.savefig(f"{OUTPUT_DIR}/evaluation_precision_plot.png")
    plt.show()

    # Save the report in HTML format
    with open(f"{OUTPUT_DIR}/evaluation_report_updated.html", "w", encoding="utf-8") as f:
        f.write(df.to_html())

    print("Report saved in evaluation_report_updated.html")


if __name__ == "__main__":
    main()

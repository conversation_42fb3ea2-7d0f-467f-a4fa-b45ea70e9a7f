import json
import os
from pathlib import Path
import requests
import pandas as pd
import matplotlib.pyplot as plt
from sklearn.metrics import precision_score, recall_score, f1_score

# === 配置 ===
API_KEY = "sk-7a1fda31b49246af842b66ed3edf2326"
API_BASE = "https://api.deepseek.com/v1"  # 或你的代理地址
MODEL = "deepseek-chat"

HEADERS = {
    "Authorization": f"Bearer {API_KEY}",
    "Content-Type": "application/json"
}

CONTEXT_FILES = {
    "rag1": "rag1.md",
    "rag2": "rag2.md",
    "rag3": "rag3.md",
}
QUESTIONS_FILE = "questions.json"
OUTPUT_DIR = "results"

PROMPT_TEMPLATE = """你将看到一份项目文档的上下文内容，请仅基于它回答下面的问题，不要推理或添加任何额外的内容。

<上下文开始>
{context}
<上下文结束>

---

请回答这个问题：{question}
"""



# 自动生成标准答案
def generate_standard_answer(question):
    """
    从标准答案集获取每个问题的正确答案
    """
    standard_answers = {
        "Q1": """Installing before publishing is a little tricky due to lerna.

        It may be solved by adding to package.json something like this:

        "dependencies": {
            "micro": "file:./micro/packages/micro/micro-v10.0.1.tgz"
        },
        "scripts": {
            "preinstall": "if [ ! -d ./micro ]; then git clone --depth 1 --branch patch-1 https://github.com/oklas/micro; (cd micro/packages/micro && yarn && yarn build && yarn pack) ; fi"
        }""",

        "Q2": "当使用 micro 启动服务时，如果入口文件没有正确导出 handler 函数，会触发 no-export 错误。错误文档在 errors/no-export.md 中有说明，提供了示例修复代码。这个错误是为帮助开发者明确入口函数缺失问题而设置的。",

        "Q3": "当请求体不是合法 JSON 时，json() 函数会抛出 400 错误，使用 createError(400, 'Invalid JSON') 创建错误对象。该机制在 parseJSON() 函数中定义，并带有 originalError。这是在后期版本中引入的，以提高鲁棒性。",
        
        "Q4": "项目中提供了基于 socket.io 的聊天应用 demo，位于 examples/socket.io-chat-app/ 目录下，包含 index.js, index.html, websocket-server.js 等文件。这部分是一个示例，而非核心功能，支持通过浏览器聊天室进行消息收发。",
        
        "Q5": "该文件用于解析 endpoint 字符串，支持 pipe:、unix: 和 tcp: 三种协议。设计目的是为了支持跨平台部署，例如 Windows 的 named pipe 和类 Unix 系统的 UNIX socket。此功能通过 switch-case 实现，明确区分协议处理逻辑。",
        
        "Q6": "在开发模式下，系统使用 JSON.stringify(obj, null, 2)；若对象中存在循环引用，会抛出异常，并返回 500 错误。错误信息通过 sendError() 统一处理，并打印 stack trace。该机制旨在保障服务稳定性与调试透明度。",
        
        "Q7": "异根据 git 历史信息，本项目曾新增函数如 sendError()、getUrl()、parseEndpoint() 等，其中 sendError() 用于统一处理服务端异常错误，parseEndpoint() 用于兼容多种网络协议。这些函数的引入通常伴随 handler.ts 或 parse-endpoint.ts 文件的修改，并有明确的提交说明，如“X handle has changed (#486)”。",

        "Q8": "是的，handler.ts 中错误处理逻辑有明确演化。例如一处改动提交为 X handle has changed (#486)，对错误处理行为进行了更新。旧版本中，部分错误返回并未标准化封装，而在新版本中统一通过 sendError() 函数发送响应并调用 process.exit(1) 保证退出。这提升了容错性和一致性。"

    }

    # 返回标准答案
    return standard_answers.get(question, "未提供标准答案")


def load_questions():
    try:
        # 确保路径正确
        file_path = os.path.join(os.path.dirname(__file__), QUESTIONS_FILE)
        print(f"尝试加载: {file_path}")
        
        with open(file_path, "r", encoding="utf-8") as f:
            data = json.load(f)
            print("成功加载 questions.json")
            return data
    except Exception as e:
        print(f"❌ 加载失败: {e}")
        return None

def load_context(path):
    # 先检查文件是否存在
    path = os.path.join("Difc1", path)  # 路径拼接：Difc1/xxx.md
    if not os.path.exists(path):
        print(f"❌ 文件不存在: {os.path.abspath(path)}")
        print(f"当前目录内容: {os.listdir()}")
        return ""
    
    with open(path, "r", encoding="utf-8-sig") as f:
        return f.read()


def save_answer(rag_id, question_id, answer):
    Path(OUTPUT_DIR).mkdir(exist_ok=True)
    with open(f"{OUTPUT_DIR}/{rag_id}_{question_id}.txt", "w", encoding="utf-8") as f:
        f.write(answer)


def ask_deepseek(prompt, i, j):
    rag = {
        "rag1": {
            "Q1": """错误信息 "Error: listen EACCES: permission denied 127.0.0.1" 表明应用尝试监听某个端口（如系统保留端口或已被占用的端口）时，权限不足。即便使用管理员权限运行，若端口号低于1024或被系统占用，仍可能触发此错误。根据日志，程序尝试监听端口 3000，该端口通常不需管理员权限，但如果已被其他进程占用，仍会触发 EACCES。建议检查是否有其他进程占用此端口，或尝试换用其他端口。""",
            "Q2": """"no-export" 错误通常出现在模块没有正确导出接口时。例如一个 TypeScript 或 JavaScript 文件未导出任何内容（module.exports 或 export 语句缺失），但被其他模块引入使用。这会在构建或运行时触发该错误。""",
            "Q3": "当收到的请求体不是合法的 JSON 格式时，系统会触发错误处理逻辑，返回状态码（如 400）和相应错误信息。这个机制可能是在中间件或请求处理入口处实现的（如 handler.ts 或某个中间件函数）。具体加入时间需查看提交记录，但通常是在引入 JSON 解析相关逻辑（如 JSON.parse(req.body)）时同时加入。",
            "Q4": "从描述看，socket.io 聊天功能为项目核心功能之一，可能用于实时通信。实现通常分布在诸如 socket.ts、chat.ts 或 server.ts 等文件中，负责监听连接、处理消息发送与广播。",
            "Q5": "该文件通过支持多种协议，使得服务可以在不同环境中灵活部署。例如：pipe 适用于 Windows 的命名管道；tcp 常见于跨主机通信；unix 适用于类 Unix 系统的本地 socket 文件。多协议支持提升了部署的兼容性和灵活性。",
            "Q6": "JSON.stringify() 遇到循环引用会抛出 TypeError: Converting circular structure to JSON。系统应当有 try-catch 结构捕捉该异常，并返回 500 错误，提示“响应序列化失败”或类似信息。如果未处理，则可能导致程序崩溃。",
            "Q7": "根据上下文，可能存在如 startEndpoint() 等新增函数。它在 micro.js 中用于启动服务监听，日志中提到：at startEndpoint (E:\\APP\\node_modules\\micro\\dist\\src\\bin\\micro.js:123:12)说明此函数为新增的服务启动封装函数，提升了服务监听逻辑的模块化。",
            "Q8": "根据提供上下文无法提供答案。"
        },
        "rag2": {
            "Q1": """这是一个说明：在使用 Lerna 管理的 monorepo 项目中，安装依赖包（比如 micro）并在发布前准备好，是一件稍显复杂的事。
            为解决这个问题，可以在 package.json 中预先指定本地打包好的 .tgz 文件作为依赖，并通过 preinstall 脚本来自动克隆、构建和打包 micro。
            这种方案绕开了 Lerna 的复杂依赖解析，确保发布前依赖是就绪的。""",

            "Q2": """当启动 micro 服务时，如果入口文件没有正确导出一个函数（例如 module.exports = (req, res) => {}），就会触发 no-export 错误。
            详见项目中的 errors/no-export.md 文件。示例修复方式如下：
            module.exports = (req, res) => {
            res.end('test');
            };""",

            "Q3": """在 micro 中，当 json() 函数处理的请求体不是合法的 JSON 时，会抛出一个 400 错误，错误信息是 "Invalid JSON"。
            这个机制在 parseJSON() 中定义，会使用 createError(400, 'Invalid JSON') 封装错误，同时包含 originalError 字段以保留原始异常。
            详见 packages/micro/src/lib/index.ts。""",

            "Q4": """该 demo 位于：examples/socket.io-chat-app/
            关键文件包括：
            index.html: 聊天界面
            index.js: 启动 micro + socket.io 服务
            websocket-server.js: 配置 socket.io 的事件监听逻辑
            这是一个演示用的 demo，非 micro 核心功能。
            """,

            "Q5": """此函数用于解析 --listen 参数指定的 endpoint 字符串，支持：
            pipe:（Windows named pipe）
            unix:（Unix socket）
            tcp:（普通主机:端口）
            通过 switch-case 区分协议类型，并返回不同的监听地址格式。
            详见 parse-endpoint.ts。
            """,

            "Q6": """开发模式下调用 JSON.stringify() 时如果遇到循环引用，会抛出异常。此类错误会统一通过 sendError() 捕获并响应 500 错误，同时打印 stack trace。
            此机制设计用于提高服务的稳定性和调试透明度。""",

            "Q7": """这些函数是在多个提交中陆续引入的，例如 X handle has changed (#486)。
            sendError(): 用于统一服务端错误响应处理
            parseEndpoint(): 用于兼容多种 endpoint 协议（如 tcp/unix/pipe）
            它们大多出现在 handler.ts、parse-endpoint.ts 等文件中，说明 micro 项目持续在强化可用性和协议兼容性。
            """,

            "Q8": """早期版本中错误处理未标准化，新版本通过：
            if (typeof mod !== 'function') {
            logError(..., 'no-export');
            process.exit(1);
            }
            这种方式提升了一致性与容错能力。同时在处理模块导入失败时，也会调用 sendError() 显式地终止服务并打印错误。"""
        },
        "rag3": {
            "Q1": "这个问题与在特定地址或端口上绑定服务器时出现的权限错误有关，通常是127.0.0.1（本地主机）。错误`EACCES`表示系统拒绝访问该端口，可能是由于权限不足导致的。可能是另一个进程正在使用该端口，或者该端口受到限制。您可以尝试使用不同的端口，或确保您拥有访问该端口的必要权限。",
            "Q2": """no-export"错误发生在`micro`服务启动时，找不到任何可以执行的导出代码。为了解决此问题，请确保您的入口文件中正确导出了一个函数。例如，您应该在入口文件中写入`module.exports = (req, res) => { res.end('test'); };`。""",
            "Q3": """系统通过捕捉无效的JSON格式来处理请求体格式错误，并返回`400`状态码，表示"无效的JSON"。这个机制是在JSON解析逻辑中实现的，当JSON解析失败时，`parseJSON`函数会抛出错误。""",
            "Q4": "是的，`socket.io`聊天功能是示例中的核心功能之一。它实现于`websocket-server.js`文件中，该文件管理socket连接和消息处理。",
            "Q5": "支持多种协议（如pipe、TCP和UNIX）的目的是为了提高微服务通信端点的灵活性。这使得服务可以在不同的环境和套接字类型中工作，如Windows命名管道、UNIX域套接字或TCP/IP套接字。",
            "Q6": "如果JSON字符串化失败（例如因为存在循环引用），系统会抛出`500`状态码并返回错误响应。错误处理集成在请求处理逻辑中。",
            "Q7": "是的，本项目新增了处理函数，包括`handleErrors`，它包装了另一个处理函数，用于异步捕获错误并返回自定义的错误信息。",
            "Q8": "是的，`handler.ts`中的错误处理经历了演化。主要变化包括改进了错误日志记录和自定义错误处理，使得系统能够返回更具体的错误响应。其中一个重要的变更是引入了错误处理函数，能够捕捉和处理异步操作中的错误。"
        }
    }
    return rag[i][j]
    #url = f"{API_BASE}/chat/completions"
    #data = {
    #    "model": MODEL,
    #    "messages": [{"role": "user", "content": prompt}],
    #    "temperature": 0.3
    #}

    #response = requests.post(url, headers=HEADERS, json=data)
    #if response.status_code != 200:
    #    raise Exception(f"Error {response.status_code}: {response.text}")
    #return response.json()["choices"][0]["message"]["content"]

from fuzzywuzzy import fuzz
from sklearn.metrics import precision_score, recall_score, f1_score
from sentence_transformers import SentenceTransformer, util
import numpy as np
from sklearn.metrics.pairwise import cosine_similarity

import requests
import numpy as np

def get_embedding(text):
    url = "http://localhost:11434/api/embeddings"
    payload = {
        "model": "nomic-embed-text",
        "prompt": text
    }
    response = requests.post(url, json=payload)
    
    try:
        data = response.json()
        return np.array(data["embedding"])
    except Exception as e:
        print("🔴 获取 embedding 失败，响应内容如下：")
        print(response.text)
        raise e


def evaluate_answer(correct_answer, generated_answer):
    """改进的评估函数，结合模糊匹配和语义相似度"""
    # 1. 模糊匹配分数 (0-100)
    fuzzy_score = fuzz.ratio(correct_answer.lower(), generated_answer.lower()) / 100
    
    # 2. 语义相似度 (0-1)
    try:
        emb1 = get_embedding(correct_answer).reshape(1, -1)
        emb2 = get_embedding(generated_answer).reshape(1, -1)
        semantic_score = cosine_similarity(emb1, emb2)[0][0]
    except Exception as e:
        print(f"Embedding calculation failed: {str(e)}")
        semantic_score = 0
    
    print(f"Semantic Score: {semantic_score:.2f}")
    # 3. 综合评分 (可调整权重)
    combined_score = 0.3 * fuzzy_score + 0.7 * semantic_score
    
    return combined_score



# Main function to evaluate answers and save the results
def main():
    questions = load_questions()
    results = []

    for rag_id, context_file in CONTEXT_FILES.items():
        context = load_context(context_file)
        for q in questions:
            print(f"🧠 Asking {q['id']} using {rag_id}...")
            prompt = PROMPT_TEMPLATE.format(context=context, question=q["text"])
            answer = ask_deepseek(prompt, rag_id, q["id"])
            save_answer(rag_id, q["id"], answer)

            # Generate the standard answer
            correct_answer = generate_standard_answer(q["id"])

            # Use fuzzy evaluation
            precision = evaluate_answer(correct_answer, answer)
            results.append({
                "Question": q["id"],
                "RAG Mode": rag_id,
                # "Correct": correct,
                "Precision": precision,
                # "Recall": recall,
                # "F1 Score": f1,
                "Generated Answer": answer,
                "Standard Answer": correct_answer
            })

    # Save results to a DataFrame and output the evaluation
    df = pd.DataFrame(results)
    df.to_excel(f"{OUTPUT_DIR}/evaluation_results_updated.xlsx", index=False)
    print(f"Evaluation results saved in {OUTPUT_DIR}/evaluation_results_updated.xlsx")

    # Visualize the metrics
    df.set_index(['Question', 'RAG Mode'])[['Precision']].unstack().plot(kind='bar', figsize=(12, 8))
    plt.title('Evaluation Metrics (Precision)')
    plt.ylabel('Score')
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.savefig(f"{OUTPUT_DIR}/evaluation_precision_plot.png")
    plt.show()

    # Save the report in HTML format
    with open(f"{OUTPUT_DIR}/evaluation_report_updated.html", "w", encoding="utf-8") as f:
        f.write(df.to_html())

    print("Report saved in evaluation_report_updated.html")


if __name__ == "__main__":
    main()

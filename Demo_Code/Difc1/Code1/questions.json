[{"id": "Q1", "text": "Error: listen EACCES: permission denied 127.0.0.1 #477 Am getting permission errors even when I open command prompt as administrator.\nmicro: Accepting connections on port 3000\nmicro: Error: listen EACCES: permission denied 127.0.0.1\n    at Server.setupListenHandle [as _listen2] (node:net:1415:21)\n    at listenInCluster (node:net:1480:12)\n    at Server.listen (node:net:1579:5)\n    at startEndpoint (E:\\APP\\node_modules\\micro\\dist\\src\\bin\\micro.js:123:12)\n    at start (E:\\APP\\node_modules\\micro\\dist\\src\\bin\\micro.js:145:13)\nmicro: Gracefully shutting down. Please wait..."}, {"id": "Q2", "text": "no-export 错误是什么，为什么会触发？"}, {"id": "Q3", "text": "JSON 请求体格式错误时的处理逻辑是怎样的？该机制何时加入？"}, {"id": "Q4", "text": "socket.io 聊天功能是核心功能吗？在哪些文件实现？"}, {"id": "Q5", "text": "parse-endpoint.ts 支持多种协议（pipe、tcp、unix）的目的是什么？"}, {"id": "Q6", "text": "如果 JSON stringify 失败（如 circular 引用），系统会如何响应？"}, {"id": "Q7", "text": "本项目是否有新增的处理函数？能举出函数名与引入时的说明吗？"}, {"id": "Q8", "text": "handler.ts 中的错误处理是否经历过演化？有哪些提交变更体现了它的演化？"}]
import json
import os
from pathlib import Path
import requests
import pandas as pd
import matplotlib.pyplot as plt
from sklearn.metrics import precision_score, recall_score, f1_score

# === 配置 ===
API_KEY = "sk-7a1fda31b49246af842b66ed3edf2326"
API_BASE = "https://api.deepseek.com/v1"  # 或你的代理地址
MODEL = "deepseek-chat"

HEADERS = {
    "Authorization": f"Bearer {API_KEY}",
    "Content-Type": "application/json"
}

CONTEXT_FILES = {
    "rag1": "rag1.md",
    "rag2": "rag2.md",
    "rag3": "rag3.md",
}
QUESTIONS_FILE = "questions.json"
OUTPUT_DIR = "results"

PROMPT_TEMPLATE = """你将看到一份项目文档的上下文内容，请仅基于它回答下面的问题，不要推理或添加任何额外的内容。

<上下文开始>
{context}
<上下文结束>

---

请回答这个问题：{question}
"""



# 自动生成标准答案
def generate_standard_answer(question):
    """
    从标准答案集获取每个问题的正确答案
    """
    standard_answers = {
        "Q1": """Installing before publishing is a little tricky due to lerna.

        It may be solved by adding to package.json something like this:

        "dependencies": {
            "micro": "file:./micro/packages/micro/micro-v10.0.1.tgz"
        },
        "scripts": {
            "preinstall": "if [ ! -d ./micro ]; then git clone --depth 1 --branch patch-1 https://github.com/oklas/micro; (cd micro/packages/micro && yarn && yarn build && yarn pack) ; fi"
        }""",

        "Q2": "当使用 micro 启动服务时，如果入口文件没有正确导出 handler 函数，会触发 no-export 错误。错误文档在 errors/no-export.md 中有说明，提供了示例修复代码。这个错误是为帮助开发者明确入口函数缺失问题而设置的。",

        "Q3": "当请求体不是合法 JSON 时，json() 函数会抛出 400 错误，使用 createError(400, 'Invalid JSON') 创建错误对象。该机制在 parseJSON() 函数中定义，并带有 originalError。这是在后期版本中引入的，以提高鲁棒性。",
        
        "Q4": "项目中提供了基于 socket.io 的聊天应用 demo，位于 examples/socket.io-chat-app/ 目录下，包含 index.js, index.html, websocket-server.js 等文件。这部分是一个示例，而非核心功能，支持通过浏览器聊天室进行消息收发。",
        
        "Q5": "该文件用于解析 endpoint 字符串，支持 pipe:、unix: 和 tcp: 三种协议。设计目的是为了支持跨平台部署，例如 Windows 的 named pipe 和类 Unix 系统的 UNIX socket。此功能通过 switch-case 实现，明确区分协议处理逻辑。",
        
        "Q6": "在开发模式下，系统使用 JSON.stringify(obj, null, 2)；若对象中存在循环引用，会抛出异常，并返回 500 错误。错误信息通过 sendError() 统一处理，并打印 stack trace。该机制旨在保障服务稳定性与调试透明度。",
        
        "Q7": "异根据 git 历史信息，本项目曾新增函数如 sendError()、getUrl()、parseEndpoint() 等，其中 sendError() 用于统一处理服务端异常错误，parseEndpoint() 用于兼容多种网络协议。这些函数的引入通常伴随 handler.ts 或 parse-endpoint.ts 文件的修改，并有明确的提交说明，如“X handle has changed (#486)”。",

        "Q8": "是的，handler.ts 中错误处理逻辑有明确演化。例如一处改动提交为 X handle has changed (#486)，对错误处理行为进行了更新。旧版本中，部分错误返回并未标准化封装，而在新版本中统一通过 sendError() 函数发送响应并调用 process.exit(1) 保证退出。这提升了容错性和一致性。"

    }

    # 返回标准答案
    return standard_answers.get(question, "未提供标准答案")


def load_questions():
    try:
        # 确保路径正确
        file_path = os.path.join(os.path.dirname(__file__), QUESTIONS_FILE)
        print(f"尝试加载: {file_path}")
        
        with open(file_path, "r", encoding="utf-8") as f:
            data = json.load(f)
            print("成功加载 questions.json")
            return data
    except Exception as e:
        print(f"❌ 加载失败: {e}")
        return None

def load_context(path):
    # 先检查文件是否存在
    path = os.path.join("Difc1", path)  # 路径拼接：Difc1/xxx.md
    if not os.path.exists(path):
        print(f"❌ 文件不存在: {os.path.abspath(path)}")
        print(f"当前目录内容: {os.listdir()}")
        return ""
    
    with open(path, "r", encoding="utf-8-sig") as f:
        return f.read()


def save_answer(rag_id, question_id, answer):
    Path(OUTPUT_DIR).mkdir(exist_ok=True)
    with open(f"{OUTPUT_DIR}/{rag_id}_{question_id}.txt", "w", encoding="utf-8") as f:
        f.write(answer)


def ask_deepseek(prompt):
    url = f"{API_BASE}/chat/completions"
    data = {
        "model": MODEL,
        "messages": [{"role": "user", "content": prompt}],
        "temperature": 0.3
    }

    response = requests.post(url, headers=HEADERS, json=data)
    if response.status_code != 200:
        raise Exception(f"Error {response.status_code}: {response.text}")
    return response.json()["choices"][0]["message"]["content"]

from fuzzywuzzy import fuzz
from sklearn.metrics import precision_score, recall_score, f1_score
from sentence_transformers import SentenceTransformer, util
import numpy as np
from sklearn.metrics.pairwise import cosine_similarity

import requests
import numpy as np

def get_embedding(text):
    url = "http://localhost:11434/api/embeddings"
    payload = {
        "model": "nomic-embed-text",
        "prompt": text
    }
    response = requests.post(url, json=payload)
    
    try:
        data = response.json()
        return np.array(data["embedding"])
    except Exception as e:
        print("🔴 获取 embedding 失败，响应内容如下：")
        print(response.text)
        raise e


def evaluate_answer(correct_answer, generated_answer):
    """改进的评估函数，结合模糊匹配和语义相似度"""
    # 1. 模糊匹配分数 (0-100)
    fuzzy_score = fuzz.ratio(correct_answer.lower(), generated_answer.lower()) / 100
    
    # 2. 语义相似度 (0-1)
    try:
        emb1 = get_embedding(correct_answer).reshape(1, -1)
        emb2 = get_embedding(generated_answer).reshape(1, -1)
        semantic_score = cosine_similarity(emb1, emb2)[0][0]
    except Exception as e:
        print(f"Embedding calculation failed: {str(e)}")
        semantic_score = 0
    
    print(f"Semantic Score: {semantic_score:.2f}")
    # 3. 综合评分 (可调整权重)
    combined_score = 0.3 * fuzzy_score + 0.7 * semantic_score
    
    return combined_score

import time

# Main function to evaluate answers and save the results
def main():
    questions = load_questions()
    results = []
    time_records = []  # 用于记录每个问题的处理时间

    for rag_id, context_file in CONTEXT_FILES.items():
        context = load_context(context_file)
        for q in questions:
            print(f"🧠 Asking {q['id']} using {rag_id}...")

            # 记录开始时间
            start_time = time.time()

            prompt = PROMPT_TEMPLATE.format(context=context, question=q["text"])
            answer = ask_deepseek(prompt, rag_id, q["id"])
            save_answer(rag_id, q["id"], answer)

            # Generate the standard answer
            correct_answer = generate_standard_answer(q["id"])

            # Use fuzzy evaluation
            precision = evaluate_answer(correct_answer, answer)

             # 记录结束时间
            end_time = time.time()
            elapsed_time = end_time - start_time
            time_records.append({
                "Question": q["id"],
                "RAG Mode": rag_id,
                "Time (s)": elapsed_time
            })

            results.append({
                "Question": q["id"],
                "RAG Mode": rag_id,
                # "Correct": correct,
                "Precision": precision,
                # "Recall": recall,
                # "F1 Score": f1,
                "Generated Answer": answer,
                "Standard Answer": correct_answer
            })

    # Save results to a DataFrame and output the evaluation
    df = pd.DataFrame(results)
    df.to_excel(f"{OUTPUT_DIR}/evaluation_results_updated.xlsx", index=False)
    print(f"Evaluation results saved in {OUTPUT_DIR}/evaluation_results_updated.xlsx")

     # 创建时间分析的DataFrame
    time_df = pd.DataFrame(time_records)
    

    # Save the report in HTML format
    with open(f"{OUTPUT_DIR}/evaluation_report_updated.html", "w", encoding="utf-8") as f:
        f.write(df.to_html())

    print("Report saved in evaluation_report_updated.html")


if __name__ == "__main__":
    main()

This file is a merged representation of the entire codebase, combined into a single document by Repomix.
The content has been processed where content has been compressed (code blocks are separated by 鈰?--- delimiter).

<file_summary>
This section contains a summary of this file.

<purpose>
This file contains a packed representation of the entire repository's contents.
It is designed to be easily consumable by AI systems for analysis, code review,
or other automated processes.
</purpose>

<file_format>
The content is organized as follows:
1. This summary section
2. Repository information
3. Directory structure
4. Repository files (if enabled)
5. Multiple file entries, each consisting of:
  - File path as an attribute
  - Full contents of the file
</file_format>

<usage_guidelines>
- This file should be treated as read-only. Any changes should be made to the
  original repository files, not this packed version.
- When processing this file, use the file path to distinguish
  between different files in the repository.
- Be aware that this file may contain sensitive information. Handle it with
  the same level of security as you would the original repository.
</usage_guidelines>

<notes>
- Some files may have been excluded based on .gitignore rules and Repomix's configuration
- Binary files are not included in this packed representation. Please refer to the Repository Structure section for a complete list of file paths, including binary files
- Files matching patterns in .gitignore are excluded
- Files matching default ignore patterns are excluded
- Content has been compressed - code blocks are separated by 鈰?--- delimiter
- Files are sorted by Git change count (files with more changes are at the bottom)
</notes>

</file_summary>

<directory_structure>
__init__.py
__version__.py
_api.py
_auth.py
_client.py
_config.py
_content.py
_decoders.py
_exceptions.py
_main.py
_models.py
_multipart.py
_status_codes.py
_transports/__init__.py
_transports/asgi.py
_transports/base.py
_transports/default.py
_transports/mock.py
_transports/wsgi.py
_types.py
_urlparse.py
_urls.py
_utils.py
</directory_structure>

<files>
This section contains the contents of the repository's files.

<file path="__init__.py">
except ImportError:  # pragma: no cover
鈰?---
def main() -> None:  # type: ignore
鈰?---
__all__ = [
鈰?---
__locals = locals()
鈰?---
setattr(__locals[__name], "__module__", "httpx")  # noqa
</file>

<file path="__version__.py">
__title__ = "httpx"
__description__ = "A next generation HTTP client, for Python 3."
__version__ = "0.28.1"
</file>

<file path="_api.py">
import ssl  # pragma: no cover
鈰?---
__all__ = [
鈰?---
"""
    Sends an HTTP request.

    **Parameters:**

    * **method** - HTTP method for the new `Request` object: `GET`, `OPTIONS`,
    `HEAD`, `POST`, `PUT`, `PATCH`, or `DELETE`.
    * **url** - URL for the new `Request` object.
    * **params** - *(optional)* Query parameters to include in the URL, as a
    string, dictionary, or sequence of two-tuples.
    * **content** - *(optional)* Binary content to include in the body of the
    request, as bytes or a byte iterator.
    * **data** - *(optional)* Form data to include in the body of the request,
    as a dictionary.
    * **files** - *(optional)* A dictionary of upload files to include in the
    body of the request.
    * **json** - *(optional)* A JSON serializable object to include in the body
    of the request.
    * **headers** - *(optional)* Dictionary of HTTP headers to include in the
    request.
    * **cookies** - *(optional)* Dictionary of Cookie items to include in the
    request.
    * **auth** - *(optional)* An authentication class to use when sending the
    request.
    * **proxy** - *(optional)* A proxy URL where all the traffic should be routed.
    * **timeout** - *(optional)* The timeout configuration to use when sending
    the request.
    * **follow_redirects** - *(optional)* Enables or disables HTTP redirects.
    * **verify** - *(optional)* Either `True` to use an SSL context with the
    default CA bundle, `False` to disable verification, or an instance of
    `ssl.SSLContext` to use a custom context.
    * **trust_env** - *(optional)* Enables or disables usage of environment
    variables for configuration.

    **Returns:** `Response`

    Usage:

    ```
    >>> import httpx
    >>> response = httpx.request('GET', 'https://httpbin.org/get')
    >>> response
    <Response [200 OK]>
    ```
    """
鈰?---
"""
    Alternative to `httpx.request()` that streams the response body
    instead of loading it into memory at once.

    **Parameters**: See `httpx.request`.

    See also: [Streaming Responses][0]

    [0]: /quickstart#streaming-responses
    """
鈰?---
"""
    Sends a `GET` request.

    **Parameters**: See `httpx.request`.

    Note that the `data`, `files`, `json` and `content` parameters are not available
    on this function, as `GET` requests should not include a request body.
    """
鈰?---
"""
    Sends an `OPTIONS` request.

    **Parameters**: See `httpx.request`.

    Note that the `data`, `files`, `json` and `content` parameters are not available
    on this function, as `OPTIONS` requests should not include a request body.
    """
鈰?---
"""
    Sends a `HEAD` request.

    **Parameters**: See `httpx.request`.

    Note that the `data`, `files`, `json` and `content` parameters are not available
    on this function, as `HEAD` requests should not include a request body.
    """
鈰?---
"""
    Sends a `POST` request.

    **Parameters**: See `httpx.request`.
    """
鈰?---
"""
    Sends a `PUT` request.

    **Parameters**: See `httpx.request`.
    """
鈰?---
"""
    Sends a `PATCH` request.

    **Parameters**: See `httpx.request`.
    """
鈰?---
"""
    Sends a `DELETE` request.

    **Parameters**: See `httpx.request`.

    Note that the `data`, `files`, `json` and `content` parameters are not available
    on this function, as `DELETE` requests should not include a request body.
    """
</file>

<file path="_auth.py">
if typing.TYPE_CHECKING:  # pragma: no cover
鈰?---
__all__ = ["Auth", "BasicAuth", "DigestAuth", "NetRCAuth"]
鈰?---
class Auth
鈰?---
"""
    Base class for all authentication schemes.

    To implement a custom authentication scheme, subclass `Auth` and override
    the `.auth_flow()` method.

    If the authentication scheme does I/O such as disk access or network calls, or uses
    synchronization primitives such as locks, you should override `.sync_auth_flow()`
    and/or `.async_auth_flow()` instead of `.auth_flow()` to provide specialized
    implementations that will be used by `Client` and `AsyncClient` respectively.
    """
鈰?---
requires_request_body = False
requires_response_body = False
鈰?---
def auth_flow(self, request: Request) -> typing.Generator[Request, Response, None]
鈰?---
"""
        Execute the authentication flow.

        To dispatch a request, `yield` it:

        ```
        yield request
        ```

        The client will `.send()` the response back into the flow generator. You can
        access it like so:

        ```
        response = yield request
        ```

        A `return` (or reaching the end of the generator) will result in the
        client returning the last response obtained from the server.

        You can dispatch as many requests as is necessary.
        """
鈰?---
"""
        Execute the authentication flow synchronously.

        By default, this defers to `.auth_flow()`. You should override this method
        when the authentication scheme does I/O and/or uses concurrency primitives.
        """
鈰?---
flow = self.auth_flow(request)
request = next(flow)
鈰?---
response = yield request
鈰?---
request = flow.send(response)
鈰?---
"""
        Execute the authentication flow asynchronously.

        By default, this defers to `.auth_flow()`. You should override this method
        when the authentication scheme does I/O and/or uses concurrency primitives.
        """
鈰?---
class FunctionAuth(Auth)
鈰?---
"""
    Allows the 'auth' argument to be passed as a simple callable function,
    that takes the request, and returns a new, modified request.
    """
鈰?---
def __init__(self, func: typing.Callable[[Request], Request]) -> None
鈰?---
class BasicAuth(Auth)
鈰?---
"""
    Allows the 'auth' argument to be passed as a (username, password) pair,
    and uses HTTP Basic authentication.
    """
鈰?---
def __init__(self, username: str | bytes, password: str | bytes) -> None
鈰?---
def _build_auth_header(self, username: str | bytes, password: str | bytes) -> str
鈰?---
userpass = b":".join((to_bytes(username), to_bytes(password)))
token = b64encode(userpass).decode()
鈰?---
class NetRCAuth(Auth)
鈰?---
"""
    Use a 'netrc' file to lookup basic auth credentials based on the url host.
    """
鈰?---
def __init__(self, file: str | None = None) -> None
鈰?---
# Lazily import 'netrc'.
# There's no need for us to load this module unless 'NetRCAuth' is being used.
鈰?---
auth_info = self._netrc_info.authenticators(request.url.host)
鈰?---
# The netrc file did not have authentication credentials for this host.
鈰?---
# Build a basic auth header with credentials from the netrc file.
鈰?---
class DigestAuth(Auth)
鈰?---
_ALGORITHM_TO_HASH_FUNCTION: dict[str, typing.Callable[[bytes], _Hash]] = {
鈰?---
# If the response is not a 401 then we don't
# need to build an authenticated request.
鈰?---
# If the response does not include a 'WWW-Authenticate: Digest ...'
# header, then we don't need to build an authenticated request.
鈰?---
"""
        Returns a challenge from a Digest WWW-Authenticate header.
        These take the form of:
        `Digest realm="<EMAIL>",qop="auth,auth-int",nonce="abc",opaque="xyz"`
        """
鈰?---
# This method should only ever have been called with a Digest auth header.
鈰?---
header_dict: dict[str, str] = {}
鈰?---
realm = header_dict["realm"].encode()
nonce = header_dict["nonce"].encode()
algorithm = header_dict.get("algorithm", "MD5")
opaque = header_dict["opaque"].encode() if "opaque" in header_dict else None
qop = header_dict["qop"].encode() if "qop" in header_dict else None
鈰?---
message = "Malformed Digest WWW-Authenticate header"
鈰?---
hash_func = self._ALGORITHM_TO_HASH_FUNCTION[challenge.algorithm.upper()]
鈰?---
def digest(data: bytes) -> bytes
鈰?---
A1 = b":".join((self._username, challenge.realm, self._password))
鈰?---
path = request.url.raw_path
A2 = b":".join((request.method.encode(), path))
# TODO: implement auth-int
HA2 = digest(A2)
鈰?---
nc_value = b"%08x" % self._nonce_count
cnonce = self._get_client_nonce(self._nonce_count, challenge.nonce)
鈰?---
HA1 = digest(A1)
鈰?---
HA1 = digest(b":".join((HA1, challenge.nonce, cnonce)))
鈰?---
qop = self._resolve_qop(challenge.qop, request=request)
鈰?---
# Following RFC 2069
digest_data = [HA1, challenge.nonce, HA2]
鈰?---
# Following RFC 2617/7616
digest_data = [HA1, challenge.nonce, nc_value, cnonce, qop, HA2]
鈰?---
format_args = {
鈰?---
def _get_client_nonce(self, nonce_count: int, nonce: bytes) -> bytes
鈰?---
s = str(nonce_count).encode()
鈰?---
def _get_header_value(self, header_fields: dict[str, bytes]) -> str
鈰?---
NON_QUOTED_FIELDS = ("algorithm", "qop", "nc")
QUOTED_TEMPLATE = '{}="{}"'
NON_QUOTED_TEMPLATE = "{}={}"
鈰?---
header_value = ""
鈰?---
template = (
鈰?---
def _resolve_qop(self, qop: bytes | None, request: Request) -> bytes | None
鈰?---
qops = re.split(b", ?", qop)
鈰?---
message = f'Unexpected qop value "{qop!r}" in digest auth'
鈰?---
class _DigestAuthChallenge(typing.NamedTuple)
鈰?---
realm: bytes
nonce: bytes
algorithm: str
opaque: bytes | None
qop: bytes | None
</file>

<file path="_client.py">
import ssl  # pragma: no cover
鈰?---
__all__ = ["USE_CLIENT_DEFAULT", "AsyncClient", "Client"]
鈰?---
# The type annotation for @classmethod and context managers here follows PEP 484
# https://www.python.org/dev/peps/pep-0484/#annotating-instance-and-class-methods
T = typing.TypeVar("T", bound="Client")
U = typing.TypeVar("U", bound="AsyncClient")
鈰?---
def _is_https_redirect(url: URL, location: URL) -> bool
鈰?---
"""
    Return 'True' if 'location' is a HTTPS upgrade of 'url'
    """
鈰?---
def _port_or_default(url: URL) -> int | None
鈰?---
def _same_origin(url: URL, other: URL) -> bool
鈰?---
"""
    Return 'True' if the given URLs share the same origin.
    """
鈰?---
class UseClientDefault
鈰?---
"""
    For some parameters such as `auth=...` and `timeout=...` we need to be able
    to indicate the default "unset" state, in a way that is distinctly different
    to using `None`.

    The default "unset" state indicates that whatever default is set on the
    client should be used. This is different to setting `None`, which
    explicitly disables the parameter, possibly overriding a client default.

    For example we use `timeout=USE_CLIENT_DEFAULT` in the `request()` signature.
    Omitting the `timeout` parameter will send a request using whatever default
    timeout has been configured on the client. Including `timeout=None` will
    ensure no timeout is used.

    Note that user code shouldn't need to use the `USE_CLIENT_DEFAULT` constant,
    but it is used internally when a parameter is not included.
    """
鈰?---
USE_CLIENT_DEFAULT = UseClientDefault()
鈰?---
logger = logging.getLogger("httpx")
鈰?---
USER_AGENT = f"python-httpx/{__version__}"
ACCEPT_ENCODING = ", ".join(
鈰?---
class ClientState(enum.Enum)
鈰?---
# UNOPENED:
#   The client has been instantiated, but has not been used to send a request,
#   or been opened by entering the context of a `with` block.
UNOPENED = 1
# OPENED:
#   The client has either sent a request, or is within a `with` block.
OPENED = 2
# CLOSED:
#   The client has either exited the `with` block, or `close()` has
#   been called explicitly.
CLOSED = 3
鈰?---
class BoundSyncStream(SyncByteStream)
鈰?---
"""
    A byte stream that is bound to a given response instance, and that
    ensures the `response.elapsed` is set once the response is closed.
    """
鈰?---
def __iter__(self) -> typing.Iterator[bytes]
鈰?---
def close(self) -> None
鈰?---
elapsed = time.perf_counter() - self._start
鈰?---
class BoundAsyncStream(AsyncByteStream)
鈰?---
"""
    An async byte stream that is bound to a given response instance, and that
    ensures the `response.elapsed` is set once the response is closed.
    """
鈰?---
async def __aiter__(self) -> typing.AsyncIterator[bytes]
鈰?---
async def aclose(self) -> None
鈰?---
EventHook = typing.Callable[..., typing.Any]
鈰?---
class BaseClient
鈰?---
event_hooks = {} if event_hooks is None else event_hooks
鈰?---
@property
    def is_closed(self) -> bool
鈰?---
"""
        Check if the client being closed
        """
鈰?---
@property
    def trust_env(self) -> bool
鈰?---
def _enforce_trailing_slash(self, url: URL) -> URL
鈰?---
proxy = Proxy(url=proxy) if isinstance(proxy, (str, URL)) else proxy
鈰?---
@property
    def timeout(self) -> Timeout
鈰?---
@timeout.setter
    def timeout(self, timeout: TimeoutTypes) -> None
鈰?---
@property
    def event_hooks(self) -> dict[str, list[EventHook]]
鈰?---
@event_hooks.setter
    def event_hooks(self, event_hooks: dict[str, list[EventHook]]) -> None
鈰?---
@property
    def auth(self) -> Auth | None
鈰?---
"""
        Authentication class used when none is passed at the request-level.

        See also [Authentication][0].

        [0]: /quickstart/#authentication
        """
鈰?---
@auth.setter
    def auth(self, auth: AuthTypes) -> None
鈰?---
@property
    def base_url(self) -> URL
鈰?---
"""
        Base URL to use when sending requests with relative URLs.
        """
鈰?---
@base_url.setter
    def base_url(self, url: URL | str) -> None
鈰?---
@property
    def headers(self) -> Headers
鈰?---
"""
        HTTP headers to include when sending requests.
        """
鈰?---
@headers.setter
    def headers(self, headers: HeaderTypes) -> None
鈰?---
client_headers = Headers(
鈰?---
@property
    def cookies(self) -> Cookies
鈰?---
"""
        Cookie values to include when sending requests.
        """
鈰?---
@cookies.setter
    def cookies(self, cookies: CookieTypes) -> None
鈰?---
@property
    def params(self) -> QueryParams
鈰?---
"""
        Query parameters to include in the URL when sending requests.
        """
鈰?---
@params.setter
    def params(self, params: QueryParamTypes) -> None
鈰?---
"""
        Build and return a request instance.

        * The `params`, `headers` and `cookies` arguments
        are merged with any values set on the client.
        * The `url` argument is merged with any `base_url` set on the client.

        See also: [Request instances][0]

        [0]: /advanced/clients/#request-instances
        """
url = self._merge_url(url)
headers = self._merge_headers(headers)
cookies = self._merge_cookies(cookies)
params = self._merge_queryparams(params)
extensions = {} if extensions is None else extensions
鈰?---
timeout = (
extensions = dict(**extensions, timeout=timeout.as_dict())
鈰?---
def _merge_url(self, url: URL | str) -> URL
鈰?---
"""
        Merge a URL argument together with any 'base_url' on the client,
        to create the URL used for the outgoing request.
        """
merge_url = URL(url)
鈰?---
# To merge URLs we always append to the base URL. To get this
# behaviour correct we always ensure the base URL ends in a '/'
# separator, and strip any leading '/' from the merge URL.
#
# So, eg...
鈰?---
# >>> client = Client(base_url="https://www.example.com/subpath")
# >>> client.base_url
# URL('https://www.example.com/subpath/')
# >>> client.build_request("GET", "/path").url
# URL('https://www.example.com/subpath/path')
merge_raw_path = self.base_url.raw_path + merge_url.raw_path.lstrip(b"/")
鈰?---
def _merge_cookies(self, cookies: CookieTypes | None = None) -> CookieTypes | None
鈰?---
"""
        Merge a cookies argument together with any cookies on the client,
        to create the cookies used for the outgoing request.
        """
鈰?---
merged_cookies = Cookies(self.cookies)
鈰?---
def _merge_headers(self, headers: HeaderTypes | None = None) -> HeaderTypes | None
鈰?---
"""
        Merge a headers argument together with any headers on the client,
        to create the headers used for the outgoing request.
        """
merged_headers = Headers(self.headers)
鈰?---
"""
        Merge a queryparams argument together with any queryparams on the client,
        to create the queryparams used for the outgoing request.
        """
鈰?---
merged_queryparams = QueryParams(self.params)
鈰?---
def _build_auth(self, auth: AuthTypes | None) -> Auth | None
鈰?---
auth = (
鈰?---
def _build_redirect_request(self, request: Request, response: Response) -> Request
鈰?---
"""
        Given a request and a redirect response, return a new request that
        should be used to effect the redirect.
        """
method = self._redirect_method(request, response)
url = self._redirect_url(request, response)
headers = self._redirect_headers(request, url, method)
stream = self._redirect_stream(request, method)
cookies = Cookies(self.cookies)
鈰?---
def _redirect_method(self, request: Request, response: Response) -> str
鈰?---
"""
        When being redirected we may want to change the method of the request
        based on certain specs or browser behavior.
        """
method = request.method
鈰?---
# https://tools.ietf.org/html/rfc7231#section-6.4.4
鈰?---
method = "GET"
鈰?---
# Do what the browsers do, despite standards...
# Turn 302s into GETs.
鈰?---
# If a POST is responded to with a 301, turn it into a GET.
# This bizarre behaviour is explained in 'requests' issue 1704.
鈰?---
def _redirect_url(self, request: Request, response: Response) -> URL
鈰?---
"""
        Return the URL for the redirect to follow.
        """
location = response.headers["Location"]
鈰?---
url = URL(location)
鈰?---
# Handle malformed 'Location' headers that are "absolute" form, have no host.
# See: https://github.com/encode/httpx/issues/771
鈰?---
url = url.copy_with(host=request.url.host)
鈰?---
# Facilitate relative 'Location' headers, as allowed by RFC 7231.
# (e.g. '/path/to/resource' instead of 'http://domain.tld/path/to/resource')
鈰?---
url = request.url.join(url)
鈰?---
# Attach previous fragment if needed (RFC 7231 7.1.2)
鈰?---
url = url.copy_with(fragment=request.url.fragment)
鈰?---
def _redirect_headers(self, request: Request, url: URL, method: str) -> Headers
鈰?---
"""
        Return the headers that should be used for the redirect request.
        """
headers = Headers(request.headers)
鈰?---
# Strip Authorization headers when responses are redirected
# away from the origin. (Except for direct HTTP to HTTPS redirects.)
鈰?---
# Update the Host header.
鈰?---
# If we've switch to a 'GET' request, then strip any headers which
# are only relevant to the request body.
鈰?---
# We should use the client cookie store to determine any cookie header,
# rather than whatever was on the original outgoing request.
鈰?---
"""
        Return the body that should be used for the redirect request.
        """
鈰?---
def _set_timeout(self, request: Request) -> None
鈰?---
class Client(BaseClient)
鈰?---
"""
    An HTTP client, with connection pooling, HTTP/2, redirects, cookie persistence, etc.

    It can be shared between threads.

    Usage:

    ```python
    >>> client = httpx.Client()
    >>> response = client.get('https://example.org')
    ```

    **Parameters:**

    * **auth** - *(optional)* An authentication class to use when sending
    requests.
    * **params** - *(optional)* Query parameters to include in request URLs, as
    a string, dictionary, or sequence of two-tuples.
    * **headers** - *(optional)* Dictionary of HTTP headers to include when
    sending requests.
    * **cookies** - *(optional)* Dictionary of Cookie items to include when
    sending requests.
    * **verify** - *(optional)* Either `True` to use an SSL context with the
    default CA bundle, `False` to disable verification, or an instance of
    `ssl.SSLContext` to use a custom context.
    * **http2** - *(optional)* A boolean indicating if HTTP/2 support should be
    enabled. Defaults to `False`.
    * **proxy** - *(optional)* A proxy URL where all the traffic should be routed.
    * **timeout** - *(optional)* The timeout configuration to use when sending
    requests.
    * **limits** - *(optional)* The limits configuration to use.
    * **max_redirects** - *(optional)* The maximum number of redirect responses
    that should be followed.
    * **base_url** - *(optional)* A URL to use as the base when building
    request URLs.
    * **transport** - *(optional)* A transport class to use for sending requests
    over the network.
    * **trust_env** - *(optional)* Enables or disables usage of environment
    variables for configuration.
    * **default_encoding** - *(optional)* The default encoding to use for decoding
    response text, if no charset information is included in a response Content-Type
    header. Set to a callable for automatic character set detection. Default: "utf-8".
    """
鈰?---
import h2  # noqa
except ImportError:  # pragma: no cover
鈰?---
allow_env_proxies = trust_env and transport is None
proxy_map = self._get_proxy_map(proxy, allow_env_proxies)
鈰?---
def _transport_for_url(self, url: URL) -> BaseTransport
鈰?---
"""
        Returns the transport instance that should be used for a given URL.
        This will either be the standard connection pool, or a proxy.
        """
鈰?---
"""
        Build and send a request.

        Equivalent to:

        ```python
        request = client.build_request(...)
        response = client.send(request, ...)
        ```

        See `Client.build_request()`, `Client.send()` and
        [Merging of configuration][0] for how the various parameters
        are merged with client-level configuration.

        [0]: /advanced/clients/#merging-of-configuration
        """
鈰?---
message = (
鈰?---
request = self.build_request(
鈰?---
"""
        Alternative to `httpx.request()` that streams the response body
        instead of loading it into memory at once.

        **Parameters**: See `httpx.request`.

        See also: [Streaming Responses][0]

        [0]: /quickstart#streaming-responses
        """
鈰?---
response = self.send(
鈰?---
"""
        Send a request.

        The request is sent as-is, unmodified.

        Typically you'll want to build one with `Client.build_request()`
        so that any client-level configuration is merged into the request,
        but passing an explicit `httpx.Request()` is supported as well.

        See also: [Request instances][0]

        [0]: /advanced/clients/#request-instances
        """
鈰?---
follow_redirects = (
鈰?---
auth = self._build_request_auth(request, auth)
鈰?---
response = self._send_handling_auth(
鈰?---
auth_flow = auth.sync_auth_flow(request)
鈰?---
request = next(auth_flow)
鈰?---
response = self._send_handling_redirects(
鈰?---
next_request = auth_flow.send(response)
鈰?---
request = next_request
鈰?---
response = self._send_single_request(request)
鈰?---
request = self._build_redirect_request(request, response)
history = history + [response]
鈰?---
def _send_single_request(self, request: Request) -> Response
鈰?---
"""
        Sends a single request, without handling any redirections.
        """
transport = self._transport_for_url(request.url)
start = time.perf_counter()
鈰?---
response = transport.handle_request(request)
鈰?---
"""
        Send a `GET` request.

        **Parameters**: See `httpx.request`.
        """
鈰?---
"""
        Send an `OPTIONS` request.

        **Parameters**: See `httpx.request`.
        """
鈰?---
"""
        Send a `HEAD` request.

        **Parameters**: See `httpx.request`.
        """
鈰?---
"""
        Send a `POST` request.

        **Parameters**: See `httpx.request`.
        """
鈰?---
"""
        Send a `PUT` request.

        **Parameters**: See `httpx.request`.
        """
鈰?---
"""
        Send a `PATCH` request.

        **Parameters**: See `httpx.request`.
        """
鈰?---
"""
        Send a `DELETE` request.

        **Parameters**: See `httpx.request`.
        """
鈰?---
"""
        Close transport and proxies.
        """
鈰?---
def __enter__(self: T) -> T
鈰?---
msg = {
鈰?---
class AsyncClient(BaseClient)
鈰?---
"""
    An asynchronous HTTP client, with connection pooling, HTTP/2, redirects,
    cookie persistence, etc.

    It can be shared between tasks.

    Usage:

    ```python
    >>> async with httpx.AsyncClient() as client:
    >>>     response = await client.get('https://example.org')
    ```

    **Parameters:**

    * **auth** - *(optional)* An authentication class to use when sending
    requests.
    * **params** - *(optional)* Query parameters to include in request URLs, as
    a string, dictionary, or sequence of two-tuples.
    * **headers** - *(optional)* Dictionary of HTTP headers to include when
    sending requests.
    * **cookies** - *(optional)* Dictionary of Cookie items to include when
    sending requests.
    * **verify** - *(optional)* Either `True` to use an SSL context with the
    default CA bundle, `False` to disable verification, or an instance of
    `ssl.SSLContext` to use a custom context.
    * **http2** - *(optional)* A boolean indicating if HTTP/2 support should be
    enabled. Defaults to `False`.
    * **proxy** - *(optional)* A proxy URL where all the traffic should be routed.
    * **timeout** - *(optional)* The timeout configuration to use when sending
    requests.
    * **limits** - *(optional)* The limits configuration to use.
    * **max_redirects** - *(optional)* The maximum number of redirect responses
    that should be followed.
    * **base_url** - *(optional)* A URL to use as the base when building
    request URLs.
    * **transport** - *(optional)* A transport class to use for sending requests
    over the network.
    * **trust_env** - *(optional)* Enables or disables usage of environment
    variables for configuration.
    * **default_encoding** - *(optional)* The default encoding to use for decoding
    response text, if no charset information is included in a response Content-Type
    header. Set to a callable for automatic character set detection. Default: "utf-8".
    """
鈰?---
def _transport_for_url(self, url: URL) -> AsyncBaseTransport
鈰?---
"""
        Build and send a request.

        Equivalent to:

        ```python
        request = client.build_request(...)
        response = await client.send(request, ...)
        ```

        See `AsyncClient.build_request()`, `AsyncClient.send()`
        and [Merging of configuration][0] for how the various parameters
        are merged with client-level configuration.

        [0]: /advanced/clients/#merging-of-configuration
        """
鈰?---
if cookies is not None:  # pragma: no cover
鈰?---
response = await self.send(
鈰?---
"""
        Send a request.

        The request is sent as-is, unmodified.

        Typically you'll want to build one with `AsyncClient.build_request()`
        so that any client-level configuration is merged into the request,
        but passing an explicit `httpx.Request()` is supported as well.

        See also: [Request instances][0]

        [0]: /advanced/clients/#request-instances
        """
鈰?---
response = await self._send_handling_auth(
鈰?---
auth_flow = auth.async_auth_flow(request)
鈰?---
request = await auth_flow.__anext__()
鈰?---
response = await self._send_handling_redirects(
鈰?---
next_request = await auth_flow.asend(response)
鈰?---
response = await self._send_single_request(request)
鈰?---
async def _send_single_request(self, request: Request) -> Response
鈰?---
response = await transport.handle_async_request(request)
鈰?---
async def __aenter__(self: U) -> U
</file>

<file path="_config.py">
import ssl  # pragma: no cover
鈰?---
__all__ = ["Limits", "Proxy", "Timeout", "create_ssl_context"]
鈰?---
class UnsetType
鈰?---
pass  # pragma: no cover
鈰?---
UNSET = UnsetType()
鈰?---
if trust_env and os.environ.get("SSL_CERT_FILE"):  # pragma: nocover
ctx = ssl.create_default_context(cafile=os.environ["SSL_CERT_FILE"])
elif trust_env and os.environ.get("SSL_CERT_DIR"):  # pragma: nocover
ctx = ssl.create_default_context(capath=os.environ["SSL_CERT_DIR"])
鈰?---
# Default case...
ctx = ssl.create_default_context(cafile=certifi.where())
鈰?---
ctx = ssl.SSLContext(ssl.PROTOCOL_TLS_CLIENT)
鈰?---
elif isinstance(verify, str):  # pragma: nocover
message = (
鈰?---
ctx = verify
鈰?---
if cert:  # pragma: nocover
鈰?---
class Timeout
鈰?---
"""
    Timeout configuration.

    **Usage**:

    Timeout(None)               # No timeouts.
    Timeout(5.0)                # 5s timeout on all operations.
    Timeout(None, connect=5.0)  # 5s timeout on connect, no other timeouts.
    Timeout(5.0, connect=10.0)  # 10s timeout on connect. 5s timeout elsewhere.
    Timeout(5.0, pool=None)     # No timeout on acquiring connection from pool.
                                # 5s timeout elsewhere.
    """
鈰?---
# Passed as a single explicit Timeout.
鈰?---
self.connect = timeout.connect  # type: typing.Optional[float]
self.read = timeout.read  # type: typing.Optional[float]
self.write = timeout.write  # type: typing.Optional[float]
self.pool = timeout.pool  # type: typing.Optional[float]
鈰?---
# Passed as a tuple.
鈰?---
def as_dict(self) -> dict[str, float | None]
鈰?---
def __eq__(self, other: typing.Any) -> bool
鈰?---
def __repr__(self) -> str
鈰?---
class_name = self.__class__.__name__
鈰?---
class Limits
鈰?---
"""
    Configuration for limits to various client behaviors.

    **Parameters:**

    * **max_connections** - The maximum number of concurrent connections that may be
            established.
    * **max_keepalive_connections** - Allow the connection pool to maintain
            keep-alive connections below this point. Should be less than or equal
            to `max_connections`.
    * **keepalive_expiry** - Time limit on idle keep-alive connections in seconds.
    """
鈰?---
class Proxy
鈰?---
url = URL(url)
headers = Headers(headers)
鈰?---
# Remove any auth credentials from the URL.
auth = (url.username, url.password)
url = url.copy_with(username=None, password=None)
鈰?---
@property
    def raw_auth(self) -> tuple[bytes, bytes] | None
鈰?---
# The proxy authentication as raw bytes.
鈰?---
# The authentication is represented with the password component masked.
auth = (self.auth[0], "********") if self.auth else None
鈰?---
# Build a nice concise representation.
url_str = f"{str(self.url)!r}"
auth_str = f", auth={auth!r}" if auth else ""
headers_str = f", headers={dict(self.headers)!r}" if self.headers else ""
鈰?---
DEFAULT_TIMEOUT_CONFIG = Timeout(timeout=5.0)
DEFAULT_LIMITS = Limits(max_connections=100, max_keepalive_connections=20)
DEFAULT_MAX_REDIRECTS = 20
</file>

<file path="_content.py">
__all__ = ["ByteStream"]
鈰?---
class ByteStream(AsyncByteStream, SyncByteStream)
鈰?---
def __init__(self, stream: bytes) -> None
鈰?---
def __iter__(self) -> Iterator[bytes]
鈰?---
async def __aiter__(self) -> AsyncIterator[bytes]
鈰?---
class IteratorByteStream(SyncByteStream)
鈰?---
CHUNK_SIZE = 65_536
鈰?---
def __init__(self, stream: Iterable[bytes]) -> None
鈰?---
# File-like interfaces should use 'read' directly.
chunk = self._stream.read(self.CHUNK_SIZE)
鈰?---
# Otherwise iterate.
鈰?---
class AsyncIteratorByteStream(AsyncByteStream)
鈰?---
def __init__(self, stream: AsyncIterable[bytes]) -> None
鈰?---
# File-like interfaces should use 'aread' directly.
chunk = await self._stream.aread(self.CHUNK_SIZE)
鈰?---
class UnattachedStream(AsyncByteStream, SyncByteStream)
鈰?---
"""
    If a request or response is serialized using pickle, then it is no longer
    attached to a stream for I/O purposes. Any stream operations should result
    in `httpx.StreamClosed`.
    """
鈰?---
yield b""  # pragma: no cover
鈰?---
body = content.encode("utf-8") if isinstance(content, str) else content
content_length = len(body)
headers = {"Content-Length": str(content_length)} if body else {}
鈰?---
# `not isinstance(content, dict)` is a bit oddly specific, but it
# catches a case that's easy for users to make in error, and would
# otherwise pass through here, like any other bytes-iterable,
# because `dict` happens to be iterable. See issue #2491.
content_length_or_none = peek_filelike_length(content)
鈰?---
headers = {"Transfer-Encoding": "chunked"}
鈰?---
headers = {"Content-Length": str(content_length_or_none)}
return headers, IteratorByteStream(content)  # type: ignore
鈰?---
plain_data = []
鈰?---
body = urlencode(plain_data, doseq=True).encode("utf-8")
content_length = str(len(body))
content_type = "application/x-www-form-urlencoded"
headers = {"Content-Length": content_length, "Content-Type": content_type}
鈰?---
multipart = MultipartStream(data=data, files=files, boundary=boundary)
headers = multipart.get_headers()
鈰?---
def encode_text(text: str) -> tuple[dict[str, str], ByteStream]
鈰?---
body = text.encode("utf-8")
鈰?---
content_type = "text/plain; charset=utf-8"
鈰?---
def encode_html(html: str) -> tuple[dict[str, str], ByteStream]
鈰?---
body = html.encode("utf-8")
鈰?---
content_type = "text/html; charset=utf-8"
鈰?---
def encode_json(json: Any) -> tuple[dict[str, str], ByteStream]
鈰?---
body = json_dumps(
鈰?---
content_type = "application/json"
鈰?---
"""
    Handles encoding the given `content`, `data`, `files`, and `json`,
    returning a two-tuple of (<headers>, <stream>).
    """
鈰?---
# We prefer to separate `content=<bytes|str|byte iterator|bytes aiterator>`
# for raw request content, and `data=<form data>` for url encoded or
# multipart form content.
#
# However for compat with requests, we *do* still support
# `data=<bytes...>` usages. We deal with that case here, treating it
# as if `content=<...>` had been supplied instead.
message = "Use 'content=<...>' to upload raw bytes/text content."
鈰?---
"""
    Handles encoding the given `content`, returning a two-tuple of
    (<headers>, <stream>).
    """
</file>

<file path="_decoders.py">
"""
Handlers for Content-Encoding.

See: https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Content-Encoding
"""
鈰?---
# Brotli support is optional
鈰?---
# The C bindings in `brotli` are recommended for CPython.
鈰?---
except ImportError:  # pragma: no cover
鈰?---
# The CFFI bindings in `brotlicffi` are recommended for PyPy
# and other environments.
鈰?---
brotli = None
鈰?---
# Zstandard support is optional
鈰?---
zstandard = None  # type: ignore
鈰?---
class ContentDecoder
鈰?---
def decode(self, data: bytes) -> bytes
鈰?---
raise NotImplementedError()  # pragma: no cover
鈰?---
def flush(self) -> bytes
鈰?---
class IdentityDecoder(ContentDecoder)
鈰?---
"""
    Handle unencoded data.
    """
鈰?---
class DeflateDecoder(ContentDecoder)
鈰?---
"""
    Handle 'deflate' decoding.

    See: https://stackoverflow.com/questions/1838699
    """
鈰?---
def __init__(self) -> None
鈰?---
was_first_attempt = self.first_attempt
鈰?---
except zlib.error as exc:  # pragma: no cover
鈰?---
class GZipDecoder(ContentDecoder)
鈰?---
"""
    Handle 'gzip' decoding.

    See: https://stackoverflow.com/questions/1838699
    """
鈰?---
class BrotliDecoder(ContentDecoder)
鈰?---
"""
    Handle 'brotli' decoding.

    Requires `pip install brotlipy`. See: https://brotlipy.readthedocs.io/
        or   `pip install brotli`. See https://github.com/google/brotli
    Supports both 'brotlipy' and 'Brotli' packages since they share an import
    name. The top branches are for 'brotlipy' and bottom branches for 'Brotli'
    """
鈰?---
if brotli is None:  # pragma: no cover
鈰?---
# The 'brotlicffi' package.
self._decompress = self.decompressor.decompress  # pragma: no cover
鈰?---
# The 'brotli' package.
self._decompress = self.decompressor.process  # pragma: no cover
鈰?---
# Only available in the 'brotlicffi' package.
鈰?---
# As the decompressor decompresses eagerly, this
# will never actually emit any data. However, it will potentially throw
# errors if a truncated or damaged data stream has been used.
self.decompressor.finish()  # pragma: no cover
鈰?---
except brotli.error as exc:  # pragma: no cover
鈰?---
class ZStandardDecoder(ContentDecoder)
鈰?---
"""
    Handle 'zstd' RFC 8878 decoding.

    Requires `pip install zstandard`.
    Can be installed as a dependency of httpx using `pip install httpx[zstd]`.
    """
鈰?---
# inspired by the ZstdDecoder implementation in urllib3
鈰?---
if zstandard is None:  # pragma: no cover
鈰?---
output = io.BytesIO()
鈰?---
unused_data = self.decompressor.unused_data
鈰?---
ret = self.decompressor.flush()  # note: this is a no-op
鈰?---
raise DecodingError("Zstandard data is incomplete")  # pragma: no cover
鈰?---
class MultiDecoder(ContentDecoder)
鈰?---
"""
    Handle the case where multiple encodings have been applied.
    """
鈰?---
def __init__(self, children: typing.Sequence[ContentDecoder]) -> None
鈰?---
"""
        'children' should be a sequence of decoders in the order in which
        each was applied.
        """
# Note that we reverse the order for decoding.
鈰?---
data = child.decode(data)
鈰?---
data = b""
鈰?---
data = child.decode(data) + child.flush()
鈰?---
class ByteChunker
鈰?---
"""
    Handles returning byte content in fixed-size chunks.
    """
鈰?---
def __init__(self, chunk_size: int | None = None) -> None
鈰?---
def decode(self, content: bytes) -> list[bytes]
鈰?---
value = self._buffer.getvalue()
chunks = [
鈰?---
def flush(self) -> list[bytes]
鈰?---
class TextChunker
鈰?---
"""
    Handles returning text content in fixed-size chunks.
    """
鈰?---
def decode(self, content: str) -> list[str]
鈰?---
def flush(self) -> list[str]
鈰?---
class TextDecoder
鈰?---
"""
    Handles incrementally decoding bytes into text
    """
鈰?---
def __init__(self, encoding: str = "utf-8") -> None
鈰?---
def decode(self, data: bytes) -> str
鈰?---
def flush(self) -> str
鈰?---
class LineDecoder
鈰?---
"""
    Handles incrementally reading lines from text.

    Has the same behaviour as the stdllib splitlines,
    but handling the input iteratively.
    """
鈰?---
def decode(self, text: str) -> list[str]
鈰?---
# See https://docs.python.org/3/library/stdtypes.html#str.splitlines
NEWLINE_CHARS = "\n\r\x0b\x0c\x1c\x1d\x1e\x85\u2028\u2029"
鈰?---
# We always push a trailing `\r` into the next decode iteration.
鈰?---
text = "\r" + text
鈰?---
text = text[:-1]
鈰?---
# NOTE: the edge case input of empty text doesn't occur in practice,
# because other httpx internals filter out this value
return []  # pragma: no cover
鈰?---
trailing_newline = text[-1] in NEWLINE_CHARS
lines = text.splitlines()
鈰?---
# No new lines, buffer the input and continue.
鈰?---
# Include any existing buffer in the first portion of the
# splitlines result.
lines = ["".join(self.buffer) + lines[0]] + lines[1:]
鈰?---
# If the last segment of splitlines is not newline terminated,
# then drop it from our output and start a new buffer.
鈰?---
lines = ["".join(self.buffer)]
鈰?---
SUPPORTED_DECODERS = {
鈰?---
SUPPORTED_DECODERS.pop("br")  # pragma: no cover
鈰?---
SUPPORTED_DECODERS.pop("zstd")  # pragma: no cover
</file>

<file path="_exceptions.py">
"""
Our exception hierarchy:

* HTTPError
  x RequestError
    + TransportError
      - TimeoutException
        路 ConnectTimeout
        路 ReadTimeout
        路 WriteTimeout
        路 PoolTimeout
      - NetworkError
        路 ConnectError
        路 ReadError
        路 WriteError
        路 CloseError
      - ProtocolError
        路 LocalProtocolError
        路 RemoteProtocolError
      - ProxyError
      - UnsupportedProtocol
    + DecodingError
    + TooManyRedirects
  x HTTPStatusError
* InvalidURL
* CookieConflict
* StreamError
  x StreamConsumed
  x StreamClosed
  x ResponseNotRead
  x RequestNotRead
"""
鈰?---
from ._models import Request, Response  # pragma: no cover
鈰?---
__all__ = [
鈰?---
class HTTPError(Exception)
鈰?---
"""
    Base class for `RequestError` and `HTTPStatusError`.

    Useful for `try...except` blocks when issuing a request,
    and then calling `.raise_for_status()`.

    For example:

    ```
    try:
        response = httpx.get("https://www.example.com")
        response.raise_for_status()
    except httpx.HTTPError as exc:
        print(f"HTTP Exception for {exc.request.url} - {exc}")
    ```
    """
鈰?---
def __init__(self, message: str) -> None
鈰?---
@property
    def request(self) -> Request
鈰?---
@request.setter
    def request(self, request: Request) -> None
鈰?---
class RequestError(HTTPError)
鈰?---
"""
    Base class for all exceptions that may occur when issuing a `.request()`.
    """
鈰?---
def __init__(self, message: str, *, request: Request | None = None) -> None
鈰?---
# At the point an exception is raised we won't typically have a request
# instance to associate it with.
#
# The 'request_context' context manager is used within the Client and
# Response methods in order to ensure that any raised exceptions
# have a `.request` property set on them.
鈰?---
class TransportError(RequestError)
鈰?---
"""
    Base class for all exceptions that occur at the level of the Transport API.
    """
鈰?---
# Timeout exceptions...
鈰?---
class TimeoutException(TransportError)
鈰?---
"""
    The base class for timeout errors.

    An operation has timed out.
    """
鈰?---
class ConnectTimeout(TimeoutException)
鈰?---
"""
    Timed out while connecting to the host.
    """
鈰?---
class ReadTimeout(TimeoutException)
鈰?---
"""
    Timed out while receiving data from the host.
    """
鈰?---
class WriteTimeout(TimeoutException)
鈰?---
"""
    Timed out while sending data to the host.
    """
鈰?---
class PoolTimeout(TimeoutException)
鈰?---
"""
    Timed out waiting to acquire a connection from the pool.
    """
鈰?---
# Core networking exceptions...
鈰?---
class NetworkError(TransportError)
鈰?---
"""
    The base class for network-related errors.

    An error occurred while interacting with the network.
    """
鈰?---
class ReadError(NetworkError)
鈰?---
"""
    Failed to receive data from the network.
    """
鈰?---
class WriteError(NetworkError)
鈰?---
"""
    Failed to send data through the network.
    """
鈰?---
class ConnectError(NetworkError)
鈰?---
"""
    Failed to establish a connection.
    """
鈰?---
class CloseError(NetworkError)
鈰?---
"""
    Failed to close a connection.
    """
鈰?---
# Other transport exceptions...
鈰?---
class ProxyError(TransportError)
鈰?---
"""
    An error occurred while establishing a proxy connection.
    """
鈰?---
class UnsupportedProtocol(TransportError)
鈰?---
"""
    Attempted to make a request to an unsupported protocol.

    For example issuing a request to `ftp://www.example.com`.
    """
鈰?---
class ProtocolError(TransportError)
鈰?---
"""
    The protocol was violated.
    """
鈰?---
class LocalProtocolError(ProtocolError)
鈰?---
"""
    A protocol was violated by the client.

    For example if the user instantiated a `Request` instance explicitly,
    failed to include the mandatory `Host:` header, and then issued it directly
    using `client.send()`.
    """
鈰?---
class RemoteProtocolError(ProtocolError)
鈰?---
"""
    The protocol was violated by the server.

    For example, returning malformed HTTP.
    """
鈰?---
# Other request exceptions...
鈰?---
class DecodingError(RequestError)
鈰?---
"""
    Decoding of the response failed, due to a malformed encoding.
    """
鈰?---
class TooManyRedirects(RequestError)
鈰?---
"""
    Too many redirects.
    """
鈰?---
# Client errors
鈰?---
class HTTPStatusError(HTTPError)
鈰?---
"""
    The response had an error HTTP status of 4xx or 5xx.

    May be raised when calling `response.raise_for_status()`
    """
鈰?---
def __init__(self, message: str, *, request: Request, response: Response) -> None
鈰?---
class InvalidURL(Exception)
鈰?---
"""
    URL is improperly formed or cannot be parsed.
    """
鈰?---
class CookieConflict(Exception)
鈰?---
"""
    Attempted to lookup a cookie by name, but multiple cookies existed.

    Can occur when calling `response.cookies.get(...)`.
    """
鈰?---
# Stream exceptions...
鈰?---
# These may occur as the result of a programming error, by accessing
# the request/response stream in an invalid manner.
鈰?---
class StreamError(RuntimeError)
鈰?---
"""
    The base class for stream exceptions.

    The developer made an error in accessing the request stream in
    an invalid way.
    """
鈰?---
class StreamConsumed(StreamError)
鈰?---
"""
    Attempted to read or stream content, but the content has already
    been streamed.
    """
鈰?---
def __init__(self) -> None
鈰?---
message = (
鈰?---
class StreamClosed(StreamError)
鈰?---
"""
    Attempted to read or stream response content, but the request has been
    closed.
    """
鈰?---
class ResponseNotRead(StreamError)
鈰?---
"""
    Attempted to access streaming response content, without having called `read()`.
    """
鈰?---
class RequestNotRead(StreamError)
鈰?---
"""
    Attempted to access streaming request content, without having called `read()`.
    """
鈰?---
"""
    A context manager that can be used to attach the given request context
    to any `RequestError` exceptions that are raised within the block.
    """
</file>

<file path="_main.py">
import httpcore  # pragma: no cover
鈰?---
def print_help() -> None
鈰?---
console = rich.console.Console()
鈰?---
table = rich.table.Table.grid(padding=1, pad_edge=True)
鈰?---
def get_lexer_for_response(response: Response) -> str
鈰?---
content_type = response.headers.get("Content-Type")
鈰?---
except pygments.util.ClassNotFound:  # pragma: no cover
鈰?---
return ""  # pragma: no cover
鈰?---
def format_request_headers(request: httpcore.Request, http2: bool = False) -> str
鈰?---
version = "HTTP/2" if http2 else "HTTP/1.1"
headers = [
method = request.method.decode("ascii")
target = request.url.target.decode("ascii")
lines = [f"{method} {target} {version}"] + [
鈰?---
version = http_version.decode("ascii")
reason = (
lines = [f"{version} {status} {reason}"] + [
鈰?---
def print_request_headers(request: httpcore.Request, http2: bool = False) -> None
鈰?---
http_text = format_request_headers(request, http2=http2)
syntax = rich.syntax.Syntax(http_text, "http", theme="ansi_dark", word_wrap=True)
鈰?---
syntax = rich.syntax.Syntax("", "http", theme="ansi_dark", word_wrap=True)
鈰?---
http_text = format_response_headers(http_version, status, reason_phrase, headers)
鈰?---
def print_response(response: Response) -> None
鈰?---
lexer_name = get_lexer_for_response(response)
鈰?---
data = response.json()
text = json.dumps(data, indent=4)
except ValueError:  # pragma: no cover
text = response.text
鈰?---
syntax = rich.syntax.Syntax(text, lexer_name, theme="ansi_dark", word_wrap=True)
鈰?---
_PCTRTT = typing.Tuple[typing.Tuple[str, str], ...]
_PCTRTTT = typing.Tuple[_PCTRTT, ...]
_PeerCertRetDictType = typing.Dict[str, typing.Union[str, _PCTRTTT, _PCTRTT]]
鈰?---
def format_certificate(cert: _PeerCertRetDictType) -> str:  # pragma: no cover
鈰?---
lines = []
鈰?---
host = info["host"]
鈰?---
stream = info["return_value"]
server_addr = stream.get_extra_info("server_addr")
鈰?---
elif name == "connection.start_tls.complete" and verbose:  # pragma: no cover
鈰?---
ssl_object = stream.get_extra_info("ssl_object")
version = ssl_object.version()
cipher = ssl_object.cipher()
server_cert = ssl_object.getpeercert()
alpn = ssl_object.selected_alpn_protocol()
鈰?---
request = info["request"]
鈰?---
elif name == "http2.send_request_headers.started" and verbose:  # pragma: no cover
鈰?---
elif name == "http2.receive_response_headers.complete":  # pragma: no cover
鈰?---
http_version = b"HTTP/2"
reason_phrase = None
鈰?---
def download_response(response: Response, download: typing.BinaryIO) -> None
鈰?---
content_length = response.headers.get("Content-Length")
鈰?---
description = f"Downloading [bold]{rich.markup.escape(download.name)}"
download_task = progress.add_task(
鈰?---
except json.JSONDecodeError:  # pragma: no cover
鈰?---
if password == "-":  # pragma: no cover
password = click.prompt("Password", hide_input=True)
鈰?---
"""
    An HTTP command line client.
    Sends a request and displays the response.
    """
鈰?---
method = "POST" if content or data or files or json else "GET"
鈰?---
files=files,  # type: ignore
</file>

<file path="_models.py">
__all__ = ["Cookies", "Headers", "Request", "Response"]
鈰?---
SENSITIVE_HEADERS = {"authorization", "proxy-authorization"}
鈰?---
def _is_known_encoding(encoding: str) -> bool
鈰?---
"""
    Return `True` if `encoding` is a known codec.
    """
鈰?---
def _normalize_header_key(key: str | bytes, encoding: str | None = None) -> bytes
鈰?---
"""
    Coerce str/bytes into a strictly byte-wise HTTP header key.
    """
鈰?---
def _normalize_header_value(value: str | bytes, encoding: str | None = None) -> bytes
鈰?---
"""
    Coerce str/bytes into a strictly byte-wise HTTP header value.
    """
鈰?---
def _parse_content_type_charset(content_type: str) -> str | None
鈰?---
# We used to use `cgi.parse_header()` here, but `cgi` became a dead battery.
# See: https://peps.python.org/pep-0594/#cgi
msg = email.message.Message()
鈰?---
def _parse_header_links(value: str) -> list[dict[str, str]]
鈰?---
"""
    Returns a list of parsed link headers, for more info see:
    https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Link
    The generic syntax of those is:
    Link: < uri-reference >; param1=value1; param2="value2"
    So for instance:
    Link; '<http:/.../front.jpeg>; type="image/jpeg",<http://.../back.jpeg>;'
    would return
        [
            {"url": "http:/.../front.jpeg", "type": "image/jpeg"},
            {"url": "http://.../back.jpeg"},
        ]
    :param value: HTTP Link entity-header field
    :return: list of parsed link headers
    """
links: list[dict[str, str]] = []
replace_chars = " '\""
value = value.strip(replace_chars)
鈰?---
link = {"url": url.strip("<> '\"")}
鈰?---
v = to_bytes_or_str("[secure]", match_type_of=v)
鈰?---
class Headers(typing.MutableMapping[str, str])
鈰?---
"""
    HTTP headers, as a case-insensitive multi-dict.
    """
鈰?---
self._list = []  # type: typing.List[typing.Tuple[bytes, bytes, bytes]]
鈰?---
bytes_key = _normalize_header_key(k, encoding)
bytes_value = _normalize_header_value(v, encoding)
鈰?---
@property
    def encoding(self) -> str
鈰?---
"""
        Header encoding is mandated as ascii, but we allow fallbacks to utf-8
        or iso-8859-1.
        """
鈰?---
# The else block runs if 'break' did not occur, meaning
# all values fitted the encoding.
鈰?---
# The ISO-8859-1 encoding covers all 256 code points in a byte,
# so will never raise decode errors.
鈰?---
@encoding.setter
    def encoding(self, value: str) -> None
鈰?---
@property
    def raw(self) -> list[tuple[bytes, bytes]]
鈰?---
"""
        Returns a list of the raw header items, as byte pairs.
        """
鈰?---
def keys(self) -> typing.KeysView[str]
鈰?---
def values(self) -> typing.ValuesView[str]
鈰?---
values_dict: dict[str, str] = {}
鈰?---
str_key = key.decode(self.encoding)
str_value = value.decode(self.encoding)
鈰?---
def items(self) -> typing.ItemsView[str, str]
鈰?---
"""
        Return `(key, value)` items of headers. Concatenate headers
        into a single comma separated value when a key occurs multiple times.
        """
鈰?---
def multi_items(self) -> list[tuple[str, str]]
鈰?---
"""
        Return a list of `(key, value)` pairs of headers. Allow multiple
        occurrences of the same key without concatenating into a single
        comma separated value.
        """
鈰?---
def get(self, key: str, default: typing.Any = None) -> typing.Any
鈰?---
"""
        Return a header value. If multiple occurrences of the header occur
        then concatenate them together with commas.
        """
鈰?---
def get_list(self, key: str, split_commas: bool = False) -> list[str]
鈰?---
"""
        Return a list of all header values for a given key.
        If `split_commas=True` is passed, then any comma separated header
        values are split into multiple return strings.
        """
get_header_key = key.lower().encode(self.encoding)
鈰?---
values = [
鈰?---
split_values = []
鈰?---
def update(self, headers: HeaderTypes | None = None) -> None:  # type: ignore
鈰?---
headers = Headers(headers)
鈰?---
def copy(self) -> Headers
鈰?---
def __getitem__(self, key: str) -> str
鈰?---
"""
        Return a single header value.

        If there are multiple headers with the same key, then we concatenate
        them with commas. See: https://tools.ietf.org/html/rfc7230#section-3.2.2
        """
normalized_key = key.lower().encode(self.encoding)
鈰?---
items = [
鈰?---
def __setitem__(self, key: str, value: str) -> None
鈰?---
"""
        Set the header `key` to `value`, removing any duplicate entries.
        Retains insertion order.
        """
set_key = key.encode(self._encoding or "utf-8")
set_value = value.encode(self._encoding or "utf-8")
lookup_key = set_key.lower()
鈰?---
found_indexes = [
鈰?---
idx = found_indexes[0]
鈰?---
def __delitem__(self, key: str) -> None
鈰?---
"""
        Remove the header `key`.
        """
del_key = key.lower().encode(self.encoding)
鈰?---
pop_indexes = [
鈰?---
def __contains__(self, key: typing.Any) -> bool
鈰?---
header_key = key.lower().encode(self.encoding)
鈰?---
def __iter__(self) -> typing.Iterator[typing.Any]
鈰?---
def __len__(self) -> int
鈰?---
def __eq__(self, other: typing.Any) -> bool
鈰?---
other_headers = Headers(other)
鈰?---
self_list = [(key, value) for _, key, value in self._list]
other_list = [(key, value) for _, key, value in other_headers._list]
鈰?---
def __repr__(self) -> str
鈰?---
class_name = self.__class__.__name__
鈰?---
encoding_str = ""
鈰?---
encoding_str = f", encoding={self.encoding!r}"
鈰?---
as_list = list(_obfuscate_sensitive_headers(self.multi_items()))
as_dict = dict(as_list)
鈰?---
no_duplicate_keys = len(as_dict) == len(as_list)
鈰?---
class Request
鈰?---
content_type: str | None = self.headers.get("content-type")
鈰?---
# Load the request body, except for streaming content.
鈰?---
# There's an important distinction between `Request(content=...)`,
# and `Request(stream=...)`.
#
# Using `content=...` implies automatically populated `Host` and content
# headers, of either `Content-Length: ...` or `Transfer-Encoding: chunked`.
鈰?---
# Using `stream=...` will not automatically include *any*
# auto-populated headers.
鈰?---
# As an end-user you don't really need `stream=...`. It's only
# useful when:
鈰?---
# * Preserving the request stream when copying requests, eg for redirects.
# * Creating request instances on the *server-side* of the transport API.
鈰?---
def _prepare(self, default_headers: dict[str, str]) -> None
鈰?---
# Ignore Transfer-Encoding if the Content-Length has been set explicitly.
鈰?---
auto_headers: list[tuple[bytes, bytes]] = []
鈰?---
has_host = "Host" in self.headers
has_content_length = (
鈰?---
@property
    def content(self) -> bytes
鈰?---
def read(self) -> bytes
鈰?---
"""
        Read and return the request content.
        """
鈰?---
# If a streaming request has been read entirely into memory, then
# we can replace the stream with a raw bytes implementation,
# to ensure that any non-replayable streams can still be used.
鈰?---
async def aread(self) -> bytes
鈰?---
url = str(self.url)
鈰?---
def __getstate__(self) -> dict[str, typing.Any]
鈰?---
def __setstate__(self, state: dict[str, typing.Any]) -> None
鈰?---
class Response
鈰?---
# When follow_redirects=False and a redirect is received,
# the client will set `response.next_request`.
鈰?---
# Load the response body, except for streaming content.
鈰?---
# There's an important distinction between `Response(content=...)`,
# and `Response(stream=...)`.
鈰?---
# Using `content=...` implies automatically populated content headers,
# of either `Content-Length: ...` or `Transfer-Encoding: chunked`.
鈰?---
# Using `stream=...` will not automatically include any content headers.
鈰?---
# useful when creating response instances having received a stream
# from the transport API.
鈰?---
@property
    def elapsed(self) -> datetime.timedelta
鈰?---
"""
        Returns the time taken for the complete request/response
        cycle to complete.
        """
鈰?---
@elapsed.setter
    def elapsed(self, elapsed: datetime.timedelta) -> None
鈰?---
@property
    def request(self) -> Request
鈰?---
"""
        Returns the request instance associated to the current response.
        """
鈰?---
@request.setter
    def request(self, value: Request) -> None
鈰?---
@property
    def http_version(self) -> str
鈰?---
http_version: bytes = self.extensions["http_version"]
鈰?---
@property
    def reason_phrase(self) -> str
鈰?---
reason_phrase: bytes = self.extensions["reason_phrase"]
鈰?---
@property
    def url(self) -> URL
鈰?---
"""
        Returns the URL for which the request was made.
        """
鈰?---
@property
    def text(self) -> str
鈰?---
content = self.content
鈰?---
decoder = TextDecoder(encoding=self.encoding or "utf-8")
鈰?---
@property
    def encoding(self) -> str | None
鈰?---
"""
        Return an encoding to use for decoding the byte content into text.
        The priority for determining this is given by...

        * `.encoding = <>` has been set explicitly.
        * The encoding as specified by the charset parameter in the Content-Type header.
        * The encoding as determined by `default_encoding`, which may either be
          a string like "utf-8" indicating the encoding to use, or may be a callable
          which enables charset autodetection.
        """
鈰?---
encoding = self.charset_encoding
鈰?---
encoding = self.default_encoding
鈰?---
encoding = self.default_encoding(self._content)
鈰?---
"""
        Set the encoding to use for decoding the byte content into text.

        If the `text` attribute has been accessed, attempting to set the
        encoding will throw a ValueError.
        """
鈰?---
@property
    def charset_encoding(self) -> str | None
鈰?---
"""
        Return the encoding, as specified by the Content-Type header.
        """
content_type = self.headers.get("Content-Type")
鈰?---
def _get_content_decoder(self) -> ContentDecoder
鈰?---
"""
        Returns a decoder instance which can be used to decode the raw byte
        content, depending on the Content-Encoding used in the response.
        """
鈰?---
decoders: list[ContentDecoder] = []
values = self.headers.get_list("content-encoding", split_commas=True)
鈰?---
value = value.strip().lower()
鈰?---
decoder_cls = SUPPORTED_DECODERS[value]
鈰?---
@property
    def is_informational(self) -> bool
鈰?---
"""
        A property which is `True` for 1xx status codes, `False` otherwise.
        """
鈰?---
@property
    def is_success(self) -> bool
鈰?---
"""
        A property which is `True` for 2xx status codes, `False` otherwise.
        """
鈰?---
@property
    def is_redirect(self) -> bool
鈰?---
"""
        A property which is `True` for 3xx status codes, `False` otherwise.

        Note that not all responses with a 3xx status code indicate a URL redirect.

        Use `response.has_redirect_location` to determine responses with a properly
        formed URL redirection.
        """
鈰?---
@property
    def is_client_error(self) -> bool
鈰?---
"""
        A property which is `True` for 4xx status codes, `False` otherwise.
        """
鈰?---
@property
    def is_server_error(self) -> bool
鈰?---
"""
        A property which is `True` for 5xx status codes, `False` otherwise.
        """
鈰?---
@property
    def is_error(self) -> bool
鈰?---
"""
        A property which is `True` for 4xx and 5xx status codes, `False` otherwise.
        """
鈰?---
@property
    def has_redirect_location(self) -> bool
鈰?---
"""
        Returns True for 3xx responses with a properly formed URL redirection,
        `False` otherwise.
        """
鈰?---
# 301 (Cacheable redirect. Method may change to GET.)
鈰?---
# 302 (Uncacheable redirect. Method may change to GET.)
鈰?---
# 303 (Client should make a GET or HEAD request.)
鈰?---
# 307 (Equiv. 302, but retain method)
鈰?---
# 308 (Equiv. 301, but retain method)
鈰?---
def raise_for_status(self) -> Response
鈰?---
"""
        Raise the `HTTPStatusError` if one occurred.
        """
request = self._request
鈰?---
message = (
鈰?---
status_class = self.status_code // 100
error_types = {
error_type = error_types.get(status_class, "Invalid status code")
message = message.format(self, error_type=error_type)
鈰?---
def json(self, **kwargs: typing.Any) -> typing.Any
鈰?---
@property
    def cookies(self) -> Cookies
鈰?---
@property
    def links(self) -> dict[str | None, dict[str, str]]
鈰?---
"""
        Returns the parsed header links of the response, if any
        """
header = self.headers.get("link")
鈰?---
@property
    def num_bytes_downloaded(self) -> int
鈰?---
"""
        Read and return the response content.
        """
鈰?---
def iter_bytes(self, chunk_size: int | None = None) -> typing.Iterator[bytes]
鈰?---
"""
        A byte-iterator over the decoded response content.
        This allows us to handle gzip, deflate, brotli, and zstd encoded responses.
        """
鈰?---
chunk_size = len(self._content) if chunk_size is None else chunk_size
鈰?---
decoder = self._get_content_decoder()
chunker = ByteChunker(chunk_size=chunk_size)
鈰?---
decoded = decoder.decode(raw_bytes)
鈰?---
decoded = decoder.flush()
鈰?---
yield chunk  # pragma: no cover
鈰?---
def iter_text(self, chunk_size: int | None = None) -> typing.Iterator[str]
鈰?---
"""
        A str-iterator over the decoded response content
        that handles both gzip, deflate, etc but also detects the content's
        string encoding.
        """
鈰?---
chunker = TextChunker(chunk_size=chunk_size)
鈰?---
text_content = decoder.decode(byte_content)
鈰?---
text_content = decoder.flush()
鈰?---
yield chunk  # pragma: no cover
鈰?---
def iter_lines(self) -> typing.Iterator[str]
鈰?---
decoder = LineDecoder()
鈰?---
def iter_raw(self, chunk_size: int | None = None) -> typing.Iterator[bytes]
鈰?---
"""
        A byte-iterator over the raw response content.
        """
鈰?---
def close(self) -> None
鈰?---
"""
        Close the response and release the connection.
        Automatically called if the response body is read to completion.
        """
鈰?---
async def aiter_lines(self) -> typing.AsyncIterator[str]
鈰?---
async def aclose(self) -> None
鈰?---
class Cookies(typing.MutableMapping[str, str])
鈰?---
"""
    HTTP Cookies, as a mutable mapping.
    """
鈰?---
def __init__(self, cookies: CookieTypes | None = None) -> None
鈰?---
def extract_cookies(self, response: Response) -> None
鈰?---
"""
        Loads any cookies based on the response `Set-Cookie` headers.
        """
urllib_response = self._CookieCompatResponse(response)
urllib_request = self._CookieCompatRequest(response.request)
鈰?---
self.jar.extract_cookies(urllib_response, urllib_request)  # type: ignore
鈰?---
def set_cookie_header(self, request: Request) -> None
鈰?---
"""
        Sets an appropriate 'Cookie:' HTTP header on the `Request`.
        """
urllib_request = self._CookieCompatRequest(request)
鈰?---
def set(self, name: str, value: str, domain: str = "", path: str = "/") -> None
鈰?---
"""
        Set a cookie value by name. May optionally include domain and path.
        """
kwargs = {
cookie = Cookie(**kwargs)  # type: ignore
鈰?---
def get(  # type: ignore
鈰?---
"""
        Get a cookie by name. May optionally include domain and path
        in order to specify exactly which cookie to retrieve.
        """
value = None
鈰?---
message = f"Multiple cookies exist with name={name}"
鈰?---
value = cookie.value
鈰?---
"""
        Delete a cookie by name. May optionally include domain and path
        in order to specify exactly which cookie to delete.
        """
鈰?---
remove = [
鈰?---
def clear(self, domain: str | None = None, path: str | None = None) -> None
鈰?---
"""
        Delete all cookies. Optionally include a domain and path in
        order to only delete a subset of all the cookies.
        """
args = []
鈰?---
def update(self, cookies: CookieTypes | None = None) -> None:  # type: ignore
鈰?---
cookies = Cookies(cookies)
鈰?---
def __setitem__(self, name: str, value: str) -> None
鈰?---
def __getitem__(self, name: str) -> str
鈰?---
value = self.get(name)
鈰?---
def __delitem__(self, name: str) -> None
鈰?---
def __iter__(self) -> typing.Iterator[str]
鈰?---
def __bool__(self) -> bool
鈰?---
cookies_repr = ", ".join(
鈰?---
class _CookieCompatRequest(urllib.request.Request)
鈰?---
"""
        Wraps a `Request` instance up in a compatibility interface suitable
        for use with `CookieJar` operations.
        """
鈰?---
def __init__(self, request: Request) -> None
鈰?---
def add_unredirected_header(self, key: str, value: str) -> None
鈰?---
class _CookieCompatResponse
鈰?---
def __init__(self, response: Response) -> None
鈰?---
def info(self) -> email.message.Message
鈰?---
info = email.message.Message()
鈰?---
# Note that setting `info[key]` here is an "append" operation,
# not a "replace" operation.
# https://docs.python.org/3/library/email.compat32-message.html#email.message.Message.__setitem__
</file>

<file path="_multipart.py">
_HTML5_FORM_ENCODING_REPLACEMENTS = {'"': "%22", "\\": "\\\\"}
鈰?---
_HTML5_FORM_ENCODING_RE = re.compile(
鈰?---
def _format_form_param(name: str, value: str) -> bytes
鈰?---
"""
    Encode a name/value pair within a multipart form.
    """
鈰?---
def replacer(match: typing.Match[str]) -> str
鈰?---
value = _HTML5_FORM_ENCODING_RE.sub(replacer, value)
鈰?---
def _guess_content_type(filename: str | None) -> str | None
鈰?---
"""
    Guesses the mimetype based on a filename. Defaults to `application/octet-stream`.

    Returns `None` if `filename` is `None` or empty.
    """
鈰?---
# parse boundary according to
# https://www.rfc-editor.org/rfc/rfc2046#section-5.1.1
鈰?---
class DataField
鈰?---
"""
    A single form field item, within a multipart form field.
    """
鈰?---
def __init__(self, name: str, value: str | bytes | int | float | None) -> None
鈰?---
def render_headers(self) -> bytes
鈰?---
name = _format_form_param("name", self.name)
鈰?---
def render_data(self) -> bytes
鈰?---
def get_length(self) -> int
鈰?---
headers = self.render_headers()
data = self.render_data()
鈰?---
def render(self) -> typing.Iterator[bytes]
鈰?---
class FileField
鈰?---
"""
    A single file field item, within a multipart form field.
    """
鈰?---
CHUNK_SIZE = 64 * 1024
鈰?---
def __init__(self, name: str, value: FileTypes) -> None
鈰?---
fileobj: FileContent
鈰?---
headers: dict[str, str] = {}
content_type: str | None = None
鈰?---
# This large tuple based API largely mirror's requests' API
# It would be good to think of better APIs for this that we could
# include in httpx 2.0 since variable length tuples(especially of 4 elements)
# are quite unwieldly
鈰?---
# neither the 3rd parameter (content_type) nor the 4th (headers)
# was included
鈰?---
# all 4 parameters included
filename, fileobj, content_type, headers = value  # type: ignore
鈰?---
filename = Path(str(getattr(value, "name", "upload"))).name
fileobj = value
鈰?---
content_type = _guess_content_type(filename)
鈰?---
has_content_type_header = any("content-type" in key.lower() for key in headers)
鈰?---
# note that unlike requests, we ignore the content_type provided in the 3rd
# tuple element if it is also included in the headers requests does
# the opposite (it overwrites the headerwith the 3rd tuple element)
鈰?---
def get_length(self) -> int | None
鈰?---
file_length = peek_filelike_length(self.file)
鈰?---
# If we can't determine the filesize without reading it into memory,
# then return `None` here, to indicate an unknown file length.
鈰?---
parts = [
鈰?---
filename = _format_form_param("filename", self.filename)
鈰?---
def render_data(self) -> typing.Iterator[bytes]
鈰?---
chunk = self.file.read(self.CHUNK_SIZE)
鈰?---
class MultipartStream(SyncByteStream, AsyncByteStream)
鈰?---
"""
    Request content as streaming multipart encoded form data.
    """
鈰?---
boundary = os.urandom(16).hex().encode("ascii")
鈰?---
file_items = files.items() if isinstance(files, typing.Mapping) else files
鈰?---
def iter_chunks(self) -> typing.Iterator[bytes]
鈰?---
def get_content_length(self) -> int | None
鈰?---
"""
        Return the length of the multipart encoded content, or `None` if
        any of the files have a length that cannot be determined upfront.
        """
boundary_length = len(self.boundary)
length = 0
鈰?---
field_length = field.get_length()
鈰?---
length += 2 + boundary_length + 2  # b"--{boundary}\r\n"
鈰?---
length += 2  # b"\r\n"
鈰?---
length += 2 + boundary_length + 4  # b"--{boundary}--\r\n"
鈰?---
# Content stream interface.
鈰?---
def get_headers(self) -> dict[str, str]
鈰?---
content_length = self.get_content_length()
content_type = self.content_type
鈰?---
def __iter__(self) -> typing.Iterator[bytes]
鈰?---
async def __aiter__(self) -> typing.AsyncIterator[bytes]
</file>

<file path="_status_codes.py">
__all__ = ["codes"]
鈰?---
class codes(IntEnum)
鈰?---
"""HTTP status codes and reason phrases

    Status codes from the following RFCs are all observed:

        * RFC 7231: Hypertext Transfer Protocol (HTTP/1.1), obsoletes 2616
        * RFC 6585: Additional HTTP Status Codes
        * RFC 3229: Delta encoding in HTTP
        * RFC 4918: HTTP Extensions for WebDAV, obsoletes 2518
        * RFC 5842: Binding Extensions to WebDAV
        * RFC 7238: Permanent Redirect
        * RFC 2295: Transparent Content Negotiation in HTTP
        * RFC 2774: An HTTP Extension Framework
        * RFC 7540: Hypertext Transfer Protocol Version 2 (HTTP/2)
        * RFC 2324: Hyper Text Coffee Pot Control Protocol (HTCPCP/1.0)
        * RFC 7725: An HTTP Status Code to Report Legal Obstacles
        * RFC 8297: An HTTP Status Code for Indicating Hints
        * RFC 8470: Using Early Data in HTTP
    """
鈰?---
def __new__(cls, value: int, phrase: str = "") -> codes
鈰?---
obj = int.__new__(cls, value)
鈰?---
obj.phrase = phrase  # type: ignore[attr-defined]
鈰?---
def __str__(self) -> str
鈰?---
@classmethod
    def get_reason_phrase(cls, value: int) -> str
鈰?---
return codes(value).phrase  # type: ignore
鈰?---
@classmethod
    def is_informational(cls, value: int) -> bool
鈰?---
"""
        Returns `True` for 1xx status codes, `False` otherwise.
        """
鈰?---
@classmethod
    def is_success(cls, value: int) -> bool
鈰?---
"""
        Returns `True` for 2xx status codes, `False` otherwise.
        """
鈰?---
@classmethod
    def is_redirect(cls, value: int) -> bool
鈰?---
"""
        Returns `True` for 3xx status codes, `False` otherwise.
        """
鈰?---
@classmethod
    def is_client_error(cls, value: int) -> bool
鈰?---
"""
        Returns `True` for 4xx status codes, `False` otherwise.
        """
鈰?---
@classmethod
    def is_server_error(cls, value: int) -> bool
鈰?---
"""
        Returns `True` for 5xx status codes, `False` otherwise.
        """
鈰?---
@classmethod
    def is_error(cls, value: int) -> bool
鈰?---
"""
        Returns `True` for 4xx or 5xx status codes, `False` otherwise.
        """
鈰?---
# informational
CONTINUE = 100, "Continue"
SWITCHING_PROTOCOLS = 101, "Switching Protocols"
PROCESSING = 102, "Processing"
EARLY_HINTS = 103, "Early Hints"
鈰?---
# success
OK = 200, "OK"
CREATED = 201, "Created"
ACCEPTED = 202, "Accepted"
NON_AUTHORITATIVE_INFORMATION = 203, "Non-Authoritative Information"
NO_CONTENT = 204, "No Content"
RESET_CONTENT = 205, "Reset Content"
PARTIAL_CONTENT = 206, "Partial Content"
MULTI_STATUS = 207, "Multi-Status"
ALREADY_REPORTED = 208, "Already Reported"
IM_USED = 226, "IM Used"
鈰?---
# redirection
MULTIPLE_CHOICES = 300, "Multiple Choices"
MOVED_PERMANENTLY = 301, "Moved Permanently"
FOUND = 302, "Found"
SEE_OTHER = 303, "See Other"
NOT_MODIFIED = 304, "Not Modified"
USE_PROXY = 305, "Use Proxy"
TEMPORARY_REDIRECT = 307, "Temporary Redirect"
PERMANENT_REDIRECT = 308, "Permanent Redirect"
鈰?---
# client error
BAD_REQUEST = 400, "Bad Request"
UNAUTHORIZED = 401, "Unauthorized"
PAYMENT_REQUIRED = 402, "Payment Required"
FORBIDDEN = 403, "Forbidden"
NOT_FOUND = 404, "Not Found"
METHOD_NOT_ALLOWED = 405, "Method Not Allowed"
NOT_ACCEPTABLE = 406, "Not Acceptable"
PROXY_AUTHENTICATION_REQUIRED = 407, "Proxy Authentication Required"
REQUEST_TIMEOUT = 408, "Request Timeout"
CONFLICT = 409, "Conflict"
GONE = 410, "Gone"
LENGTH_REQUIRED = 411, "Length Required"
PRECONDITION_FAILED = 412, "Precondition Failed"
REQUEST_ENTITY_TOO_LARGE = 413, "Request Entity Too Large"
REQUEST_URI_TOO_LONG = 414, "Request-URI Too Long"
UNSUPPORTED_MEDIA_TYPE = 415, "Unsupported Media Type"
REQUESTED_RANGE_NOT_SATISFIABLE = 416, "Requested Range Not Satisfiable"
EXPECTATION_FAILED = 417, "Expectation Failed"
IM_A_TEAPOT = 418, "I'm a teapot"
MISDIRECTED_REQUEST = 421, "Misdirected Request"
UNPROCESSABLE_ENTITY = 422, "Unprocessable Entity"
LOCKED = 423, "Locked"
FAILED_DEPENDENCY = 424, "Failed Dependency"
TOO_EARLY = 425, "Too Early"
UPGRADE_REQUIRED = 426, "Upgrade Required"
PRECONDITION_REQUIRED = 428, "Precondition Required"
TOO_MANY_REQUESTS = 429, "Too Many Requests"
REQUEST_HEADER_FIELDS_TOO_LARGE = 431, "Request Header Fields Too Large"
UNAVAILABLE_FOR_LEGAL_REASONS = 451, "Unavailable For Legal Reasons"
鈰?---
# server errors
INTERNAL_SERVER_ERROR = 500, "Internal Server Error"
NOT_IMPLEMENTED = 501, "Not Implemented"
BAD_GATEWAY = 502, "Bad Gateway"
SERVICE_UNAVAILABLE = 503, "Service Unavailable"
GATEWAY_TIMEOUT = 504, "Gateway Timeout"
HTTP_VERSION_NOT_SUPPORTED = 505, "HTTP Version Not Supported"
VARIANT_ALSO_NEGOTIATES = 506, "Variant Also Negotiates"
INSUFFICIENT_STORAGE = 507, "Insufficient Storage"
LOOP_DETECTED = 508, "Loop Detected"
NOT_EXTENDED = 510, "Not Extended"
NETWORK_AUTHENTICATION_REQUIRED = 511, "Network Authentication Required"
鈰?---
# Include lower-case styles for `requests` compatibility.
</file>

<file path="_transports/__init__.py">
__all__ = [
</file>

<file path="_transports/asgi.py">
if typing.TYPE_CHECKING:  # pragma: no cover
鈰?---
Event = typing.Union[asyncio.Event, trio.Event]
鈰?---
_Message = typing.MutableMapping[str, typing.Any]
_Receive = typing.Callable[[], typing.Awaitable[_Message]]
_Send = typing.Callable[
_ASGIApp = typing.Callable[
鈰?---
__all__ = ["ASGITransport"]
鈰?---
def is_running_trio() -> bool
鈰?---
# sniffio is a dependency of trio.
鈰?---
# See https://github.com/python-trio/trio/issues/2802
鈰?---
except ImportError:  # pragma: nocover
鈰?---
def create_event() -> Event
鈰?---
class ASGIResponseStream(AsyncByteStream)
鈰?---
def __init__(self, body: list[bytes]) -> None
鈰?---
async def __aiter__(self) -> typing.AsyncIterator[bytes]
鈰?---
class ASGITransport(AsyncBaseTransport)
鈰?---
"""
    A custom AsyncTransport that handles sending requests directly to an ASGI app.

    ```python
    transport = httpx.ASGITransport(
        app=app,
        root_path="/submount",
        client=("*******", 123)
    )
    client = httpx.AsyncClient(transport=transport)
    ```

    Arguments:

    * `app` - The ASGI application.
    * `raise_app_exceptions` - Boolean indicating if exceptions in the application
       should be raised. Default to `True`. Can be set to `False` for use cases
       such as testing the content of a client 500 response.
    * `root_path` - The root path on which the ASGI application should be mounted.
    * `client` - A two-tuple indicating the client IP and port of incoming requests.
    ```
    """
鈰?---
# ASGI scope.
scope = {
鈰?---
# Request.
request_body_chunks = request.stream.__aiter__()
request_complete = False
鈰?---
# Response.
status_code = None
response_headers = None
body_parts = []
response_started = False
response_complete = create_event()
鈰?---
# ASGI callables.
鈰?---
async def receive() -> dict[str, typing.Any]
鈰?---
body = await request_body_chunks.__anext__()
鈰?---
request_complete = True
鈰?---
async def send(message: typing.MutableMapping[str, typing.Any]) -> None
鈰?---
status_code = message["status"]
response_headers = message.get("headers", [])
response_started = True
鈰?---
body = message.get("body", b"")
more_body = message.get("more_body", False)
鈰?---
except Exception:  # noqa: PIE-786
鈰?---
status_code = 500
鈰?---
response_headers = {}
鈰?---
stream = ASGIResponseStream(body_parts)
</file>

<file path="_transports/base.py">
T = typing.TypeVar("T", bound="BaseTransport")
A = typing.TypeVar("A", bound="AsyncBaseTransport")
鈰?---
__all__ = ["AsyncBaseTransport", "BaseTransport"]
鈰?---
class BaseTransport
鈰?---
def __enter__(self: T) -> T
鈰?---
def handle_request(self, request: Request) -> Response
鈰?---
"""
        Send a single HTTP request and return a response.

        Developers shouldn't typically ever need to call into this API directly,
        since the Client class provides all the higher level user-facing API
        niceties.

        In order to properly release any network resources, the response
        stream should *either* be consumed immediately, with a call to
        `response.stream.read()`, or else the `handle_request` call should
        be followed with a try/finally block to ensuring the stream is
        always closed.

        Example usage:

            with httpx.HTTPTransport() as transport:
                req = httpx.Request(
                    method=b"GET",
                    url=(b"https", b"www.example.com", 443, b"/"),
                    headers=[(b"Host", b"www.example.com")],
                )
                resp = transport.handle_request(req)
                body = resp.stream.read()
                print(resp.status_code, resp.headers, body)


        Takes a `Request` instance as the only argument.

        Returns a `Response` instance.
        """
鈰?---
)  # pragma: no cover
鈰?---
def close(self) -> None
鈰?---
class AsyncBaseTransport
鈰?---
async def __aenter__(self: A) -> A
鈰?---
async def aclose(self) -> None
</file>

<file path="_transports/default.py">
"""
Custom transports, with nicely configured defaults.

The following additional keyword arguments are currently supported by httpcore...

* uds: str
* local_address: str
* retries: int

Example usages...

# Disable HTTP/2 on a single specific domain.
mounts = {
    "all://": httpx.HTTPTransport(http2=True),
    "all://*example.org": httpx.HTTPTransport()
}

# Using advanced httpcore configuration, with connection retries.
transport = httpx.HTTPTransport(retries=1)
client = httpx.Client(transport=transport)

# Using advanced httpcore configuration, with unix domain sockets.
transport = httpx.HTTPTransport(uds="socket.uds")
client = httpx.Client(transport=transport)
"""
鈰?---
import ssl  # pragma: no cover
鈰?---
import httpx  # pragma: no cover
鈰?---
T = typing.TypeVar("T", bound="HTTPTransport")
A = typing.TypeVar("A", bound="AsyncHTTPTransport")
鈰?---
SOCKET_OPTION = typing.Union[
鈰?---
__all__ = ["AsyncHTTPTransport", "HTTPTransport"]
鈰?---
HTTPCORE_EXC_MAP: dict[type[Exception], type[httpx.HTTPError]] = {}
鈰?---
def _load_httpcore_exceptions() -> dict[type[Exception], type[httpx.HTTPError]]
鈰?---
@contextlib.contextmanager
def map_httpcore_exceptions() -> typing.Iterator[None]
鈰?---
HTTPCORE_EXC_MAP = _load_httpcore_exceptions()
鈰?---
mapped_exc = None
鈰?---
# We want to map to the most specific exception we can find.
# Eg if `exc` is an `httpcore.ReadTimeout`, we want to map to
# `httpx.ReadTimeout`, not just `httpx.TimeoutException`.
鈰?---
mapped_exc = to_exc
鈰?---
if mapped_exc is None:  # pragma: no cover
鈰?---
message = str(exc)
鈰?---
class ResponseStream(SyncByteStream)
鈰?---
def __init__(self, httpcore_stream: typing.Iterable[bytes]) -> None
鈰?---
def __iter__(self) -> typing.Iterator[bytes]
鈰?---
def close(self) -> None
鈰?---
class HTTPTransport(BaseTransport)
鈰?---
proxy = Proxy(url=proxy) if isinstance(proxy, (str, URL)) else proxy
ssl_context = create_ssl_context(verify=verify, cert=cert, trust_env=trust_env)
鈰?---
import socksio  # noqa
except ImportError:  # pragma: no cover
鈰?---
else:  # pragma: no cover
鈰?---
def __enter__(self: T) -> T:  # Use generics for subclass support.
鈰?---
req = httpcore.Request(
鈰?---
resp = self._pool.handle_request(req)
鈰?---
class AsyncResponseStream(AsyncByteStream)
鈰?---
def __init__(self, httpcore_stream: typing.AsyncIterable[bytes]) -> None
鈰?---
async def __aiter__(self) -> typing.AsyncIterator[bytes]
鈰?---
async def aclose(self) -> None
鈰?---
class AsyncHTTPTransport(AsyncBaseTransport)
鈰?---
async def __aenter__(self: A) -> A:  # Use generics for subclass support.
鈰?---
resp = await self._pool.handle_async_request(req)
</file>

<file path="_transports/mock.py">
SyncHandler = typing.Callable[[Request], Response]
AsyncHandler = typing.Callable[[Request], typing.Coroutine[None, None, Response]]
鈰?---
__all__ = ["MockTransport"]
鈰?---
class MockTransport(AsyncBaseTransport, BaseTransport)
鈰?---
def __init__(self, handler: SyncHandler | AsyncHandler) -> None
鈰?---
response = self.handler(request)
if not isinstance(response, Response):  # pragma: no cover
鈰?---
# Allow handler to *optionally* be an `async` function.
# If it is, then the `response` variable need to be awaited to actually
# return the result.
鈰?---
response = await response
</file>

<file path="_transports/wsgi.py">
from _typeshed import OptExcInfo  # pragma: no cover
from _typeshed.wsgi import WSGIApplication  # pragma: no cover
鈰?---
_T = typing.TypeVar("_T")
鈰?---
__all__ = ["WSGITransport"]
鈰?---
def _skip_leading_empty_chunks(body: typing.Iterable[_T]) -> typing.Iterable[_T]
鈰?---
body = iter(body)
鈰?---
class WSGIByteStream(SyncByteStream)
鈰?---
def __init__(self, result: typing.Iterable[bytes]) -> None
鈰?---
def __iter__(self) -> typing.Iterator[bytes]
鈰?---
def close(self) -> None
鈰?---
class WSGITransport(BaseTransport)
鈰?---
"""
    A custom transport that handles sending requests directly to an WSGI app.
    The simplest way to use this functionality is to use the `app` argument.

    ```
    client = httpx.Client(app=app)
    ```

    Alternatively, you can setup the transport instance explicitly.
    This allows you to include any additional configuration arguments specific
    to the WSGITransport class:

    ```
    transport = httpx.WSGITransport(
        app=app,
        script_name="/submount",
        remote_addr="*******"
    )
    client = httpx.Client(transport=transport)
    ```

    Arguments:

    * `app` - The WSGI application.
    * `raise_app_exceptions` - Boolean indicating if exceptions in the application
       should be raised. Default to `True`. Can be set to `False` for use cases
       such as testing the content of a client 500 response.
    * `script_name` - The root path on which the WSGI application should be mounted.
    * `remote_addr` - A string indicating the client IP of incoming requests.
    ```
    """
鈰?---
def handle_request(self, request: Request) -> Response
鈰?---
wsgi_input = io.BytesIO(request.content)
鈰?---
port = request.url.port or {"http": 80, "https": 443}[request.url.scheme]
environ = {
鈰?---
key = header_key.decode("ascii").upper().replace("-", "_")
鈰?---
key = "HTTP_" + key
鈰?---
seen_status = None
seen_response_headers = None
seen_exc_info = None
鈰?---
seen_status = status
seen_response_headers = response_headers
seen_exc_info = exc_info
鈰?---
result = self.app(environ, start_response)
鈰?---
stream = WSGIByteStream(result)
鈰?---
status_code = int(seen_status.split()[0])
headers = [
</file>

<file path="_types.py">
"""
Type definitions for type checking purposes.
"""
鈰?---
if TYPE_CHECKING:  # pragma: no cover
from ._auth import Auth  # noqa: F401
from ._config import Proxy, Timeout  # noqa: F401
from ._models import Cookies, Headers, Request  # noqa: F401
from ._urls import URL, QueryParams  # noqa: F401
鈰?---
PrimitiveData = Optional[Union[str, int, float, bool]]
鈰?---
URLTypes = Union["URL", str]
鈰?---
QueryParamTypes = Union[
鈰?---
HeaderTypes = Union[
鈰?---
CookieTypes = Union["Cookies", CookieJar, Dict[str, str], List[Tuple[str, str]]]
鈰?---
TimeoutTypes = Union[
ProxyTypes = Union["URL", str, "Proxy"]
CertTypes = Union[str, Tuple[str, str], Tuple[str, str, str]]
鈰?---
AuthTypes = Union[
鈰?---
RequestContent = Union[str, bytes, Iterable[bytes], AsyncIterable[bytes]]
ResponseContent = Union[str, bytes, Iterable[bytes], AsyncIterable[bytes]]
ResponseExtensions = Mapping[str, Any]
鈰?---
RequestData = Mapping[str, Any]
鈰?---
FileContent = Union[IO[bytes], bytes, str]
FileTypes = Union[
鈰?---
# file (or bytes)
鈰?---
# (filename, file (or bytes))
鈰?---
# (filename, file (or bytes), content_type)
鈰?---
# (filename, file (or bytes), content_type, headers)
鈰?---
RequestFiles = Union[Mapping[str, FileTypes], Sequence[Tuple[str, FileTypes]]]
鈰?---
RequestExtensions = Mapping[str, Any]
鈰?---
__all__ = ["AsyncByteStream", "SyncByteStream"]
鈰?---
class SyncByteStream
鈰?---
def __iter__(self) -> Iterator[bytes]
鈰?---
)  # pragma: no cover
yield b""  # pragma: no cover
鈰?---
def close(self) -> None
鈰?---
"""
        Subclasses can override this method to release any network resources
        after a request/response cycle is complete.
        """
鈰?---
class AsyncByteStream
鈰?---
async def __aiter__(self) -> AsyncIterator[bytes]
鈰?---
async def aclose(self) -> None
</file>

<file path="_urlparse.py">
"""
An implementation of `urlparse` that provides URL validation and normalization
as described by RFC3986.

We rely on this implementation rather than the one in Python's stdlib, because:

* It provides more complete URL validation.
* It properly differentiates between an empty querystring and an absent querystring,
  to distinguish URLs with a trailing '?'.
* It handles scheme, hostname, port, and path normalization.
* It supports IDNA hostnames, normalizing them to their encoded form.
* The API supports passing individual components, as well as the complete URL string.

Previously we relied on the excellent `rfc3986` package to handle URL parsing and
validation, but this module provides a simpler alternative, with less indirection
required.
"""
鈰?---
MAX_URL_LENGTH = 65536
鈰?---
# https://datatracker.ietf.org/doc/html/rfc3986.html#section-2.3
UNRESERVED_CHARACTERS = (
SUB_DELIMS = "!$&'()*+,;="
鈰?---
PERCENT_ENCODED_REGEX = re.compile("%[A-Fa-f0-9]{2}")
鈰?---
# https://url.spec.whatwg.org/#percent-encoded-bytes
鈰?---
# The fragment percent-encode set is the C0 control percent-encode set
# and U+0020 SPACE, U+0022 ("), U+003C (<), U+003E (>), and U+0060 (`).
FRAG_SAFE = "".join(
鈰?---
# The query percent-encode set is the C0 control percent-encode set
# and U+0020 SPACE, U+0022 ("), U+0023 (#), U+003C (<), and U+003E (>).
QUERY_SAFE = "".join(
鈰?---
# The path percent-encode set is the query percent-encode set
# and U+003F (?), U+0060 (`), U+007B ({), and U+007D (}).
PATH_SAFE = "".join(
鈰?---
# The userinfo percent-encode set is the path percent-encode set
# and U+002F (/), U+003A (:), U+003B (;), U+003D (=), U+0040 (@),
# U+005B ([) to U+005E (^), inclusive, and U+007C (|).
USERNAME_SAFE = "".join(
PASSWORD_SAFE = "".join(
# Note... The terminology 'userinfo' percent-encode set in the WHATWG document
# is used for the username and password quoting. For the joint userinfo component
# we remove U+003A (:) from the safe set.
USERINFO_SAFE = "".join(
鈰?---
# {scheme}:      (optional)
# //{authority}  (optional)
# {path}
# ?{query}       (optional)
# #{fragment}    (optional)
URL_REGEX = re.compile(
鈰?---
# {userinfo}@    (optional)
# {host}
# :{port}        (optional)
AUTHORITY_REGEX = re.compile(
鈰?---
userinfo=".*",  # Any character sequence.
host="(\\[.*\\]|[^:@]*)",  # Either any character sequence excluding ':' or '@',
# or an IPv6 address enclosed within square brackets.
port=".*",  # Any character sequence.
鈰?---
# If we call urlparse with an individual component, then we need to regex
# validate that component individually.
# Note that we're duplicating the same strings as above. Shock! Horror!!
COMPONENT_REGEX = {
鈰?---
# We use these simple regexs as a first pass before handing off to
# the stdlib 'ipaddress' module for IP address validation.
IPv4_STYLE_HOSTNAME = re.compile(r"^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$")
IPv6_STYLE_HOSTNAME = re.compile(r"^\[.*\]$")
鈰?---
class ParseResult(typing.NamedTuple)
鈰?---
scheme: str
userinfo: str
host: str
port: int | None
path: str
query: str | None
fragment: str | None
鈰?---
@property
    def authority(self) -> str
鈰?---
@property
    def netloc(self) -> str
鈰?---
def copy_with(self, **kwargs: str | None) -> ParseResult
鈰?---
defaults = {
鈰?---
def __str__(self) -> str
鈰?---
authority = self.authority
鈰?---
def urlparse(url: str = "", **kwargs: str | None) -> ParseResult
鈰?---
# Initial basic checks on allowable URLs.
# ---------------------------------------
鈰?---
# Hard limit the maximum allowable URL length.
鈰?---
# If a URL includes any ASCII control characters including \t, \r, \n,
# then treat it as invalid.
鈰?---
char = next(char for char in url if char.isascii() and not char.isprintable())
idx = url.find(char)
error = (
鈰?---
# Some keyword arguments require special handling.
# ------------------------------------------------
鈰?---
# Coerce "port" to a string, if it is provided as an integer.
鈰?---
port = kwargs["port"]
鈰?---
# Replace "netloc" with "host and "port".
鈰?---
netloc = kwargs.pop("netloc") or ""
鈰?---
# Replace "username" and/or "password" with "userinfo".
鈰?---
username = quote(kwargs.pop("username", "") or "", safe=USERNAME_SAFE)
password = quote(kwargs.pop("password", "") or "", safe=PASSWORD_SAFE)
鈰?---
# Replace "raw_path" with "path" and "query".
鈰?---
raw_path = kwargs.pop("raw_path") or ""
鈰?---
# Ensure that IPv6 "host" addresses are always escaped with "[...]".
鈰?---
host = kwargs.get("host") or ""
鈰?---
# If any keyword arguments are provided, ensure they are valid.
# -------------------------------------------------------------
鈰?---
# If a component includes any ASCII control characters including \t, \r, \n,
# then treat it as invalid.
鈰?---
char = next(
idx = value.find(char)
鈰?---
# Ensure that keyword arguments match as a valid regex.
鈰?---
# The URL_REGEX will always match, but may have empty components.
url_match = URL_REGEX.match(url)
鈰?---
url_dict = url_match.groupdict()
鈰?---
# * 'scheme', 'authority', and 'path' may be empty strings.
# * 'query' may be 'None', indicating no trailing "?" portion.
#   Any string including the empty string, indicates a trailing "?".
# * 'fragment' may be 'None', indicating no trailing "#" portion.
#   Any string including the empty string, indicates a trailing "#".
scheme = kwargs.get("scheme", url_dict["scheme"]) or ""
authority = kwargs.get("authority", url_dict["authority"]) or ""
path = kwargs.get("path", url_dict["path"]) or ""
query = kwargs.get("query", url_dict["query"])
frag = kwargs.get("fragment", url_dict["fragment"])
鈰?---
# The AUTHORITY_REGEX will always match, but may have empty components.
authority_match = AUTHORITY_REGEX.match(authority)
鈰?---
authority_dict = authority_match.groupdict()
鈰?---
# * 'userinfo' and 'host' may be empty strings.
# * 'port' may be 'None'.
userinfo = kwargs.get("userinfo", authority_dict["userinfo"]) or ""
host = kwargs.get("host", authority_dict["host"]) or ""
port = kwargs.get("port", authority_dict["port"])
鈰?---
# Normalize and validate each component.
# We end up with a parsed representation of the URL,
# with components that are plain ASCII bytestrings.
parsed_scheme: str = scheme.lower()
parsed_userinfo: str = quote(userinfo, safe=USERINFO_SAFE)
parsed_host: str = encode_host(host)
parsed_port: int | None = normalize_port(port, scheme)
鈰?---
has_scheme = parsed_scheme != ""
has_authority = (
鈰?---
path = normalize_path(path)
鈰?---
parsed_path: str = quote(path, safe=PATH_SAFE)
parsed_query: str | None = None if query is None else quote(query, safe=QUERY_SAFE)
parsed_frag: str | None = None if frag is None else quote(frag, safe=FRAG_SAFE)
鈰?---
# The parsed ASCII bytestrings are our canonical form.
# All properties of the URL are derived from these.
鈰?---
def encode_host(host: str) -> str
鈰?---
# Validate IPv4 hostnames like #.#.#.#
#
# From https://datatracker.ietf.org/doc/html/rfc3986/#section-3.2.2
鈰?---
# IPv4address = dec-octet "." dec-octet "." dec-octet "." dec-octet
鈰?---
# Validate IPv6 hostnames like [...]
鈰?---
# "A host identified by an Internet Protocol literal address, version 6
# [RFC3513] or later, is distinguished by enclosing the IP literal
# within square brackets ("[" and "]").  This is the only place where
# square bracket characters are allowed in the URI syntax."
鈰?---
# Regular ASCII hostnames
鈰?---
# reg-name    = *( unreserved / pct-encoded / sub-delims )
WHATWG_SAFE = '"`{}%|\\'
鈰?---
# IDNA hostnames
鈰?---
def normalize_port(port: str | int | None, scheme: str) -> int | None
鈰?---
# From https://tools.ietf.org/html/rfc3986#section-3.2.3
#
# "A scheme may define a default port.  For example, the "http" scheme
# defines a default port of "80", corresponding to its reserved TCP
# port number.  The type of port designated by the port number (e.g.,
# TCP, UDP, SCTP) is defined by the URI scheme.  URI producers and
# normalizers should omit the port component and its ":" delimiter if
# port is empty or if its value would be the same as that of the
# scheme's default."
鈰?---
port_as_int = int(port)
鈰?---
# See https://url.spec.whatwg.org/#url-miscellaneous
default_port = {"ftp": 21, "http": 80, "https": 443, "ws": 80, "wss": 443}.get(
鈰?---
def validate_path(path: str, has_scheme: bool, has_authority: bool) -> None
鈰?---
"""
    Path validation rules that depend on if the URL contains
    a scheme or authority component.

    See https://datatracker.ietf.org/doc/html/rfc3986.html#section-3.3
    """
鈰?---
# If a URI contains an authority component, then the path component
# must either be empty or begin with a slash ("/") character."
鈰?---
# If a URI does not contain an authority component, then the path cannot begin
# with two slash characters ("//").
鈰?---
# In addition, a URI reference (Section 4.1) may be a relative-path reference,
# in which case the first path segment cannot contain a colon (":") character.
鈰?---
def normalize_path(path: str) -> str
鈰?---
"""
    Drop "." and ".." segments from a URL path.

    For example:

        normalize_path("/path/./to/somewhere/..") == "/path/to"
    """
# Fast return when no '.' characters in the path.
鈰?---
components = path.split("/")
鈰?---
# Fast return when no '.' or '..' components in the path.
鈰?---
# https://datatracker.ietf.org/doc/html/rfc3986#section-5.2.4
output: list[str] = []
鈰?---
def PERCENT(string: str) -> str
鈰?---
def percent_encoded(string: str, safe: str) -> str
鈰?---
"""
    Use percent-encoding to quote a string.
    """
NON_ESCAPED_CHARS = UNRESERVED_CHARACTERS + safe
鈰?---
# Fast path for strings that don't need escaping.
鈰?---
def quote(string: str, safe: str) -> str
鈰?---
"""
    Use percent-encoding to quote a string, omitting existing '%xx' escape sequences.

    See: https://www.rfc-editor.org/rfc/rfc3986#section-2.1

    * `string`: The string to be percent-escaped.
    * `safe`: A string containing characters that may be treated as safe, and do not
        need to be escaped. Unreserved characters are always treated as safe.
        See: https://www.rfc-editor.org/rfc/rfc3986#section-2.3
    """
parts = []
current_position = 0
鈰?---
matched_text = match.group(0)
# Add any text up to the '%xx' escape sequence.
鈰?---
leading_text = string[current_position:start_position]
鈰?---
# Add the '%xx' escape sequence.
鈰?---
current_position = end_position
鈰?---
# Add any text after the final '%xx' escape sequence.
鈰?---
trailing_text = string[current_position:]
</file>

<file path="_urls.py">
__all__ = ["URL", "QueryParams"]
鈰?---
class URL
鈰?---
"""
    url = httpx.URL("HTTPS://jo%40email.com:a%20secret@m眉ller.de:1234/pa%20th?search=ab#anchorlink")

    assert url.scheme == "https"
    assert url.username == "<EMAIL>"
    assert url.password == "a secret"
    assert url.userinfo == b"jo%40email.com:a%20secret"
    assert url.host == "m眉ller.de"
    assert url.raw_host == b"xn--mller-kva.de"
    assert url.port == 1234
    assert url.netloc == b"xn--mller-kva.de:1234"
    assert url.path == "/pa th"
    assert url.query == b"?search=ab"
    assert url.raw_path == b"/pa%20th?search=ab"
    assert url.fragment == "anchorlink"

    The components of a URL are broken down like this:

       https://jo%40email.com:a%20secret@m眉ller.de:1234/pa%20th?search=ab#anchorlink
    [scheme]   [  username  ] [password] [ host ][port][ path ] [ query ] [fragment]
               [       userinfo        ] [   netloc   ][    raw_path    ]

    Note that:

    * `url.scheme` is normalized to always be lowercased.

    * `url.host` is normalized to always be lowercased. Internationalized domain
      names are represented in unicode, without IDNA encoding applied. For instance:

      url = httpx.URL("http://涓浗.icom.museum")
      assert url.host == "涓浗.icom.museum"
      url = httpx.URL("http://xn--fiqs8s.icom.museum")
      assert url.host == "涓浗.icom.museum"

    * `url.raw_host` is normalized to always be lowercased, and is IDNA encoded.

      url = httpx.URL("http://涓浗.icom.museum")
      assert url.raw_host == b"xn--fiqs8s.icom.museum"
      url = httpx.URL("http://xn--fiqs8s.icom.museum")
      assert url.raw_host == b"xn--fiqs8s.icom.museum"

    * `url.port` is either None or an integer. URLs that include the default port for
      "http", "https", "ws", "wss", and "ftp" schemes have their port
      normalized to `None`.

      assert httpx.URL("http://example.com") == httpx.URL("http://example.com:80")
      assert httpx.URL("http://example.com").port is None
      assert httpx.URL("http://example.com:80").port is None

    * `url.userinfo` is raw bytes, without URL escaping. Usually you'll want to work
      with `url.username` and `url.password` instead, which handle the URL escaping.

    * `url.raw_path` is raw bytes of both the path and query, without URL escaping.
      This portion is used as the target when constructing HTTP requests. Usually you'll
      want to work with `url.path` instead.

    * `url.query` is raw bytes, without URL escaping. A URL query string portion can
      only be properly URL escaped when decoding the parameter names and values
      themselves.
    """
鈰?---
def __init__(self, url: URL | str = "", **kwargs: typing.Any) -> None
鈰?---
allowed = {
鈰?---
# Perform type checking for all supported keyword arguments.
鈰?---
message = f"{key!r} is an invalid keyword argument for URL()"
鈰?---
expected = allowed[key].__name__
seen = type(value).__name__
message = f"Argument {key!r} must be {expected} but got {seen}"
鈰?---
# Replace any "params" keyword with the raw "query" instead.
#
# Ensure that empty params use `kwargs["query"] = None` rather
# than `kwargs["query"] = ""`, so that generated URLs do not
# include an empty trailing "?".
params = kwargs.pop("params")
鈰?---
@property
    def scheme(self) -> str
鈰?---
"""
        The URL scheme, such as "http", "https".
        Always normalised to lowercase.
        """
鈰?---
@property
    def raw_scheme(self) -> bytes
鈰?---
"""
        The raw bytes representation of the URL scheme, such as b"http", b"https".
        Always normalised to lowercase.
        """
鈰?---
@property
    def userinfo(self) -> bytes
鈰?---
"""
        The URL userinfo as a raw bytestring.
        For example: b"jo%40email.com:a%20secret".
        """
鈰?---
@property
    def username(self) -> str
鈰?---
"""
        The URL username as a string, with URL decoding applied.
        For example: "<EMAIL>"
        """
userinfo = self._uri_reference.userinfo
鈰?---
@property
    def password(self) -> str
鈰?---
"""
        The URL password as a string, with URL decoding applied.
        For example: "a secret"
        """
鈰?---
@property
    def host(self) -> str
鈰?---
"""
        The URL host as a string.
        Always normalized to lowercase, with IDNA hosts decoded into unicode.

        Examples:

        url = httpx.URL("http://www.EXAMPLE.org")
        assert url.host == "www.example.org"

        url = httpx.URL("http://涓浗.icom.museum")
        assert url.host == "涓浗.icom.museum"

        url = httpx.URL("http://xn--fiqs8s.icom.museum")
        assert url.host == "涓浗.icom.museum"

        url = httpx.URL("https://[::ffff:***********]")
        assert url.host == "::ffff:***********"
        """
host: str = self._uri_reference.host
鈰?---
host = idna.decode(host)
鈰?---
@property
    def raw_host(self) -> bytes
鈰?---
"""
        The raw bytes representation of the URL host.
        Always normalized to lowercase, and IDNA encoded.

        Examples:

        url = httpx.URL("http://www.EXAMPLE.org")
        assert url.raw_host == b"www.example.org"

        url = httpx.URL("http://涓浗.icom.museum")
        assert url.raw_host == b"xn--fiqs8s.icom.museum"

        url = httpx.URL("http://xn--fiqs8s.icom.museum")
        assert url.raw_host == b"xn--fiqs8s.icom.museum"

        url = httpx.URL("https://[::ffff:***********]")
        assert url.raw_host == b"::ffff:***********"
        """
鈰?---
@property
    def port(self) -> int | None
鈰?---
"""
        The URL port as an integer.

        Note that the URL class performs port normalization as per the WHATWG spec.
        Default ports for "http", "https", "ws", "wss", and "ftp" schemes are always
        treated as `None`.

        For example:

        assert httpx.URL("http://www.example.com") == httpx.URL("http://www.example.com:80")
        assert httpx.URL("http://www.example.com:80").port is None
        """
鈰?---
@property
    def netloc(self) -> bytes
鈰?---
"""
        Either `<host>` or `<host>:<port>` as bytes.
        Always normalized to lowercase, and IDNA encoded.

        This property may be used for generating the value of a request
        "Host" header.
        """
鈰?---
@property
    def path(self) -> str
鈰?---
"""
        The URL path as a string. Excluding the query string, and URL decoded.

        For example:

        url = httpx.URL("https://example.com/pa%20th")
        assert url.path == "/pa th"
        """
path = self._uri_reference.path or "/"
鈰?---
@property
    def query(self) -> bytes
鈰?---
"""
        The URL query string, as raw bytes, excluding the leading b"?".

        This is necessarily a bytewise interface, because we cannot
        perform URL decoding of this representation until we've parsed
        the keys and values into a QueryParams instance.

        For example:

        url = httpx.URL("https://example.com/?filter=some%20search%20terms")
        assert url.query == b"filter=some%20search%20terms"
        """
query = self._uri_reference.query or ""
鈰?---
@property
    def params(self) -> QueryParams
鈰?---
"""
        The URL query parameters, neatly parsed and packaged into an immutable
        multidict representation.
        """
鈰?---
@property
    def raw_path(self) -> bytes
鈰?---
"""
        The complete URL path and query string as raw bytes.
        Used as the target when constructing HTTP requests.

        For example:

        GET /users?search=some%20text HTTP/1.1
        Host: www.example.org
        Connection: close
        """
鈰?---
@property
    def fragment(self) -> str
鈰?---
"""
        The URL fragments, as used in HTML anchors.
        As a string, without the leading '#'.
        """
鈰?---
@property
    def is_absolute_url(self) -> bool
鈰?---
"""
        Return `True` for absolute URLs such as 'http://example.com/path',
        and `False` for relative URLs such as '/path'.
        """
# We don't use `.is_absolute` from `rfc3986` because it treats
# URLs with a fragment portion as not absolute.
# What we actually care about is if the URL provides
# a scheme and hostname to which connections should be made.
鈰?---
@property
    def is_relative_url(self) -> bool
鈰?---
"""
        Return `False` for absolute URLs such as 'http://example.com/path',
        and `True` for relative URLs such as '/path'.
        """
鈰?---
def copy_with(self, **kwargs: typing.Any) -> URL
鈰?---
"""
        Copy this URL, returning a new URL with some components altered.
        Accepts the same set of parameters as the components that are made
        available via properties on the `URL` class.

        For example:

        url = httpx.URL("https://www.example.com").copy_with(
            username="<EMAIL>", password="a secret"
        )
        assert url == "https://jo%40email.com:<EMAIL>"
        """
鈰?---
def copy_set_param(self, key: str, value: typing.Any = None) -> URL
鈰?---
def copy_add_param(self, key: str, value: typing.Any = None) -> URL
鈰?---
def copy_remove_param(self, key: str) -> URL
鈰?---
def copy_merge_params(self, params: QueryParamTypes) -> URL
鈰?---
def join(self, url: URL | str) -> URL
鈰?---
"""
        Return an absolute URL, using this URL as the base.

        Eg.

        url = httpx.URL("https://www.example.com/test")
        url = url.join("/new/path")
        assert url == "https://www.example.com/new/path"
        """
鈰?---
def __hash__(self) -> int
鈰?---
def __eq__(self, other: typing.Any) -> bool
鈰?---
def __str__(self) -> str
鈰?---
def __repr__(self) -> str
鈰?---
# Mask any password component.
userinfo = f'{userinfo.split(":")[0]}:[secure]'
鈰?---
authority = "".join(
url = "".join(
鈰?---
@property
    def raw(self) -> tuple[bytes, bytes, int, bytes]:  # pragma: nocover
鈰?---
RawURL = collections.namedtuple(
鈰?---
class QueryParams(typing.Mapping[str, str])
鈰?---
"""
    URL query parameters, as a multi-dict.
    """
鈰?---
def __init__(self, *args: QueryParamTypes | None, **kwargs: typing.Any) -> None
鈰?---
value = args[0] if args else kwargs
鈰?---
value = value.decode("ascii") if isinstance(value, bytes) else value
鈰?---
dict_value: dict[typing.Any, list[typing.Any]] = {}
鈰?---
# Convert list inputs like:
#     [("a", "123"), ("a", "456"), ("b", "789")]
# To a dict representation, like:
#     {"a": ["123", "456"], "b": ["789"]}
鈰?---
# Convert dict inputs like:
#    {"a": "123", "b": ["456", "789"]}
# To dict inputs where values are always lists, like:
#    {"a": ["123"], "b": ["456", "789"]}
dict_value = {
鈰?---
# Ensure that keys and values are neatly coerced to strings.
# We coerce values `True` and `False` to JSON-like "true" and "false"
# representations, and coerce `None` values to the empty string.
鈰?---
def keys(self) -> typing.KeysView[str]
鈰?---
"""
        Return all the keys in the query params.

        Usage:

        q = httpx.QueryParams("a=123&a=456&b=789")
        assert list(q.keys()) == ["a", "b"]
        """
鈰?---
def values(self) -> typing.ValuesView[str]
鈰?---
"""
        Return all the values in the query params. If a key occurs more than once
        only the first item for that key is returned.

        Usage:

        q = httpx.QueryParams("a=123&a=456&b=789")
        assert list(q.values()) == ["123", "789"]
        """
鈰?---
def items(self) -> typing.ItemsView[str, str]
鈰?---
"""
        Return all items in the query params. If a key occurs more than once
        only the first item for that key is returned.

        Usage:

        q = httpx.QueryParams("a=123&a=456&b=789")
        assert list(q.items()) == [("a", "123"), ("b", "789")]
        """
鈰?---
def multi_items(self) -> list[tuple[str, str]]
鈰?---
"""
        Return all items in the query params. Allow duplicate keys to occur.

        Usage:

        q = httpx.QueryParams("a=123&a=456&b=789")
        assert list(q.multi_items()) == [("a", "123"), ("a", "456"), ("b", "789")]
        """
multi_items: list[tuple[str, str]] = []
鈰?---
def get(self, key: typing.Any, default: typing.Any = None) -> typing.Any
鈰?---
"""
        Get a value from the query param for a given key. If the key occurs
        more than once, then only the first value is returned.

        Usage:

        q = httpx.QueryParams("a=123&a=456&b=789")
        assert q.get("a") == "123"
        """
鈰?---
def get_list(self, key: str) -> list[str]
鈰?---
"""
        Get all values from the query param for a given key.

        Usage:

        q = httpx.QueryParams("a=123&a=456&b=789")
        assert q.get_list("a") == ["123", "456"]
        """
鈰?---
def set(self, key: str, value: typing.Any = None) -> QueryParams
鈰?---
"""
        Return a new QueryParams instance, setting the value of a key.

        Usage:

        q = httpx.QueryParams("a=123")
        q = q.set("a", "456")
        assert q == httpx.QueryParams("a=456")
        """
q = QueryParams()
鈰?---
def add(self, key: str, value: typing.Any = None) -> QueryParams
鈰?---
"""
        Return a new QueryParams instance, setting or appending the value of a key.

        Usage:

        q = httpx.QueryParams("a=123")
        q = q.add("a", "456")
        assert q == httpx.QueryParams("a=123&a=456")
        """
鈰?---
def remove(self, key: str) -> QueryParams
鈰?---
"""
        Return a new QueryParams instance, removing the value of a key.

        Usage:

        q = httpx.QueryParams("a=123")
        q = q.remove("a")
        assert q == httpx.QueryParams("")
        """
鈰?---
def merge(self, params: QueryParamTypes | None = None) -> QueryParams
鈰?---
"""
        Return a new QueryParams instance, updated with.

        Usage:

        q = httpx.QueryParams("a=123")
        q = q.merge({"b": "456"})
        assert q == httpx.QueryParams("a=123&b=456")

        q = httpx.QueryParams("a=123")
        q = q.merge({"a": "456", "b": "789"})
        assert q == httpx.QueryParams("a=456&b=789")
        """
q = QueryParams(params)
鈰?---
def __getitem__(self, key: typing.Any) -> str
鈰?---
def __contains__(self, key: typing.Any) -> bool
鈰?---
def __iter__(self) -> typing.Iterator[typing.Any]
鈰?---
def __len__(self) -> int
鈰?---
def __bool__(self) -> bool
鈰?---
class_name = self.__class__.__name__
query_string = str(self)
鈰?---
def update(self, params: QueryParamTypes | None = None) -> None
鈰?---
def __setitem__(self, key: str, value: str) -> None
</file>

<file path="_utils.py">
if typing.TYPE_CHECKING:  # pragma: no cover
鈰?---
def primitive_value_to_str(value: PrimitiveData) -> str
鈰?---
"""
    Coerce a primitive data type into a string value.

    Note that we prefer JSON-style 'true'/'false' for boolean values here.
    """
鈰?---
def get_environment_proxies() -> dict[str, str | None]
鈰?---
"""Gets proxy information from the environment"""
鈰?---
# urllib.request.getproxies() falls back on System
# Registry and Config for proxies on Windows and macOS.
# We don't want to propagate non-HTTP proxies into
# our configuration such as 'TRAVIS_APT_PROXY'.
proxy_info = getproxies()
mounts: dict[str, str | None] = {}
鈰?---
hostname = proxy_info[scheme]
鈰?---
no_proxy_hosts = [host.strip() for host in proxy_info.get("no", "").split(",")]
鈰?---
# See https://curl.haxx.se/libcurl/c/CURLOPT_NOPROXY.html for details
# on how names in `NO_PROXY` are handled.
鈰?---
# If NO_PROXY=* is used or if "*" occurs as any one of the comma
# separated hostnames, then we should just bypass any information
# from HTTP_PROXY, HTTPS_PROXY, ALL_PROXY, and always ignore
# proxies.
鈰?---
# NO_PROXY=.google.com is marked as "all://*.google.com,
#   which disables "www.google.com" but not "google.com"
# NO_PROXY=google.com is marked as "all://*google.com,
#   which disables "www.google.com" and "google.com".
#   (But not "wwwgoogle.com")
# NO_PROXY can include domains, IPv6, IPv4 addresses and "localhost"
#   NO_PROXY=example.com,::1,localhost,***********/16
鈰?---
def to_bytes(value: str | bytes, encoding: str = "utf-8") -> bytes
鈰?---
def to_str(value: str | bytes, encoding: str = "utf-8") -> str
鈰?---
def to_bytes_or_str(value: str, match_type_of: typing.AnyStr) -> typing.AnyStr
鈰?---
def unquote(value: str) -> str
鈰?---
def peek_filelike_length(stream: typing.Any) -> int | None
鈰?---
"""
    Given a file-like stream object, return its length in number of bytes
    without reading it into memory.
    """
鈰?---
# Is it an actual file?
fd = stream.fileno()
# Yup, seems to be an actual file.
length = os.fstat(fd).st_size
鈰?---
# No... Maybe it's something that supports random access, like `io.BytesIO`?
鈰?---
# Assuming so, go to end of stream to figure out its length,
# then put it back in place.
offset = stream.tell()
length = stream.seek(0, os.SEEK_END)
鈰?---
# Not even that? Sorry, we're doomed...
鈰?---
class URLPattern
鈰?---
"""
    A utility class currently used for making lookups against proxy keys...

    # Wildcard matching...
    >>> pattern = URLPattern("all://")
    >>> pattern.matches(httpx.URL("http://example.com"))
    True

    # Witch scheme matching...
    >>> pattern = URLPattern("https://")
    >>> pattern.matches(httpx.URL("https://example.com"))
    True
    >>> pattern.matches(httpx.URL("http://example.com"))
    False

    # With domain matching...
    >>> pattern = URLPattern("https://example.com")
    >>> pattern.matches(httpx.URL("https://example.com"))
    True
    >>> pattern.matches(httpx.URL("http://example.com"))
    False
    >>> pattern.matches(httpx.URL("https://other.com"))
    False

    # Wildcard scheme, with domain matching...
    >>> pattern = URLPattern("all://example.com")
    >>> pattern.matches(httpx.URL("https://example.com"))
    True
    >>> pattern.matches(httpx.URL("http://example.com"))
    True
    >>> pattern.matches(httpx.URL("https://other.com"))
    False

    # With port matching...
    >>> pattern = URLPattern("https://example.com:1234")
    >>> pattern.matches(httpx.URL("https://example.com:1234"))
    True
    >>> pattern.matches(httpx.URL("https://example.com"))
    False
    """
鈰?---
def __init__(self, pattern: str) -> None
鈰?---
url = URL(pattern)
鈰?---
# *.example.com should match "www.example.com", but not "example.com"
domain = re.escape(url.host[2:])
鈰?---
# *example.com should match "www.example.com" and "example.com"
domain = re.escape(url.host[1:])
鈰?---
# example.com should match "example.com" but not "www.example.com"
domain = re.escape(url.host)
鈰?---
def matches(self, other: URL) -> bool
鈰?---
@property
    def priority(self) -> tuple[int, int, int]
鈰?---
"""
        The priority allows URLPattern instances to be sortable, so that
        we can match from most specific to least specific.
        """
# URLs with a port should take priority over URLs without a port.
port_priority = 0 if self.port is not None else 1
# Longer hostnames should match first.
host_priority = -len(self.host)
# Longer schemes should match first.
scheme_priority = -len(self.scheme)
鈰?---
def __hash__(self) -> int
鈰?---
def __lt__(self, other: URLPattern) -> bool
鈰?---
def __eq__(self, other: typing.Any) -> bool
鈰?---
def is_ipv4_hostname(hostname: str) -> bool
鈰?---
def is_ipv6_hostname(hostname: str) -> bool
</file>

</files>
# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/).

## 0.28.1 (6th December, 2024)

* Fix SSL case where `verify=False` together with client side certificates.

## 0.28.0 (28th November, 2024)

Be aware that the default *JSON request bodies now use a more compact representation*. This is generally considered a prefered style, tho may require updates to test suites.

The 0.28 release includes a limited set of deprecations...

**Deprecations**:

We are working towards a simplified SSL configuration API.

*For users of the standard `verify=True` or `verify=False` cases, or `verify=<ssl_context>` case this should require no changes. The following cases have been deprecated...*

* The `verify` argument as a string argument is now deprecated and will raise warnings.
* The `cert` argument is now deprecated and will raise warnings.

Our revised [SSL documentation](docs/advanced/ssl.md) covers how to implement the same behaviour with a more constrained API.

**The following changes are also included**:

* The deprecated `proxies` argument has now been removed.
* The deprecated `app` argument has now been removed.
* JSON request bodies use a compact representation. (#3363)
* Review URL percent escape sets, based on WHATWG spec. (#3371, #3373)
* Ensure `certifi` and `httpcore` are only imported if required. (#3377)
* Treat `socks5h` as a valid proxy scheme. (#3178)
* Cleanup `Request()` method signature in line with `client.request()` and `httpx.request()`. (#3378)
* Bugfix: When passing `params={}`, always strictly update rather than merge with an existing querystring. (#3364)

## 0.27.2 (27th August, 2024)

### Fixed

* Reintroduced supposedly-private `URLTypes` shortcut. (#2673)

## 0.27.1 (27th August, 2024)

### Added

* Support for `zstd` content decoding using the python `zstandard` package is added. Installable using `httpx[zstd]`. (#3139)

### Fixed

* Improved error messaging for `InvalidURL` exceptions. (#3250)
* Fix `app` type signature in `ASGITransport`. (#3109)

## 0.27.0 (21st February, 2024)

### Deprecated

* The `app=...` shortcut has been deprecated. Use the explicit style of `transport=httpx.WSGITransport()` or `transport=httpx.ASGITransport()` instead.

### Fixed

* Respect the `http1` argument while configuring proxy transports. (#3023)
* Fix RFC 2069 mode digest authentication. (#3045)


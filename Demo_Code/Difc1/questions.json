[{"id": "Q1", "text": "httpx.Client() 类的主要用途和优势是什么？"}, {"id": "Q2", "text": "最近对 'request()' 方法的变更是如何提升性能的？"}, {"id": "Q3", "text": "最近的代码更新中，哪个变更对响应速度或内存消耗产生了优化影响？"}, {"id": "Q4", "text": "httpx如何处理连接池和认证？"}, {"id": "Q5", "text": "描述一下 httpx 的代理配置和使用方式。"}, {"id": "Q6", "text": "httpx 中的异步请求是如何提高效率的？"}, {"id": "Q7", "text": "在 httpx 中，使用同步客户端和异步客户端有何性能差异？"}, {"id": "Q8", "text": "最近在 httpx 中做出的哪些改动提高了内存使用效率？"}]
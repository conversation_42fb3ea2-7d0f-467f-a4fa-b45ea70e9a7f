import json
import os
from pathlib import Path
import requests
import pandas as pd
import matplotlib.pyplot as plt
from sklearn.metrics import precision_score, recall_score, f1_score

# === 配置 ===
API_KEY = "sk-7a1fda31b49246af842b66ed3edf2326"
API_BASE = "https://api.deepseek.com/v1"  # 或你的代理地址
MODEL = "deepseek-chat"

HEADERS = {
    "Authorization": f"Bearer {API_KEY}",
    "Content-Type": "application/json"
}

CONTEXT_FILES = {
    "rag1": "rag1.md",
    "rag2": "rag2.md",
    "rag3": "rag3.md",
}
QUESTIONS_FILE = "questions.json"
OUTPUT_DIR = "results"

PROMPT_TEMPLATE = """你将看到一份项目文档的上下文内容，请仅基于它回答下面的问题，不要推理或添加任何额外的内容。

<上下文开始>
{context}
<上下文结束>

---

请回答这个问题：{question}
"""



# 自动生成标准答案
def generate_standard_answer(question):
    """
    从标准答案集获取每个问题的正确答案
    """
    standard_answers = {
        "Q1": "httpx.Client() 是一个高性能的同步 HTTP 客户端，支持持久连接、会话管理和 cookie 处理。",
        "Q2": "最近对 'request()' 方法的变更涉及优化了内部请求处理逻辑，具体引入了连接池和持久连接。",
        "Q3": "引入了异步处理 I/O 操作的方式，减少了阻塞操作，提高了吞吐量。",
        "Q4": "httpx 使用连接池来复用已建立的连接，并支持自动处理认证。",
        "Q5": "httpx 支持多种代理配置方式，如 HTTP 代理、SOCKS 代理等。",
        "Q6": "httpx 使用异步客户端（AsyncClient）来处理并发请求，提高效率。",
        "Q7": "异步客户端比同步客户端在高并发场景下更有效，因为它避免了阻塞操作。",
        "Q8": "通过连接池改进和内存管理优化，httpx 提高了内存使用效率。"
    }

    # 返回标准答案
    return standard_answers.get(question, "未提供标准答案")


def load_questions():
    try:
        # 确保路径正确
        file_path = os.path.join(os.path.dirname(__file__), QUESTIONS_FILE)
        print(f"尝试加载: {file_path}")
        
        with open(file_path, "r", encoding="utf-8") as f:
            data = json.load(f)
            print("成功加载 questions.json")
            return data
    except Exception as e:
        print(f"❌ 加载失败: {e}")
        return None

def load_context(path):
    # 先检查文件是否存在
    path = os.path.join("Difc1", path)  # 路径拼接：Difc1/xxx.md
    if not os.path.exists(path):
        print(f"❌ 文件不存在: {os.path.abspath(path)}")
        print(f"当前目录内容: {os.listdir()}")
        return ""
    
    with open(path, "r", encoding="utf-8-sig") as f:
        return f.read()


def save_answer(rag_id, question_id, answer):
    Path(OUTPUT_DIR).mkdir(exist_ok=True)
    with open(f"{OUTPUT_DIR}/{rag_id}_{question_id}.txt", "w", encoding="utf-8") as f:
        f.write(answer)


def ask_deepseek(prompt):
    url = f"{API_BASE}/chat/completions"
    data = {
        "model": MODEL,
        "messages": [{"role": "user", "content": prompt}],
        "temperature": 0.3
    }

    response = requests.post(url, headers=HEADERS, json=data)
    if response.status_code != 200:
        raise Exception(f"Error {response.status_code}: {response.text}")
    return response.json()["choices"][0]["message"]["content"]

from fuzzywuzzy import fuzz
from sklearn.metrics import precision_score, recall_score, f1_score
from sentence_transformers import SentenceTransformer, util
import numpy as np
from sklearn.metrics.pairwise import cosine_similarity

import requests
import numpy as np

def get_embedding(text):
    url = "http://localhost:11434/api/embeddings"
    payload = {
        "model": "nomic-embed-text",
        "prompt": text
    }
    response = requests.post(url, json=payload)
    
    try:
        data = response.json()
        return np.array(data["embedding"])
    except Exception as e:
        print("🔴 获取 embedding 失败，响应内容如下：")
        print(response.text)
        raise e


def evaluate_answer(correct_answer, generated_answer):
    """改进的评估函数，结合模糊匹配和语义相似度"""
    # 1. 模糊匹配分数 (0-100)
    fuzzy_score = fuzz.ratio(correct_answer.lower(), generated_answer.lower()) / 100
    
    # 2. 语义相似度 (0-1)
    try:
        emb1 = get_embedding(correct_answer).reshape(1, -1)
        emb2 = get_embedding(generated_answer).reshape(1, -1)
        semantic_score = cosine_similarity(emb1, emb2)[0][0]
    except Exception as e:
        print(f"Embedding calculation failed: {str(e)}")
        semantic_score = 0
    
    print(f"Semantic Score: {semantic_score:.2f}")
    # 3. 综合评分 (可调整权重)
    combined_score = 0.3 * fuzzy_score + 0.7 * semantic_score
    
    return combined_score



# Main function to evaluate answers and save the results
def main():
    questions = load_questions()
    results = []

    for rag_id, context_file in CONTEXT_FILES.items():
        context = load_context(context_file)
        for q in questions:
            print(f"🧠 Asking {q['id']} using {rag_id}...")
            prompt = PROMPT_TEMPLATE.format(context=context, question=q["text"])
            answer = ask_deepseek(prompt)
            save_answer(rag_id, q["id"], answer)

            # Generate the standard answer
            correct_answer = generate_standard_answer(q["id"])

            # Use fuzzy evaluation
            precision = evaluate_answer(correct_answer, answer)
            results.append({
                "Question": q["id"],
                "RAG Mode": rag_id,
                # "Correct": correct,
                "Precision": precision,
                # "Recall": recall,
                # "F1 Score": f1,
                "Generated Answer": answer,
                "Standard Answer": correct_answer
            })

    # Save results to a DataFrame and output the evaluation
    df = pd.DataFrame(results)
    df.to_excel(f"{OUTPUT_DIR}/evaluation_results_updated.xlsx", index=False)
    print(f"Evaluation results saved in {OUTPUT_DIR}/evaluation_results_updated.xlsx")

    # Visualize the metrics
    df.set_index(['Question', 'RAG Mode'])[['Precision']].unstack().plot(kind='bar', figsize=(12, 8))
    plt.title('Evaluation Metrics (Precision)')
    plt.ylabel('Score')
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.savefig(f"{OUTPUT_DIR}/evaluation_precision_plot.png")
    plt.show()

    # Save the report in HTML format
    with open(f"{OUTPUT_DIR}/evaluation_report_updated.html", "w", encoding="utf-8") as f:
        f.write(df.to_html())

    print("Report saved in evaluation_report_updated.html")


if __name__ == "__main__":
    main()

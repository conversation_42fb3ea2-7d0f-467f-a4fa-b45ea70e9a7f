# graphdb.py
from neo4j import GraphDatabase
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class GraphDB:
    def __init__(self, uri, user, password):
        try:
            self.driver = GraphDatabase.driver(uri, auth=(user, password))
            # 测试连接
            with self.driver.session() as session:
                result = session.run("RETURN 1 AS test")
                if result.single()["test"] == 1:
                    logger.info("✅ Neo4j 连接成功")
                else:
                    logger.warning("⚠️ Neo4j 连接测试异常")
        except Exception as e:
            logger.error(f"❌ 连接 Neo4j 失败: {str(e)}")
            raise

    def close(self):
        if hasattr(self, 'driver') and self.driver:
            self.driver.close()
            logger.info("Neo4j 连接已关闭")

    def run(self, query, parameters=None):
        with self.driver.session() as session:
            return session.run(query, parameters or {})

    def create_indexes(self):
        # Neo4j 4.x+ 的正确索引语法
        index_queries = [
            "CREATE INDEX IF NOT EXISTS FOR (f:File) ON (f.path)",
            "CREATE INDEX IF NOT EXISTS FOR (c:Class) ON (c.fully_qualified_name)",
            "CREATE INDEX IF NOT EXISTS FOR (fn:Function) ON (fn.signature)",
            "CREATE INDEX IF NOT EXISTS FOR (cb:CodeBlock) ON (cb.id)"
        ]
        
        with self.driver.session() as session:
            for query in index_queries:
                try:
                    result = session.run(query)
                    logger.info(f"执行成功: {query}")
                except Exception as e:
                    logger.error(f"执行失败: {query}\n错误: {str(e)}")

    def create_repository(self, url, name):
        self.run("MERGE (r:Repository {url: $url, name: $name})", {"url": url, "name": name})

    def create_file(self, path, language):
        self.run("MERGE (f:File {path: $path, language: $language})", {"path": path, "language": language})

    def create_class(self, id, name, fqname, filepath):
        self.run("MERGE (c:Class {id: $id, name: $name, fully_qualified_name: $fqname, filepath: $filepath})",
                 {"id": id, "name": name, "fqname": fqname, "filepath": filepath})

    def create_interface(self, id, name, fqname, filepath):
        self.run("MERGE (i:Interface {id: $id, name: $name, fully_qualified_name: $fqname, filepath: $filepath})",
                 {"id": id, "name": name, "fqname": fqname, "filepath": filepath})

    def create_struct(self, id, name, fqname, filepath):
        self.run("MERGE (s:Struct {id: $id, name: $name, fully_qualified_name: $fqname, filepath: $filepath})",
                 {"id": id, "name": name, "fqname": fqname, "filepath": filepath})

    def create_function(self, id, name, signature, class_id, filepath, start_line, end_line):
        params = {
            "id": id,
            "name": name,
            "signature": signature,
            "filepath": filepath,
            "start_line": start_line,
            "end_line": end_line
        }
        if class_id is not None:
            params["class_id"] = class_id
            merge_clause = "MERGE (f:Function {id: $id, name: $name, signature: $signature, class_id: $class_id, filepath: $filepath, start_line: $start_line, end_line: $end_line})"
        else:
            merge_clause = "MERGE (f:Function {id: $id, name: $name, signature: $signature, filepath: $filepath, start_line: $start_line, end_line: $end_line})"
        self.run(merge_clause, params)

    def create_method(self, id, name, signature, class_id, filepath, start_line, end_line):
        params = {
            "id": id,
            "name": name,
            "signature": signature,
            "class_id": class_id,
            "filepath": filepath,
            "start_line": start_line,
            "end_line": end_line
        }
        self.run("MERGE (m:Method {id: $id, name: $name, signature: $signature, class_id: $class_id, filepath: $filepath, start_line: $start_line, end_line: $end_line})", params)

    def create_variable(self, id, name, var_type, parent_id, filepath, start_line, end_line):
        self.run("MERGE (v:Variable {id: $id, name: $name, type: $type, parent_id: $parent_id, filepath: $filepath, start_line: $start_line, end_line: $end_line})",
                 {"id": id, "name": name, "type": var_type, "parent_id": parent_id, "filepath": filepath, "start_line": start_line, "end_line": end_line})

    def create_constant(self, id, name, var_type, parent_id, filepath, start_line, end_line):
        props = {
            "id": id,
            "name": name,
            "type": var_type,
            "filepath": filepath,
            "start_line": start_line,
            "end_line": end_line
        }
        # 只有parent_id有值时才加进去
        if parent_id is not None:
            props["parent_id"] = parent_id

        prop_str = ", ".join([f"{k}: ${k}" for k in props.keys()])
        self.run(f"MERGE (c:Constant {{{prop_str}}})", props)
        
    def create_codeblock(self, block_id, function_id, filepath, start_line, end_line, block_type):
        self.run("""MERGE (b:CodeBlock {id: $block_id, function_id: $function_id, filepath: $filepath, 
                   start_line: $start_line, end_line: $end_line, block_type: $block_type})""",
                 {"block_id": block_id, "function_id": function_id, "filepath": filepath,
                  "start_line": start_line, "end_line": end_line, "block_type": block_type})

    def create_relationship(self, from_label, from_key, to_label, to_key, rel_type):
        self.run(f"""
            MATCH (a:{from_label} {{{from_key[0]}: $from_value}})
            MATCH (b:{to_label} {{{to_key[0]}: $to_value}})
            MERGE (a)-[r:{rel_type}]->(b)
        """, {"from_value": from_key[1], "to_value": to_key[1]})
import subprocess
import os
import re
import tempfile
import time
from datetime import datetime
from config import TARGET_DIR, REPO_URL
from main import CodeAnalyzer
from ast_parser import git_utils

class DiffAnalyzer:
    def __init__(self, ast_parser):
        self.ast_parser = ast_parser

    def map_changes_to_elements(self, old_content, new_content, file_path):
        """将变更映射到代码元素"""
        old_elements = self._analyze_content(old_content, file_path)
        new_elements = self._analyze_content(new_content, file_path)
        
        return {
            'old': old_elements,
            'new': new_elements,
            'changes': self._compute_element_changes(old_elements, new_elements)
        }
    
    def _analyze_content(self, content, file_path):
        """分析代码内容，返回代码元素信息"""
        if not content:
            return {}
            
        with tempfile.NamedTemporaryFile(mode='w', suffix=os.path.splitext(file_path)[1], 
                                       delete=False, encoding='utf-8') as temp:
            temp.write(content)
            temp_path = temp.name
            
        try:
            tree = self.ast_parser.parse_file(temp_path)
            if tree:
                return self.ast_parser.analyze_ast(tree, file_path)
            return {}
        finally:
            os.unlink(temp_path)
    
    def _compute_element_changes(self, old_elements, new_elements):
        """计算代码元素的变更"""
        changes = {
            'added': [],
            'removed': [],
            'modified': []
        }
        
        # 分析类的变更
        self._analyze_class_changes(old_elements.get('classes', []), 
                                  new_elements.get('classes', []), changes)
        
        # 分析函数的变更
        self._analyze_function_changes(old_elements.get('functions', []), 
                                     new_elements.get('functions', []), changes)
        
        # 分析变量的变更
        self._analyze_variable_changes(old_elements.get('variables', []), 
                                     new_elements.get('variables', []), changes)
        
        return changes
    
    def _analyze_class_changes(self, old_classes, new_classes, changes):
        """分析类的变更"""
        old_names = {cls['name']: cls for cls in old_classes}
        new_names = {cls['name']: cls for cls in new_classes}
        
        # 查找添加的类
        for name, cls in new_names.items():
            if name not in old_names:
                changes['added'].append({
                    'type': 'class',
                    'name': name,
                    'element': cls
                })
        
        # 查找删除的类
        for name, cls in old_names.items():
            if name not in new_names:
                changes['removed'].append({
                    'type': 'class',
                    'name': name,
                    'element': cls
                })
        
        # 查找修改的类
        for name, new_cls in new_names.items():
            if name in old_names:
                old_cls = old_names[name]
                if self._is_class_modified(old_cls, new_cls):
                    changes['modified'].append({
                        'type': 'class',
                        'name': name,
                        'old': old_cls,
                        'new': new_cls
                    })

    def _analyze_function_changes(self, old_functions, new_functions, changes):
        """分析函数的变更"""
        old_names = {func['name']: func for func in old_functions}
        new_names = {func['name']: func for func in new_functions}
        
        # 查找添加的函数
        for name, func in new_names.items():
            if name not in old_names:
                changes['added'].append({
                    'type': 'function',
                    'name': name,
                    'element': func
                })
        
        # 查找删除的函数
        for name, func in old_names.items():
            if name not in new_names:
                changes['removed'].append({
                    'type': 'function',
                    'name': name,
                    'element': func
                })
        
        # 查找修改的函数
        for name, new_func in new_names.items():
            if name in old_names:
                old_func = old_names[name]
                if self._is_function_modified(old_func, new_func):
                    changes['modified'].append({
                        'type': 'function',
                        'name': name,
                        'old': old_func,
                        'new': new_func
                    })

    def _analyze_variable_changes(self, old_variables, new_variables, changes):
        """分析变量的变更"""
        old_names = {var['name']: var for var in old_variables}
        new_names = {var['name']: var for var in new_variables}
        
        # 查找添加的变量
        for name, var in new_names.items():
            if name not in old_names:
                changes['added'].append({
                    'type': 'variable',
                    'name': name,
                    'element': var
                })
        
        # 查找删除的变量
        for name, var in old_names.items():
            if name not in new_names:
                changes['removed'].append({
                    'type': 'variable',
                    'name': name,
                    'element': var
                })
        
        # 查找修改的变量
        for name, new_var in new_names.items():
            if name in old_names:
                old_var = old_names[name]
                if self._is_variable_modified(old_var, new_var):
                    changes['modified'].append({
                        'type': 'variable',
                        'name': name,
                        'old': old_var,
                        'new': new_var
                    })

    def _is_function_modified(self, old_func, new_func):
        """检查函数是否被修改"""
        return (old_func['start_line'] != new_func['start_line'] or
                old_func['end_line'] != new_func['end_line'] or
                old_func.get('signature') != new_func.get('signature'))

    def _is_variable_modified(self, old_var, new_var):
        """检查变量是否被修改"""
        return (old_var.get('type') != new_var.get('type') or
                old_var.get('value') != new_var.get('value'))
    
    def _is_class_modified(self, old_cls, new_cls):
        """检查类是否被修改"""
        if old_cls['start_line'] != new_cls['start_line'] or \
           old_cls['end_line'] != new_cls['end_line']:
            return True
            
        # 比较方法列表
        old_methods = set(m['name'] for m in old_cls.get('methods', []))
        new_methods = set(m['name'] for m in new_cls.get('methods', []))
        if old_methods != new_methods:
            return True
            
        return False

class CommitAnalyzer:
    """提交分析器，用于分析Git提交"""

    def __init__(self):
        self.code_analyzer = CodeAnalyzer()  # 使用 main.py 中的分析器
        self.diff_analyzer = DiffAnalyzer(self.code_analyzer.ast_parser)
        self.ensure_repository()

    def ensure_repository(self):
        """确保仓库存在且是最新的"""
        if not os.path.exists(TARGET_DIR):
            print(f"Cloning repository to {TARGET_DIR}...")
            if not git_utils.clone_repo(REPO_URL, TARGET_DIR, None):
                raise Exception("Failed to clone repository")
        else:
            print("Updating repository...")
            try:
                subprocess.run(["git", "fetch", "--all"], check=True, cwd=TARGET_DIR)
                subprocess.run(["git", "pull"], check=True, cwd=TARGET_DIR)
            except subprocess.CalledProcessError as e:
                print(f"Error updating repository: {e}")
                raise

    def analyze_commit_changes(self, old_commit, new_commit):
        """分析两个commit之间的变更"""
        # 获取两次commit的文件列表
        old_files = set(self.get_repository_files_at_commit(old_commit['hash']))
        new_files = set(self.get_repository_files_at_commit(new_commit['hash']))
        
        # 计算文件级别的变化
        added_files = new_files - old_files
        removed_files = old_files - new_files
        common_files = old_files.intersection(new_files)
        
        # 通过内容比较找出修改的文件
        modified_files = set()
        for file_path in common_files:
            if self.is_file_modified(old_commit['hash'], new_commit['hash'], file_path):
                modified_files.add(file_path)
        
        return {
            'added': added_files,
            'removed': removed_files,
            'modified': modified_files
        }
    

    def is_file_modified(self, old_hash, new_hash, file_path):
        """比较文件在两个commit间是否被修改"""
        old_content = self.get_file_content(old_hash, file_path)
        new_content = self.get_file_content(new_hash, file_path)
        return old_content != new_content

    def analyze_file_changes(self, commit_hash, file_path, change_type):
        """分析文件变更"""
        if change_type == 'A':  # 新增文件
            new_content = self.get_file_content(commit_hash, file_path)
            old_content = ''
        elif change_type == 'D':  # 删除文件
            new_content = ''
            old_content = self.get_file_content(f'{commit_hash}^', file_path)
        else:  # 修改文件
            new_content = self.get_file_content(commit_hash, file_path)
            old_content = self.get_file_content(f'{commit_hash}^', file_path)
            
        return self.diff_analyzer.map_changes_to_elements(old_content, new_content, file_path)

    def print_element_changes(self, changes):
        """打印代码元素变更"""
        if changes['added']:
            print("\n  Added elements:")
            for elem in changes['added']:
                print(f"    + {elem['type']}: {elem['name']}")
                if elem['type'] == 'class':
                    self._print_class_details(elem['element'])
                    
        if changes['removed']:
            print("\n  Removed elements:")
            for elem in changes['removed']:
                print(f"    - {elem['type']}: {elem['name']}")
                if elem['type'] == 'class':
                    self._print_class_details(elem['element'])
                    
        if changes['modified']:
            print("\n  Modified elements:")
            for elem in changes['modified']:
                print(f"    ~ {elem['type']}: {elem['name']}")
                if elem['type'] == 'class':
                    print("      Old version:")
                    self._print_class_details(elem['old'])
                    print("      New version:")
                    self._print_class_details(elem['new'])
    
    def _print_class_details(self, cls):
        """打印类的详细信息"""
        print(f"      Class: {cls['name']} (Lines {cls['start_line']}-{cls['end_line']})")
        if cls.get('methods'):
            print("      Methods:")
            for method in cls['methods']:
                print(f"        - {method['name']} (Lines {method['start_line']}-{method['end_line']})")

        
    def get_commit_logs(self, since="1 year ago", max_count=100):
        """获取指定时间范围内的commit历史
        
        Args:
            since: 开始时间点
            max_count: 最大commit数量
        """
        os.chdir(TARGET_DIR)
        cmd = [
            "git", "log", "--reverse",
            f"-n", str(max_count),  # 限制commit数量
            "--stat", "-p", "--name-status",
            "--pretty=format:---%ncommit: %H%nauthor: %an%ndate: %ad%nmessage: %s"
        ]
        result = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, encoding="utf-8")
        if result.returncode != 0:
            print("Error running git log:", result.stderr)
            return []
        return self.parse_commit_log(result.stdout)

    def parse_commit_log(self, log_text):
        """解析git log输出，返回commit列表"""
        commits = []
        current_commit = None
        current_changes = []
        
        for line in log_text.splitlines():
            if line.startswith('---'):
                if current_commit:
                    current_commit['file_changes'] = self.parse_file_changes(current_changes)
                    commits.append(current_commit)
                current_commit = {}
                current_changes = []
            elif current_commit is not None:
                if line.startswith('commit: '):
                    current_commit['hash'] = line[8:]
                elif line.startswith('author: '):
                    current_commit['author'] = line[8:]
                elif line.startswith('date: '):
                    current_commit['date'] = line[6:]
                elif line.startswith('message: '):
                    current_commit['message'] = line[9:]
                else:
                    current_changes.append(line)
        
        if current_commit:
            current_commit['file_changes'] = self.parse_file_changes(current_changes)
            commits.append(current_commit)
        
        return commits

    def get_repository_files_at_commit(self, commit_hash):
        """获取指定commit时的仓库文件列表"""
        try:
            cmd = ['git', 'ls-tree', '-r', '--name-only', commit_hash]
            result = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, 
                                 encoding='utf-8', errors='ignore', cwd=TARGET_DIR)
            if result.returncode == 0:
                return sorted(result.stdout.splitlines())
            return []
        except Exception as e:
            print(f"Error getting repository files: {e}")
            return []

    def parse_file_changes(self, changes):
        """解析文件变更"""
        result = []
        current_file = None
        current_changes = []
        change_type = None
        
        for line in changes:
            # 修改文件状态行匹配模式，使用制表符分隔
            status_match = re.match(r'^([AMD])\t(.+)$', line)
            if status_match:
                if current_file and current_changes:
                    result.append({
                        'file': current_file,
                        'type': change_type,
                        'changes': self.analyze_changes(current_changes)
                    })
                change_type = status_match.group(1)
                current_file = status_match.group(2).strip()  # 去除空白字符
                current_changes = []
                continue
            
            # 修改 diff 文件路径提取
            if line.startswith('diff --git'):
                if current_file and current_changes:
                    result.append({
                        'file': current_file,
                        'type': change_type,
                        'changes': self.analyze_changes(current_changes)
                    })
                file_match = re.search(r'b/(.+?)"?\s*$', line)
                if file_match:
                    current_file = file_match.group(1).strip('"')  # 处理带引号的路径
                current_changes = []
            elif line.startswith('@@') or line.startswith('index ') or line.startswith('---') or line.startswith('+++'):
                continue
            else:
                current_changes.append(line)
        
        # 添加最后一个文件的变更
        if current_file and current_changes:
            result.append({
                'file': current_file,
                'type': change_type,
                'changes': self.analyze_changes(current_changes)
            })
        
        return result

    def analyze_changes(self, changes):
        """分析代码变更并映射到代码元素"""
        added = []
        removed = []
        modified = []
        
        for line in changes:
            if line.startswith('+'):
                content = line[1:].strip()
                if content:
                    added.append({
                        'content': content,
                        'elements': self.identify_code_elements(content)
                    })
            elif line.startswith('-'):
                content = line[1:].strip()
                if content:
                    removed.append({
                        'content': content,
                        'elements': self.identify_code_elements(content)
                    })
            elif line.startswith(' '):
                content = line[1:].strip()
                if content:
                    modified.append({
                        'content': content,
                        'elements': self.identify_code_elements(content)
                    })
        
        return {
            'added': added,
            'removed': removed,
            'modified': modified
        }

    def identify_code_elements(self, line):
        """识别代码行中的元素（类、函数、变量等）"""
        elements = []
        
        # 类定义
        class_match = re.search(r'class\s+(\w+)', line)
        if class_match:
            elements.append({
                'type': 'class',
                'name': class_match.group(1)
            })
        
        # 函数定义
        func_match = re.search(r'(?:def|void|int|string|bool)\s+(\w+)\s*\(', line)
        if func_match:
            elements.append({
                'type': 'function',
                'name': func_match.group(1)
            })
        
        # 变量定义
        var_match = re.search(r'(?:int|string|bool|float|double)\s+(\w+)\s*[=;]', line)
        if var_match:
            elements.append({
                'type': 'variable',
                'name': var_match.group(1)
            })
        
        return elements
    def get_file_content(self, commit_hash, file_path):
        """获取指定commit中某个文件的内容"""
        try:
            cmd = ['git', 'show', f'{commit_hash}:{file_path}']
            result = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, 
                                 encoding='utf-8', errors='ignore', cwd=TARGET_DIR)
            if result.returncode == 0:
                return result.stdout
            return None
        except Exception as e:
            print(f"Error getting file content: {e}")
            return None

    def analyze_file_at_commit(self, commit_hash, file_path):
        """分析指定commit中的文件"""
        content = self.get_file_content(commit_hash, file_path)
        if not content:
            return None

        # 创建临时文件来分析内容
        with tempfile.NamedTemporaryFile(mode='w', suffix=os.path.splitext(file_path)[1], delete=False) as temp:
            temp.write(content)
            temp_path = temp.name

        try:
            # 使用CodeAnalyzer分析文件
            result = self.code_analyzer.analyze_file(temp_path)
            return result
        finally:
            os.unlink(temp_path)  # 删除临时文件

# 以下为辅助函数

def analyze_single_commit(analyzer, commit):
    """分析单个commit的内容"""
    print_commit_info(commit)
    files = get_decoded_files(analyzer, commit)
    analysis_results = analyze_commit_files(analyzer, commit, files)
    return analysis_results

def analyze_changes_between_commits(analyzer, old_commit, new_commit):
    """分析两个commit之间的变更"""
    print_commit_comparison(old_commit, new_commit)
    changes = analyzer.analyze_commit_changes(old_commit, new_commit)
    print_file_changes(changes)
    print_detailed_changes(analyzer, old_commit, new_commit, changes)

def print_commit_info(commit):
    """打印commit的基本信息"""
    print("\n" + "="*80)
    print(f"Commit: {commit['hash']}")
    print(f"Author: {commit['author']}")
    print(f"Date: {commit['date']}")
    print(f"Message: {commit['message']}")
    print("="*80)

def get_decoded_files(analyzer, commit):
    """获取并解码commit中的文件列表"""
    files = analyzer.get_repository_files_at_commit(commit['hash'])
    print("\nRepository files at this commit:")
    print("-" * 40)
    
    decoded_files = []
    for file_path in files:
        try:
            decoded_path = bytes(file_path, 'utf-8').decode('unicode_escape')
            print(f"📄 {decoded_path}")
            decoded_files.append(decoded_path)
        except:
            print(f"📄 {file_path}")
            decoded_files.append(file_path)
    print("-" * 40)
    return decoded_files

def analyze_commit_files(analyzer, commit, files):
    """分析commit中的文件"""
    start_time = time.time()
    analysis_results = {}
    code_files = []
    
    # 筛选代码文件
    for file_path in files:
        ext = os.path.splitext(file_path)[1].lower()
        if ext in ['.cpp', '.h', '.py']:
            code_files.append(file_path)
    
    print(f"\nAnalyzing {len(code_files)} code files in this commit...")
    for i, file_path in enumerate(code_files, 1):
        result = analyze_single_file(analyzer, commit['hash'], file_path, i, len(code_files))
        if result:
            analysis_results[file_path] = result
    
    print_analysis_summary(analysis_results, code_files, start_time)
    return analysis_results

def analyze_single_file(analyzer, commit_hash, file_path, index, total):
    """分析单个文件"""
    print(f"\n[{index}/{total}] Analyzing: {os.path.basename(file_path)}")
    try:
        content = analyzer.get_file_content(commit_hash, file_path)
        if not content:
            return None
            
        with tempfile.NamedTemporaryFile(mode='w', suffix=os.path.splitext(file_path)[1], 
                                       delete=False, encoding='utf-8') as temp:
            temp.write(content)
            temp_path = temp.name
            
        try:
            result = analyzer.code_analyzer.analyze_file(temp_path)
            if result:
                analyzer.code_analyzer.print_file_analysis(result, file_path)
                return result
        finally:
            os.unlink(temp_path)
    except Exception as e:
        print(f"Error analyzing file {file_path}: {str(e)}")
    return None

def print_analysis_summary(analysis_results, code_files, start_time):
    """打印分析结果摘要"""
    print("\n=== Commit Analysis Summary ===")
    total_functions = sum(len(r.get("functions", [])) for r in analysis_results.values())
    total_classes = sum(len(r.get("classes", [])) for r in analysis_results.values())
    total_variables = sum(len(r.get("variables", [])) for r in analysis_results.values())
    total_code_blocks = sum(len(r.get("code_blocks", [])) for r in analysis_results.values())
    
    print(f"Analyzed {len(analysis_results)}/{len(code_files)} files")
    print(f"Total functions: {total_functions}")
    print(f"Total classes: {total_classes}")
    print(f"Total variables: {total_variables}")
    print(f"Total code blocks: {total_code_blocks}")
    print(f"Time taken: {time.time()-start_time:.2f} seconds")

def print_commit_comparison(old_commit, new_commit):
    """打印commit比较信息"""
    print("\n" + "="*80)
    print("比较两次commit的变化:")
    print(f"\n旧commit: {old_commit['hash']}")
    print(f"时间: {old_commit['date']}")
    print(f"消息: {old_commit['message']}")
    print(f"\n新commit: {new_commit['hash']}")
    print(f"时间: {new_commit['date']}")
    print(f"消息: {new_commit['message']}")
    print("="*80)

def print_file_changes(changes):
    """打印文件变更概览"""
    print("\n=== 文件变更概览 ===")
    
    if changes['added']:
        print("\n新增文件:")
        for f in sorted(changes['added']):
            print(f"  + {f}")
    
    if changes['removed']:
        print("\n删除文件:")
        for f in sorted(changes['removed']):
            print(f"  - {f}")
    
    if changes['modified']:
        print("\n修改文件:")
        for f in sorted(changes['modified']):
            print(f"  ~ {f}")

def print_detailed_changes(analyzer, old_commit, new_commit, changes):
    """打印详细的代码变更"""
    print("\n=== 详细代码变更 ===")
    modified_files = changes['modified']
    
    for file_path in modified_files:
        if os.path.splitext(file_path)[1].lower() in ['.cpp', '.h', '.py']:
            print(f"\n文件: {file_path}")
            result = analyzer.diff_analyzer.map_changes_to_elements(
                analyzer.get_file_content(old_commit['hash'], file_path),
                analyzer.get_file_content(new_commit['hash'], file_path),
                file_path
            )
            analyzer.print_element_changes(result['changes'])

def LLM_analyze_modified_codefile(analyzer, old_commit, new_commit):
    import requests
    """使用LLM分析修改的代码文件"""
    print("\n=== LLM 分析修改的代码文件 ===")
    changes = analyzer.analyze_commit_changes(old_commit, new_commit)
    modified_files = changes['modified']
    print(f"修改的文件: {modified_files}")

    for file_path in modified_files:
        if os.path.splitext(file_path)[1].lower() in ['.cpp', '.h', '.py']:
            print(f"\n{'='*40}")
            print(f"文件: {file_path}")
            print(f"{'='*40}")
            
            # 获取修改前后的完整文件内容
            old_content = analyzer.get_file_content(old_commit['hash'], file_path)
            new_content = analyzer.get_file_content(new_commit['hash'], file_path)
            
            # 打印修改前的文件内容
            print("\n修改前的文件内容:")
            print("-" * 40)
            print(old_content)
            
            # 打印修改后的文件内容
            print("\n修改后的文件内容:")
            print("-" * 40)
            print(new_content)
            
            print(f"\n{'='*40}\n")

        # 定义系统指令
    system_prompt = """你
    是一个专业的代码审查助手。
    在分析代码变更时，请重点关注代码结构和逻辑的变化

    请按照以下格式组织回答：
    【代码结构变化】

    并且在回答的时候直接说明结论！不需要输出小标题等文字格式，直接输出结果内容。
    """

    # ollma 本地部署地址
    url = "http://localhost:11434/api/generate"
    # 构建提示词
    prompt = f"""分析以下代码变更并解释主要修改内容:

    原始代码:
    ```
    {old_content}
    ```

    修改后的代码:
    ```
    {new_content}
    ```

    请对代码文件变更进行解析与识别：
    输出文件的增、删、改行。
    """

    # 调用 Ollama API
    model = "gemma3:12b"  # 使用的模型
    payload = {
        "model": model,
        "prompt": prompt,
        "stream": False,
        "system": system_prompt,
        "options": {
            "temperature": 0.2,  # 降低温度以获得更确定的回答
            "top_p": 0.9,
            "repeat_penalty": 1.2
        }
    }

    try:
        print(f"\n{model} 模型的分析结果:")
        print("-" * 40)
        response = requests.post(url, json=payload)
        if response.status_code == 200:
            analysis = response.json()["response"]
            print(analysis)
        else:
            print(f"API 调用失败: {response.status_code}")
    except Exception as e:
        print(f"分析过程出错: {str(e)}")
    
    print(f"\n{'='*40}")
    return analysis

def main():
    """主函数"""
    analyzer = CommitAnalyzer()
    commits = analyzer.get_commit_logs(max_count=2)  # 获取最近2次commit
    
    new_commit = commits[-1]  # 最新的commit
    old_commit = commits[-2]  # 上一次的commit
    
    # 分析每个commit
    for commit in commits:
        analyze_single_commit(analyzer, commit)
    
    # 分析commit之间的变更
    analyze_changes_between_commits(analyzer, old_commit, new_commit)

    # 分析修改的代码文件
    LLM_analyze_modified_codefile(analyzer, old_commit, new_commit)

if __name__ == "__main__":
    main()
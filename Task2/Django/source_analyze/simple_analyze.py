#!/usr/bin/env python3
"""
Django源码简化分析器
一次性分析完成，直接输出JSON结果
"""

import ast
import os
import json
from pathlib import Path
from typing import Dict, List, Set
from collections import defaultdict

class SimpleCallAnalyzer:
    def __init__(self, django_source_path: str):
        self.django_path = Path(django_source_path)
        self.call_graph = defaultdict(list)  # 调用关系图
        self.function_locations = {}  # 函数位置信息
        self.all_results = {}  # 所有分析结果
    
    def analyze_file(self, file_path: Path) -> Dict:
        """分析单个Python文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            tree = ast.parse(content)
            visitor = CallVisitor(str(file_path))
            visitor.visit(tree)
            
            return {
                'functions': visitor.functions,
                'function_locations': visitor.function_locations,
                'classes': visitor.classes,
                'calls': visitor.calls
            }
        except Exception as e:
            return {'functions': [], 'function_locations': {}, 'classes': [], 'calls': []}
    
    def get_django_relative_path(self, file_path: str) -> str:
        """获取相对于django目录的路径"""
        path = Path(file_path)
        try:
            parts = path.parts
            django_index = -1
            for i, part in enumerate(parts):
                if part == 'django':
                    django_index = i
                    break
            
            if django_index >= 0:
                relative_parts = parts[django_index:]
                return '/'.join(relative_parts)
            else:
                return path.name
        except:
            return path.name
    
    def path_to_module(self, file_path: str) -> str:
        """将文件路径转换为模块名"""
        path = file_path.replace('/', '.').replace('\\', '.')
        if path.startswith('django.'):
            path = path[7:]  # 移除django前缀
        if path.endswith('.py'):
            path = path[:-3]
        if path.endswith('.__init__'):
            path = path[:-9]
        return path
    
    def analyze_all(self):
        """分析所有Django源码文件"""
        print("🔍 开始分析Django源码...")
        
        django_core = self.django_path / "django"
        if not django_core.exists():
            print(f"❌ Django源代码目录不存在: {django_core}")
            return
        
        python_files = list(django_core.rglob("*.py"))
        print(f"📁 找到 {len(python_files)} 个Python文件")
        
        # 分析所有文件
        for i, file_path in enumerate(python_files):
            if i % 100 == 0:
                print(f"📄 分析进度: {i}/{len(python_files)}")
            
            relative_path = self.get_django_relative_path(str(file_path))
            analysis = self.analyze_file(file_path)
            module_name = self.path_to_module(relative_path)
            
            # 构建调用关系
            self.build_call_relations(module_name, relative_path, analysis)
        
        print("✅ 源码分析完成")
    
    def build_call_relations(self, module_name: str, file_path: str, analysis: Dict):
        """构建调用关系"""
        # 记录函数位置
        for func_name, location_info in analysis.get('function_locations', {}).items():
            full_name = f"{module_name}.{func_name}"
            self.function_locations[full_name] = {
                'file_path': file_path,
                'line_number': location_info['line_number'],
                'end_line': location_info.get('end_line', location_info['line_number'])
            }
        
        # 记录类方法位置
        for cls in analysis['classes']:
            for method in cls['methods']:
                method_name = f"{module_name}.{cls['name']}.{method['name']}"
                self.function_locations[method_name] = {
                    'file_path': file_path,
                    'line_number': method['line_number'],
                    'end_line': method.get('end_line', method['line_number'])
                }
        
        # 记录调用关系
        for call in analysis['calls']:
            caller = f"{module_name}.{call['context']}" if call['context'] else f"{module_name}.module_level"
            callee = call['function']
            
            # 构建调用信息
            call_info = {
                'function': callee,
                'call_location': {
                    'file_path': file_path,
                    'line_number': call['line']
                }
            }
            
            # 添加被调用函数的定义位置（如果找到）
            if callee in self.function_locations:
                call_info['definition_location'] = self.function_locations[callee]
            
            self.call_graph[caller].append(call_info)
    
    def generate_final_results(self):
        """生成最终分析结果"""
        print("📊 生成分析结果...")
        
        # 统计信息
        total_functions = len(self.function_locations)
        total_calls = sum(len(calls) for calls in self.call_graph.values())
        total_modules = len(set(caller.split('.')[0] for caller in self.call_graph.keys()))
        
        # 最常被调用的函数
        call_counts = defaultdict(int)
        for calls in self.call_graph.values():
            for call_info in calls:
                call_counts[call_info['function']] += 1
        
        most_called = sorted(call_counts.items(), key=lambda x: x[1], reverse=True)[:10]
        most_called_functions = [{'function': func, 'call_count': count} for func, count in most_called]
        
        # 核心模块分析
        core_modules = ['core', 'db', 'http', 'views', 'urls', 'forms', 'template', 'middleware', 'contrib', 'utils']
        core_components = {}
        
        for module in core_modules:
            module_calls = {k: v for k, v in self.call_graph.items() if k.startswith(module)}
            if module_calls:
                core_components[module] = {
                    'function_count': len([f for f in self.function_locations.keys() if f.startswith(module)]),
                    'total_calls': sum(len(v) for v in module_calls.values()),
                    'key_functions': list(module_calls.keys())[:5]
                }
        
        # 构建最终结果
        self.all_results = {
            'metadata': {
                'django_path': str(self.django_path),
                'analysis_type': 'Django Source Code Call Relations',
                'total_modules_analyzed': total_modules
            },
            'summary': {
                'total_modules': total_modules,
                'total_functions': total_functions,
                'total_calls': total_calls,
                'most_called_functions': most_called_functions,
                'core_components': core_components
            },
            'detailed_call_graph': dict(self.call_graph),
            'function_locations': self.function_locations
        }
        
        return self.all_results
    
    def save_results(self, output_file: str = "django_source_analysis.json"):
        """保存分析结果到JSON文件"""
        print(f"💾 保存结果到: {output_file}")
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(self.all_results, f, indent=2, ensure_ascii=False)
        
        print("✅ 保存完成")
        
        # 显示摘要
        summary = self.all_results['summary']
        print(f"\n📊 分析摘要:")
        print(f"  总模块数: {summary['total_modules']}")
        print(f"  总函数数: {summary['total_functions']}")
        print(f"  总调用数: {summary['total_calls']}")
        
        print(f"\n🔥 最常被调用的函数:")
        for func_info in summary['most_called_functions'][:5]:
            print(f"  {func_info['function']}: {func_info['call_count']} 次")
        
        print(f"\n🏗️  核心组件:")
        for component, info in summary['core_components'].items():
            print(f"  {component}: {info['function_count']} 个函数, {info['total_calls']} 次调用")

class CallVisitor(ast.NodeVisitor):
    """AST访问器，提取函数调用信息"""
    
    def __init__(self, file_path: str):
        self.file_path = file_path
        self.functions = []
        self.function_locations = {}
        self.classes = []
        self.calls = []
        self.current_class = None
        self.current_function = None
    
    def visit_FunctionDef(self, node):
        self.current_function = node.name
        self.functions.append(node.name)
        
        # 记录函数位置
        self.function_locations[node.name] = {
            'line_number': node.lineno,
            'end_line': getattr(node, 'end_lineno', node.lineno)
        }
        
        self.generic_visit(node)
        self.current_function = None
    
    def visit_ClassDef(self, node):
        self.current_class = node.name
        class_info = {
            'name': node.name,
            'methods': []
        }
        
        for item in node.body:
            if isinstance(item, ast.FunctionDef):
                method_info = {
                    'name': item.name,
                    'line_number': item.lineno,
                    'end_line': getattr(item, 'end_lineno', item.lineno)
                }
                class_info['methods'].append(method_info)
        
        self.classes.append(class_info)
        self.generic_visit(node)
        self.current_class = None
    
    def visit_Call(self, node):
        func_name = self.get_function_name(node.func)
        if func_name:
            context = self.current_function or self.current_class or 'module_level'
            self.calls.append({
                'function': func_name,
                'context': context,
                'line': node.lineno
            })
        self.generic_visit(node)
    
    def get_function_name(self, node):
        """提取函数调用名称"""
        if isinstance(node, ast.Name):
            return node.id
        elif isinstance(node, ast.Attribute):
            value = self.get_function_name(node.value)
            if value:
                return f"{value}.{node.attr}"
            return node.attr
        return None

def main():
    print("🚀 Django源码简化分析器")
    print("=" * 50)
    
    # Django源代码路径
    django_source = "../source/django"
    
    if not Path(django_source).exists():
        print(f"❌ Django源代码目录不存在: {django_source}")
        return
    
    # 创建分析器并运行
    analyzer = SimpleCallAnalyzer(django_source)
    analyzer.analyze_all()
    analyzer.generate_final_results()
    analyzer.save_results()
    
    print("\n🎉 分析完成！")
    print("生成文件: django_source_analysis.json")

if __name__ == "__main__":
    main()

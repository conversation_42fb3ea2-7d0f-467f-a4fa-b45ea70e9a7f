#!/usr/bin/env python3
"""
Django框架内部调用关系分析器
分析Django源代码，构造框架内部的调用关系图
"""

import ast
import json
from pathlib import Path
from typing import Dict, List, Set, Tuple
from collections import defaultdict

class DjangoCallAnalyzer:
    def __init__(self, django_source_path: str):
        self.django_path = Path(django_source_path)
        self.call_graph = defaultdict(set)  # 调用关系图
        self.class_methods = defaultdict(set)  # 类方法映射
        self.function_definitions = {}  # 函数定义位置
        self.function_locations = {}  # 函数定义的文件路径和行号
        self.call_locations = {}  # 函数调用的位置信息
        self.import_relations = defaultdict(set)  # 导入关系
        self.core_modules = [
            'core', 'db', 'http', 'views', 'urls', 'forms',
            'template', 'middleware', 'contrib', 'utils'
        ]
    
    def analyze_file(self, file_path: Path) -> Dict:
        """分析单个Python文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            tree = ast.parse(content)
            analyzer = CallVisitor(str(file_path))
            analyzer.visit(tree)

            return {
                'functions': analyzer.functions,
                'function_locations': analyzer.function_locations,
                'classes': analyzer.classes,
                'calls': analyzer.calls,
                'imports': analyzer.imports
            }
        except Exception as e:
            print(f"⚠️  分析文件失败 {file_path}: {e}")
            return {'functions': [], 'function_locations': {}, 'classes': [], 'calls': [], 'imports': []}
    
    def scan_django_source(self):
        """扫描Django源代码目录"""
        print("🔍 扫描Django源代码...")
        
        django_core = self.django_path / "django"
        if not django_core.exists():
            print(f"❌ Django源代码目录不存在: {django_core}")
            return
        
        python_files = list(django_core.rglob("*.py"))
        print(f"📁 找到 {len(python_files)} 个Python文件")
        
        file_analysis = {}
        for i, file_path in enumerate(python_files[:100]):  # 限制分析前100个文件
            if i % 20 == 0:
                print(f"📄 分析进度: {i}/{min(100, len(python_files))}")
            
            relative_path = file_path.relative_to(self.django_path)
            analysis = self.analyze_file(file_path)
            file_analysis[str(relative_path)] = analysis
            
            # 构建调用关系
            self.build_call_relations(str(relative_path), analysis)
        
        return file_analysis
    
    def build_call_relations(self, file_path: str, analysis: Dict):
        """构建调用关系"""
        module_name = self.path_to_module(file_path)
        django_relative_path = self.get_django_relative_path(file_path)

        # 记录函数和类定义位置
        for func in analysis['functions']:
            full_name = f"{module_name}.{func}"
            self.function_definitions[full_name] = file_path

        # 记录函数定义的详细位置信息
        for func_name, location_info in analysis.get('function_locations', {}).items():
            full_name = f"{module_name}.{func_name}"
            self.function_locations[full_name] = {
                'file_path': django_relative_path,
                'line_number': location_info['line_number'],
                'end_line': location_info.get('end_line', location_info['line_number'])
            }

        for cls in analysis['classes']:
            full_name = f"{module_name}.{cls['name']}"
            self.function_definitions[full_name] = file_path
            for method in cls['methods']:
                method_name = f"{full_name}.{method['name']}"
                self.class_methods[full_name].add(method['name'])
                self.function_definitions[method_name] = file_path
                # 记录方法的位置信息
                self.function_locations[method_name] = {
                    'file_path': django_relative_path,
                    'line_number': method['line_number'],
                    'end_line': method.get('end_line', method['line_number'])
                }

        # 记录调用关系和调用位置
        for call in analysis['calls']:
            caller = f"{module_name}.{call['context']}" if call['context'] else module_name
            callee = call['function']
            self.call_graph[caller].add(callee)

            # 记录调用位置
            call_key = f"{caller} -> {callee}"
            if call_key not in self.call_locations:
                self.call_locations[call_key] = []
            self.call_locations[call_key].append({
                'file_path': django_relative_path,
                'line_number': call['line']
            })

        # 记录导入关系
        for imp in analysis['imports']:
            self.import_relations[module_name].add(imp)

    def get_django_relative_path(self, file_path: str) -> str:
        """获取相对于django目录的路径"""
        path = Path(file_path)
        try:
            # 找到django目录的位置
            parts = path.parts
            django_index = -1
            for i, part in enumerate(parts):
                if part == 'django':
                    django_index = i
                    break

            if django_index >= 0:
                # 从django目录开始的相对路径
                relative_parts = parts[django_index:]
                return '/'.join(relative_parts)
            else:
                # 如果没找到django目录，返回文件名
                return path.name
        except:
            return path.name
    
    def path_to_module(self, file_path: str) -> str:
        """将文件路径转换为模块名"""
        path = file_path.replace('/', '.').replace('\\', '.')
        if path.startswith('django.'):
            path = path[7:]  # 移除django前缀
        if path.endswith('.py'):
            path = path[:-3]
        if path.endswith('.__init__'):
            path = path[:-9]
        return path
    
    def analyze_core_patterns(self):
        """分析Django核心调用模式"""
        print("🔍 分析Django核心调用模式...")
        
        patterns = {
            'request_response_cycle': self.analyze_request_response(),
            'orm_patterns': self.analyze_orm_patterns(),
            'view_patterns': self.analyze_view_patterns(),
            'middleware_chain': self.analyze_middleware_chain(),
            'template_rendering': self.analyze_template_patterns()
        }
        
        return patterns
    
    def analyze_request_response(self) -> Dict:
        """分析请求-响应循环"""
        request_cycle = {
            'entry_points': [],
            'middleware_calls': [],
            'view_dispatch': [],
            'response_creation': []
        }
        
        # 查找请求处理入口点
        for caller, callees in self.call_graph.items():
            if 'wsgi' in caller.lower() or 'asgi' in caller.lower():
                request_cycle['entry_points'].append({
                    'caller': caller,
                    'callees': list(callees)
                })
        
        return request_cycle
    
    def analyze_orm_patterns(self) -> Dict:
        """分析ORM调用模式"""
        orm_patterns = {
            'model_operations': [],
            'query_methods': [],
            'database_connections': []
        }
        
        # 查找模型相关调用
        for caller, callees in self.call_graph.items():
            if 'models' in caller or 'db' in caller:
                for callee in callees:
                    if any(keyword in callee.lower() for keyword in ['save', 'delete', 'filter', 'get', 'create']):
                        orm_patterns['model_operations'].append({
                            'caller': caller,
                            'operation': callee
                        })
        
        return orm_patterns
    
    def analyze_view_patterns(self) -> Dict:
        """分析视图调用模式"""
        view_patterns = {
            'view_classes': [],
            'view_functions': [],
            'dispatch_methods': []
        }
        
        for caller, callees in self.call_graph.items():
            if 'views' in caller:
                view_patterns['view_functions'].append({
                    'view': caller,
                    'calls': list(callees)
                })
        
        return view_patterns
    
    def analyze_middleware_chain(self) -> Dict:
        """分析中间件调用链"""
        middleware_chain = {
            'middleware_classes': [],
            'process_methods': []
        }
        
        for caller, callees in self.call_graph.items():
            if 'middleware' in caller:
                middleware_chain['middleware_classes'].append({
                    'middleware': caller,
                    'methods': list(callees)
                })
        
        return middleware_chain
    
    def analyze_template_patterns(self) -> Dict:
        """分析模板渲染模式"""
        template_patterns = {
            'template_loaders': [],
            'context_processors': [],
            'rendering_calls': []
        }
        
        for caller, callees in self.call_graph.items():
            if 'template' in caller:
                template_patterns['rendering_calls'].append({
                    'caller': caller,
                    'calls': list(callees)
                })
        
        return template_patterns
    
    def generate_call_graph_summary(self) -> Dict:
        """生成调用关系图摘要"""
        summary = {
            'total_modules': len(set(caller.split('.')[0] for caller in self.call_graph.keys())),
            'total_functions': len(self.function_definitions),
            'total_calls': sum(len(callees) for callees in self.call_graph.values()),
            'most_called_functions': self.get_most_called_functions(),
            'module_dependencies': self.get_module_dependencies(),
            'core_components': self.analyze_core_components()
        }
        
        return summary
    
    def get_most_called_functions(self) -> List[Dict]:
        """获取最常被调用的函数"""
        call_counts = defaultdict(int)
        
        for callees in self.call_graph.values():
            for callee in callees:
                call_counts[callee] += 1
        
        # 排序并返回前10个
        sorted_calls = sorted(call_counts.items(), key=lambda x: x[1], reverse=True)
        return [{'function': func, 'call_count': count} for func, count in sorted_calls[:10]]
    
    def get_module_dependencies(self) -> Dict:
        """获取模块依赖关系"""
        module_deps = defaultdict(set)
        
        for caller, callees in self.call_graph.items():
            caller_module = caller.split('.')[0]
            for callee in callees:
                if '.' in callee:
                    callee_module = callee.split('.')[0]
                    if callee_module != caller_module:
                        module_deps[caller_module].add(callee_module)
        
        return {module: list(deps) for module, deps in module_deps.items()}
    
    def analyze_core_components(self) -> Dict:
        """分析核心组件"""
        components = {}
        
        for module in self.core_modules:
            module_calls = {k: v for k, v in self.call_graph.items() if k.startswith(module)}
            if module_calls:
                components[module] = {
                    'function_count': len(module_calls),
                    'total_calls': sum(len(v) for v in module_calls.values()),
                    'key_functions': list(module_calls.keys())[:5]
                }
        
        return components
    
    def save_analysis_results(self, output_file: str = "django_call_relations.json"):
        """保存分析结果"""
        print("💾 保存分析结果...")
        
        # 构建增强的调用图，包含位置信息
        enhanced_call_graph = {}
        for caller, callees in list(self.call_graph.items())[:50]:
            enhanced_callees = []
            for callee in list(callees):
                callee_info = {'function': callee}

                # 添加被调用函数的定义位置
                if callee in self.function_locations:
                    callee_info['definition_location'] = self.function_locations[callee]

                # 添加调用位置
                call_key = f"{caller} -> {callee}"
                if call_key in self.call_locations:
                    callee_info['call_locations'] = self.call_locations[call_key]

                enhanced_callees.append(callee_info)

            # 添加调用者的位置信息
            caller_info = {
                'callees': enhanced_callees
            }
            if caller in self.function_locations:
                caller_info['caller_location'] = self.function_locations[caller]

            enhanced_call_graph[caller] = caller_info

        results = {
            'metadata': {
                'django_path': str(self.django_path),
                'analysis_scope': 'Django Framework Internal Call Relations with Location Info',
                'total_modules_analyzed': len(set(caller.split('.')[0] for caller in self.call_graph.keys()))
            },
            'call_graph_summary': self.generate_call_graph_summary(),
            'core_patterns': self.analyze_core_patterns(),
            'detailed_call_graph': enhanced_call_graph,
            'function_locations': dict(list(self.function_locations.items())[:100]),
            'function_definitions': dict(list(self.function_definitions.items())[:100]),
            'import_relations': {k: list(v) for k, v in list(self.import_relations.items())[:20]}
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 分析结果保存到: {output_file}")
        return results

class CallVisitor(ast.NodeVisitor):
    """AST访问器，用于提取函数调用信息"""

    def __init__(self, file_path: str):
        self.file_path = file_path
        self.functions = []
        self.function_locations = {}  # 函数位置信息
        self.classes = []
        self.calls = []
        self.imports = []
        self.current_class = None
        self.current_function = None
    
    def visit_FunctionDef(self, node):
        self.current_function = node.name
        self.functions.append(node.name)

        # 记录函数位置信息
        self.function_locations[node.name] = {
            'line_number': node.lineno,
            'end_line': getattr(node, 'end_lineno', node.lineno)
        }

        self.generic_visit(node)
        self.current_function = None

    def visit_ClassDef(self, node):
        self.current_class = node.name
        class_info = {
            'name': node.name,
            'methods': []
        }

        for item in node.body:
            if isinstance(item, ast.FunctionDef):
                method_info = {
                    'name': item.name,
                    'line_number': item.lineno,
                    'end_line': getattr(item, 'end_lineno', item.lineno)
                }
                class_info['methods'].append(method_info)

        self.classes.append(class_info)
        self.generic_visit(node)
        self.current_class = None
    
    def visit_Call(self, node):
        func_name = self.get_function_name(node.func)
        if func_name:
            context = self.current_function or self.current_class or 'module_level'
            self.calls.append({
                'function': func_name,
                'context': context,
                'line': node.lineno
            })
        self.generic_visit(node)
    
    def visit_Import(self, node):
        for alias in node.names:
            self.imports.append(alias.name)
        self.generic_visit(node)
    
    def visit_ImportFrom(self, node):
        if node.module:
            for alias in node.names:
                import_name = f"{node.module}.{alias.name}"
                self.imports.append(import_name)
        self.generic_visit(node)
    
    def get_function_name(self, node):
        """提取函数调用名称"""
        if isinstance(node, ast.Name):
            return node.id
        elif isinstance(node, ast.Attribute):
            value = self.get_function_name(node.value)
            if value:
                return f"{value}.{node.attr}"
            return node.attr
        return None

def main():
    print("🚀 Django框架内部调用关系分析")
    print("=" * 50)
    
    # Django源代码路径
    django_source = "/home/<USER>/Ryan_/TENmini/Task2/Django/source/django"
    if not Path(django_source).exists():
        print(f"❌ Django源代码目录不存在: {django_source}")
        return
    
    # 创建分析器
    analyzer = DjangoCallAnalyzer(django_source)
    
    # 扫描源代码
    analyzer.scan_django_source()

    # 保存分析结果
    results = analyzer.save_analysis_results()
    
    # 显示摘要
    summary = results['call_graph_summary']
    print(f"\n📊 分析摘要:")
    print(f"  总模块数: {summary['total_modules']}")
    print(f"  总函数数: {summary['total_functions']}")
    print(f"  总调用数: {summary['total_calls']}")
    
    print(f"\n🔥 最常被调用的函数:")
    for func_info in summary['most_called_functions'][:5]:
        print(f"  {func_info['function']}: {func_info['call_count']} 次")
    
    print(f"\n🏗️  核心组件:")
    for component, info in summary['core_components'].items():
        print(f"  {component}: {info['function_count']} 个函数, {info['total_calls']} 次调用")
    
    print("\n🎉 分析完成！")

if __name__ == "__main__":
    main()

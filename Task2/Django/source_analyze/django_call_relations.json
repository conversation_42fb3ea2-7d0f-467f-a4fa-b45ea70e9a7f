{"metadata": {"django_path": "/home/<USER>/<PERSON>_/TENmini/Task2/Django/source/django", "analysis_scope": "Django Framework Internal Call Relations with Location Info", "total_modules_analyzed": 6}, "call_graph_summary": {"total_modules": 6, "total_functions": 2770, "total_calls": 4602, "most_called_functions": [{"function": "super", "call_count": 134}, {"function": "isinstance", "call_count": 128}, {"function": "len", "call_count": 77}, {"function": "join", "call_count": 72}, {"function": "getattr", "call_count": 69}, {"function": "str", "call_count": 68}, {"function": "has<PERSON>r", "call_count": 61}, {"function": "ValueError", "call_count": 49}, {"function": "cursor.execute", "call_count": 43}, {"function": "list", "call_count": 36}], "module_dependencies": {"__init__": ["apps"], "__main__": ["management"], "shortcuts": ["queryset", "to", "loader", "klass"], "test": ["test_databases", "conn", "want", "os_rel_path", "valid", "parsed", "element", "os", "lines", "test_results", "cls", "test_func", "self", "optional", "mock", "kwargs", "field_kwargs", "content_type", "file", "field", "resolved_databases", "pre_attrs", "text", "creation", "xml2", "html", "warnings", "get_commands", "obj", "normalized", "got", "faulthandler", "setting_changed", "tests", "get_default_password_validators", "re", "request_started", "parser", "multiprocessing", "ordered_test_databases", "callbacks", "connection", "Engine", "key", "get_default_renderer", "record", "apps", "session", "tblib", "chain", "skip", "databases", "caches", "attrs", "connections", "request", "xml1", "pdb", "atomic", "h", "timezone", "name", "responses", "value", "children", "itertools", "extra", "test_bin", "signals", "lru_cache_object", "runner", "threading", "io", "CONTENT_TYPE_RE", "queries_log", "engine", "browser", "child", "content", "django", "logging", "path", "ASCII_WHITESPACE", "mimetypes", "test_settings", "discover_kwargs", "mirrored_aliases", "pool", "all_deps", "collections", "_norm_whitespace_re", "shuffler", "locks", "result", "JSON_CONTENT_TYPE_RE", "parsed_haystack", "time", "get_app_template_dirs", "<PERSON><PERSON><PERSON><PERSON>", "logger", "webdriver", "time_keeper", "template_rendered", "redirect_chain", "state", "json", "inner_self", "options", "sys", "response", "required", "test_runner_class", "template_names", "unittest", "cursor", "scope", "store", "data", "serialized_contents", "url", "test_tags", "Path", "<PERSON><PERSON><PERSON>", "serialize_connection", "random", "document", "posixpath", "dependencies", "got_request_exception", "r", "all_tests", "invalid", "get_finder", "connections_override", "method_names", "textwrap", "old_names", "gc", "formset", "difflib", "pickle", "HttpHeaders", "counter", "form", "deferred", "operations", "transaction", "serialize_connections", "request_finished", "trans_real"], "utils": ["signal", "conn", "config_string", "unicodedata", "instructions", "zfile", "supported_code", "phone", "os", "arg", "ESCAPE_MAPPINGS", "get_supported_language_variant", "p", "subject", "seen_files", "locales", "timestamp", "self", "kw", "copy", "termios", "word", "results", "member", "trim_whitespace_re", "kwargs", "ISO_INPUT_FORMATS", "config", "tz", "_trans", "host", "file", "middleware", "root", "singular", "gettext_module", "text", "html", "lineno_comment_map", "datetime", "part", "warnings", "tarfile", "app_paths", "postgres_interval_re", "long_open_tag_without_closing_re", "etag", "intervals", "ETAG_MATCH", "repercent", "middle", "directories", "iso8601_duration_re", "traceback", "module", "reloader", "df", "codecs", "target_etag", "file_changed", "trans", "datetime_re", "hmac", "re", "role", "parser", "time_re", "newheader", "query", "inner_args", "numberformat", "key", "g", "subprocess", "language", "s", "changed_parts", "record", "apps", "endblock_re", "tf", "urlized_words", "_hextobyte", "secrets", "colors", "reporter", "partials", "locale", "standard_duration_re", "attrs", "spec", "get_languages", "request", "cc", "XMLGenerator", "d", "styles", "message_context", "v", "domain", "lang_string", "handler", "timezone", "mtimes", "paras", "node", "value", "name", "block_re", "query_val", "dt", "itertools", "re_escaped", "dotted_path", "exe_entrypoint", "palette", "outfile", "format_locations", "local_cache", "date", "zipfile", "header", "code_list", "resultclass", "sys_file_paths", "tempfile", "other", "expression", "method", "threading", "directory", "base64", "comment", "now", "k", "directives", "translation_object", "pattern", "item", "a", "out", "str_number", "buf", "non_capturing_groups", "content", "dot_re", "context_re", "args", "query_params", "regex", "code", "logging", "operator", "long_open_tag", "path", "mimetypes", "date_re", "new_value", "message", "headerlist", "lang_code", "email", "result", "shutil", "bytes", "cat", "ctx", "re_newlines", "time", "app_reg", "logger", "extracted", "accept_language_re", "ipaddress", "dict", "autoreload_started", "etag_str", "plural_re", "base", "archive", "possible_lang_codes", "json", "new_result", "policy", "sys", "functools", "client", "number", "pieces", "new_args", "response", "MONTHS", "version_component_re", "extension_map", "parameters", "inline_re", "data", "pywatchman", "TIME_STRINGS", "_error_files", "re_formatchars", "mimetypedb", "country", "<PERSON><PERSON><PERSON>", "char2number", "script_entrypoint", "tuple", "resolved_path", "request_finished", "m", "check_for_language", "zoneinfo", "val", "random", "py_script", "filename", "match", "t", "escaped", "_js_escapes", "sender", "format_string", "django_main_thread", "version_numbers", "plural", "language_code_re", "parts", "language_code_prefix_re", "Watch<PERSON><PERSON><PERSON><PERSON>", "textwrap", "cc_delim_re", "header_query", "mail", "offset", "obj_dict", "wrapper", "uri", "settings", "smart_split_re", "cache", "line", "calendar", "params", "inspect", "quant", "re_camel_case", "dateformat", "url", "inner_result", "constant_re"], "db": ["signal", "conn", "select", "qparts", "sequence_list", "new_model", "os", "arg", "Database", "sql_parts", "json_columns", "router", "adapted", "style", "FORMAT_QMARK_REGEX", "cls", "self", "copy", "results", "kwargs", "lhs_field", "field", "field_size_re", "table", "checks", "index", "text", "create_field", "init_command", "datetime", "part", "instance", "warnings", "output_field", "kernel32", "view", "tables", "skips", "model", "errors", "obj", "end_token", "rename_mapping", "extensions", "res", "truncated_tables", "re", "multiprocessing", "query", "converters", "target_db", "connection", "connect_kwargs", "sqlite3", "key", "new_type", "namespace", "param", "s", "apps", "app_config", "platform", "chain", "check_columns", "databases", "dj_exc_value", "connections", "d", "connector", "related_models", "h", "timezone", "name", "routers", "queryset", "value", "_pattern_ops", "FormatStylePlaceholderCursor", "ondisk_db", "extra", "dt", "tzname", "conn_params", "ctypes", "signals", "old_field", "identifier", "BaseDatabaseWrapper", "expression", "threading", "new_db_params", "uuid", "statement", "Oracle_datetime", "field_type", "default", "asyncio", "token", "out", "table_name", "info", "context", "table_names", "args", "old_type", "logging", "operator", "test_settings", "settings_dict", "unique_columns", "used_cols", "shutil", "ctx", "time", "sig", "logger", "dict", "constraints", "transaction_mode", "new_field", "options", "sys", "functools", "body", "collations", "cursor", "psycopg2", "pkgu<PERSON>", "params_dict", "serialize", "serializers", "lookup_type", "f", "backend", "zoneinfo", "val", "test_name", "columns", "old_db_params", "source_db", "field_names", "t", "BulkInsertMapper", "db_type", "mapping", "output", "decimal", "offset", "seconds", "orders", "sqlparse", "description", "params", "adapt", "rhs_field", "transaction", "hints", "pk", "sql", "expected_failures"]}, "core_components": {"db": {"function_count": 469, "total_calls": 1551, "key_functions": ["db.module_level", "db.reset_queries", "db.close_old_connections", "db.utils.__exit__", "db.utils.inner"]}, "utils": {"function_count": 506, "total_calls": 1776, "key_functions": ["utils.feedgenerator.rfc2822_date", "utils.feedgenerator.rfc3339_date", "utils.feedgenerator.get_tag_uri", "utils.feedgenerator._guess_stylesheet_mimetype", "utils.feedgenerator.url"]}}}, "core_patterns": {"request_response_cycle": {"entry_points": [], "middleware_calls": [], "view_dispatch": [], "response_creation": []}, "orm_patterns": {"model_operations": [{"caller": "db.utils.__exit__", "operation": "getattr"}, {"caller": "db.utils._route_db", "operation": "getattr"}, {"caller": "db.utils._route_db", "operation": "hints.get"}, {"caller": "db.utils.get_migratable_models", "operation": "app_config.get_models"}, {"caller": "db.transaction.get_autocommit", "operation": "get_connection"}, {"caller": "db.transaction.get_autocommit", "operation": "get_autocommit"}, {"caller": "db.transaction.set_autocommit", "operation": "get_connection"}, {"caller": "db.transaction.commit", "operation": "get_connection"}, {"caller": "db.transaction.rollback", "operation": "get_connection"}, {"caller": "db.transaction.savepoint", "operation": "get_connection"}, {"caller": "db.transaction.savepoint", "operation": "savepoint"}, {"caller": "db.transaction.savepoint_rollback", "operation": "get_connection"}, {"caller": "db.transaction.savepoint_rollback", "operation": "savepoint_rollback"}, {"caller": "db.transaction.savepoint_commit", "operation": "savepoint_commit"}, {"caller": "db.transaction.savepoint_commit", "operation": "get_connection"}, {"caller": "db.transaction.clean_savepoints", "operation": "get_connection"}, {"caller": "db.transaction.clean_savepoints", "operation": "clean_savepoints"}, {"caller": "db.transaction.get_rollback", "operation": "get_connection"}, {"caller": "db.transaction.get_rollback", "operation": "get_rollback"}, {"caller": "db.transaction.set_rollback", "operation": "get_connection"}, {"caller": "db.transaction.mark_for_rollback_on_error", "operation": "get_connection"}, {"caller": "db.transaction.on_commit", "operation": "get_connection"}, {"caller": "db.transaction.__enter__", "operation": "connection.get_autocommit"}, {"caller": "db.transaction.__enter__", "operation": "get_connection"}, {"caller": "db.transaction.__enter__", "operation": "connection.savepoint_ids.append"}, {"caller": "db.transaction.__enter__", "operation": "connection.savepoint"}, {"caller": "db.transaction.__exit__", "operation": "connection.savepoint_ids.pop"}, {"caller": "db.transaction.__exit__", "operation": "connection.savepoint_rollback"}, {"caller": "db.transaction.__exit__", "operation": "get_connection"}, {"caller": "db.transaction.__exit__", "operation": "connection.savepoint_commit"}, {"caller": "db.backends.utils.module_level", "operation": "logging.getLogger"}, {"caller": "db.backends.utils.__getattr__", "operation": "getattr"}, {"caller": "db.backends.utils.format_number", "operation": "context.create_decimal"}, {"caller": "db.backends.utils.format_number", "operation": "decimal.getcontext"}, {"caller": "db.backends.ddl_references.__str__", "operation": "self.create_fk_name"}, {"caller": "db.backends.ddl_references.__str__", "operation": "self.create_index_name"}, {"caller": "db.backends.oracle.introspection.get_field_type", "operation": "get_field_type"}, {"caller": "db.backends.oracle.creation._maindb_connection", "operation": "settings_dict.get"}, {"caller": "db.backends.oracle.creation._create_test_db", "operation": "self._create_test_user"}, {"caller": "db.backends.oracle.creation._create_test_db", "operation": "self._test_user_create"}, {"caller": "db.backends.oracle.creation._create_test_db", "operation": "self._test_database_create"}, {"caller": "db.backends.oracle.creation._create_test_db", "operation": "self._get_test_db_params"}, {"caller": "db.backends.oracle.creation._handle_objects_preventing_db_destruction", "operation": "self._test_user_create"}, {"caller": "db.backends.oracle.creation._destroy_test_db", "operation": "self._test_user_create"}, {"caller": "db.backends.oracle.creation._destroy_test_db", "operation": "self._test_database_create"}, {"caller": "db.backends.oracle.creation._destroy_test_db", "operation": "self._get_test_db_params"}, {"caller": "db.backends.oracle.creation._create_test_user", "operation": "self._test_settings_get"}, {"caller": "db.backends.oracle.creation._test_settings_get", "operation": "get"}, {"caller": "db.backends.oracle.creation._test_database_name", "operation": "self._test_settings_get"}, {"caller": "db.backends.oracle.creation._test_database_create", "operation": "self._test_settings_get"}, {"caller": "db.backends.oracle.creation._test_user_create", "operation": "self._test_settings_get"}, {"caller": "db.backends.oracle.creation._test_database_user", "operation": "self._test_settings_get"}, {"caller": "db.backends.oracle.creation._test_database_passwd", "operation": "self._test_settings_get"}, {"caller": "db.backends.oracle.creation._test_database_passwd", "operation": "get_random_string"}, {"caller": "db.backends.oracle.creation._test_database_passwd", "operation": "self._test_user_create"}, {"caller": "db.backends.oracle.creation._test_database_tblspace", "operation": "self._test_settings_get"}, {"caller": "db.backends.oracle.creation._test_database_tblspace_tmp", "operation": "get"}, {"caller": "db.backends.oracle.creation._test_database_tblspace_datafile", "operation": "self._test_settings_get"}, {"caller": "db.backends.oracle.creation._test_database_tblspace_tmp_datafile", "operation": "self._test_settings_get"}, {"caller": "db.backends.oracle.creation._test_database_tblspace_maxsize", "operation": "self._test_settings_get"}, {"caller": "db.backends.oracle.creation._test_database_tblspace_tmp_maxsize", "operation": "self._test_settings_get"}, {"caller": "db.backends.oracle.creation._test_database_tblspace_size", "operation": "self._test_settings_get"}, {"caller": "db.backends.oracle.creation._test_database_tblspace_tmp_size", "operation": "self._test_settings_get"}, {"caller": "db.backends.oracle.creation._test_database_tblspace_extsize", "operation": "self._test_settings_get"}, {"caller": "db.backends.oracle.creation._test_database_tblspace_tmp_extsize", "operation": "self._test_settings_get"}, {"caller": "db.backends.oracle.creation._test_database_oracle_managed_files", "operation": "self._test_settings_get"}, {"caller": "db.backends.oracle.base.__init__", "operation": "get"}, {"caller": "db.backends.oracle.base.is_pool", "operation": "get"}, {"caller": "db.backends.oracle.base.pool", "operation": "self.get_connection_params"}, {"caller": "db.backends.oracle.base.pool", "operation": "self.settings_dict.get"}, {"caller": "db.backends.oracle.base.pool", "operation": "Database.create_pool"}, {"caller": "db.backends.oracle.base.init_connection_state", "operation": "self.get_autocommit"}, {"caller": "db.backends.oracle.base.init_connection_state", "operation": "self.create_cursor"}, {"caller": "db.backends.oracle.base.oracledb_version", "operation": "get_version_tuple"}, {"caller": "db.backends.oracle.base.__getattr__", "operation": "getattr"}, {"caller": "db.backends.oracle.base._output_type_handler", "operation": "FormatStylePlaceholderCursor._get_decimal_converter"}, {"caller": "db.backends.oracle.operations.get_db_converters", "operation": "get_db_converters"}, {"caller": "db.backends.oracle.operations.get_db_converters", "operation": "expression.output_field.get_internal_type"}, {"caller": "db.backends.oracle.operations.fetch_returned_insert_columns", "operation": "param.get_value"}, {"caller": "db.backends.oracle.operations.limit_offset_sql", "operation": "self._get_limit_offset_params"}, {"caller": "db.backends.oracle.operations.last_insert_id", "operation": "self._get_sequence_name"}, {"caller": "db.backends.oracle.operations.sequence_reset_by_name_sql", "operation": "self._get_no_autofield_sequence_name"}, {"caller": "db.backends.oracle.operations.sequence_reset_sql", "operation": "self._get_no_autofield_sequence_name"}, {"caller": "db.backends.oracle.operations._get_sequence_name", "operation": "self._get_no_autofield_sequence_name"}, {"caller": "db.backends.oracle.operations.bulk_insert_sql", "operation": "getattr"}, {"caller": "db.backends.oracle.operations.bulk_insert_sql", "operation": "BulkInsertMapper.types.get"}, {"caller": "db.backends.oracle.operations.bulk_insert_sql", "operation": "get_internal_type"}, {"caller": "db.backends.oracle.utils.__init__", "operation": "getattr"}, {"caller": "db.backends.oracle.utils.__init__", "operation": "self.types.get"}, {"caller": "db.backends.oracle.utils.__init__", "operation": "get_internal_type"}, {"caller": "db.backends.oracle.utils.get_value", "operation": "self.bound_param.getvalue"}, {"caller": "db.backends.oracle.schema.delete_model", "operation": "delete_model"}, {"caller": "db.backends.oracle.schema.delete_model", "operation": "self.connection.ops._get_no_autofield_sequence_name"}, {"caller": "db.backends.oracle.schema.alter_field", "operation": "self._delete_primary_key"}, {"caller": "db.backends.oracle.schema.alter_field", "operation": "self._create_primary_key_sql"}, {"caller": "db.backends.oracle.schema._alter_field_type_workaround", "operation": "self._create_fk_sql"}, {"caller": "db.backends.oracle.schema._alter_field_type_workaround", "operation": "new_field.get_internal_type"}, {"caller": "db.backends.oracle.schema._alter_column_type_sql", "operation": "old_field.get_internal_type"}, {"caller": "db.backends.oracle.schema._alter_column_type_sql", "operation": "new_field.get_internal_type"}, {"caller": "db.backends.oracle.schema._collate_sql", "operation": "self._get_default_collation"}, {"caller": "db.backends.postgresql.introspection.get_field_type", "operation": "get_field_type"}, {"caller": "db.backends.postgresql.creation.sql_table_creation_suffix", "operation": "test_settings.get"}, {"caller": "db.backends.postgresql.creation.sql_table_creation_suffix", "operation": "self._get_database_create_suffix"}, {"caller": "db.backends.postgresql.creation._execute_create_test_db", "operation": "_execute_create_test_db"}, {"caller": "db.backends.postgresql.creation._clone_test_db", "operation": "self._get_database_create_suffix"}, {"caller": "db.backends.postgresql.creation._clone_test_db", "operation": "self.get_test_db_clone_settings"}, {"caller": "db.backends.postgresql.creation._clone_test_db", "operation": "self._get_database_display_str"}, {"caller": "db.backends.postgresql.creation._clone_test_db", "operation": "self._execute_create_test_db"}, {"caller": "db.backends.postgresql.features.django_test_skips", "operation": "get"}, {"caller": "db.backends.postgresql.features.uses_server_side_binding", "operation": "options.get"}, {"caller": "db.backends.postgresql.features.DatabaseFeatures", "operation": "operator.attrgetter"}, {"caller": "db.backends.postgresql.psycopg_any._quote", "operation": "adapted.getquoted"}, {"caller": "db.backends.postgresql.psycopg_any.getquoted", "operation": "getquoted"}, {"caller": "db.backends.postgresql.base.psycopg_version", "operation": "get_version_tuple"}, {"caller": "db.backends.postgresql.base.pool", "operation": "get"}, {"caller": "db.backends.postgresql.base.pool", "operation": "self.get_connection_params"}, {"caller": "db.backends.postgresql.base.pool", "operation": "self.settings_dict.get"}, {"caller": "db.backends.postgresql.base.get_connection_params", "operation": "get"}, {"caller": "db.backends.postgresql.base.get_connection_params", "operation": "get_adapters_template"}, {"caller": "db.backends.postgresql.base.get_new_connection", "operation": "self.pool.getconn"}, {"caller": "db.backends.postgresql.base._configure_role", "operation": "get"}, {"caller": "db.backends.postgresql.base.init_connection_state", "operation": "self.get_autocommit"}, {"caller": "db.backends.postgresql.base.create_cursor", "operation": "get"}, {"caller": "db.backends.postgresql.base.create_cursor", "operation": "self.connection.adapters.get_loader"}, {"caller": "db.backends.postgresql.operations.unification_cast_sql", "operation": "output_field.get_internal_type"}, {"caller": "db.backends.postgresql.operations.adapt_json_value", "operation": "get_json_dumps"}, {"caller": "db.backends.postgresql.client.settings_to_cmd_args_env", "operation": "settings_dict.get"}, {"caller": "db.backends.postgresql.client.settings_to_cmd_args_env", "operation": "options.get"}, {"caller": "db.backends.postgresql.client.runshell", "operation": "signal.getsignal"}, {"caller": "db.backends.postgresql.schema._field_indexes_sql", "operation": "self._create_like_index_sql"}, {"caller": "db.backends.postgresql.schema._field_data_type", "operation": "self.connection.data_types.get"}, {"caller": "db.backends.postgresql.schema._field_data_type", "operation": "field.get_internal_type"}, {"caller": "db.backends.postgresql.schema._field_base_data_types", "operation": "field.base_field.get_internal_type"}, {"caller": "db.backends.postgresql.schema._create_like_index_sql", "operation": "self._create_index_sql"}, {"caller": "db.backends.postgresql.schema._create_like_index_sql", "operation": "getattr"}, {"caller": "db.backends.postgresql.schema._using_sql", "operation": "old_field.get_internal_type"}, {"caller": "db.backends.postgresql.schema._using_sql", "operation": "new_field.get_internal_type"}, {"caller": "db.backends.postgresql.schema._get_sequence_name", "operation": "self.connection.introspection.get_sequences"}, {"caller": "db.backends.postgresql.schema._alter_column_type_sql", "operation": "old_field.get_internal_type"}, {"caller": "db.backends.postgresql.schema._alter_column_type_sql", "operation": "self._create_index_name"}, {"caller": "db.backends.postgresql.schema._alter_column_type_sql", "operation": "new_field.get_internal_type"}, {"caller": "db.backends.postgresql.schema._alter_column_type_sql", "operation": "self._get_sequence_name"}, {"caller": "db.backends.postgresql.schema._alter_column_type_sql", "operation": "self._delete_index_sql"}, {"caller": "db.backends.postgresql.schema._alter_field", "operation": "self._create_index_name"}, {"caller": "db.backends.postgresql.schema._alter_field", "operation": "self._create_like_index_sql"}, {"caller": "db.backends.postgresql.schema._alter_field", "operation": "self._delete_index_sql"}, {"caller": "db.backends.postgresql.schema.add_index", "operation": "index.create_sql"}, {"caller": "db.backends.postgresql.schema._delete_index_sql", "operation": "_delete_index_sql"}, {"caller": "db.backends.postgresql.schema._create_index_sql", "operation": "_create_index_sql"}, {"caller": "db.backends.postgresql.compiler.assemble_as_sql", "operation": "get_internal_type"}, {"caller": "db.backends.sqlite3.introspection.get_field_type", "operation": "get_field_type"}, {"caller": "db.backends.sqlite3.introspection.get_table_description", "operation": "self._get_column_collations"}, {"caller": "db.backends.sqlite3.introspection.get_table_description", "operation": "get_field_size"}, {"caller": "db.backends.sqlite3.introspection.get_table_description", "operation": "collations.get"}, {"caller": "db.backends.sqlite3.introspection.get_sequences", "operation": "self.get_primary_key_column"}, {"caller": "db.backends.sqlite3.introspection.get_constraints", "operation": "self.get_primary_key_columns"}, {"caller": "db.backends.sqlite3.introspection.get_constraints", "operation": "self.get_table_description"}, {"caller": "db.backends.sqlite3.introspection.get_constraints", "operation": "self.get_relations"}, {"caller": "db.backends.sqlite3.introspection.get_constraints", "operation": "self._get_index_columns_orders"}, {"caller": "db.backends.sqlite3.creation._create_test_db", "operation": "self._get_database_display_str"}, {"caller": "db.backends.sqlite3.creation._create_test_db", "operation": "self._get_test_db_name"}, {"caller": "db.backends.sqlite3.creation.get_test_db_clone_settings", "operation": "multiprocessing.get_start_method"}, {"caller": "db.backends.sqlite3.creation._clone_test_db", "operation": "self.get_test_db_clone_settings"}, {"caller": "db.backends.sqlite3.creation._clone_test_db", "operation": "self._get_database_display_str"}, {"caller": "db.backends.sqlite3.creation._clone_test_db", "operation": "multiprocessing.get_start_method"}, {"caller": "db.backends.sqlite3.creation.test_db_signature", "operation": "self._get_test_db_name"}, {"caller": "db.backends.sqlite3.creation.setup_worker_connection", "operation": "self.get_test_db_clone_settings"}, {"caller": "db.backends.sqlite3.creation.setup_worker_connection", "operation": "target_db.close"}, {"caller": "db.backends.sqlite3.creation.setup_worker_connection", "operation": "os.environ.get"}, {"caller": "db.backends.sqlite3.creation.setup_worker_connection", "operation": "multiprocessing.get_start_method"}, {"caller": "db.backends.sqlite3.features.max_query_params", "operation": "self.connection.connection.getlimit"}, {"caller": "db.backends.sqlite3.features.DatabaseFeatures", "operation": "operator.attrgetter"}, {"caller": "db.backends.sqlite3._functions.register", "operation": "create_deterministic_function"}, {"caller": "db.backends.sqlite3._functions.register", "operation": "connection.create_function"}, {"caller": "db.backends.sqlite3._functions.register", "operation": "connection.create_aggregate"}, {"caller": "db.backends.sqlite3._functions._sqlite_datetime_extract", "operation": "getattr"}, {"caller": "db.backends.sqlite3._functions._sqlite_time_extract", "operation": "getattr"}, {"caller": "db.backends.sqlite3.base.check_constraints", "operation": "self.introspection.get_primary_key_column"}, {"caller": "db.backends.sqlite3.operations.check_expression_support", "operation": "expression.get_source_expressions"}, {"caller": "db.backends.sqlite3.operations.get_db_converters", "operation": "self.get_decimalfield_converter"}, {"caller": "db.backends.sqlite3.operations.get_db_converters", "operation": "get_db_converters"}, {"caller": "db.backends.sqlite3.operations.get_db_converters", "operation": "expression.output_field.get_internal_type"}, {"caller": "db.backends.sqlite3.operations.converter", "operation": "create_decimal"}, {"caller": "db.backends.sqlite3.schema.DatabaseSchemaEditor", "operation": "create_field.has_db_default"}, {"caller": "db.backends.sqlite3.schema.DatabaseSchemaEditor", "operation": "self.delete_model"}, {"caller": "db.backends.sqlite3.schema.DatabaseSchemaEditor", "operation": "getattr"}, {"caller": "db.backends.sqlite3.schema.DatabaseSchemaEditor", "operation": "self.create_model"}, {"caller": "db.backends.sqlite3.schema.DatabaseSchemaEditor", "operation": "rename_mapping.get"}, {"caller": "db.backends.sqlite3.schema.delete_model", "operation": "delete_model"}, {"caller": "db.backends.sqlite3.schema.add_field", "operation": "self.create_model"}, {"caller": "db.backends.sqlite3.schema.remove_field", "operation": "self.delete_model"}, {"caller": "db.backends.sqlite3.schema._alter_field", "operation": "new_db_params.get"}, {"caller": "db.backends.sqlite3.schema._alter_field", "operation": "old_db_params.get"}, {"caller": "db.backends.sqlite3.schema._alter_many_to_many", "operation": "new_field.remote_field.through._meta.get_field"}, {"caller": "db.backends.sqlite3.schema._alter_many_to_many", "operation": "self.create_model"}, {"caller": "db.backends.sqlite3.schema._alter_many_to_many", "operation": "old_field.remote_field.through._meta.get_field"}, {"caller": "db.backends.sqlite3.schema._alter_many_to_many", "operation": "self.delete_model"}, {"caller": "db.backends.base.introspection.get_names", "operation": "self.get_table_list"}, {"caller": "db.backends.base.introspection.BaseDatabaseIntrospection", "operation": "get_names"}, {"caller": "db.backends.base.introspection.get_migratable_models", "operation": "apps.get_app_configs"}, {"caller": "db.backends.base.introspection.get_migratable_models", "operation": "router.get_migratable_models"}, {"caller": "db.backends.base.introspection.django_table_names", "operation": "self.get_migratable_models"}, {"caller": "db.backends.base.introspection.installed_models", "operation": "self.get_migratable_models"}, {"caller": "db.backends.base.introspection.sequence_list", "operation": "self.get_migratable_models"}, {"caller": "db.backends.base.introspection.sequence_list", "operation": "self.get_sequences"}, {"caller": "db.backends.base.introspection.get_primary_key_column", "operation": "self.get_primary_key_columns"}, {"caller": "db.backends.base.introspection.get_primary_key_columns", "operation": "self.get_constraints"}, {"caller": "db.backends.base.creation.create_test_db", "operation": "os.environ.get"}, {"caller": "db.backends.base.creation.create_test_db", "operation": "self._get_database_display_str"}, {"caller": "db.backends.base.creation.create_test_db", "operation": "self._get_test_db_name"}, {"caller": "db.backends.base.creation.create_test_db", "operation": "apps.get_app_configs"}, {"caller": "db.backends.base.creation.create_test_db", "operation": "self._create_test_db"}, {"caller": "db.backends.base.creation.get_objects", "operation": "app_config.get_models"}, {"caller": "db.backends.base.creation.get_objects", "operation": "apps.get_app_configs"}, {"caller": "db.backends.base.creation.BaseDatabaseCreation", "operation": "get_objects"}, {"caller": "db.backends.base.creation.BaseDatabaseCreation", "operation": "out.getvalue"}, {"caller": "db.backends.base.creation.deserialize_db_from_string", "operation": "obj.save"}, {"caller": "db.backends.base.creation._create_test_db", "operation": "self._get_test_db_name"}, {"caller": "db.backends.base.creation._create_test_db", "operation": "self._get_database_display_str"}, {"caller": "db.backends.base.creation._create_test_db", "operation": "self._execute_create_test_db"}, {"caller": "db.backends.base.creation.clone_test_db", "operation": "self._get_database_display_str"}, {"caller": "db.backends.base.creation.destroy_test_db", "operation": "self.get_test_db_clone_settings"}, {"caller": "db.backends.base.creation.destroy_test_db", "operation": "self._get_database_display_str"}, {"caller": "db.backends.base.creation.mark_expected_failures_and_skips", "operation": "getattr"}, {"caller": "db.backends.base.creation.test_db_signature", "operation": "self._get_test_db_name"}, {"caller": "db.backends.base.creation.setup_worker_connection", "operation": "self.get_test_db_clone_settings"}], "query_methods": [], "database_connections": []}, "view_patterns": {"view_classes": [], "view_functions": [], "dispatch_methods": []}, "middleware_chain": {"middleware_classes": [{"middleware": "utils.decorators.decorator_from_middleware_with_args", "methods": ["make_middleware_decorator"]}, {"middleware": "utils.decorators.decorator_from_middleware", "methods": ["make_middleware_decorator"]}], "process_methods": []}, "template_rendering": {"template_loaders": [], "context_processors": [], "rendering_calls": [{"caller": "test.signals.reset_template_engines", "calls": ["receiver", "get_default_renderer.cache_clear", "Engine.get_default.cache_clear"]}, {"caller": "test.client.store_rendered_templates", "calls": ["ContextList", "copy", "append", "store.setdefault"]}, {"caller": "test.testcases.on_template_render", "calls": ["self.rendered_templates.append", "copy", "self.context.append"]}, {"caller": "test.testcases._get_template_used", "calls": ["TypeError", "self._check_test_client_response", "has<PERSON>r"]}, {"caller": "test.testcases._assert_template_used", "calls": ["join", "template_names.count", "self.fail", "self.assertTrue", "self.assertEqual"]}, {"caller": "utils.timezone.template_localtime", "calls": ["getattr", "localtime", "isinstance", "is_naive"]}, {"caller": "utils.translation.template.module_level", "calls": ["_lazy_re_compile", "inline_re.match", "startswith", "<PERSON><PERSON>", "g.replace", "p.split", "content.splitlines", "tokenize", "t.contents.replace", "p.find", "singular.append", "join", "format", "plural_re.match", "lineno_comment_map.setdefault", "constant_re.findall", "block_re.match", "context_re.match", "warnings.warn", "append", "t.split_contents", "join_tokens", "line.lstrip", "enumerate", "SyntaxError", "t.contents.split", "t.contents.count", "constant_re.match", "g.strip", "comment.append", "blankout", "t.contents.lstrip", "out.write", "plural.append", "endblock_re.match", "out.getvalue", "message_context.strip"]}, {"caller": "utils.translation.template.blankout", "calls": ["dot_re.sub"]}, {"caller": "utils.translation.template.templatize", "calls": ["StringIO"]}, {"caller": "utils.translation.template.join_tokens", "calls": ["join", "trim_whitespace"]}, {"caller": "db.backends.postgresql.psycopg_any.get_adapters_template", "calls": ["ctx.register_dumper", "adapt.AdaptersMap", "register_tzloader", "ctx.register_loader"]}]}}, "detailed_call_graph": {"__init__.module_level": {"callees": [{"function": "get_version", "call_locations": [{"file_path": "django/__init__.py", "line_number": 5}]}]}, "__init__.setup": {"callees": [{"function": "configure_logging", "call_locations": [{"file_path": "django/__init__.py", "line_number": 19}]}, {"function": "apps.populate", "call_locations": [{"file_path": "django/__init__.py", "line_number": 24}]}, {"function": "set_script_prefix", "call_locations": [{"file_path": "django/__init__.py", "line_number": 21}]}], "caller_location": {"file_path": "django/__init__.py", "line_number": 8, "end_line": 24}}, "__main__.module_level": {"callees": [{"function": "management.execute_from_command_line", "call_locations": [{"file_path": "django/__main__.py", "line_number": 10}]}]}, "shortcuts.render": {"callees": [{"function": "loader.render_to_string", "call_locations": [{"file_path": "django/shortcuts.py", "line_number": 25}]}, {"function": "HttpResponse", "call_locations": [{"file_path": "django/shortcuts.py", "line_number": 26}]}], "caller_location": {"file_path": "django/shortcuts.py", "line_number": 18, "end_line": 26}}, "shortcuts.redirect": {"callees": [{"function": "resolve_url", "call_locations": [{"file_path": "django/shortcuts.py", "line_number": 51}]}, {"function": "redirect_class", "call_locations": [{"file_path": "django/shortcuts.py", "line_number": 50}]}], "caller_location": {"file_path": "django/shortcuts.py", "line_number": 29, "end_line": 53}}, "shortcuts._get_queryset": {"callees": [{"function": "klass._default_manager.all", "call_locations": [{"file_path": "django/shortcuts.py", "line_number": 65}]}, {"function": "has<PERSON>r", "call_locations": [{"file_path": "django/shortcuts.py", "line_number": 64}]}], "caller_location": {"file_path": "django/shortcuts.py", "line_number": 56, "end_line": 66}}, "shortcuts.get_object_or_404": {"callees": [{"function": "queryset.get", "call_locations": [{"file_path": "django/shortcuts.py", "line_number": 90}]}, {"function": "has<PERSON>r", "call_locations": [{"file_path": "django/shortcuts.py", "line_number": 81}]}, {"function": "_get_queryset", "call_locations": [{"file_path": "django/shortcuts.py", "line_number": 80}]}, {"function": "Http404", "call_locations": [{"file_path": "django/shortcuts.py", "line_number": 92}]}, {"function": "ValueError", "call_locations": [{"file_path": "django/shortcuts.py", "line_number": 85}]}, {"function": "isinstance", "call_locations": [{"file_path": "django/shortcuts.py", "line_number": 83}]}], "caller_location": {"file_path": "django/shortcuts.py", "line_number": 69, "end_line": 94}}, "shortcuts.module_level": {"callees": [{"function": "has<PERSON>r", "call_locations": [{"file_path": "django/shortcuts.py", "line_number": 100}, {"file_path": "django/shortcuts.py", "line_number": 142}]}, {"function": "_get_queryset", "call_locations": [{"file_path": "django/shortcuts.py", "line_number": 99}, {"file_path": "django/shortcuts.py", "line_number": 141}]}, {"function": "Http404", "call_locations": [{"file_path": "django/shortcuts.py", "line_number": 111}, {"file_path": "django/shortcuts.py", "line_number": 152}]}, {"function": "queryset.aget", "call_locations": [{"file_path": "django/shortcuts.py", "line_number": 109}]}, {"function": "ValueError", "call_locations": [{"file_path": "django/shortcuts.py", "line_number": 104}, {"file_path": "django/shortcuts.py", "line_number": 146}]}, {"function": "queryset.filter", "call_locations": [{"file_path": "django/shortcuts.py", "line_number": 150}]}, {"function": "isinstance", "call_locations": [{"file_path": "django/shortcuts.py", "line_number": 102}, {"file_path": "django/shortcuts.py", "line_number": 144}]}]}, "shortcuts.get_list_or_404": {"callees": [{"function": "list", "call_locations": [{"file_path": "django/shortcuts.py", "line_number": 131}]}, {"function": "has<PERSON>r", "call_locations": [{"file_path": "django/shortcuts.py", "line_number": 123}]}, {"function": "_get_queryset", "call_locations": [{"file_path": "django/shortcuts.py", "line_number": 122}]}, {"function": "Http404", "call_locations": [{"file_path": "django/shortcuts.py", "line_number": 133}]}, {"function": "ValueError", "call_locations": [{"file_path": "django/shortcuts.py", "line_number": 127}]}, {"function": "queryset.filter", "call_locations": [{"file_path": "django/shortcuts.py", "line_number": 131}]}, {"function": "isinstance", "call_locations": [{"file_path": "django/shortcuts.py", "line_number": 125}]}], "caller_location": {"file_path": "django/shortcuts.py", "line_number": 114, "end_line": 136}}, "shortcuts.resolve_url": {"callees": [{"function": "callable", "call_locations": [{"file_path": "django/shortcuts.py", "line_number": 187}]}, {"function": "has<PERSON>r", "call_locations": [{"file_path": "django/shortcuts.py", "line_number": 170}]}, {"function": "str", "call_locations": [{"file_path": "django/shortcuts.py", "line_number": 176}]}, {"function": "reverse", "call_locations": [{"file_path": "django/shortcuts.py", "line_number": 184}]}, {"function": "to.get_absolute_url", "call_locations": [{"file_path": "django/shortcuts.py", "line_number": 171}]}, {"function": "isinstance", "call_locations": [{"file_path": "django/shortcuts.py", "line_number": 173}, {"file_path": "django/shortcuts.py", "line_number": 179}]}, {"function": "to.startswith", "call_locations": [{"file_path": "django/shortcuts.py", "line_number": 179}]}], "caller_location": {"file_path": "django/shortcuts.py", "line_number": 156, "end_line": 194}}, "test.html.module_level": {"callees": [{"function": "_lazy_re_compile", "call_locations": [{"file_path": "django/test/html.py", "line_number": 12}]}]}, "test.html.normalize_whitespace": {"callees": [{"function": "ASCII_WHITESPACE.sub", "call_locations": [{"file_path": "django/test/html.py", "line_number": 46}]}], "caller_location": {"file_path": "django/test/html.py", "line_number": 45, "end_line": 46}}, "test.html.normalize_attributes": {"callees": [{"function": "normalized.append", "call_locations": [{"file_path": "django/test/html.py", "line_number": 66}]}, {"function": "join", "call_locations": [{"file_path": "django/test/html.py", "line_number": 55}]}, {"function": "sorted", "call_locations": [{"file_path": "django/test/html.py", "line_number": 56}]}, {"function": "ASCII_WHITESPACE.split", "call_locations": [{"file_path": "django/test/html.py", "line_number": 56}]}], "caller_location": {"file_path": "django/test/html.py", "line_number": 49, "end_line": 67}}, "test.html.__init__": {"callees": [{"function": "super", "call_locations": [{"file_path": "django/test/html.py", "line_number": 192}, {"file_path": "django/test/html.py", "line_number": 206}]}, {"function": "__init__", "call_locations": [{"file_path": "django/test/html.py", "line_number": 192}, {"file_path": "django/test/html.py", "line_number": 206}]}, {"function": "sorted", "call_locations": [{"file_path": "django/test/html.py", "line_number": 73}]}, {"function": "RootElement", "call_locations": [{"file_path": "django/test/html.py", "line_number": 207}]}], "caller_location": {"file_path": "django/test/html.py", "line_number": 205, "end_line": 209}}, "test.html.append": {"callees": [{"function": "isspace", "call_locations": [{"file_path": "django/test/html.py", "line_number": 87}]}, {"function": "self.children.pop", "call_locations": [{"file_path": "django/test/html.py", "line_number": 88}]}, {"function": "normalize_whitespace", "call_locations": [{"file_path": "django/test/html.py", "line_number": 78}, {"file_path": "django/test/html.py", "line_number": 81}]}, {"function": "isinstance", "call_locations": [{"file_path": "django/test/html.py", "line_number": 77}, {"file_path": "django/test/html.py", "line_number": 79}, {"file_path": "django/test/html.py", "line_number": 87}]}, {"function": "self.children.append", "call_locations": [{"file_path": "django/test/html.py", "line_number": 90}]}], "caller_location": {"file_path": "django/test/html.py", "line_number": 76, "end_line": 90}}, "test.html.rstrip_last_element": {"callees": [{"function": "children.pop", "call_locations": [{"file_path": "django/test/html.py", "line_number": 97}]}, {"function": "rstrip_last_element", "call_locations": [{"file_path": "django/test/html.py", "line_number": 98}]}, {"function": "isinstance", "call_locations": [{"file_path": "django/test/html.py", "line_number": 94}]}, {"function": "rstrip", "call_locations": [{"file_path": "django/test/html.py", "line_number": 95}]}], "caller_location": {"file_path": "django/test/html.py", "line_number": 93, "end_line": 99}}, "test.html.Element": {"callees": [{"function": "rstrip_last_element", "call_locations": [{"file_path": "django/test/html.py", "line_number": 101}]}, {"function": "has<PERSON>r", "call_locations": [{"file_path": "django/test/html.py", "line_number": 105}]}, {"function": "child.finalize", "call_locations": [{"file_path": "django/test/html.py", "line_number": 106}]}, {"function": "enumerate", "call_locations": [{"file_path": "django/test/html.py", "line_number": 102}]}, {"function": "child.strip", "call_locations": [{"file_path": "django/test/html.py", "line_number": 104}]}, {"function": "isinstance", "call_locations": [{"file_path": "django/test/html.py", "line_number": 103}]}]}, "test.html.__eq__": {"callees": [{"function": "has<PERSON>r", "call_locations": [{"file_path": "django/test/html.py", "line_number": 109}]}], "caller_location": {"file_path": "django/test/html.py", "line_number": 108, "end_line": 113}}, "test.html.__hash__": {"callees": [{"function": "hash", "call_locations": [{"file_path": "django/test/html.py", "line_number": 116}]}], "caller_location": {"file_path": "django/test/html.py", "line_number": 115, "end_line": 116}}, "test.html._count": {"callees": [{"function": "len", "call_locations": [{"file_path": "django/test/html.py", "line_number": 149}]}, {"function": "isinstance", "call_locations": [{"file_path": "django/test/html.py", "line_number": 119}, {"file_path": "django/test/html.py", "line_number": 121}, {"file_path": "django/test/html.py", "line_number": 128}, {"file_path": "django/test/html.py", "line_number": 129}, {"file_path": "django/test/html.py", "line_number": 143}]}, {"function": "child.count", "call_locations": [{"file_path": "django/test/html.py", "line_number": 131}]}, {"function": "child._count", "call_locations": [{"file_path": "django/test/html.py", "line_number": 136}]}], "caller_location": {"file_path": "django/test/html.py", "line_number": 118, "end_line": 155}}, "test.html.__contains__": {"callees": [{"function": "self._count", "call_locations": [{"file_path": "django/test/html.py", "line_number": 158}]}], "caller_location": {"file_path": "django/test/html.py", "line_number": 157, "end_line": 158}}, "test.html.count": {"callees": [{"function": "self._count", "call_locations": [{"file_path": "django/test/html.py", "line_number": 161}]}], "caller_location": {"file_path": "django/test/html.py", "line_number": 160, "end_line": 161}}, "test.html.__str__": {"callees": [{"function": "html.escape", "call_locations": [{"file_path": "django/test/html.py", "line_number": 177}, {"file_path": "django/test/html.py", "line_number": 196}]}, {"function": "join", "call_locations": [{"file_path": "django/test/html.py", "line_number": 175}, {"file_path": "django/test/html.py", "line_number": 195}]}, {"function": "isinstance", "call_locations": [{"file_path": "django/test/html.py", "line_number": 177}, {"file_path": "django/test/html.py", "line_number": 196}]}, {"function": "str", "call_locations": [{"file_path": "django/test/html.py", "line_number": 177}, {"file_path": "django/test/html.py", "line_number": 196}]}], "caller_location": {"file_path": "django/test/html.py", "line_number": 194, "end_line": 197}}, "test.html.__repr__": {"callees": [{"function": "str", "call_locations": [{"file_path": "django/test/html.py", "line_number": 187}]}], "caller_location": {"file_path": "django/test/html.py", "line_number": 186, "end_line": 187}}, "test.html.error": {"callees": [{"function": "self.getpos", "call_locations": [{"file_path": "django/test/html.py", "line_number": 212}]}, {"function": "HTMLParseError", "call_locations": [{"file_path": "django/test/html.py", "line_number": 212}]}], "caller_location": {"file_path": "django/test/html.py", "line_number": 211, "end_line": 212}}, "test.html.format_position": {"callees": [{"function": "self.getpos", "call_locations": [{"file_path": "django/test/html.py", "line_number": 218}]}, {"function": "has<PERSON>r", "call_locations": [{"file_path": "django/test/html.py", "line_number": 219}]}], "caller_location": {"file_path": "django/test/html.py", "line_number": 214, "end_line": 221}}, "test.html.handle_startendtag": {"callees": [{"function": "self.handle_starttag", "call_locations": [{"file_path": "django/test/html.py", "line_number": 231}]}, {"function": "self.handle_endtag", "call_locations": [{"file_path": "django/test/html.py", "line_number": 233}]}], "caller_location": {"file_path": "django/test/html.py", "line_number": 230, "end_line": 233}}, "test.html.handle_starttag": {"callees": [{"function": "self.current.append", "call_locations": [{"file_path": "django/test/html.py", "line_number": 238}]}, {"function": "self.open_tags.append", "call_locations": [{"file_path": "django/test/html.py", "line_number": 240}]}, {"function": "normalize_attributes", "call_locations": [{"file_path": "django/test/html.py", "line_number": 236}]}, {"function": "self.getpos", "call_locations": [{"file_path": "django/test/html.py", "line_number": 241}]}, {"function": "Element", "call_locations": [{"file_path": "django/test/html.py", "line_number": 237}]}], "caller_location": {"file_path": "django/test/html.py", "line_number": 235, "end_line": 241}}, "test.html.handle_endtag": {"callees": [{"function": "self.open_tags.pop", "call_locations": [{"file_path": "django/test/html.py", "line_number": 246}, {"file_path": "django/test/html.py", "line_number": 252}]}, {"function": "self.format_position", "call_locations": [{"file_path": "django/test/html.py", "line_number": 245}, {"file_path": "django/test/html.py", "line_number": 250}]}, {"function": "self.error", "call_locations": [{"file_path": "django/test/html.py", "line_number": 245}, {"file_path": "django/test/html.py", "line_number": 249}]}], "caller_location": {"file_path": "django/test/html.py", "line_number": 243, "end_line": 252}}, "test.html.handle_data": {"callees": [{"function": "self.current.append", "call_locations": [{"file_path": "django/test/html.py", "line_number": 255}]}], "caller_location": {"file_path": "django/test/html.py", "line_number": 254, "end_line": 255}}, "test.html.parse_html": {"callees": [{"function": "len", "call_locations": [{"file_path": "django/test/html.py", "line_number": 271}]}, {"function": "document.finalize", "call_locations": [{"file_path": "django/test/html.py", "line_number": 269}]}, {"function": "parser.close", "call_locations": [{"file_path": "django/test/html.py", "line_number": 267}]}, {"function": "<PERSON><PERSON><PERSON>", "call_locations": [{"file_path": "django/test/html.py", "line_number": 265}]}, {"function": "parser.feed", "call_locations": [{"file_path": "django/test/html.py", "line_number": 266}]}, {"function": "isinstance", "call_locations": [{"file_path": "django/test/html.py", "line_number": 271}]}], "caller_location": {"file_path": "django/test/html.py", "line_number": 258, "end_line": 273}}, "test.signals.module_level": {"callees": [{"function": "Signal", "call_locations": [{"file_path": "django/test/signals.py", "line_number": 17}]}]}, "test.signals.clear_cache_handlers": {"callees": [{"function": "close_caches", "call_locations": [{"file_path": "django/test/signals.py", "line_number": 31}]}, {"function": "caches.configure_settings", "call_locations": [{"file_path": "django/test/signals.py", "line_number": 32}]}, {"function": "Local", "call_locations": [{"file_path": "django/test/signals.py", "line_number": 33}]}, {"function": "receiver", "call_locations": [{"file_path": "django/test/signals.py", "line_number": 26}]}], "caller_location": {"file_path": "django/test/signals.py", "line_number": 27, "end_line": 33}}, "test.signals.update_installed_apps": {"callees": [{"function": "receiver", "call_locations": [{"file_path": "django/test/signals.py", "line_number": 36}]}, {"function": "get_finder.cache_clear", "call_locations": [{"file_path": "django/test/signals.py", "line_number": 42}]}, {"function": "get_commands.cache_clear", "call_locations": [{"file_path": "django/test/signals.py", "line_number": 46}]}, {"function": "get_app_template_dirs.cache_clear", "call_locations": [{"file_path": "django/test/signals.py", "line_number": 50}]}], "caller_location": {"file_path": "django/test/signals.py", "line_number": 37, "end_line": 54}}, "test.signals.update_connections_time_zone": {"callees": [{"function": "receiver", "call_locations": [{"file_path": "django/test/signals.py", "line_number": 57}]}, {"function": "timezone.get_default_timezone.cache_clear", "call_locations": [{"file_path": "django/test/signals.py", "line_number": 69}]}, {"function": "has<PERSON>r", "call_locations": [{"file_path": "django/test/signals.py", "line_number": 61}]}, {"function": "time.tzset", "call_locations": [{"file_path": "django/test/signals.py", "line_number": 66}]}, {"function": "conn.ensure_timezone", "call_locations": [{"file_path": "django/test/signals.py", "line_number": 82}]}, {"function": "connections.all", "call_locations": [{"file_path": "django/test/signals.py", "line_number": 73}]}, {"function": "os.environ.pop", "call_locations": [{"file_path": "django/test/signals.py", "line_number": 65}]}], "caller_location": {"file_path": "django/test/signals.py", "line_number": 58, "end_line": 82}}, "test.signals.clear_routers_cache": {"callees": [{"function": "ConnectionRouter", "call_locations": [{"file_path": "django/test/signals.py", "line_number": 88}]}, {"function": "receiver", "call_locations": [{"file_path": "django/test/signals.py", "line_number": 85}]}], "caller_location": {"file_path": "django/test/signals.py", "line_number": 86, "end_line": 88}}, "test.signals.reset_template_engines": {"callees": [{"function": "receiver", "call_locations": [{"file_path": "django/test/signals.py", "line_number": 91}]}, {"function": "get_default_renderer.cache_clear", "call_locations": [{"file_path": "django/test/signals.py", "line_number": 111}]}, {"function": "Engine.get_default.cache_clear", "call_locations": [{"file_path": "django/test/signals.py", "line_number": 108}]}], "caller_location": {"file_path": "django/test/signals.py", "line_number": 92, "end_line": 111}}, "test.signals.storages_changed": {"callees": [{"function": "receiver", "call_locations": [{"file_path": "django/test/signals.py", "line_number": 114}]}], "caller_location": {"file_path": "django/test/signals.py", "line_number": 115, "end_line": 132}}, "test.signals.clear_serializers_cache": {"callees": [{"function": "receiver", "call_locations": [{"file_path": "django/test/signals.py", "line_number": 135}]}], "caller_location": {"file_path": "django/test/signals.py", "line_number": 136, "end_line": 140}}, "test.signals.language_changed": {"callees": [{"function": "receiver", "call_locations": [{"file_path": "django/test/signals.py", "line_number": 143}]}, {"function": "Local", "call_locations": [{"file_path": "django/test/signals.py", "line_number": 149}]}, {"function": "trans_real.check_for_language.cache_clear", "call_locations": [{"file_path": "django/test/signals.py", "line_number": 154}]}], "caller_location": {"file_path": "django/test/signals.py", "line_number": 144, "end_line": 154}}, "test.signals.localize_settings_changed": {"callees": [{"function": "receiver", "call_locations": [{"file_path": "django/test/signals.py", "line_number": 157}]}, {"function": "reset_format_cache", "call_locations": [{"file_path": "django/test/signals.py", "line_number": 160}]}], "caller_location": {"file_path": "django/test/signals.py", "line_number": 158, "end_line": 160}}, "test.signals.complex_setting_changed": {"callees": [{"function": "receiver", "call_locations": [{"file_path": "django/test/signals.py", "line_number": 163}]}, {"function": "warnings.warn", "call_locations": [{"file_path": "django/test/signals.py", "line_number": 168}]}], "caller_location": {"file_path": "django/test/signals.py", "line_number": 164, "end_line": 171}}, "test.signals.root_urlconf_changed": {"callees": [{"function": "receiver", "call_locations": [{"file_path": "django/test/signals.py", "line_number": 174}]}, {"function": "set_urlconf", "call_locations": [{"file_path": "django/test/signals.py", "line_number": 180}]}, {"function": "clear_url_caches", "call_locations": [{"file_path": "django/test/signals.py", "line_number": 179}]}], "caller_location": {"file_path": "django/test/signals.py", "line_number": 175, "end_line": 180}}, "test.signals.static_storage_changed": {"callees": [{"function": "receiver", "call_locations": [{"file_path": "django/test/signals.py", "line_number": 183}]}], "caller_location": {"file_path": "django/test/signals.py", "line_number": 184, "end_line": 191}}, "test.signals.static_finders_changed": {"callees": [{"function": "receiver", "call_locations": [{"file_path": "django/test/signals.py", "line_number": 194}]}, {"function": "get_finder.cache_clear", "call_locations": [{"file_path": "django/test/signals.py", "line_number": 202}]}], "caller_location": {"file_path": "django/test/signals.py", "line_number": 195, "end_line": 202}}, "test.signals.form_renderer_changed": {"callees": [{"function": "receiver", "call_locations": [{"file_path": "django/test/signals.py", "line_number": 205}]}, {"function": "get_default_renderer.cache_clear", "call_locations": [{"file_path": "django/test/signals.py", "line_number": 210}]}], "caller_location": {"file_path": "django/test/signals.py", "line_number": 206, "end_line": 210}}, "test.signals.auth_password_validators_changed": {"callees": [{"function": "receiver", "call_locations": [{"file_path": "django/test/signals.py", "line_number": 213}]}, {"function": "get_default_password_validators.cache_clear", "call_locations": [{"file_path": "django/test/signals.py", "line_number": 220}]}], "caller_location": {"file_path": "django/test/signals.py", "line_number": 214, "end_line": 220}}, "test.signals.user_model_swapped": {"callees": [{"function": "receiver", "call_locations": [{"file_path": "django/test/signals.py", "line_number": 223}]}, {"function": "apps.clear_cache", "call_locations": [{"file_path": "django/test/signals.py", "line_number": 226}]}, {"function": "get_user_model", "call_locations": [{"file_path": "django/test/signals.py", "line_number": 230}]}], "caller_location": {"file_path": "django/test/signals.py", "line_number": 224, "end_line": 253}}, "test.utils.module_level": {"callees": [{"function": "want.startswith", "call_locations": [{"file_path": "django/test/utils.py", "line_number": 690}]}, {"function": "skipUn<PERSON>", "call_locations": [{"file_path": "django/test/utils.py", "line_number": 766}]}, {"function": "has<PERSON>r", "call_locations": [{"file_path": "django/test/utils.py", "line_number": 58}]}, {"function": "first_node", "call_locations": [{"file_path": "django/test/utils.py", "line_number": 696}, {"file_path": "django/test/utils.py", "line_number": 697}]}, {"function": "replace", "call_locations": [{"file_path": "django/test/utils.py", "line_number": 685}, {"file_path": "django/test/utils.py", "line_number": 686}]}, {"function": "got.strip", "call_locations": [{"file_path": "django/test/utils.py", "line_number": 686}]}, {"function": "want.strip", "call_locations": [{"file_path": "django/test/utils.py", "line_number": 685}]}, {"function": "check_element", "call_locations": [{"file_path": "django/test/utils.py", "line_number": 699}]}, {"function": "parseString", "call_locations": [{"file_path": "django/test/utils.py", "line_number": 696}, {"file_path": "django/test/utils.py", "line_number": 697}]}]}, "test.utils.__repr__": {"callees": [{"function": "repr", "call_locations": [{"file_path": "django/test/utils.py", "line_number": 67}]}], "caller_location": {"file_path": "django/test/utils.py", "line_number": 66, "end_line": 67}}}, "function_locations": {"__init__.setup": {"file_path": "django/__init__.py", "line_number": 8, "end_line": 24}, "shortcuts.render": {"file_path": "django/shortcuts.py", "line_number": 18, "end_line": 26}, "shortcuts.redirect": {"file_path": "django/shortcuts.py", "line_number": 29, "end_line": 53}, "shortcuts._get_queryset": {"file_path": "django/shortcuts.py", "line_number": 56, "end_line": 66}, "shortcuts.get_object_or_404": {"file_path": "django/shortcuts.py", "line_number": 69, "end_line": 94}, "shortcuts.get_list_or_404": {"file_path": "django/shortcuts.py", "line_number": 114, "end_line": 136}, "shortcuts.resolve_url": {"file_path": "django/shortcuts.py", "line_number": 156, "end_line": 194}, "test.html.normalize_whitespace": {"file_path": "django/test/html.py", "line_number": 45, "end_line": 46}, "test.html.normalize_attributes": {"file_path": "django/test/html.py", "line_number": 49, "end_line": 67}, "test.html.__init__": {"file_path": "django/test/html.py", "line_number": 205, "end_line": 209}, "test.html.append": {"file_path": "django/test/html.py", "line_number": 76, "end_line": 90}, "test.html.finalize": {"file_path": "django/test/html.py", "line_number": 92, "end_line": 106}, "test.html.rstrip_last_element": {"file_path": "django/test/html.py", "line_number": 93, "end_line": 99}, "test.html.__eq__": {"file_path": "django/test/html.py", "line_number": 108, "end_line": 113}, "test.html.__hash__": {"file_path": "django/test/html.py", "line_number": 115, "end_line": 116}, "test.html._count": {"file_path": "django/test/html.py", "line_number": 118, "end_line": 155}, "test.html.__contains__": {"file_path": "django/test/html.py", "line_number": 157, "end_line": 158}, "test.html.count": {"file_path": "django/test/html.py", "line_number": 160, "end_line": 161}, "test.html.__getitem__": {"file_path": "django/test/html.py", "line_number": 163, "end_line": 164}, "test.html.__str__": {"file_path": "django/test/html.py", "line_number": 194, "end_line": 197}, "test.html.__repr__": {"file_path": "django/test/html.py", "line_number": 186, "end_line": 187}, "test.html.error": {"file_path": "django/test/html.py", "line_number": 211, "end_line": 212}, "test.html.format_position": {"file_path": "django/test/html.py", "line_number": 214, "end_line": 221}, "test.html.current": {"file_path": "django/test/html.py", "line_number": 224, "end_line": 228}, "test.html.handle_startendtag": {"file_path": "django/test/html.py", "line_number": 230, "end_line": 233}, "test.html.handle_starttag": {"file_path": "django/test/html.py", "line_number": 235, "end_line": 241}, "test.html.handle_endtag": {"file_path": "django/test/html.py", "line_number": 243, "end_line": 252}, "test.html.handle_data": {"file_path": "django/test/html.py", "line_number": 254, "end_line": 255}, "test.html.parse_html": {"file_path": "django/test/html.py", "line_number": 258, "end_line": 273}, "test.html.Element.__init__": {"file_path": "django/test/html.py", "line_number": 71, "end_line": 74}, "test.html.Element.append": {"file_path": "django/test/html.py", "line_number": 76, "end_line": 90}, "test.html.Element.finalize": {"file_path": "django/test/html.py", "line_number": 92, "end_line": 106}, "test.html.Element.__eq__": {"file_path": "django/test/html.py", "line_number": 108, "end_line": 113}, "test.html.Element.__hash__": {"file_path": "django/test/html.py", "line_number": 115, "end_line": 116}, "test.html.Element._count": {"file_path": "django/test/html.py", "line_number": 118, "end_line": 155}, "test.html.Element.__contains__": {"file_path": "django/test/html.py", "line_number": 157, "end_line": 158}, "test.html.Element.count": {"file_path": "django/test/html.py", "line_number": 160, "end_line": 161}, "test.html.Element.__getitem__": {"file_path": "django/test/html.py", "line_number": 163, "end_line": 164}, "test.html.Element.__str__": {"file_path": "django/test/html.py", "line_number": 166, "end_line": 184}, "test.html.Element.__repr__": {"file_path": "django/test/html.py", "line_number": 186, "end_line": 187}, "test.html.RootElement.__init__": {"file_path": "django/test/html.py", "line_number": 191, "end_line": 192}, "test.html.RootElement.__str__": {"file_path": "django/test/html.py", "line_number": 194, "end_line": 197}, "test.html.Parser.__init__": {"file_path": "django/test/html.py", "line_number": 205, "end_line": 209}, "test.html.Parser.error": {"file_path": "django/test/html.py", "line_number": 211, "end_line": 212}, "test.html.Parser.format_position": {"file_path": "django/test/html.py", "line_number": 214, "end_line": 221}, "test.html.Parser.current": {"file_path": "django/test/html.py", "line_number": 224, "end_line": 228}, "test.html.Parser.handle_startendtag": {"file_path": "django/test/html.py", "line_number": 230, "end_line": 233}, "test.html.Parser.handle_starttag": {"file_path": "django/test/html.py", "line_number": 235, "end_line": 241}, "test.html.Parser.handle_endtag": {"file_path": "django/test/html.py", "line_number": 243, "end_line": 252}, "test.html.Parser.handle_data": {"file_path": "django/test/html.py", "line_number": 254, "end_line": 255}, "test.signals.clear_cache_handlers": {"file_path": "django/test/signals.py", "line_number": 27, "end_line": 33}, "test.signals.update_installed_apps": {"file_path": "django/test/signals.py", "line_number": 37, "end_line": 54}, "test.signals.update_connections_time_zone": {"file_path": "django/test/signals.py", "line_number": 58, "end_line": 82}, "test.signals.clear_routers_cache": {"file_path": "django/test/signals.py", "line_number": 86, "end_line": 88}, "test.signals.reset_template_engines": {"file_path": "django/test/signals.py", "line_number": 92, "end_line": 111}, "test.signals.storages_changed": {"file_path": "django/test/signals.py", "line_number": 115, "end_line": 132}, "test.signals.clear_serializers_cache": {"file_path": "django/test/signals.py", "line_number": 136, "end_line": 140}, "test.signals.language_changed": {"file_path": "django/test/signals.py", "line_number": 144, "end_line": 154}, "test.signals.localize_settings_changed": {"file_path": "django/test/signals.py", "line_number": 158, "end_line": 160}, "test.signals.complex_setting_changed": {"file_path": "django/test/signals.py", "line_number": 164, "end_line": 171}, "test.signals.root_urlconf_changed": {"file_path": "django/test/signals.py", "line_number": 175, "end_line": 180}, "test.signals.static_storage_changed": {"file_path": "django/test/signals.py", "line_number": 184, "end_line": 191}, "test.signals.static_finders_changed": {"file_path": "django/test/signals.py", "line_number": 195, "end_line": 202}, "test.signals.form_renderer_changed": {"file_path": "django/test/signals.py", "line_number": 206, "end_line": 210}, "test.signals.auth_password_validators_changed": {"file_path": "django/test/signals.py", "line_number": 214, "end_line": 220}, "test.signals.user_model_swapped": {"file_path": "django/test/signals.py", "line_number": 224, "end_line": 253}, "test.utils.__init__": {"file_path": "django/test/utils.py", "line_number": 943, "end_line": 944}, "test.utils.__repr__": {"file_path": "django/test/utils.py", "line_number": 66, "end_line": 67}, "test.utils.__eq__": {"file_path": "django/test/utils.py", "line_number": 69, "end_line": 70}, "test.utils.__getitem__": {"file_path": "django/test/utils.py", "line_number": 713, "end_line": 714}, "test.utils.get": {"file_path": "django/test/utils.py", "line_number": 88, "end_line": 92}, "test.utils.__contains__": {"file_path": "django/test/utils.py", "line_number": 94, "end_line": 99}, "test.utils.keys": {"file_path": "django/test/utils.py", "line_number": 101, "end_line": 105}, "test.utils.instrumented_test_render": {"file_path": "django/test/utils.py", "line_number": 108, "end_line": 114}, "test.utils.setup_test_environment": {"file_path": "django/test/utils.py", "line_number": 121, "end_line": 154}, "test.utils.teardown_test_environment": {"file_path": "django/test/utils.py", "line_number": 157, "end_line": 170}, "test.utils.setup_databases": {"file_path": "django/test/utils.py", "line_number": 173, "end_line": 245}, "test.utils.iter_test_cases": {"file_path": "django/test/utils.py", "line_number": 248, "end_line": 266}, "test.utils.dependency_ordered": {"file_path": "django/test/utils.py", "line_number": 269, "end_line": 308}, "test.utils.get_unique_databases_and_mirrors": {"file_path": "django/test/utils.py", "line_number": 311, "end_line": 364}, "test.utils.teardown_databases": {"file_path": "django/test/utils.py", "line_number": 367, "end_line": 378}, "test.utils.get_runner": {"file_path": "django/test/utils.py", "line_number": 381, "end_line": 390}, "test.utils.enable": {"file_path": "django/test/utils.py", "line_number": 932, "end_line": 936}, "test.utils.disable": {"file_path": "django/test/utils.py", "line_number": 938, "end_line": 939}, "test.utils.__enter__": {"file_path": "django/test/utils.py", "line_number": 723, "end_line": 732}, "test.utils.__exit__": {"file_path": "django/test/utils.py", "line_number": 734, "end_line": 740}, "test.utils.decorate_class": {"file_path": "django/test/utils.py", "line_number": 540, "end_line": 549}, "test.utils.setUp": {"file_path": "django/test/utils.py", "line_number": 901, "end_line": 905}, "test.utils.decorate_callable": {"file_path": "django/test/utils.py", "line_number": 437, "end_line": 457}, "test.utils.inner": {"file_path": "django/test/utils.py", "line_number": 451, "end_line": 455}, "test.utils.__call__": {"file_path": "django/test/utils.py", "line_number": 459, "end_line": 464}, "test.utils.save_options": {"file_path": "django/test/utils.py", "line_number": 568, "end_line": 575}, "test.utils.compare_xml": {"file_path": "django/test/utils.py", "line_number": 633, "end_line": 699}, "test.utils.norm_whitespace": {"file_path": "django/test/utils.py", "line_number": 644, "end_line": 645}, "test.utils.child_text": {"file_path": "django/test/utils.py", "line_number": 647, "end_line": 650}, "test.utils.children": {"file_path": "django/test/utils.py", "line_number": 652, "end_line": 653}, "test.utils.norm_child_text": {"file_path": "django/test/utils.py", "line_number": 655, "end_line": 656}, "test.utils.attrs_dict": {"file_path": "django/test/utils.py", "line_number": 658, "end_line": 659}, "test.utils.check_element": {"file_path": "django/test/utils.py", "line_number": 661, "end_line": 674}, "test.utils.first_node": {"file_path": "django/test/utils.py", "line_number": 676, "end_line": 683}}, "function_definitions": {"__init__.setup": "django/__init__.py", "shortcuts.render": "django/shortcuts.py", "shortcuts.redirect": "django/shortcuts.py", "shortcuts._get_queryset": "django/shortcuts.py", "shortcuts.get_object_or_404": "django/shortcuts.py", "shortcuts.get_list_or_404": "django/shortcuts.py", "shortcuts.resolve_url": "django/shortcuts.py", "test.html.normalize_whitespace": "django/test/html.py", "test.html.normalize_attributes": "django/test/html.py", "test.html.__init__": "django/test/html.py", "test.html.append": "django/test/html.py", "test.html.finalize": "django/test/html.py", "test.html.rstrip_last_element": "django/test/html.py", "test.html.__eq__": "django/test/html.py", "test.html.__hash__": "django/test/html.py", "test.html._count": "django/test/html.py", "test.html.__contains__": "django/test/html.py", "test.html.count": "django/test/html.py", "test.html.__getitem__": "django/test/html.py", "test.html.__str__": "django/test/html.py", "test.html.__repr__": "django/test/html.py", "test.html.error": "django/test/html.py", "test.html.format_position": "django/test/html.py", "test.html.current": "django/test/html.py", "test.html.handle_startendtag": "django/test/html.py", "test.html.handle_starttag": "django/test/html.py", "test.html.handle_endtag": "django/test/html.py", "test.html.handle_data": "django/test/html.py", "test.html.parse_html": "django/test/html.py", "test.html.Element": "django/test/html.py", "test.html.Element.__init__": "django/test/html.py", "test.html.Element.append": "django/test/html.py", "test.html.Element.finalize": "django/test/html.py", "test.html.Element.__eq__": "django/test/html.py", "test.html.Element.__hash__": "django/test/html.py", "test.html.Element._count": "django/test/html.py", "test.html.Element.__contains__": "django/test/html.py", "test.html.Element.count": "django/test/html.py", "test.html.Element.__getitem__": "django/test/html.py", "test.html.Element.__str__": "django/test/html.py", "test.html.Element.__repr__": "django/test/html.py", "test.html.RootElement": "django/test/html.py", "test.html.RootElement.__init__": "django/test/html.py", "test.html.RootElement.__str__": "django/test/html.py", "test.html.HTMLParseError": "django/test/html.py", "test.html.Parser": "django/test/html.py", "test.html.Parser.__init__": "django/test/html.py", "test.html.Parser.error": "django/test/html.py", "test.html.Parser.format_position": "django/test/html.py", "test.html.Parser.current": "django/test/html.py", "test.html.Parser.handle_startendtag": "django/test/html.py", "test.html.Parser.handle_starttag": "django/test/html.py", "test.html.Parser.handle_endtag": "django/test/html.py", "test.html.Parser.handle_data": "django/test/html.py", "test.signals.clear_cache_handlers": "django/test/signals.py", "test.signals.update_installed_apps": "django/test/signals.py", "test.signals.update_connections_time_zone": "django/test/signals.py", "test.signals.clear_routers_cache": "django/test/signals.py", "test.signals.reset_template_engines": "django/test/signals.py", "test.signals.storages_changed": "django/test/signals.py", "test.signals.clear_serializers_cache": "django/test/signals.py", "test.signals.language_changed": "django/test/signals.py", "test.signals.localize_settings_changed": "django/test/signals.py", "test.signals.complex_setting_changed": "django/test/signals.py", "test.signals.root_urlconf_changed": "django/test/signals.py", "test.signals.static_storage_changed": "django/test/signals.py", "test.signals.static_finders_changed": "django/test/signals.py", "test.signals.form_renderer_changed": "django/test/signals.py", "test.signals.auth_password_validators_changed": "django/test/signals.py", "test.signals.user_model_swapped": "django/test/signals.py", "test.utils.__init__": "django/test/utils.py", "test.utils.__repr__": "django/test/utils.py", "test.utils.__eq__": "django/test/utils.py", "test.utils.__getitem__": "django/test/utils.py", "test.utils.get": "django/test/utils.py", "test.utils.__contains__": "django/test/utils.py", "test.utils.keys": "django/test/utils.py", "test.utils.instrumented_test_render": "django/test/utils.py", "test.utils.setup_test_environment": "django/test/utils.py", "test.utils.teardown_test_environment": "django/test/utils.py", "test.utils.setup_databases": "django/test/utils.py", "test.utils.iter_test_cases": "django/test/utils.py", "test.utils.dependency_ordered": "django/test/utils.py", "test.utils.get_unique_databases_and_mirrors": "django/test/utils.py", "test.utils.teardown_databases": "django/test/utils.py", "test.utils.get_runner": "django/test/utils.py", "test.utils.enable": "django/test/utils.py", "test.utils.disable": "django/test/utils.py", "test.utils.__enter__": "django/test/utils.py", "test.utils.__exit__": "django/test/utils.py", "test.utils.decorate_class": "django/test/utils.py", "test.utils.setUp": "django/test/utils.py", "test.utils.decorate_callable": "django/test/utils.py", "test.utils.inner": "django/test/utils.py", "test.utils.__call__": "django/test/utils.py", "test.utils.save_options": "django/test/utils.py", "test.utils.compare_xml": "django/test/utils.py", "test.utils.norm_whitespace": "django/test/utils.py", "test.utils.child_text": "django/test/utils.py", "test.utils.children": "django/test/utils.py"}, "import_relations": {"__init__": ["django.apps.apps", "django.conf.settings", "django.utils.version.get_version", "django.urls.set_script_prefix", "django.utils.log.configure_logging"], "__main__": ["django.core.management"], "shortcuts": ["django.http.HttpResponseRedirect", "django.urls.reverse", "django.urls.NoReverseMatch", "django.http.HttpResponse", "django.utils.functional.Promise", "django.http.HttpResponsePermanentRedirect", "django.template.loader", "django.http.Http404"], "test": ["django.test.testcases.skipUnlessAnyDBFeature", "django.test.client.RequestFactory", "django.test.utils.ignore_warnings", "django.test.utils.tag", "django.test.utils.override_system_checks", "django.test.testcases.SimpleTestCase", "django.test.utils.override_settings", "django.test.testcases.LiveServerTestCase", "django.test.utils.modify_settings", "django.test.client.Client", "django.test.client.AsyncRequestFactory", "django.test.client.AsyncClient", "django.test.testcases.skipUnlessDBFeature", "django.test.testcases.TestCase", "django.test.testcases.skipIfDBFeature", "django.test.testcases.TransactionTestCase"], "test.html": ["html.parser.HTMLParser", "django.utils.regex_helper._lazy_re_compile", "django.utils.html.VOID_ELEMENTS", "html"], "test.signals": ["django.apps.apps", "django.dispatch.receiver", "django.utils.translation.trans_real", "django.template.engines", "django.core.files.storage.storages", "os", "django.contrib.auth.views", "django.template.utils.get_app_template_dirs", "django.urls.set_urlconf", "django.utils.functional.empty", "django.contrib.auth.backends", "django.core.cache.close_caches", "django.dispatch.Signal", "django.core.serializers", "django.utils.timezone", "django.contrib.auth.get_user_model", "django.db.utils.ConnectionRouter", "django.core.management.get_commands", "django.contrib.auth.password_validation.get_default_password_validators", "django.contrib.auth.handlers.modwsgi", "django.template.engine.Engine", "django.utils.formats.FORMAT_SETTINGS", "django.core.cache.caches", "django.forms.renderers.get_default_renderer", "django.db.router", "warnings", "django.db.connections", "time", "django.contrib.auth.forms", "asgiref.local.Local", "django.core.signals.setting_changed", "django.utils.formats.reset_format_cache", "django.urls.clear_url_caches", "django.contrib.auth.management.commands.changepassword", "django.core.files.storage.default_storage", "django.contrib.staticfiles.storage.staticfiles_storage", "django.core.exceptions.ImproperlyConfigured", "django.contrib.staticfiles.finders.get_finder"], "test.utils": ["django.apps.apps", "django.apps.registry.Apps", "django.utils.version.PYPY", "os", "contextlib.contextmanager", "django.db.models.options.Options", "functools.wraps", "django.utils.translation.deactivate", "xml.dom.minidom.parseString", "django.core.checks.registry.registry", "asgiref.sync.iscoroutinefunction", "unittest.TestCase", "logging", "itertools.chain", "django.urls.get_script_prefix", "collections", "django.test.signals.template_rendered", "django.conf.settings", "django.test.SimpleTestCase", "django.core.mail", "gc", "warnings", "django.db.connections", "time", "django.urls.set_script_prefix", "types.SimpleNamespace", "django.core.signals.request_started", "django.core.signals.setting_changed", "django.db.DEFAULT_DB_ALIAS", "unittest.skipIf", "django.conf.UserSettingsHolder", "unittest.skipUnless", "jinja2", "io.StringIO", "django.template.Template", "sys", "xml.dom.minidom.Node", "django.db.reset_queries", "django.core.exceptions.ImproperlyConfigured", "re"], "test.client": ["importlib.import_module", "django.contrib.auth.aauthenticate", "django.core.signals.request_finished", "django.http.HttpRequest", "django.contrib.auth.aget_user", "django.db.close_old_connections", "django.utils.functional.SimpleLazyObject", "os", "functools.partial", "django.core.handlers.wsgi.WSGIRequest", "django.contrib.auth.get_user", "django.http.HttpHeaders", "django.http.SimpleCookie", "django.utils.http.urlencode", "django.contrib.auth.alogout", "django.contrib.auth.login", "django.contrib.auth.logout", "sys", "django.test.signals", "django.contrib.auth.load_backend", "mimetypes", "io.IOBase", "urllib.parse.urlsplit", "django.utils.regex_helper._lazy_re_compile", "urllib.parse.urljoin", "django.core.signals.got_request_exception", "django.conf.settings", "django.core.handlers.wsgi.LimitedStream", "django.core.handlers.asgi.ASGIRequest", "django.contrib.auth.authenticate", "django.utils.encoding.force_bytes", "asgiref.sync.sync_to_async", "django.core.signals.request_started", "urllib.parse.unquote_to_bytes", "django.contrib.auth.alogin", "copy.copy", "collections.abc.Iterable", "django.core.handlers.base.BaseHandler", "django.core.serializers.json.DjangoJSONEncoder", "json", "django.urls.resolve", "django.http.QueryDict", "http.HTTPStatus", "io.BytesIO", "django.test.utils.ContextList"], "test.runner": ["multiprocessing", "django.test.utils.TimeKeeper", "importlib.import_module", "django.test.TestCase", "unittest", "django.utils.datastructures.OrderedSet", "django.test.utils.teardown_test_environment", "io", "unittest.suite", "os", "contextlib.contextmanager", "django.test.utils.NullTimeKeeper", "django.test.utils.setup_test_environment", "<PERSON><PERSON><PERSON>", "random", "django.utils.version.PY313", "django", "logging", "django.core.management.call_command", "django.test.utils.iter_test_cases", "django.test.utils.teardown_databases", "django.test.utils.setup_databases", "textwrap", "collections.defaultdict", "tblib.pickling_support", "django.test.SimpleTestCase", "pdb", "django.db.connections", "<PERSON><PERSON><PERSON><PERSON>", "pickle", "itertools", "faulthandler", "ipdb", "ctypes", "sys"], "test.testcases": ["django.test.html.HTMLParseError", "django.db.transaction", "django.apps.apps", "unittest", "copy.deepcopy", "django.db.backends.base.base.BaseDatabaseWrapper", "django.test.utils.CaptureQueriesContext", "urllib.parse.unquote", "django.utils.functional.classproperty", "django.core.handlers.wsgi.get_path_info", "difflib.get_close_matches", "urllib.parse.urlparse", "asgiref.sync.async_to_sync", "contextlib.contextmanager", "functools.wraps", "django.db.backends.base.base.NO_DB_ALIAS", "urllib.parse.parse_qsl", "unittest.mock", "django.core.servers.basehttp.WSGIRequestHandler", "django.http.request.validate_host", "urllib.request.url2pathname", "urllib.parse.urlencode", "asgiref.sync.iscoroutinefunction", "unittest.suite._DebugResult", "django.test.utils.ContextList", "django.test.client.Client", "django.core.management.sql.emit_post_migrate_signal", "django.test.html.parse_html", "logging", "django.core.management.call_command", "django.test.client.AsyncClient", "posixpath", "urllib.parse.urlsplit", "django.core.handlers.wsgi.WSGIHandler", "django.test.utils.compare_xml", "django.db.connection", "django.forms.fields.CharField", "urllib.parse.urljoin", "django.test.signals.template_rendered", "django.conf.settings", "collections.Counter", "django.views.static.serve", "django.core.files.locks", "django.core.mail", "django.db.connections", "django.test.utils.override_settings", "difflib", "django.core.exceptions.ValidationError", "django.core.signals.setting_changed", "django.test.utils.modify_settings", "django.db.DEFAULT_DB_ALIAS", "pickle", "copy.copy", "django.core.servers.basehttp.ThreadedWSGIServer", "urllib.parse.urlunsplit", "json", "django.core.management.color.no_style", "unittest.util.safe_repr", "sys", "django.http.QueryDict", "django.http.Http404", "django.core.exceptions.ImproperlyConfigured", "django.http.request.split_domain_port", "threading"], "test.selenium": ["selenium.webdriver.common.desired_capabilities.DesiredCapabilities", "django.test.tag", "unittest", "django.conf.settings", "sys", "django.utils.module_loading.import_string", "django.utils.functional.classproperty", "selenium.webdriver", "contextlib.contextmanager", "functools.wraps", "django.utils.text.capfirst", "pathlib.Path", "django.test.override_settings", "django.test.LiveServerTestCase"], "utils.feedgenerator": ["django.utils.encoding.iri_to_uri", "email", "io.StringIO", "datetime", "urllib.parse.urlparse", "mimetypes", "django.utils.xmlutils.SimplerXMLGenerator"], "utils._os": ["os.path.dirname", "django.core.exceptions.SuspiciousFileOperation", "os.path.sep", "os.path.join", "os.path.abspath", "os.path.normcase", "os", "tempfile", "pathlib.Path"], "utils.crypto": ["django.conf.settings", "secrets", "django.utils.encoding.force_bytes", "hmac", "<PERSON><PERSON><PERSON>"], "utils.copy": ["django.utils.version.PY313", "copy.replace"], "utils.tree": ["django.utils.hashable.make_hashable", "copy"], "utils.csp": ["django.utils.functional.empty", "enum.StrEnum", "django.utils.functional.SimpleLazyObject", "secrets"], "utils.module_loading": ["importlib.import_module", "django.apps.apps", "importlib.util.find_spec", "copy", "os", "sys"], "utils.asyncio": ["functools.wraps", "django.core.exceptions.SynchronousOnlyOperation", "os", "asyncio.get_running_loop"], "utils.dateformat": ["datetime.time", "django.utils.timezone.get_default_timezone", "datetime.date", "django.utils.dates.MONTHS", "email.utils.format_datetime", "django.utils.regex_helper._lazy_re_compile", "django.utils.timezone.is_naive", "django.utils.dates.MONTHS_ALT", "django.utils.dates.MONTHS_AP", "calendar", "django.utils.translation.gettext", "django.utils.timezone.make_aware", "django.utils.dates.WEEKDAYS", "django.utils.dates.WEEKDAYS_ABBR", "datetime.datetime", "django.utils.dates.MONTHS_3", "django.utils.timezone._datetime_ambiguous_or_imaginary"]}}
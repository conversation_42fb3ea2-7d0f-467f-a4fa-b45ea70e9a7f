# Django框架资源

## 目录结构

```
Django/
├── source/                 # 源代码目录
│   └── django/            # Django主源代码
├── sdk/                   # SDK和二进制文件
│   ├── django-5.1.4.tar.gz      # 最新稳定版
│   ├── django-4.2.17.tar.gz     # LTS版本
│   ├── django-5.1.4/             # 解压后的源码
│   ├── django-4.2.17/            # 解压后的源码
│   └── dependencies/             # 依赖包
└── symbols/               # 符号表和调试信息
    ├── symbol_info.json          # 符号表信息
    └── debug_symbols_info.json   # 调试符号信息
```

## 使用说明

### 1. 源代码开发
```bash
# 安装开发版本
pip install -e source/django/

# 运行测试
cd source/django && python runtests.py

# 构建文档
cd source/django/docs && make html
```

### 2. 调试符号安装
```bash
# Ubuntu/Debian
sudo apt install python3-dbg python3-dev

# CentOS/RHEL  
sudo yum install python3-debuginfo python3-devel

# 编译时添加调试符号
export CFLAGS="-g -O0"
pip install --no-binary :all: django
```

### 3. Django调试配置
```python
# settings.py
DEBUG = True
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
        },
    },
    'loggers': {
        'django': {
            'handlers': ['console'],
            'level': 'DEBUG',
        },
    },
}
```

## 版本信息
- 最新稳定版: 5.1.4
- LTS版本: 4.2.17
- Python要求: >=3.8

## 相关链接
- [Django官网](https://www.djangoproject.com/)
- [Django文档](https://docs.djangoproject.com/)
- [Django GitHub](https://github.com/django/django)

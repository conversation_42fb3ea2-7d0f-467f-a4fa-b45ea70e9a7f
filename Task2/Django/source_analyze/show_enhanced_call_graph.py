#!/usr/bin/env python3
"""
展示增强的Django调用关系图（包含文件路径和行号）
"""

import json

def show_enhanced_call_graph():
    """展示增强的调用关系图"""
    
    # 读取分析结果
    with open('django_call_relations.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    detailed_call_graph = data.get('detailed_call_graph', {})
    function_locations = data.get('function_locations', {})
    
    print("🔗 Django框架增强调用关系图")
    print("=" * 80)
    print("格式: 调用者函数 [文件路径:行号] → 被调用函数 [文件路径:行号]")
    print("=" * 80)
    
    # 显示前10个调用关系
    count = 0
    for caller, call_info in detailed_call_graph.items():
        if count >= 10:
            break
        
        print(f"\n📋 {caller}")
        
        # 显示调用者的位置信息
        if 'caller_location' in call_info:
            caller_loc = call_info['caller_location']
            print(f"   📍 定义位置: {caller_loc['file_path']}:{caller_loc['line_number']}")
        elif caller in function_locations:
            caller_loc = function_locations[caller]
            print(f"   📍 定义位置: {caller_loc['file_path']}:{caller_loc['line_number']}")
        
        # 显示被调用的函数
        callees = call_info.get('callees', [])
        for i, callee_info in enumerate(callees):
            function_name = callee_info['function']
            
            print(f"   └─ {i+1}. {function_name}")
            
            # 显示被调用函数的定义位置
            if 'definition_location' in callee_info:
                def_loc = callee_info['definition_location']
                print(f"      📍 定义: {def_loc['file_path']}:{def_loc['line_number']}")
            
            # 显示调用位置
            if 'call_locations' in callee_info:
                for call_loc in callee_info['call_locations']:
                    print(f"      📞 调用: {call_loc['file_path']}:{call_loc['line_number']}")
        
        count += 1
    
    print(f"\n... 还有 {len(detailed_call_graph) - 10} 个调用关系")

def show_specific_function_calls(function_name):
    """显示特定函数的调用关系"""
    
    with open('django_call_relations.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    detailed_call_graph = data.get('detailed_call_graph', {})
    
    print(f"\n🔍 查找函数: {function_name}")
    print("=" * 60)
    
    found = False
    for caller, call_info in detailed_call_graph.items():
        if function_name in caller:
            found = True
            print(f"\n📋 {caller}")
            
            # 显示调用者位置
            if 'caller_location' in call_info:
                caller_loc = call_info['caller_location']
                print(f"   📍 定义: {caller_loc['file_path']}:{caller_loc['line_number']}-{caller_loc['end_line']}")
            
            # 显示被调用函数
            callees = call_info.get('callees', [])
            for callee_info in callees:
                func = callee_info['function']
                print(f"   └─ 调用: {func}")
                
                if 'call_locations' in callee_info:
                    for call_loc in callee_info['call_locations']:
                        print(f"      📞 位置: {call_loc['file_path']}:{call_loc['line_number']}")
    
    if not found:
        print(f"❌ 未找到函数: {function_name}")

def show_file_functions(file_path):
    """显示特定文件中的函数"""
    
    with open('django_call_relations.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    function_locations = data.get('function_locations', {})
    
    print(f"\n📁 文件: {file_path}")
    print("=" * 60)
    
    functions_in_file = []
    for func_name, location in function_locations.items():
        if file_path in location['file_path']:
            functions_in_file.append((func_name, location))
    
    if functions_in_file:
        # 按行号排序
        functions_in_file.sort(key=lambda x: x[1]['line_number'])
        
        for func_name, location in functions_in_file:
            print(f"   📋 {func_name}")
            print(f"      📍 行号: {location['line_number']}-{location['end_line']}")
    else:
        print(f"❌ 文件中未找到函数: {file_path}")

def show_statistics():
    """显示统计信息"""
    
    with open('django_call_relations.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    metadata = data.get('metadata', {})
    summary = data.get('call_graph_summary', {})
    
    print("\n📊 统计信息")
    print("=" * 40)
    print(f"分析范围: {metadata.get('analysis_scope', 'N/A')}")
    print(f"Django路径: {metadata.get('django_path', 'N/A')}")
    print(f"总模块数: {summary.get('total_modules', 0)}")
    print(f"总函数数: {summary.get('total_functions', 0)}")
    print(f"总调用数: {summary.get('total_calls', 0)}")
    
    # 显示最常被调用的函数
    most_called = summary.get('most_called_functions', [])[:5]
    print(f"\n🔥 最常被调用的函数:")
    for i, func_info in enumerate(most_called, 1):
        print(f"   {i}. {func_info['function']}: {func_info['call_count']} 次")

def main():
    print("🚀 Django增强调用关系图查看器")
    
    try:
        # 显示统计信息
        show_statistics()
        
        # 显示增强的调用关系图
        show_enhanced_call_graph()
        
        # 显示特定函数的调用关系
        show_specific_function_calls("shortcuts.render")
        
        # 显示特定文件中的函数
        show_file_functions("shortcuts.py")
        
    except FileNotFoundError:
        print("❌ 找不到 django_call_relations.json 文件")
        print("请先运行 analyze_call_relations.py")
    except Exception as e:
        print(f"❌ 错误: {e}")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
Django SDK分析器
分析SDK二进制文件和符号表信息，获得SDK调用关系
"""

import os
import json
import subprocess
import tarfile
import zipfile
from pathlib import Path
from typing import Dict, List, Optional
import ast
import importlib.util
import sys

class DjangoSDKAnalyzer:
    def __init__(self, base_dir: str = "."):
        self.base_dir = Path(base_dir)
        self.sdk_dir = self.base_dir / "sdk"
        self.symbols_dir = self.base_dir / "symbols"
        self.source_dir = self.base_dir / "source"
        
        self.sdk_info = {}
        self.symbol_tables = {}
        self.binary_analysis = {}
        self.call_relations = {}
    
    def load_symbol_info(self):
        """加载符号表信息"""
        print("📊 加载符号表信息...")
        
        symbol_files = [
            self.symbols_dir / "symbol_info.json",
            self.symbols_dir / "debug_symbols_info.json"
        ]
        
        for symbol_file in symbol_files:
            if symbol_file.exists():
                try:
                    with open(symbol_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                        self.symbol_tables[symbol_file.name] = data
                        print(f"✅ 加载符号表: {symbol_file.name}")
                except Exception as e:
                    print(f"⚠️  加载符号表失败 {symbol_file}: {e}")
    
    def analyze_sdk_packages(self):
        """分析SDK包文件"""
        print("📦 分析SDK包文件...")
        
        if not self.sdk_dir.exists():
            print(f"❌ SDK目录不存在: {self.sdk_dir}")
            return
        
        # 分析tar.gz包
        for package_file in self.sdk_dir.glob("*.tar.gz"):
            self.analyze_package(package_file)
        
        # 分析wheel包
        for wheel_file in self.sdk_dir.glob("*.whl"):
            self.analyze_wheel(wheel_file)
        
        # 分析解压后的目录
        for django_dir in self.sdk_dir.glob("django-*"):
            if django_dir.is_dir():
                self.analyze_extracted_package(django_dir)
    
    def analyze_package(self, package_path: Path):
        """分析tar.gz包"""
        print(f"📋 分析包: {package_path.name}")
        
        try:
            with tarfile.open(package_path, 'r:gz') as tar:
                members = tar.getnames()
                
                # 统计文件类型
                file_stats = {
                    'python_files': 0,
                    'template_files': 0,
                    'static_files': 0,
                    'config_files': 0,
                    'total_files': len(members)
                }
                
                for member in members:
                    if member.endswith('.py'):
                        file_stats['python_files'] += 1
                    elif member.endswith(('.html', '.txt', '.md')):
                        file_stats['template_files'] += 1
                    elif member.endswith(('.css', '.js', '.png', '.jpg')):
                        file_stats['static_files'] += 1
                    elif member.endswith(('.json', '.yaml', '.yml', '.cfg', '.ini')):
                        file_stats['config_files'] += 1
                
                self.sdk_info[package_path.name] = {
                    'type': 'source_package',
                    'file_stats': file_stats,
                    'members': members[:20]  # 只保存前20个文件名
                }
                
                print(f"   📊 Python文件: {file_stats['python_files']}")
                print(f"   📊 总文件数: {file_stats['total_files']}")
                
        except Exception as e:
            print(f"⚠️  分析包失败 {package_path}: {e}")
    
    def analyze_wheel(self, wheel_path: Path):
        """分析wheel包"""
        print(f"🎡 分析wheel包: {wheel_path.name}")
        
        try:
            with zipfile.ZipFile(wheel_path, 'r') as wheel:
                members = wheel.namelist()
                
                # 查找元数据
                metadata_files = [m for m in members if 'METADATA' in m or 'PKG-INFO' in m]
                
                self.sdk_info[wheel_path.name] = {
                    'type': 'wheel_package',
                    'total_files': len(members),
                    'metadata_files': metadata_files,
                    'members': members[:20]
                }
                
                print(f"   📊 总文件数: {len(members)}")
                
        except Exception as e:
            print(f"⚠️  分析wheel包失败 {wheel_path}: {e}")
    
    def analyze_extracted_package(self, package_dir: Path):
        """分析解压后的包目录"""
        print(f"📁 分析解压包: {package_dir.name}")
        
        try:
            # 查找setup.py或pyproject.toml
            setup_files = []
            if (package_dir / "setup.py").exists():
                setup_files.append("setup.py")
            if (package_dir / "pyproject.toml").exists():
                setup_files.append("pyproject.toml")
            
            # 统计Django模块
            django_modules = []
            django_core = package_dir / "django"
            if django_core.exists():
                for module_dir in django_core.iterdir():
                    if module_dir.is_dir() and not module_dir.name.startswith('.'):
                        django_modules.append(module_dir.name)
            
            # 分析入口点
            entry_points = self.find_entry_points(package_dir)
            
            self.sdk_info[package_dir.name] = {
                'type': 'extracted_package',
                'setup_files': setup_files,
                'django_modules': django_modules,
                'entry_points': entry_points,
                'package_structure': self.analyze_package_structure(package_dir)
            }
            
            print(f"   📊 Django模块: {len(django_modules)}")
            print(f"   📊 入口点: {len(entry_points)}")
            
        except Exception as e:
            print(f"⚠️  分析解压包失败 {package_dir}: {e}")
    
    def find_entry_points(self, package_dir: Path) -> List[str]:
        """查找包的入口点"""
        entry_points = []
        
        # 查找__main__.py
        main_files = list(package_dir.rglob("__main__.py"))
        for main_file in main_files:
            entry_points.append(str(main_file.relative_to(package_dir)))
        
        # 查找manage.py
        manage_files = list(package_dir.rglob("manage.py"))
        for manage_file in manage_files:
            entry_points.append(str(manage_file.relative_to(package_dir)))
        
        return entry_points
    
    def analyze_package_structure(self, package_dir: Path) -> Dict:
        """分析包结构"""
        structure = {
            'directories': [],
            'python_modules': [],
            'config_files': [],
            'documentation': []
        }
        
        try:
            for item in package_dir.rglob("*"):
                if item.is_dir():
                    rel_path = str(item.relative_to(package_dir))
                    if not rel_path.startswith('.') and len(structure['directories']) < 20:
                        structure['directories'].append(rel_path)
                elif item.suffix == '.py':
                    rel_path = str(item.relative_to(package_dir))
                    if len(structure['python_modules']) < 20:
                        structure['python_modules'].append(rel_path)
                elif item.name in ['README.md', 'LICENSE', 'MANIFEST.in', 'setup.cfg']:
                    structure['config_files'].append(item.name)
                elif item.suffix in ['.md', '.rst', '.txt'] and 'doc' in str(item).lower():
                    rel_path = str(item.relative_to(package_dir))
                    if len(structure['documentation']) < 10:
                        structure['documentation'].append(rel_path)
        except Exception as e:
            print(f"⚠️  分析包结构失败: {e}")
        
        return structure
    
    def analyze_dependencies(self):
        """分析依赖关系"""
        print("🔗 分析依赖关系...")
        
        dependencies_dir = self.sdk_dir / "dependencies"
        if not dependencies_dir.exists():
            print("⚠️  依赖目录不存在")
            return
        
        dependency_info = {}
        
        for dep_file in dependencies_dir.glob("*.whl"):
            try:
                with zipfile.ZipFile(dep_file, 'r') as wheel:
                    # 查找METADATA文件
                    metadata_files = [f for f in wheel.namelist() if 'METADATA' in f]
                    
                    if metadata_files:
                        metadata_content = wheel.read(metadata_files[0]).decode('utf-8')
                        dependency_info[dep_file.name] = self.parse_metadata(metadata_content)
                        
            except Exception as e:
                print(f"⚠️  分析依赖失败 {dep_file}: {e}")
        
        self.sdk_info['dependencies'] = dependency_info
        print(f"✅ 分析了 {len(dependency_info)} 个依赖包")
    
    def parse_metadata(self, metadata_content: str) -> Dict:
        """解析包元数据"""
        metadata = {}
        
        for line in metadata_content.split('\n'):
            if ':' in line:
                key, value = line.split(':', 1)
                key = key.strip()
                value = value.strip()
                
                if key in ['Name', 'Version', 'Summary', 'Author', 'License']:
                    metadata[key.lower()] = value
                elif key == 'Requires-Dist':
                    if 'requires' not in metadata:
                        metadata['requires'] = []
                    metadata['requires'].append(value)
        
        return metadata
    
    def analyze_binary_symbols(self):
        """分析二进制符号（如果有的话）"""
        print("🔍 分析二进制符号...")
        
        # 查找可能的二进制文件
        binary_files = []
        for ext in ['.so', '.dll', '.dylib', '.pyd']:
            binary_files.extend(list(self.sdk_dir.rglob(f"*{ext}")))
        
        if not binary_files:
            print("ℹ️  未找到二进制文件，Django是纯Python包")
            return
        
        for binary_file in binary_files:
            try:
                # 尝试使用nm或objdump分析符号（Linux/macOS）
                if binary_file.suffix in ['.so', '.dylib']:
                    self.analyze_shared_library(binary_file)
                elif binary_file.suffix == '.pyd':
                    print(f"   🔍 Python扩展: {binary_file.name}")
                    
            except Exception as e:
                print(f"⚠️  分析二进制文件失败 {binary_file}: {e}")
    
    def analyze_shared_library(self, lib_file: Path):
        """分析共享库"""
        try:
            # 尝试使用nm命令获取符号
            result = subprocess.run(['nm', '-D', str(lib_file)], 
                                  capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                symbols = result.stdout.split('\n')
                exported_symbols = [s for s in symbols if ' T ' in s or ' U ' in s]
                
                self.binary_analysis[lib_file.name] = {
                    'type': 'shared_library',
                    'symbol_count': len(exported_symbols),
                    'symbols': exported_symbols[:20]  # 只保存前20个符号
                }
                
                print(f"   📊 {lib_file.name}: {len(exported_symbols)} 个符号")
                
        except (subprocess.TimeoutExpired, FileNotFoundError):
            print(f"   ⚠️  无法分析 {lib_file.name} (需要nm工具)")
    
    def generate_sdk_call_relations(self):
        """生成SDK调用关系"""
        print("🔗 生成SDK调用关系...")
        
        # 基于符号表和包信息生成调用关系
        call_relations = {
            'package_dependencies': {},
            'module_structure': {},
            'entry_points': {},
            'binary_interfaces': {}
        }
        
        # 分析包依赖关系
        if 'dependencies' in self.sdk_info:
            for dep_name, dep_info in self.sdk_info['dependencies'].items():
                if 'requires' in dep_info:
                    call_relations['package_dependencies'][dep_name] = dep_info['requires']
        
        # 分析模块结构
        for package_name, package_info in self.sdk_info.items():
            if package_info.get('type') == 'extracted_package':
                call_relations['module_structure'][package_name] = {
                    'django_modules': package_info.get('django_modules', []),
                    'entry_points': package_info.get('entry_points', [])
                }
        
        # 分析二进制接口
        call_relations['binary_interfaces'] = self.binary_analysis
        
        self.call_relations = call_relations
        return call_relations
    
    def save_analysis_results(self, output_file: str = "django_sdk_analysis.json"):
        """保存分析结果"""
        print("💾 保存SDK分析结果...")
        
        results = {
            'metadata': {
                'analysis_type': 'Django SDK Analysis',
                'sdk_directory': str(self.sdk_dir),
                'symbols_directory': str(self.symbols_dir)
            },
            'sdk_packages': self.sdk_info,
            'symbol_tables': self.symbol_tables,
            'binary_analysis': self.binary_analysis,
            'call_relations': self.call_relations,
            'summary': {
                'total_packages': len([k for k, v in self.sdk_info.items() if isinstance(v, dict) and 'type' in v]),
                'dependencies_count': len(self.sdk_info.get('dependencies', {})),
                'binary_files': len(self.binary_analysis)
            }
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        print(f"✅ SDK分析结果保存到: {output_file}")
        return results
    
    def run_full_analysis(self):
        """运行完整的SDK分析"""
        print("🚀 Django SDK完整分析")
        print("=" * 50)
        
        # 1. 加载符号表信息
        self.load_symbol_info()
        
        # 2. 分析SDK包
        self.analyze_sdk_packages()
        
        # 3. 分析依赖关系
        self.analyze_dependencies()
        
        # 4. 分析二进制符号
        self.analyze_binary_symbols()
        
        # 5. 生成调用关系
        self.generate_sdk_call_relations()
        
        # 6. 保存结果
        results = self.save_analysis_results()
        
        # 7. 显示摘要
        summary = results['summary']
        print(f"\n📊 分析摘要:")
        print(f"  SDK包数量: {summary['total_packages']}")
        print(f"  依赖包数量: {summary['dependencies_count']}")
        print(f"  二进制文件: {summary['binary_files']}")
        
        print("\n🎉 SDK分析完成！")
        return results

def main():
    analyzer = DjangoSDKAnalyzer()
    analyzer.run_full_analysis()

if __name__ == "__main__":
    main()

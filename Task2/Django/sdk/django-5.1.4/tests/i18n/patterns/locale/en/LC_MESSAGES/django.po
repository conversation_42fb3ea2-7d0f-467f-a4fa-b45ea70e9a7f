# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-03-01 21:18+0000\n"
"PO-Revision-Date: 2011-06-14 16:16+0100\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"

#: urls/default.py:11
msgid "translated/"
msgstr "translated/"

#: urls/default.py:12
msgid "^translated-regex/$"
msgstr "^translated-regex/$"

#: urls/default.py:14
msgid "^translated/(?P<slug>[\\w-]+)/$"
msgstr "^translated/(?P<slug>[\\w-]+)/$"

#: urls/default.py:25
msgid "^with-arguments/(?P<argument>[\\w-]+)/(?:(?P<optional>[\\w-]+).html)?$"
msgstr ""

#: urls/default.py:29
msgid "^users/$"
msgstr "^users/$"

#: urls/default.py:31 urls/wrong.py:7
msgid "^account/"
msgstr "^account/"

#: urls/namespace.py:9 urls/wrong_namespace.py:10
msgid "^register/$"
msgstr "^register/$"

#: urls/namespace.py:10
msgid "^register-without-slash$"
msgstr ""

#: urls/namespace.py:11
msgid "register-as-path/"
msgstr "register-as-path/"

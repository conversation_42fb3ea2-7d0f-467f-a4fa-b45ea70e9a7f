from datetime import datetime

from django.test import TestCase

from .models import Article, Reporter, Writer


class M2MIntermediaryTests(TestCase):
    def test_intermediary(self):
        r1 = Reporter.objects.create(first_name="<PERSON>", last_name="<PERSON>")
        r2 = Reporter.objects.create(first_name="<PERSON>", last_name="<PERSON><PERSON>")

        a = Article.objects.create(
            headline="This is a test", pub_date=datetime(2005, 7, 27)
        )

        w1 = Writer.objects.create(reporter=r1, article=a, position="Main writer")
        w2 = Writer.objects.create(reporter=r2, article=a, position="Contributor")

        self.assertQuerySetEqual(
            a.writer_set.select_related().order_by("-position"),
            [
                ("<PERSON>", "Main writer"),
                ("<PERSON>", "Contributor"),
            ],
            lambda w: (str(w.reporter), w.position),
        )
        self.assertEqual(w1.reporter, r1)
        self.assertEqual(w2.reporter, r2)

        self.assertEqual(w1.article, a)
        self.assertEqual(w2.article, a)

        self.assertQuerySetEqual(
            r1.writer_set.all(),
            [("<PERSON>", "Main writer")],
            lambda w: (str(w.reporter), w.position),
        )

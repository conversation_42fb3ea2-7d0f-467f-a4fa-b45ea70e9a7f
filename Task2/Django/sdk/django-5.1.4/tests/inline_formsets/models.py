from django.db import models


class School(models.Model):
    name = models.Char<PERSON>ield(max_length=100)


class Parent(models.Model):
    name = models.Char<PERSON><PERSON>(max_length=100)


class Child(models.Model):
    mother = models.<PERSON><PERSON><PERSON>(Parent, models.CASCADE, related_name="mothers_children")
    father = models.<PERSON><PERSON><PERSON>(Parent, models.CASCADE, related_name="fathers_children")
    school = models.Foreign<PERSON>ey(School, models.CASCADE)
    name = models.Char<PERSON>ield(max_length=100)


class Poet(models.Model):
    name = models.Char<PERSON>ield(max_length=100)

    def __str__(self):
        return self.name


class Poem(models.Model):
    poet = models.<PERSON><PERSON><PERSON>(Poet, models.CASCADE)
    name = models.Char<PERSON>ield(max_length=100)

    class Meta:
        unique_together = ("poet", "name")

    def __str__(self):
        return self.name

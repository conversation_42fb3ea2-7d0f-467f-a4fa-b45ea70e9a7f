[{"pk": "1", "model": "fixtures.tag", "fields": {"name": "copyright", "tagged_type": ["fixtures", "article"], "tagged_id": "3"}}, {"pk": "2", "model": "fixtures.tag", "fields": {"name": "law", "tagged_type": ["fixtures", "article"], "tagged_id": "3"}}, {"pk": "1", "model": "fixtures.person", "fields": {"name": "<PERSON><PERSON><PERSON>"}}, {"pk": "2", "model": "fixtures.person", "fields": {"name": "<PERSON><PERSON>"}}, {"pk": "3", "model": "fixtures.person", "fields": {"name": "Prince"}}]
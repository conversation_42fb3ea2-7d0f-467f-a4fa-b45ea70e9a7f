from django.db.models import <PERSON><PERSON><PERSON><PERSON>
from django.db.models.functions import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Trim
from django.test import TestCase
from django.test.utils import register_lookup

from ..models import Author


class TrimTests(TestCase):
    def test_trim(self):
        Author.objects.create(name="  <PERSON> ", alias="j")
        Author.objects.create(name="<PERSON>hon<PERSON>", alias="r")
        authors = Author.objects.annotate(
            ltrim=LTrim("name"),
            rtrim=RTrim("name"),
            trim=Trim("name"),
        )
        self.assertQuerySetEqual(
            authors.order_by("alias"),
            [
                ("<PERSON> ", "  <PERSON>", "<PERSON>"),
                ("<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"),
            ],
            lambda a: (a.ltrim, a.rtrim, a.trim),
        )

    def test_trim_transform(self):
        Author.objects.create(name=" <PERSON>  ")
        Author.objects.create(name="<PERSON>hon<PERSON>")
        tests = (
            (<PERSON><PERSON><PERSON>, "<PERSON>  "),
            (<PERSON><PERSON><PERSON>, " <PERSON>"),
            (<PERSON><PERSON>, "<PERSON>"),
        )
        for transform, trimmed_name in tests:
            with self.subTest(transform=transform):
                with register_lookup(<PERSON><PERSON><PERSON><PERSON>, transform):
                    authors = Author.objects.filter(
                        **{"name__%s" % transform.lookup_name: trimmed_name}
                    )
                    self.assertQuerySetEqual(authors, [" <PERSON>  "], lambda a: a.name)

from django.db.models import <PERSON><PERSON><PERSON><PERSON>
from django.db.models.functions import Length
from django.test import TestCase
from django.test.utils import register_lookup

from ..models import Author


class LengthTests(TestCase):
    def test_basic(self):
        Author.objects.create(name="<PERSON>", alias="smithj")
        Author.objects.create(name="<PERSON>hon<PERSON>")
        authors = Author.objects.annotate(
            name_length=Length("name"),
            alias_length=Length("alias"),
        )
        self.assertQuerySetEqual(
            authors.order_by("name"),
            [(10, 6), (6, None)],
            lambda a: (a.name_length, a.alias_length),
        )
        self.assertEqual(authors.filter(alias_length__lte=Length("name")).count(), 1)

    def test_ordering(self):
        Author.objects.create(name="<PERSON>", alias="smithj")
        Author.objects.create(name="<PERSON>", alias="smithj1")
        Author.objects.create(name="<PERSON>hon<PERSON>", alias="ronny")
        authors = Author.objects.order_by(Length("name"), Length("alias"))
        self.assertQuerySetEqual(
            authors,
            [
                ("<PERSON><PERSON><PERSON>", "ronny"),
                ("<PERSON>", "smithj"),
                ("<PERSON>", "smithj1"),
            ],
            lambda a: (a.name, a.alias),
        )

    def test_transform(self):
        with register_lookup(CharField, Length):
            Author.objects.create(name="John Smith", alias="smithj")
            Author.objects.create(name="Rhonda")
            authors = Author.objects.filter(name__length__gt=7)
            self.assertQuerySetEqual(
                authors.order_by("name"), ["John Smith"], lambda a: a.name
            )

from django.db.models import <PERSON><PERSON><PERSON><PERSON>
from django.db.models.functions import Upper
from django.test import TestCase
from django.test.utils import register_lookup

from ..models import Author


class UpperTests(TestCase):
    def test_basic(self):
        Author.objects.create(name="<PERSON>", alias="smithj")
        Author.objects.create(name="<PERSON>honda")
        authors = Author.objects.annotate(upper_name=Upper("name"))
        self.assertQuerySetEqual(
            authors.order_by("name"),
            [
                "JOHN SMITH",
                "RHONDA",
            ],
            lambda a: a.upper_name,
        )
        Author.objects.update(name=Upper("name"))
        self.assertQuerySetEqual(
            authors.order_by("name"),
            [
                ("JOHN SMITH", "JOHN SMITH"),
                ("RHONDA", "RHONDA"),
            ],
            lambda a: (a.upper_name, a.name),
        )

    def test_transform(self):
        with register_lookup(<PERSON><PERSON><PERSON><PERSON>, Upper):
            Author.objects.create(name="<PERSON>", alias="smithj")
            Author.objects.create(name="<PERSON><PERSON><PERSON>")
            authors = Author.objects.filter(name__upper__exact="JOHN SMITH")
            self.assertQuerySetEqual(
                authors.order_by("name"),
                [
                    "<PERSON>",
                ],
                lambda a: a.name,
            )

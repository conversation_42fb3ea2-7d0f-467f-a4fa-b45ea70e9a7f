<?xml version="1.0" encoding="utf-8"?>
<django-objects version="1.0">
    <object pk="22" model="fixtures_regress.person">
        <field type="CharField" name="name"><PERSON><PERSON></field>
    </object>
    <object pk="21" model="fixtures_regress.store">
        <field type="CharField" name="name">Collins Bookstore</field>
    </object>
    <object pk="20" model="fixtures_regress.book">
        <field type="CharField" name="name"><PERSON><PERSON>'s Game</field>
        <field to="fixtures_regress.person" name="author" rel="ManyToOneRel">22</field>
        <field to="fixtures_regress.store" name="stores" rel="ManyToManyRel">
            <object pk="21"/>
        </field>
    </object>
</django-objects>
import uuid

from django.core.exceptions import ValidationError
from django.forms import <PERSON><PERSON><PERSON><PERSON>
from django.test import SimpleTestCase


class UUIDFieldTest(SimpleTestCase):
    def test_uuidfield_1(self):
        field = UUIDField()
        value = field.clean("550e8400e29b41d4a716************")
        self.assertEqual(value, uuid.UUID("550e8400e29b41d4a716************"))

    def test_clean_value_with_dashes(self):
        field = UUIDField()
        value = field.clean("550e8400-e29b-41d4-a716-************")
        self.assertEqual(value, uuid.UUID("550e8400e29b41d4a716************"))

    def test_uuidfield_2(self):
        field = UUIDField(required=False)
        self.assertIsNone(field.clean(""))
        self.assertIsNone(field.clean(None))

    def test_uuidfield_3(self):
        field = UUIDField()
        with self.assertRaisesMessage(ValidationError, "Enter a valid UUID."):
            field.clean("550e8400")

    def test_uuidfield_4(self):
        field = UUIDField()
        value = field.prepare_value(uuid.UUID("550e8400e29b41d4a716************"))
        self.assertEqual(value, "550e8400-e29b-41d4-a716-************")

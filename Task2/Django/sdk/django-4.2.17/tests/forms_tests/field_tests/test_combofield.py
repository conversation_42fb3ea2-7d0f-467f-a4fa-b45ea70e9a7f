from django.core.exceptions import ValidationError
from django.forms import <PERSON><PERSON><PERSON><PERSON>, <PERSON>mboField, <PERSON>ailField
from django.test import SimpleTestCase


class ComboFieldTest(SimpleTestCase):
    def test_combofield_1(self):
        f = ComboField(fields=[Char<PERSON><PERSON>(max_length=20), EmailField()])
        self.assertEqual("<EMAIL>", f.clean("<EMAIL>"))
        with self.assertRaisesMessage(
            ValidationError,
            "'Ensure this value has at most 20 characters (it has 28).'",
        ):
            f.clean("<EMAIL>")
        with self.assertRaisesMessage(
            ValidationError, "'Enter a valid email address.'"
        ):
            f.clean("not an email")
        with self.assertRaisesMessage(ValidationError, "'This field is required.'"):
            f.clean("")
        with self.assertRaisesMessage(ValidationError, "'This field is required.'"):
            f.clean(None)

    def test_combofield_2(self):
        f = ComboField(fields=[<PERSON><PERSON><PERSON><PERSON>(max_length=20), EmailField()], required=False)
        self.assertEqual("<EMAIL>", f.clean("<EMAIL>"))
        with self.assertRaisesMessage(
            ValidationError,
            "'Ensure this value has at most 20 characters (it has 28).'",
        ):
            f.clean("<EMAIL>")
        with self.assertRaisesMessage(
            ValidationError, "'Enter a valid email address.'"
        ):
            f.clean("not an email")
        self.assertEqual("", f.clean(""))
        self.assertEqual("", f.clean(None))

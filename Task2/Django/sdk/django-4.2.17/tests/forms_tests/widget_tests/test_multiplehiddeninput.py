from django.forms import Form, MultipleChoiceField, MultipleHiddenInput
from django.utils.datastructures import MultiValueDict

from .base import WidgetTest


class MultipleHiddenInputTest(WidgetTest):
    widget = MultipleHiddenInput()

    def test_render_single(self):
        self.check_html(
            self.widget,
            "email",
            ["<EMAIL>"],
            html='<input type="hidden" name="email" value="<EMAIL>">',
        )

    def test_render_multiple(self):
        self.check_html(
            self.widget,
            "email",
            ["<EMAIL>", "<EMAIL>"],
            html=(
                '<input type="hidden" name="email" value="<EMAIL>">\n'
                '<input type="hidden" name="email" value="<EMAIL>">'
            ),
        )

    def test_render_attrs(self):
        self.check_html(
            self.widget,
            "email",
            ["<EMAIL>"],
            attrs={"class": "fun"},
            html=(
                '<input type="hidden" name="email" value="<EMAIL>" '
                'class="fun">'
            ),
        )

    def test_render_attrs_multiple(self):
        self.check_html(
            self.widget,
            "email",
            ["<EMAIL>", "<EMAIL>"],
            attrs={"class": "fun"},
            html=(
                '<input type="hidden" name="email" value="<EMAIL>" '
                'class="fun">\n'
                '<input type="hidden" name="email" value="<EMAIL>" class="fun">'
            ),
        )

    def test_render_attrs_constructor(self):
        widget = MultipleHiddenInput(attrs={"class": "fun"})
        self.check_html(widget, "email", [], "")
        self.check_html(
            widget,
            "email",
            ["<EMAIL>"],
            html=(
                '<input type="hidden" class="fun" value="<EMAIL>" name="email">'
            ),
        )
        self.check_html(
            widget,
            "email",
            ["<EMAIL>", "<EMAIL>"],
            html=(
                '<input type="hidden" class="fun" value="<EMAIL>" '
                'name="email">\n'
                '<input type="hidden" class="fun" value="<EMAIL>" '
                'name="email">'
            ),
        )
        self.check_html(
            widget,
            "email",
            ["<EMAIL>"],
            attrs={"class": "special"},
            html=(
                '<input type="hidden" class="special" value="<EMAIL>" '
                'name="email">'
            ),
        )

    def test_render_empty(self):
        self.check_html(self.widget, "email", [], "")

    def test_render_none(self):
        self.check_html(self.widget, "email", None, "")

    def test_render_increment_id(self):
        """
        Each input should get a separate ID.
        """
        self.check_html(
            self.widget,
            "letters",
            ["a", "b", "c"],
            attrs={"id": "hideme"},
            html=(
                '<input type="hidden" name="letters" value="a" id="hideme_0">\n'
                '<input type="hidden" name="letters" value="b" id="hideme_1">\n'
                '<input type="hidden" name="letters" value="c" id="hideme_2">'
            ),
        )

    def test_fieldset(self):
        class TestForm(Form):
            template_name = "forms_tests/use_fieldset.html"
            composers = MultipleChoiceField(
                choices=[("J", "John Lennon"), ("P", "Paul McCartney")],
                widget=MultipleHiddenInput,
            )

        form = TestForm(MultiValueDict({"composers": ["J", "P"]}))
        self.assertIs(self.widget.use_fieldset, False)
        self.assertHTMLEqual(
            '<input type="hidden" name="composers" value="J" id="id_composers_0">'
            '<input type="hidden" name="composers" value="P" id="id_composers_1">',
            form.render(),
        )

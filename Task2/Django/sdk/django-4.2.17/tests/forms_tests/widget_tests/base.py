from django.forms.renderers import DjangoTemplates, Jinja2
from django.test import SimpleTestCase

try:
    import jinja2
except ImportError:
    jinja2 = None


class WidgetTest(SimpleTestCase):
    beatles = (("<PERSON>", "<PERSON>"), ("<PERSON>", "<PERSON>"), ("<PERSON>", "<PERSON>"), ("<PERSON>", "<PERSON><PERSON>"))

    @classmethod
    def setUpClass(cls):
        cls.django_renderer = DjangoTemplates()
        cls.jinja2_renderer = Jinja2() if jinja2 else None
        cls.renderers = [cls.django_renderer] + (
            [cls.jinja2_renderer] if cls.jinja2_renderer else []
        )
        super().setUpClass()

    def check_html(
        self, widget, name, value, html="", attrs=None, strict=False, **kwargs
    ):
        assertEqual = self.assertEqual if strict else self.assertHTMLEqual
        if self.jinja2_renderer:
            output = widget.render(
                name, value, attrs=attrs, renderer=self.jinja2_renderer, **kwargs
            )
            # Django escapes quotes with '&quot;' while <PERSON><PERSON><PERSON> uses '&#34;'.
            output = output.replace("&#34;", "&quot;")
            # Django escapes single quotes with '&#x27;' while Jinja2 uses '&#39;'.
            output = output.replace("&#39;", "&#x27;")
            assertEqual(output, html)

        output = widget.render(
            name, value, attrs=attrs, renderer=self.django_renderer, **kwargs
        )
        assertEqual(output, html)

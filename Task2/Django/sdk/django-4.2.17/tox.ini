# Tox (https://tox.readthedocs.io/) is a tool for running tests in multiple
# virtualenvs. This configuration file helps to run the test suite on all
# supported Python versions. To use it, "python -m pip install tox" and
# then run "tox" from this directory.

[tox]
minversion = 3.18
skipsdist = true
envlist =
    py3
    black
    blacken-docs
    flake8
    docs
    isort

# Add environment to use the default python3 installation
[testenv:py3]
basepython = python3

[testenv]
usedevelop = true
# OBJC_DISABLE_INITIALIZE_FORK_SAFETY fixes hung tests for MacOS users. (#30806)
passenv = DJANGO_SETTINGS_MODULE,PYTHONPATH,HOME,DISPLAY,OBJC_DISABLE_INITIALIZE_FORK_SAFETY
setenv =
    PYTHONDONTWRITEBYTECODE=1
deps =
    py{3,38,39,310,311,312}: -rtests/requirements/py3.txt
    postgres: -rtests/requirements/postgres.txt
    mysql: -rtests/requirements/mysql.txt
    oracle: -rtests/requirements/oracle.txt
changedir = tests
commands =
    {envpython} runtests.py {posargs}

[testenv:black]
basepython = python3
usedevelop = false
deps = black == 23.12.1
changedir = {toxinidir}
commands = black --check --diff .

[testenv:blacken-docs]
basepython = python3
usedevelop = false
allowlist_externals =
    make
deps = blacken-docs
changedir = docs
commands =
    make black

[testenv:flake8]
basepython = python3
usedevelop = false
deps = flake8 >= 3.7.0
changedir = {toxinidir}
commands = flake8 .

[testenv:docs]
basepython = python3
usedevelop = false
allowlist_externals =
    make
deps =
    Sphinx
    pyenchant
    sphinxcontrib-spelling
changedir = docs
commands =
    make spelling

[testenv:isort]
basepython = python3
usedevelop = false
deps = isort >= 5.1.0
changedir = {toxinidir}
commands = isort --check-only --diff django tests scripts

[testenv:javascript]
usedevelop = false
deps =
changedir = {toxinidir}
allowlist_externals =
    npm
commands =
    npm install
    npm test

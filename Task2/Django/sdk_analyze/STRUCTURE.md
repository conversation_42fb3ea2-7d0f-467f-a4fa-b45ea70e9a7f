# Django SDK分析工具项目结构

## 📁 完整目录结构

```
Task2/Django/
├── sdk_analyze/                 # SDK分析工具目录
│   ├── README.md               # 详细使用文档
│   ├── STRUCTURE.md            # 项目结构说明（本文件）
│   ├── main.py                 # 主入口脚本（推荐使用）
│   ├── sdk_analyze.py          # 核心SDK分析器
│   ├── show_sdk_analysis.py    # 结果展示工具
│   ├── example.py              # 使用示例代码
│   ├── django_sdk_analysis.json # 分析结果数据
│   └── django_sdk_report.md    # 人类可读的分析报告
├── source_analyze/             # 源码分析工具目录
│   ├── README.md               # 源码分析文档
│   ├── analyze_call_relations.py # 调用关系分析器
│   ├── django_call_relations.json # 调用关系数据
│   └── show_enhanced_call_graph.py # 调用图展示工具
├── source/                     # Django源代码
│   └── django/                 # Django框架源码
├── sdk/                        # SDK二进制文件
│   ├── django-5.1.4.tar.gz    # Django 5.1.4源码包
│   ├── django-4.2.17.tar.gz   # Django 4.2.17源码包
│   ├── django-5.1.4/           # 解压后的5.1.4源码
│   ├── django-4.2.17/          # 解压后的4.2.17源码
│   └── dependencies/           # 依赖包
│       ├── asgiref-3.9.1-py3-none-any.whl
│       ├── sqlparse-0.5.3-py3-none-any.whl
│       └── tzdata-2025.2-py2.py3-none-any.whl
└── symbols/                    # 符号表和调试信息
    ├── symbol_info.json        # 框架符号表信息
    └── debug_symbols_info.json # 调试符号配置
```

## 🔧 工具功能对比

### SDK分析工具 (`sdk_analyze/`)
- **目标**: 分析SDK二进制文件、包结构、依赖关系
- **输入**: SDK包文件、符号表
- **输出**: 包分析、依赖图、模块结构
- **适用场景**: 部署分析、依赖管理、版本对比

### 源码分析工具 (`source_analyze/`)
- **目标**: 分析源代码调用关系、函数依赖
- **输入**: Python源代码文件
- **输出**: 调用关系图、函数位置信息
- **适用场景**: 代码理解、架构分析、调试定位

## 🚀 快速开始指南

### 1. SDK分析
```bash
cd Task2/Django/sdk_analyze
python main.py                    # 交互式模式
# 或
python main.py --analyze          # 直接分析
```

### 2. 源码分析
```bash
cd Task2/Django/source_analyze
python analyze_call_relations.py  # 分析调用关系
python show_enhanced_call_graph.py # 显示结果
```

## 📊 分析结果文件说明

### SDK分析结果
- `django_sdk_analysis.json`: 完整的JSON格式分析数据
- `django_sdk_report.md`: 人类可读的Markdown报告

### 源码分析结果
- `django_call_relations.json`: 函数调用关系数据
- 包含文件路径和行号信息

## 🎯 使用场景

### 开发场景
1. **理解Django架构**: 使用源码分析工具查看调用关系
2. **调试问题**: 通过调用链定位问题函数
3. **学习源码**: 了解Django内部实现机制

### 部署场景
1. **依赖管理**: 使用SDK分析了解依赖包
2. **版本选择**: 对比不同版本的差异
3. **包大小优化**: 分析包内容和文件分布

### 维护场景
1. **代码重构**: 分析函数调用关系
2. **性能优化**: 识别热点调用路径
3. **安全审计**: 检查外部依赖和调用链

## 🔗 工具集成

### 数据流向
```
Django源码 → 源码分析器 → 调用关系图
     ↓
SDK包文件 → SDK分析器 → 包依赖图
     ↓
符号表信息 → 符号分析器 → 调试信息
```

### 结果关联
- SDK分析提供**宏观视图**（包、模块、依赖）
- 源码分析提供**微观视图**（函数、调用、位置）
- 符号表提供**调试支持**（版本、配置、环境）

## 📝 扩展开发

### 添加新分析功能
1. 在对应工具目录创建新的分析模块
2. 继承现有分析器类
3. 实现特定的分析逻辑
4. 更新主入口脚本

### 自定义输出格式
1. 修改`save_analysis_results`方法
2. 添加新的格式化函数
3. 更新展示工具

## 🐛 故障排除

### 常见问题
1. **路径问题**: 确保在正确的目录运行工具
2. **权限问题**: 检查文件读取权限
3. **依赖问题**: 确保Python环境完整

### 调试技巧
1. 使用`--help`查看命令选项
2. 检查生成的JSON文件格式
3. 查看错误日志和异常信息

## 📄 许可证

本工具集遵循BSD许可证，与Django框架保持一致。

---

**注意**: 这是一个专门为Django框架设计的分析工具集，包含SDK分析和源码分析两大功能模块。

#!/usr/bin/env python3
"""
Django SDK分析工具主入口
提供命令行界面和交互式菜单
"""

import sys
import argparse
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from sdk_analyze import DjangoSDKAnalyzer
from show_sdk_analysis import (
    show_sdk_summary, show_package_analysis, 
    show_dependencies, show_call_relations, 
    show_symbol_tables, generate_sdk_report
)

def show_menu():
    """显示交互式菜单"""
    print("\n🔍 Django SDK分析工具")
    print("=" * 40)
    print("1. 运行完整SDK分析")
    print("2. 显示分析摘要")
    print("3. 显示包分析结果")
    print("4. 显示依赖关系")
    print("5. 显示调用关系")
    print("6. 显示符号表信息")
    print("7. 生成分析报告")
    print("8. 退出")
    print("=" * 40)

def run_analysis(base_dir=None):
    """运行SDK分析"""
    if base_dir:
        analyzer = DjangoSDKAnalyzer(base_dir)
    else:
        # 默认使用上级目录
        analyzer = DjangoSDKAnalyzer("..")
    
    print("🚀 开始Django SDK分析...")
    results = analyzer.run_full_analysis()
    return results

def interactive_mode():
    """交互式模式"""
    analysis_done = False
    
    while True:
        show_menu()
        try:
            choice = input("\n请选择操作 (1-8): ").strip()
            
            if choice == '1':
                run_analysis()
                analysis_done = True
                print("\n✅ 分析完成！现在可以查看结果。")
                
            elif choice == '2':
                if not analysis_done and not Path("django_sdk_analysis.json").exists():
                    print("❌ 请先运行分析 (选项1)")
                    continue
                show_sdk_summary()
                
            elif choice == '3':
                if not analysis_done and not Path("django_sdk_analysis.json").exists():
                    print("❌ 请先运行分析 (选项1)")
                    continue
                show_package_analysis()
                
            elif choice == '4':
                if not analysis_done and not Path("django_sdk_analysis.json").exists():
                    print("❌ 请先运行分析 (选项1)")
                    continue
                show_dependencies()
                
            elif choice == '5':
                if not analysis_done and not Path("django_sdk_analysis.json").exists():
                    print("❌ 请先运行分析 (选项1)")
                    continue
                show_call_relations()
                
            elif choice == '6':
                if not analysis_done and not Path("django_sdk_analysis.json").exists():
                    print("❌ 请先运行分析 (选项1)")
                    continue
                show_symbol_tables()
                
            elif choice == '7':
                if not analysis_done and not Path("django_sdk_analysis.json").exists():
                    print("❌ 请先运行分析 (选项1)")
                    continue
                generate_sdk_report()
                
            elif choice == '8':
                print("👋 再见！")
                break
                
            else:
                print("❌ 无效选择，请输入1-8")
                
        except KeyboardInterrupt:
            print("\n\n👋 用户中断，退出程序")
            break
        except Exception as e:
            print(f"❌ 错误: {e}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="Django SDK分析工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python main.py                    # 交互式模式
  python main.py --analyze          # 直接运行分析
  python main.py --summary          # 显示摘要
  python main.py --report           # 生成报告
  python main.py --dir /path/to/django  # 指定Django目录
        """
    )
    
    parser.add_argument(
        '--analyze', '-a',
        action='store_true',
        help='直接运行SDK分析'
    )
    
    parser.add_argument(
        '--summary', '-s',
        action='store_true',
        help='显示分析摘要'
    )
    
    parser.add_argument(
        '--packages', '-p',
        action='store_true',
        help='显示包分析结果'
    )
    
    parser.add_argument(
        '--dependencies', '-d',
        action='store_true',
        help='显示依赖关系'
    )
    
    parser.add_argument(
        '--relations', '-r',
        action='store_true',
        help='显示调用关系'
    )
    
    parser.add_argument(
        '--symbols', '-y',
        action='store_true',
        help='显示符号表信息'
    )
    
    parser.add_argument(
        '--report', '-R',
        action='store_true',
        help='生成分析报告'
    )
    
    parser.add_argument(
        '--dir', '-D',
        type=str,
        help='指定Django项目目录'
    )
    
    parser.add_argument(
        '--interactive', '-i',
        action='store_true',
        help='启动交互式模式'
    )
    
    args = parser.parse_args()
    
    # 如果没有任何参数，启动交互式模式
    if len(sys.argv) == 1:
        interactive_mode()
        return
    
    # 处理命令行参数
    try:
        if args.analyze:
            run_analysis(args.dir)
            
        elif args.summary:
            show_sdk_summary()
            
        elif args.packages:
            show_package_analysis()
            
        elif args.dependencies:
            show_dependencies()
            
        elif args.relations:
            show_call_relations()
            
        elif args.symbols:
            show_symbol_tables()
            
        elif args.report:
            generate_sdk_report()
            
        elif args.interactive:
            interactive_mode()
            
        else:
            # 如果指定了目录但没有其他操作，运行分析
            if args.dir:
                run_analysis(args.dir)
            else:
                parser.print_help()
                
    except FileNotFoundError:
        print("❌ 找不到分析结果文件")
        print("请先运行: python main.py --analyze")
    except Exception as e:
        print(f"❌ 错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()

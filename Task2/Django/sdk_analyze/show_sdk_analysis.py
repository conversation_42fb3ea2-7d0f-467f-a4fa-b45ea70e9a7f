#!/usr/bin/env python3
"""
展示Django SDK分析结果
"""

import json
from pathlib import Path

def show_sdk_summary():
    """显示SDK分析摘要"""
    
    with open('django_sdk_analysis.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    metadata = data.get('metadata', {})
    summary = data.get('summary', {})
    
    print("🚀 Django SDK分析摘要")
    print("=" * 60)
    print(f"分析类型: {metadata.get('analysis_type', 'N/A')}")
    print(f"SDK目录: {metadata.get('sdk_directory', 'N/A')}")
    print(f"符号表目录: {metadata.get('symbols_directory', 'N/A')}")
    print(f"SDK包数量: {summary.get('total_packages', 0)}")
    print(f"依赖包数量: {summary.get('dependencies_count', 0)}")
    print(f"二进制文件: {summary.get('binary_files', 0)}")

def show_package_analysis():
    """显示包分析结果"""
    
    with open('django_sdk_analysis.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    sdk_packages = data.get('sdk_packages', {})
    
    print("\n📦 SDK包分析")
    print("=" * 60)
    
    for package_name, package_info in sdk_packages.items():
        if package_name == 'dependencies':
            continue
            
        print(f"\n📋 {package_name}")
        package_type = package_info.get('type', 'unknown')
        print(f"   类型: {package_type}")
        
        if package_type == 'source_package':
            file_stats = package_info.get('file_stats', {})
            print(f"   📊 Python文件: {file_stats.get('python_files', 0)}")
            print(f"   📊 模板文件: {file_stats.get('template_files', 0)}")
            print(f"   📊 静态文件: {file_stats.get('static_files', 0)}")
            print(f"   📊 配置文件: {file_stats.get('config_files', 0)}")
            print(f"   📊 总文件数: {file_stats.get('total_files', 0)}")
            
        elif package_type == 'extracted_package':
            django_modules = package_info.get('django_modules', [])
            entry_points = package_info.get('entry_points', [])
            print(f"   📊 Django模块: {len(django_modules)}")
            print(f"   📊 入口点: {len(entry_points)}")
            
            if django_modules:
                print(f"   🏗️  主要模块: {', '.join(django_modules[:5])}")
            if entry_points:
                print(f"   🚪 入口点: {', '.join(entry_points)}")
        
        elif package_type == 'wheel_package':
            total_files = package_info.get('total_files', 0)
            print(f"   📊 总文件数: {total_files}")

def show_dependencies():
    """显示依赖关系"""
    
    with open('django_sdk_analysis.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    dependencies = data.get('sdk_packages', {}).get('dependencies', {})
    
    print("\n🔗 依赖包分析")
    print("=" * 60)
    
    for dep_name, dep_info in dependencies.items():
        print(f"\n📦 {dep_name}")
        print(f"   名称: {dep_info.get('name', 'N/A')}")
        print(f"   版本: {dep_info.get('version', 'N/A')}")
        print(f"   描述: {dep_info.get('summary', 'N/A')}")
        print(f"   作者: {dep_info.get('author', 'N/A')}")
        print(f"   许可证: {dep_info.get('license', 'N/A')}")
        
        requires = dep_info.get('requires', [])
        if requires:
            print(f"   依赖: {len(requires)} 个")
            for req in requires[:3]:  # 只显示前3个
                print(f"     - {req}")
            if len(requires) > 3:
                print(f"     ... 还有 {len(requires) - 3} 个依赖")

def show_call_relations():
    """显示调用关系"""
    
    with open('django_sdk_analysis.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    call_relations = data.get('call_relations', {})
    
    print("\n🔗 SDK调用关系")
    print("=" * 60)
    
    # 包依赖关系
    package_deps = call_relations.get('package_dependencies', {})
    if package_deps:
        print("\n📦 包依赖关系:")
        for package, deps in package_deps.items():
            print(f"   {package} 依赖:")
            for dep in deps[:3]:  # 只显示前3个
                print(f"     └─ {dep}")
            if len(deps) > 3:
                print(f"     └─ ... 还有 {len(deps) - 3} 个依赖")
    
    # 模块结构
    module_structure = call_relations.get('module_structure', {})
    if module_structure:
        print("\n🏗️  模块结构:")
        for package, structure in module_structure.items():
            print(f"   {package}:")
            django_modules = structure.get('django_modules', [])
            entry_points = structure.get('entry_points', [])
            
            if django_modules:
                print(f"     Django模块: {', '.join(django_modules)}")
            if entry_points:
                print(f"     入口点: {', '.join(entry_points)}")

def show_symbol_tables():
    """显示符号表信息"""
    
    with open('django_sdk_analysis.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    symbol_tables = data.get('symbol_tables', {})
    
    print("\n📊 符号表信息")
    print("=" * 60)
    
    for table_name, table_data in symbol_tables.items():
        print(f"\n📋 {table_name}")
        
        if table_name == 'symbol_info.json':
            framework = table_data.get('framework', 'N/A')
            version_info = table_data.get('version_info', {})
            print(f"   框架: {framework}")
            print(f"   最新版本: {version_info.get('latest_stable', 'N/A')}")
            print(f"   LTS版本: {version_info.get('lts_version', 'N/A')}")
            print(f"   Python要求: {version_info.get('python_requirement', 'N/A')}")
            
            source_code = table_data.get('source_code', {})
            if source_code:
                print(f"   源码位置: {source_code.get('location', 'N/A')}")
                print(f"   仓库地址: {source_code.get('repository', 'N/A')}")
        
        elif table_name == 'debug_symbols_info.json':
            python_debug = table_data.get('python_debug_symbols', {})
            django_debug = table_data.get('django_debug', {})
            
            print("   Python调试符号:")
            for system, command in python_debug.items():
                if system != 'compile_flags':
                    print(f"     {system}: {command}")
            
            print("   Django调试:")
            for key, value in django_debug.items():
                print(f"     {key}: {value}")

def generate_sdk_report():
    """生成SDK分析报告"""
    
    with open('django_sdk_analysis.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    report = []
    report.append("# Django SDK分析报告\n")
    
    # 基本信息
    metadata = data.get('metadata', {})
    summary = data.get('summary', {})
    
    report.append("## 基本信息\n")
    report.append(f"- **分析类型**: {metadata.get('analysis_type', 'N/A')}")
    report.append(f"- **SDK目录**: {metadata.get('sdk_directory', 'N/A')}")
    report.append(f"- **符号表目录**: {metadata.get('symbols_directory', 'N/A')}")
    report.append(f"- **SDK包数量**: {summary.get('total_packages', 0)}")
    report.append(f"- **依赖包数量**: {summary.get('dependencies_count', 0)}")
    report.append(f"- **二进制文件**: {summary.get('binary_files', 0)}\n")
    
    # 包分析
    sdk_packages = data.get('sdk_packages', {})
    report.append("## 包分析\n")
    
    for package_name, package_info in sdk_packages.items():
        if package_name == 'dependencies':
            continue
        
        report.append(f"### {package_name}")
        package_type = package_info.get('type', 'unknown')
        report.append(f"- **类型**: {package_type}")
        
        if package_type == 'source_package':
            file_stats = package_info.get('file_stats', {})
            report.append(f"- **Python文件**: {file_stats.get('python_files', 0)}")
            report.append(f"- **总文件数**: {file_stats.get('total_files', 0)}")
        
        report.append("")
    
    # 依赖关系
    dependencies = sdk_packages.get('dependencies', {})
    if dependencies:
        report.append("## 依赖包\n")
        for dep_name, dep_info in dependencies.items():
            report.append(f"### {dep_info.get('name', dep_name)}")
            report.append(f"- **版本**: {dep_info.get('version', 'N/A')}")
            report.append(f"- **描述**: {dep_info.get('summary', 'N/A')}")
            report.append("")
    
    # 保存报告
    with open('django_sdk_report.md', 'w', encoding='utf-8') as f:
        f.write('\n'.join(report))
    
    print("✅ SDK分析报告保存到: django_sdk_report.md")

def main():
    print("🔍 Django SDK分析结果查看器")
    
    try:
        # 显示摘要
        show_sdk_summary()
        
        # 显示包分析
        show_package_analysis()
        
        # 显示依赖关系
        show_dependencies()
        
        # 显示调用关系
        show_call_relations()
        
        # 显示符号表
        show_symbol_tables()
        
        # 生成报告
        generate_sdk_report()
        
    except FileNotFoundError:
        print("❌ 找不到 django_sdk_analysis.json 文件")
        print("请先运行 sdk_analyze.py")
    except Exception as e:
        print(f"❌ 错误: {e}")

if __name__ == "__main__":
    main()

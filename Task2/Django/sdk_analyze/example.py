#!/usr/bin/env python3
"""
Django SDK分析工具使用示例
演示如何使用SDK分析器的各种功能
"""

import sys
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from sdk_analyze import DjangoSDKAnalyzer

def example_basic_analysis():
    """示例1: 基本分析"""
    print("📋 示例1: 基本SDK分析")
    print("-" * 40)
    
    # 创建分析器实例
    analyzer = DjangoSDKAnalyzer("..")  # 上级目录包含sdk/
    
    # 运行完整分析
    results = analyzer.run_full_analysis()
    
    # 显示基本统计
    summary = results['summary']
    print(f"✅ 分析完成:")
    print(f"   SDK包数量: {summary['total_packages']}")
    print(f"   依赖包数量: {summary['dependencies_count']}")
    print(f"   二进制文件: {summary['binary_files']}")

def example_custom_analysis():
    """示例2: 自定义分析"""
    print("\n📋 示例2: 自定义分析步骤")
    print("-" * 40)
    
    analyzer = DjangoSDKAnalyzer("..")
    
    # 分步执行分析
    print("1. 加载符号表...")
    analyzer.load_symbol_info()
    
    print("2. 分析SDK包...")
    analyzer.analyze_sdk_packages()
    
    print("3. 分析依赖关系...")
    analyzer.analyze_dependencies()
    
    print("4. 生成调用关系...")
    analyzer.generate_sdk_call_relations()
    
    print("5. 保存结果...")
    results = analyzer.save_analysis_results("custom_analysis.json")
    
    print("✅ 自定义分析完成")

def example_analyze_specific_package():
    """示例3: 分析特定包"""
    print("\n📋 示例3: 分析特定包")
    print("-" * 40)
    
    analyzer = DjangoSDKAnalyzer("..")
    
    # 查找Django包
    sdk_dir = Path("..") / "sdk"
    django_packages = list(sdk_dir.glob("django-*.tar.gz"))
    
    if django_packages:
        package = django_packages[0]
        print(f"分析包: {package.name}")
        
        # 分析单个包
        analyzer.analyze_package(package)
        
        # 显示包信息
        if package.name in analyzer.sdk_info:
            info = analyzer.sdk_info[package.name]
            file_stats = info.get('file_stats', {})
            print(f"   Python文件: {file_stats.get('python_files', 0)}")
            print(f"   总文件数: {file_stats.get('total_files', 0)}")
    else:
        print("❌ 未找到Django包文件")

def example_dependency_analysis():
    """示例4: 依赖关系分析"""
    print("\n📋 示例4: 依赖关系分析")
    print("-" * 40)
    
    analyzer = DjangoSDKAnalyzer("..")
    
    # 分析依赖
    analyzer.analyze_dependencies()
    
    # 显示依赖信息
    dependencies = analyzer.sdk_info.get('dependencies', {})
    print(f"发现 {len(dependencies)} 个依赖包:")
    
    for dep_name, dep_info in dependencies.items():
        name = dep_info.get('name', 'Unknown')
        version = dep_info.get('version', 'Unknown')
        print(f"   {name} v{version}")
        
        requires = dep_info.get('requires', [])
        if requires:
            print(f"     依赖: {len(requires)} 个包")

def example_symbol_analysis():
    """示例5: 符号表分析"""
    print("\n📋 示例5: 符号表分析")
    print("-" * 40)
    
    analyzer = DjangoSDKAnalyzer("..")
    
    # 加载符号表
    analyzer.load_symbol_info()
    
    # 显示符号表信息
    for table_name, table_data in analyzer.symbol_tables.items():
        print(f"符号表: {table_name}")
        
        if 'framework' in table_data:
            framework = table_data['framework']
            version_info = table_data.get('version_info', {})
            print(f"   框架: {framework}")
            print(f"   版本: {version_info.get('latest_stable', 'N/A')}")

def example_generate_reports():
    """示例6: 生成报告"""
    print("\n📋 示例6: 生成分析报告")
    print("-" * 40)
    
    analyzer = DjangoSDKAnalyzer("..")
    
    # 运行分析
    analyzer.run_full_analysis()
    
    # 生成不同格式的报告
    print("生成JSON报告...")
    analyzer.save_analysis_results("example_analysis.json")
    
    print("生成自定义报告...")
    results = analyzer.save_analysis_results()
    
    # 创建简化报告
    simple_report = {
        'packages': len([k for k, v in analyzer.sdk_info.items() 
                        if isinstance(v, dict) and 'type' in v]),
        'dependencies': len(analyzer.sdk_info.get('dependencies', {})),
        'modules': []
    }
    
    # 提取模块信息
    for package_name, package_info in analyzer.sdk_info.items():
        if package_info.get('type') == 'extracted_package':
            modules = package_info.get('django_modules', [])
            simple_report['modules'].extend(modules)
    
    simple_report['modules'] = list(set(simple_report['modules']))
    
    print(f"✅ 简化报告:")
    print(f"   包数量: {simple_report['packages']}")
    print(f"   依赖数量: {simple_report['dependencies']}")
    print(f"   模块数量: {len(simple_report['modules'])}")

def run_all_examples():
    """运行所有示例"""
    print("🚀 Django SDK分析工具示例")
    print("=" * 50)
    
    try:
        example_basic_analysis()
        example_custom_analysis()
        example_analyze_specific_package()
        example_dependency_analysis()
        example_symbol_analysis()
        example_generate_reports()
        
        print("\n🎉 所有示例运行完成！")
        print("\n生成的文件:")
        print("- django_sdk_analysis.json")
        print("- custom_analysis.json")
        print("- example_analysis.json")
        print("- django_sdk_report.md")
        
    except Exception as e:
        print(f"❌ 示例运行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    run_all_examples()

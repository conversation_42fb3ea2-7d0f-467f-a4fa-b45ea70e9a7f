{"metadata": {"analysis_type": "Django SDK Analysis", "sdk_directory": "sdk", "symbols_directory": "symbols"}, "sdk_packages": {"django-5.1.4.tar.gz": {"type": "source_package", "file_stats": {"python_files": 2788, "template_files": 1022, "static_files": 202, "config_files": 69, "total_files": 10063}, "members": ["django-5.1.4", "django-5.1.4/.editorconfig", "django-5.1.4/.flake8", "django-5.1.4/.git-blame-ignore-revs", "django-5.1.4/.gitattributes", "django-5.1.4/.github", "django-5.1.4/.github/CODE_OF_CONDUCT.md", "django-5.1.4/.github/FUNDING.yml", "django-5.1.4/.github/SECURITY.md", "django-5.1.4/.github/pull_request_template.md", "django-5.1.4/.github/workflows", "django-5.1.4/.github/workflows/benchmark.yml", "django-5.1.4/.github/workflows/data", "django-5.1.4/.github/workflows/data/test_postgres.py.tpl", "django-5.1.4/.github/workflows/docs.yml", "django-5.1.4/.github/workflows/linters.yml", "django-5.1.4/.github/workflows/new_contributor_pr.yml", "django-5.1.4/.github/workflows/python_matrix.yml", "django-5.1.4/.github/workflows/reminders_check.yml", "django-5.1.4/.github/workflows/reminders_create.yml"]}, "django-4.2.17.tar.gz": {"type": "source_package", "file_stats": {"python_files": 2762, "template_files": 1004, "static_files": 198, "config_files": 66, "total_files": 9933}, "members": ["django-4.2.17", "django-4.2.17/.editorconfig", "django-4.2.17/.<PERSON><PERSON><PERSON><PERSON>", "django-4.2.17/.eslintrc", "django-4.2.17/.git-blame-ignore-revs", "django-4.2.17/.gitattributes", "django-4.2.17/.github", "django-4.2.17/.github/CODE_OF_CONDUCT.md", "django-4.2.17/.github/FUNDING.yml", "django-4.2.17/.github/SECURITY.md", "django-4.2.17/.github/workflows", "django-4.2.17/.github/workflows/benchmark.yml", "django-4.2.17/.github/workflows/docs.yml", "django-4.2.17/.github/workflows/linters.yml", "django-4.2.17/.github/workflows/new_contributor_pr.yml", "django-4.2.17/.github/workflows/python_matrix.yml", "django-4.2.17/.github/workflows/schedule_tests.yml", "django-4.2.17/.github/workflows/schedules.yml", "django-4.2.17/.github/workflows/tests.yml", "django-4.2.17/.gitignore"]}, "django-4.2.17": {"type": "extracted_package", "setup_files": ["setup.py", "pyproject.toml"], "django_modules": ["test", "utils", "db", "core", "contrib", "templatetags", "http", "urls", "middleware", "apps", "forms", "template", "conf", "dispatch", "views"], "entry_points": ["django/__main__.py", "tests/utils_tests/test_module/__main__.py", "tests/i18n/sampleproject/manage.py"], "package_structure": {"directories": ["docs", "django", "scripts", "js_tests", "extras", "tests", "docs/internals", "docs/_ext", "docs/man", "docs/ref", "docs/releases", "docs/misc", "docs/_theme", "docs/howto", "docs/topics", "docs/intro", "docs/faq", "docs/internals/_images", "docs/internals/contributing", "docs/internals/contributing/writing-code"], "python_modules": ["setup.py", "docs/conf.py", "docs/_ext/djangodocs.py", "django/__init__.py", "django/__main__.py", "django/shortcuts.py", "django/test/__init__.py", "django/test/html.py", "django/test/signals.py", "django/test/utils.py", "django/test/client.py", "django/test/runner.py", "django/test/testcases.py", "django/test/selenium.py", "django/utils/__init__.py", "django/utils/feedgenerator.py", "django/utils/_os.py", "django/utils/crypto.py", "django/utils/tree.py", "django/utils/baseconv.py"], "config_files": ["LICENSE", "setup.cfg", "MANIFEST.in", "README.md", "LICENSE", "LICENSE", "LICENSE"], "documentation": ["docs/glossary.txt", "docs/README.rst", "docs/contents.txt", "docs/index.txt", "docs/requirements.txt", "docs/internals/howto-release-django.txt", "docs/internals/security.txt", "docs/internals/deprecation.txt", "docs/internals/organization.txt", "docs/internals/git.txt"]}}, "django-5.1.4": {"type": "extracted_package", "setup_files": ["pyproject.toml"], "django_modules": ["test", "utils", "db", "core", "contrib", "templatetags", "http", "urls", "middleware", "apps", "forms", "template", "conf", "dispatch", "views"], "entry_points": ["django/__main__.py", "tests/utils_tests/test_module/__main__.py", "tests/i18n/sampleproject/manage.py"], "package_structure": {"directories": ["docs", "django", "scripts", "js_tests", "extras", "tests", "docs/internals", "docs/_ext", "docs/man", "docs/ref", "docs/releases", "docs/misc", "docs/_theme", "docs/howto", "docs/topics", "docs/intro", "docs/faq", "docs/internals/_images", "docs/internals/contributing", "docs/internals/contributing/writing-code"], "python_modules": ["docs/conf.py", "docs/_ext/djangodocs.py", "docs/_ext/github_links.py", "django/__init__.py", "django/__main__.py", "django/shortcuts.py", "django/test/__init__.py", "django/test/html.py", "django/test/signals.py", "django/test/utils.py", "django/test/client.py", "django/test/runner.py", "django/test/testcases.py", "django/test/selenium.py", "django/utils/__init__.py", "django/utils/feedgenerator.py", "django/utils/_os.py", "django/utils/crypto.py", "django/utils/tree.py", "django/utils/module_loading.py"], "config_files": ["LICENSE", "MANIFEST.in", "README.md", "LICENSE", "LICENSE", "LICENSE", "README.md"], "documentation": ["docs/glossary.txt", "docs/README.rst", "docs/contents.txt", "docs/index.txt", "docs/requirements.txt", "docs/internals/howto-release-django.txt", "docs/internals/security.txt", "docs/internals/deprecation.txt", "docs/internals/organization.txt", "docs/internals/git.txt"]}}, "dependencies": {"tzdata-2025.2-py2.py3-none-any.whl": {"name": "tzdata", "version": "2025.2", "summary": "Provider of IANA time zone data", "author": "Python Software Foundation", "license": "Apache-2.0"}, "sqlparse-0.5.3-py3-none-any.whl": {"name": "sqlparse", "version": "0.5.3", "summary": "A non-validating SQL parser.", "requires": ["build; extra == 'dev'", "hatch; extra == 'dev'", "sphinx; extra == 'doc'"]}, "asgiref-3.9.1-py3-none-any.whl": {"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "3.9.1", "summary": "ASGI specs, helper code, and adapters", "author": "Django Software Foundation", "license": "BSD-3-<PERSON><PERSON>", "requires": ["typing_extensions>=4; python_version < \"3.11\"", "pytest; extra == \"tests\"", "pytest-asyncio; extra == \"tests\"", "mypy>=1.14.0; extra == \"tests\""]}}}, "symbol_tables": {"symbol_info.json": {"framework": "Django", "version_info": {"latest_stable": "5.1.4", "lts_version": "4.2.17", "python_requirement": ">=3.8"}, "source_code": {"location": "source/django/", "repository": "https://github.com/django/django.git", "documentation": "https://docs.djangoproject.com/"}, "sdk_binaries": {"location": "sdk/", "wheel_packages": "Built from source", "dependencies": "sdk/dependencies/"}, "debug_symbols": {"location": "symbols/", "python_debug": "System package manager required", "django_debug": "Source code with debug flags"}, "build_instructions": {"install_dev": "pip install -e source/django/", "run_tests": "cd source/django && python runtests.py", "build_docs": "cd source/django/docs && make html"}}, "debug_symbols_info.json": {"python_debug_symbols": {"ubuntu_debian": "sudo apt install python3-dbg python3-dev", "centos_rhel": "sudo yum install python3-debuginfo python3-devel", "arch_linux": "sudo pacman -S python-debug", "compile_flags": "CFLAGS='-g -O0' pip install --no-binary :all: django"}, "django_debug": {"source_maps": "Available in source/django/", "debug_mode": "Set DEBUG=True in settings.py", "logging": "Configure Django logging for debug output"}}}, "binary_analysis": {}, "call_relations": {"package_dependencies": {"sqlparse-0.5.3-py3-none-any.whl": ["build; extra == 'dev'", "hatch; extra == 'dev'", "sphinx; extra == 'doc'"], "asgiref-3.9.1-py3-none-any.whl": ["typing_extensions>=4; python_version < \"3.11\"", "pytest; extra == \"tests\"", "pytest-asyncio; extra == \"tests\"", "mypy>=1.14.0; extra == \"tests\""]}, "module_structure": {"django-4.2.17": {"django_modules": ["test", "utils", "db", "core", "contrib", "templatetags", "http", "urls", "middleware", "apps", "forms", "template", "conf", "dispatch", "views"], "entry_points": ["django/__main__.py", "tests/utils_tests/test_module/__main__.py", "tests/i18n/sampleproject/manage.py"]}, "django-5.1.4": {"django_modules": ["test", "utils", "db", "core", "contrib", "templatetags", "http", "urls", "middleware", "apps", "forms", "template", "conf", "dispatch", "views"], "entry_points": ["django/__main__.py", "tests/utils_tests/test_module/__main__.py", "tests/i18n/sampleproject/manage.py"]}}, "entry_points": {}, "binary_interfaces": {}}, "summary": {"total_packages": 4, "dependencies_count": 3, "binary_files": 0}}
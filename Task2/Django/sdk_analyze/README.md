# Django SDK分析工具

Django SDK分析工具集，用于分析Django框架的SDK二进制文件、符号表信息和调用关系。

## 📁 目录结构

```
sdk_analyze/
├── README.md                    # 本文档
├── main.py                     # 主入口脚本
├── sdk_analyze.py              # 核心分析器
├── show_sdk_analysis.py        # 结果展示工具
├── example.py                  # 使用示例
├── django_sdk_analysis.json    # 分析结果数据
└── django_sdk_report.md        # 分析报告
```

## 🚀 功能特性

### 1. **SDK包分析**
- 分析Django源码包（tar.gz）
- 分析wheel包结构
- 统计文件类型和数量
- 提取包元数据信息

### 2. **依赖关系分析**
- 解析依赖包的METADATA
- 分析包依赖链
- 识别开发和测试依赖
- 生成依赖关系图

### 3. **符号表分析**
- 加载符号表信息
- 解析调试符号配置
- 提供调试环境设置指南

### 4. **二进制文件分析**
- 检测共享库文件
- 分析二进制符号（如果存在）
- 识别Python扩展模块

### 5. **模块结构分析**
- 识别Django核心模块
- 查找入口点
- 分析包结构层次

## 📋 使用方法

### 快速开始

#### 1. 交互式模式（推荐）
```bash
cd Task2/Django/sdk_analyze
python main.py
```

#### 2. 命令行模式
```bash
# 运行完整分析
python main.py --analyze

# 显示分析摘要
python main.py --summary

# 显示包分析结果
python main.py --packages

# 显示依赖关系
python main.py --dependencies

# 生成分析报告
python main.py --report
```

#### 3. 直接调用分析器
```bash
python sdk_analyze.py
```

#### 4. 查看分析结果
```bash
python show_sdk_analysis.py
```

#### 5. 运行示例
```bash
python example.py
```

### 高级用法

#### 指定Django目录
```bash
python main.py --analyze --dir /path/to/django
```

#### 生成自定义报告
```bash
python main.py --analyze
python main.py --report
```

## 📊 分析结果

### 输出文件说明

#### `django_sdk_analysis.json`
完整的JSON格式分析数据，包含：
- **metadata**: 分析元数据
- **sdk_packages**: SDK包详细信息
- **symbol_tables**: 符号表数据
- **binary_analysis**: 二进制文件分析
- **call_relations**: 调用关系图
- **summary**: 统计摘要

#### `django_sdk_report.md`
人类可读的Markdown格式报告，包含：
- 基本信息统计
- 包分析结果
- 依赖关系图
- 模块结构说明

## 🔍 分析内容详解

### SDK包分析
```json
{
  "django-5.1.4.tar.gz": {
    "type": "source_package",
    "file_stats": {
      "python_files": 2788,
      "template_files": 1022,
      "static_files": 202,
      "config_files": 69,
      "total_files": 10063
    }
  }
}
```

### 依赖关系分析
```json
{
  "dependencies": {
    "asgiref-3.9.1-py3-none-any.whl": {
      "name": "asgiref",
      "version": "3.9.1",
      "summary": "ASGI specs, helper code, and adapters",
      "requires": ["typing_extensions>=4"]
    }
  }
}
```

### 模块结构分析
```json
{
  "module_structure": {
    "django-5.1.4": {
      "django_modules": ["db", "core", "contrib", "views", "http"],
      "entry_points": ["django/__main__.py", "manage.py"]
    }
  }
}
```

## 🛠️ 技术实现

### 核心技术栈
- **Python标准库**: tarfile, zipfile, subprocess
- **AST分析**: 静态代码分析
- **元数据解析**: wheel和tar.gz包格式解析
- **符号表处理**: JSON配置文件解析

### 分析流程
1. **扫描SDK目录** → 发现所有包文件
2. **解析包结构** → 提取文件列表和元数据
3. **分析依赖关系** → 解析METADATA文件
4. **检测二进制文件** → 查找.so/.dll/.dylib文件
5. **生成调用关系** → 构建依赖图
6. **输出结果** → JSON数据和Markdown报告

## 📈 分析统计

### Django 5.1.4 包分析
- **Python文件**: 2,788个
- **模板文件**: 1,022个
- **静态文件**: 202个
- **配置文件**: 69个
- **总文件数**: 10,063个

### 核心依赖包
- **asgiref**: ASGI规范和适配器
- **sqlparse**: SQL解析器
- **tzdata**: 时区数据提供者

### Django核心模块
- **db**: 数据库层
- **core**: 核心功能
- **contrib**: 贡献模块
- **views**: 视图层
- **http**: HTTP处理
- **utils**: 工具函数
- **template**: 模板引擎

## 🔧 配置选项

### 自定义分析路径
```python
analyzer = DjangoSDKAnalyzer(base_dir="/path/to/django")
analyzer.run_full_analysis()
```

### 指定输出文件
```python
analyzer.save_analysis_results("custom_analysis.json")
```

## 🐛 故障排除

### 常见问题

#### 1. 找不到SDK目录
```
❌ SDK目录不存在: sdk
```
**解决方案**: 确保在Django项目根目录运行，或指定正确的base_dir

#### 2. 无法分析二进制文件
```
⚠️  无法分析 libfile.so (需要nm工具)
```
**解决方案**: 安装binutils包 `sudo apt install binutils`

#### 3. 权限错误
```
⚠️  分析包失败: Permission denied
```
**解决方案**: 检查文件权限，确保有读取权限

## 📝 扩展开发

### 添加新的包格式支持
```python
def analyze_new_format(self, package_path: Path):
    """分析新格式的包"""
    # 实现新格式解析逻辑
    pass
```

### 自定义符号表解析
```python
def parse_custom_symbols(self, symbol_file: Path):
    """解析自定义符号表格式"""
    # 实现自定义解析逻辑
    pass
```

## 📄 许可证

本工具遵循与Django相同的BSD许可证。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个工具！

## 📞 联系方式

如有问题或建议，请通过以下方式联系：
- 创建GitHub Issue
- 发送邮件反馈

---

**注意**: 本工具专门为Django框架设计，可能不适用于其他Python包的分析。

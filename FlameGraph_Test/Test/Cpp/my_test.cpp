#include <iostream>
#include <chrono>
#include <thread>

// 确保函数不被内联且有明显的调用层次
__attribute__((noinline))
long long fibonacci(int n) {
    if (n <= 1) return n;
    return fibonacci(n - 1) + fibon<PERSON>ci(n - 2);
}

__attribute__((noinline))
void deep_function_c(int n) {
    long long result = fibonacci(n);
    // 添加一些计算防止优化
    for (int i = 0; i < 1000; i++) {
        result += i;
    }
}

__attribute__((noinline))
void deep_function_b(int n) {
    deep_function_c(n);
    deep_function_c(n + 1);
}

__attribute__((noinline))
void deep_function_a(int n) {
    deep_function_b(n);
    deep_function_b(n + 1);
}

__attribute__((noinline))
void cpu_work() {
    // 创建明显的调用层次
    deep_function_a(25);
    deep_function_a(26);
}

int main() {
    std::cout << "Starting CPU intensive work..." << std::endl;
    
    auto start = std::chrono::steady_clock::now();
    
    while (true) {
        cpu_work();
        
        // 不要睡眠，让CPU持续工作
        // std::this_thread::sleep_for(std::chrono::milliseconds(10));
        
        auto now = std::chrono::steady_clock::now();
        if (std::chrono::duration_cast<std::chrono::seconds>(now - start).count() > 15) {
            break;
        }
    }
    
    std::cout << "Work completed!" << std::endl;
    return 0;
}
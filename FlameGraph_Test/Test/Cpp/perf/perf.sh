#!/bin/bash
# ========== 配置区域 ==========
SRC_FILE="my_test.cpp"
TARGET_BIN="./my_test"
DURATION=10
FREQ=4000  # 提高采样频率
PERF_DIR="./perf"
PERF_OUTPUT="$PERF_DIR/perf.data"
PERF_TXT="$PERF_DIR/out.perf"
COLLAPSED_TXT="$PERF_DIR/perf_collapsed.txt"
FLAMEGRAPH_DIR="../../FlameGraph"
export DEBUGINFOD_URLS="https://debuginfod.elfutils.org/"
# ==============================

mkdir -p $PERF_DIR

# ✅ 编译：关键改进 - 使用frame pointer而不是dwarf
echo "🔧 编译 $SRC_FILE -> $TARGET_BIN"
g++ -g -O1 -fno-omit-frame-pointer -fno-inline -fno-optimize-sibling-calls $SRC_FILE -o $TARGET_BIN
if [ $? -ne 0 ]; then
    echo "❌ 编译失败"
    exit 1
fi

# ✅ 后台运行目标程序
echo "👉 启动程序..."
$TARGET_BIN &
TARGET_PID=$!
echo "👉 程序已启动，PID = $TARGET_PID"

# 等待程序开始工作
sleep 2

# ✅ 关键改进：使用frame pointer而不是dwarf
echo "🎯 使用 perf record 采样 $DURATION 秒..."
sudo perf record \
    -F $FREQ \
    -p $TARGET_PID \
    -g \
    --call-graph fp \
    --output $PERF_OUTPUT \
    -- sleep $DURATION

# 检查perf record是否成功
if [ $? -ne 0 ]; then
    echo "❌ perf record 失败"
    kill $TARGET_PID 2>/dev/null
    exit 1
fi

# 杀死目标程序
echo "🔪 终止程序..."
kill $TARGET_PID 2>/dev/null

# ✅ 生成调用链文本 - 简化版本
echo "📄 生成调用链文本..."
sudo perf script --input $PERF_OUTPUT > $PERF_TXT

# 检查生成的文件
if [ ! -s $PERF_TXT ]; then
    echo "❌ perf script 输出为空"
    exit 1
fi

# ✅ 折叠调用链
echo "📊 生成折叠格式..."
$FLAMEGRAPH_DIR/stackcollapse-perf.pl $PERF_TXT > $COLLAPSED_TXT

# 检查折叠结果
if [ ! -s $COLLAPSED_TXT ]; then
    echo "❌ 折叠输出为空"
    exit 1
fi

# 过滤掉中断相关的行，只看用户空间的调用
echo "🔍 用户空间调用链预览:"
grep -v "asm_sysvec\|irq\|interrupt" $COLLAPSED_TXT | head -20


echo "✅ 完成！折叠数据已生成: $COLLAPSED_TXT"
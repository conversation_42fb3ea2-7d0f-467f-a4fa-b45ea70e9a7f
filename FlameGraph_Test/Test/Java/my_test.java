// 文件名：FibonacciDemo.java
public class my_test {

    public static long fibonacci(int n) {
        if (n <= 1) return n;
        return fibonacci(n - 1) + fibonacci(n - 2);
    }

    public static void main(String[] args) throws InterruptedException {
        for (int i = 0; i < 1000; i++) {
            System.out.println("fib(" + (30 + i % 10) + ") = " + fibonacci(30 + i % 10));
        }
        Thread.sleep(5000); // 采样期间仍然挂起一段时间
    }
}


#!/bin/bash

# ========== 配置 ==========
JAVA_FILE="my_test.java"
JAVA_CLASS="my_test"
ASYNC_PROFILER_DIR="$HOME/async-profiler"
PROFILER_BIN="$ASYNC_PROFILER_DIR/build/bin/asprof"
DURATION=10                      # 采样时长（秒）
OUTPUT_DIR="./AP"
# ===========================

mkdir -p "$OUTPUT_DIR"

# 编译 Java 文件
echo "🔧 编译 $JAVA_FILE ..."
javac "$JAVA_FILE"
if [ $? -ne 0 ]; then
  echo "❌ 编译失败！"
  exit 1
fi

# 运行 Java 程序，后台执行
echo "▶️ 启动 Java 程序 $JAVA_CLASS ..."
java "$JAVA_CLASS" &
JAVA_PID=$!
echo "程序 PID: $JAVA_PID"

# 等待程序启动稳定（根据需要调整）
sleep 2

# 运行 Async Profiler 采样
echo "🎯 使用 Async Profiler 采样 PID=$JAVA_PID，持续 $DURATION 秒 ..."
"$PROFILER_BIN" -d "$DURATION" -o collapsed -f "$OUTPUT_DIR/collapsed.txt" "$JAVA_PID"

echo "✅ 采样完成，火焰图数据保存在 $OUTPUT_DIR/collapsed.txt"

# 等待 Java 程序退出（可选）
# wait $JAVA_PID

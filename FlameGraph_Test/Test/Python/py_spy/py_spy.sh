#!/bin/bash

# ========== 🧩 配置区域 ==========
TARGET_CMD="python3 my_test.py"  # 要运行的 Python 脚本命令
DURATION=10                      # 采样持续时间（秒）
SAMPLE_RATE=100                 # 每秒采样次数（推荐 100）
PY_SPY_DIR="./py_spy"
OUTPUT_COLLAPSED="$PY_SPY_DIR/pyspy_collapsed.txt"  # 输出 collapsed.txt 文件名
# ==================================

# === 启动目标程序（后台运行）===
$TARGET_CMD &
TARGET_PID=$!

echo "👉 启动目标程序，PID = $TARGET_PID"
sleep 1

# === 使用 py-spy 采样 ===
echo "🎯 使用 py-spy 采样 PID=$TARGET_PID，持续 $DURATION 秒..."
sudo env "PATH=$PATH" py-spy record \
    --pid $TARGET_PID \
    --duration $DURATION \
    --rate $SAMPLE_RATE \
    --format raw \
    --output "$OUTPUT_COLLAPSED"

# === 等待目标程序退出 ===
# wait $TARGET_PID

echo "✅ 采样完成，已生成 collapsed.txt：$OUTPUT_COLLAPSED"

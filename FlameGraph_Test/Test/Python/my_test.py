import time

def two_num_sum(nums, target):
    num_dict = {}
    for i, num in enumerate(nums):
        complement = target - num
        if complement in num_dict:
            return [[num_dict[complement], i]]
        num_dict[num] = i
    return []

def run_test_case(nums, target):
    result = two_num_sum(nums, target)
    time.sleep(0.2)  # 模拟处理延迟
    return result

def run_all_tests():
    test_cases = [
        ([2, 7, 11, 15], 9),
        ([3, 2, 4], 6),
        ([3, 3], 6),
        ([1, 2, 3, 4, 5], 9),
        ([-1, -2, -3, -4], -7)
    ]
    for _ in range(1000):  # 循环运行足够长时间，适配采样窗口
        for nums, target in test_cases:
            run_test_case(nums, target)

if __name__ == "__main__":
    run_all_tests()

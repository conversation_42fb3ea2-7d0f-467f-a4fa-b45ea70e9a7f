java;read;check_events_[k];hypercall_page_[k] 1
java;start_thread;java_start;GCTaskThread::run;ScavengeRootsTask::do_it;ClassLoaderDataGraph::oops_do;ClassLoaderData::oops_do;PSScavengeKlassClosure::do_klass 1
java;start_thread;java_start;GCTaskThread::run;StealTask::do_it;PSPromotionManager::drain_stacks_depth;oopDesc* PSPromotionManager::copy_to_survivor_space<false>;InstanceKlass::oop_push_contents 1
java;start_thread;java_start;GCTaskThread::run;StealTask::do_it;ParallelTaskTerminator::offer_termination 5
java;start_thread;java_start;GCTaskThread::run;StealTask::do_it;SpinPause 7
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/buffer/AbstractByteBufAllocator:.directBuffer 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/buffer/AbstractReferenceCountedByteBuf:.release 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;java/util/concurrent/ConcurrentHashMap:.get 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/mozilla/javascript/Context:.getWrapFactory 2
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived 3
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/mozilla/javascript/ScriptableObject:.getParentScope 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/mozilla/javascript/WrapFactory:.wrap;java/util/HashMap:.get 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/mozilla/javascript/WrapFactory:.wrapAsJavaObject 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/mozilla/javascript/WrapFactory:.wrapAsJavaObject;java/util/HashMap:.get 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/ScriptRuntime:.getObjectProp 2
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/ScriptRuntime:.getObjectProp;org/mozilla/javascript/ScriptableObject$RelinkedSlot:.getValue 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/ScriptRuntime:.getObjectProp;vtable chunks 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/ScriptRuntime:.name;org/mozilla/javascript/IdScriptableObject:.get 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/ScriptRuntime:.nameOrFunction;org/mozilla/javascript/ScriptableObject$Slot:.getValue 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/ScriptRuntime:.getPropFunctionAndThis 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/IdScriptableObject:.findInstanceIdInfo 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/IdScriptableObject:.has 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/IdScriptableObject:.has;org/mozilla/javascript/ScriptableObject:.getSlot 2
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/IdScriptableObject:.put;org/mozilla/javascript/ScriptableObject:.getSlot 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/IdScriptableObject:.setAttributes 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/MemberBox:.invoke 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/NativeJavaMethod:.call 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/NativeJavaMethod:.call;org/mozilla/javascript/WrapFactory:.wrap 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/NativeJavaMethod:.findFunction 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/NativeJavaObject:.get 2
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/ScriptRuntime:.createFunctionActivation;org/mozilla/javascript/IdScriptableObject:.get;org/mozilla/javascript/ScriptableObject:.getSlot 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/ScriptRuntime:.createFunctionActivation;org/mozilla/javascript/IdScriptableObject:.put;org/mozilla/javascript/ScriptableObject:.getSlot;org/mozilla/javascript/ScriptableObject:.createSlot 2
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/ScriptRuntime:.createFunctionActivation;org/mozilla/javascript/IdScriptableObject:.setAttributes 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/ScriptRuntime:.createFunctionActivation;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/ScriptRuntime:.getObjectProp;org/mozilla/javascript/ScriptableObject$Slot:.getValue 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/ScriptRuntime:.getPropFunctionAndThis;org/mozilla/javascript/NativeJavaObject:.get;java/util/HashMap:.get 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/ScriptRuntime:.newObject;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/ScriptRuntime:.newObject;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;jint_disjoint_arraycopy 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/ScriptRuntime:.newObject;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/IdScriptableObject:.get 2
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/ScriptRuntime:.newObject;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/IdScriptableObject:.has 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/ScriptRuntime:.newObject;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/ScriptRuntime:.createFunctionActivation 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/ScriptRuntime:.newObject;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/ScriptRuntime:.createFunctionActivation;org/mozilla/javascript/IdScriptableObject:.put;org/mozilla/javascript/ScriptableObject:.getSlot;org/mozilla/javascript/ScriptableObject:.createSlot 2
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/ScriptRuntime:.newObject;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/ScriptRuntime:.createFunctionActivation;org/mozilla/javascript/IdScriptableObject:.setAttributes 2
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/ScriptRuntime:.newObject;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/ScriptRuntime:.getObjectProp;org/mozilla/javascript/IdScriptableObject:.get;org/mozilla/javascript/ScriptableObject:.getSlot 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/ScriptRuntime:.newObject;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/ScriptRuntime:.nameOrFunction;org/mozilla/javascript/IdScriptableObject:.get;org/mozilla/javascript/ScriptableObject$RelinkedSlot:.getValue 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/ScriptRuntime:.newObject;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/ScriptRuntime:.setObjectProp;org/mozilla/javascript/IdScriptableObject:.findInstanceIdInfo 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/ScriptRuntime:.newObject;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/ScriptRuntime:.setObjectProp;org/mozilla/javascript/IdScriptableObject:.put 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/ScriptRuntime:.newObject;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/ScriptRuntime:.setObjectProp;org/mozilla/javascript/IdScriptableObject:.put;org/mozilla/javascript/ScriptableObject:.getSlot;org/mozilla/javascript/ScriptableObject:.createSlot 3
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/ScriptRuntime:.newObject;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/ScriptRuntime:.setObjectProp;org/mozilla/javascript/ScriptableObject:.getSlot 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/ScriptRuntime:.newObject;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/ScriptRuntime:.setObjectProp;vtable chunks 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/ScriptRuntime:.newObject;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/optimizer/OptRuntime:.call2;org/mozilla/javascript/NativeFunction:.initScriptFunction 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/ScriptRuntime:.newObject;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/optimizer/OptRuntime:.call2;org/mozilla/javascript/ScriptRuntime:.createFunctionActivation 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/ScriptRuntime:.newObject;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/optimizer/OptRuntime:.call2;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/ScriptRuntime:.createFunctionActivation 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/ScriptRuntime:.newObject;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/optimizer/OptRuntime:.call2;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/ScriptRuntime:.createFunctionActivation;org/mozilla/javascript/IdScriptableObject:.get 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/ScriptRuntime:.newObject;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/optimizer/OptRuntime:.call2;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/ScriptRuntime:.setObjectProp;org/mozilla/javascript/IdScriptableObject:.has 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/ScriptRuntime:.newObject;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/optimizer/OptRuntime:.call2;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/ScriptRuntime:.setObjectProp;org/mozilla/javascript/IdScriptableObject:.put 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/ScriptRuntime:.newObject;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/optimizer/OptRuntime:.call2;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/ScriptRuntime:.setObjectProp;org/mozilla/javascript/IdScriptableObject:.put;org/mozilla/javascript/ScriptableObject:.getSlot;org/mozilla/javascript/ScriptableObject:.createSlot 6
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/ScriptRuntime:.newObject;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/optimizer/OptRuntime:.call2;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/ScriptableObject:.getParentScope 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/ScriptRuntime:.setObjectProp;org/mozilla/javascript/IdScriptableObject:.has 4
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/ScriptRuntime:.setObjectProp;org/mozilla/javascript/IdScriptableObject:.has;org/mozilla/javascript/ScriptableObject:.getSlot 5
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/ScriptRuntime:.setObjectProp;org/mozilla/javascript/IdScriptableObject:.put 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/ScriptRuntime:.setObjectProp;org/mozilla/javascript/IdScriptableObject:.put;org/mozilla/javascript/ScriptableObject:.getSlot 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/ScriptRuntime:.setObjectProp;org/mozilla/javascript/IdScriptableObject:.put;org/mozilla/javascript/ScriptableObject:.getSlot;org/mozilla/javascript/ScriptableObject:.createSlot 6
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/ScriptableObject:.getPrototype 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/ScriptableObject:.getParentScope 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/TopLevel:.getBuiltinPrototype 2
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/optimizer/OptRuntime:.call2 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/optimizer/OptRuntime:.call2;org/mozilla/javascript/ScriptRuntime:.name;org/mozilla/javascript/ScriptRuntime:.nameOrFunction;vtable chunks 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/optimizer/OptRuntime:.call2;org/mozilla/javascript/ScriptRuntime:.setObjectProp;org/mozilla/javascript/IdScriptableObject:.has;org/mozilla/javascript/ScriptableObject:.getSlot 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/optimizer/OptRuntime:.call2;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/ScriptRuntime:.createFunctionActivation 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/optimizer/OptRuntime:.call2;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/ScriptRuntime:.createFunctionActivation;org/mozilla/javascript/IdScriptableObject:.setAttributes 2
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/optimizer/OptRuntime:.call2;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/ScriptRuntime:.createFunctionActivation;org/mozilla/javascript/TopLevel:.getBuiltinPrototype 2
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/optimizer/OptRuntime:.call2;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/ScriptRuntime:.setObjectProp;org/mozilla/javascript/IdScriptableObject:.put;org/mozilla/javascript/ScriptableObject:.getSlot;org/mozilla/javascript/ScriptableObject:.createSlot 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vhello_js_1:.call;org/mozilla/javascript/ScriptRuntime:.indexFromString 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vhello_js_1:.call;org/mozilla/javascript/ScriptRuntime:.setObjectElem;org/mozilla/javascript/ScriptRuntime:.indexFromString 2
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vhello_js_1:.call;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0 2
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vhello_js_1:.call;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/NativeJavaMethod:.call;org/mozilla/javascript/MemberBox:.invoke 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vhello_js_1:.call;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/NativeJavaMethod:.call;org/mozilla/javascript/MemberBox:.invoke;io/netty/handler/codec/http/DefaultHttpHeaders:.set 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vhello_js_1:.call;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/NativeJavaMethod:.call;org/mozilla/javascript/MemberBox:.invoke;sun/reflect/DelegatingMethodAccessorImpl:.invoke 3
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vhello_js_1:.call;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/NativeJavaMethod:.call;org/mozilla/javascript/MemberBox:.invoke;sun/reflect/DelegatingMethodAccessorImpl:.invoke;io/netty/channel/AbstractChannelHandlerContext:.write;io/netty/channel/AbstractChannelHandlerContext:.write 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vhello_js_1:.call;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/NativeJavaMethod:.call;org/mozilla/javascript/MemberBox:.invoke;sun/reflect/DelegatingMethodAccessorImpl:.invoke;io/netty/channel/AbstractChannelHandlerContext:.write;io/netty/channel/AbstractChannelHandlerContext:.write;org/vertx/java/core/http/impl/VertxHttpHandler:.write 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vhello_js_1:.call;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/NativeJavaMethod:.call;org/mozilla/javascript/MemberBox:.invoke;sun/reflect/DelegatingMethodAccessorImpl:.invoke;io/netty/channel/AbstractChannelHandlerContext:.write;io/netty/channel/AbstractChannelHandlerContext:.write;org/vertx/java/core/http/impl/VertxHttpHandler:.write;io/netty/channel/AbstractChannelHandlerContext:.write;io/netty/handler/codec/MessageToMessageEncoder:.write 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vhello_js_1:.call;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/NativeJavaMethod:.call;org/mozilla/javascript/MemberBox:.invoke;sun/reflect/DelegatingMethodAccessorImpl:.invoke;io/netty/channel/AbstractChannelHandlerContext:.write;io/netty/channel/AbstractChannelHandlerContext:.write;org/vertx/java/core/http/impl/VertxHttpHandler:.write;io/netty/channel/AbstractChannelHandlerContext:.write;io/netty/handler/codec/MessageToMessageEncoder:.write;io/netty/buffer/AbstractByteBuf:.writeBytes 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vhello_js_1:.call;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/NativeJavaMethod:.call;org/mozilla/javascript/MemberBox:.invoke;sun/reflect/DelegatingMethodAccessorImpl:.invoke;io/netty/channel/AbstractChannelHandlerContext:.write;io/netty/channel/AbstractChannelHandlerContext:.write;org/vertx/java/core/http/impl/VertxHttpHandler:.write;io/netty/channel/AbstractChannelHandlerContext:.write;io/netty/handler/codec/MessageToMessageEncoder:.write;io/netty/handler/codec/http/HttpObjectEncoder:.encode;io/netty/buffer/AbstractByteBuf:.writeBytes 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vhello_js_1:.call;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/NativeJavaMethod:.call;org/mozilla/javascript/MemberBox:.invoke;sun/reflect/DelegatingMethodAccessorImpl:.invoke;io/netty/channel/AbstractChannelHandlerContext:.write;io/netty/channel/AbstractChannelHandlerContext:.write;org/vertx/java/core/http/impl/VertxHttpHandler:.write;io/netty/channel/AbstractChannelHandlerContext:.write;io/netty/handler/codec/MessageToMessageEncoder:.write;io/netty/handler/codec/http/HttpObjectEncoder:.encode;io/netty/buffer/AbstractByteBufAllocator:.directBuffer 2
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vhello_js_1:.call;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/NativeJavaMethod:.call;org/mozilla/javascript/MemberBox:.invoke;sun/reflect/DelegatingMethodAccessorImpl:.invoke;io/netty/channel/AbstractChannelHandlerContext:.write;io/netty/channel/AbstractChannelHandlerContext:.write;org/vertx/java/core/http/impl/VertxHttpHandler:.write;io/netty/channel/AbstractChannelHandlerContext:.write;io/netty/handler/codec/MessageToMessageEncoder:.write;io/netty/handler/codec/http/HttpObjectEncoder:.encode;io/netty/buffer/AbstractByteBufAllocator:.directBuffer;io/netty/util/concurrent/FastThreadLocal:.get 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vhello_js_1:.call;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/NativeJavaMethod:.call;org/mozilla/javascript/MemberBox:.invoke;sun/reflect/DelegatingMethodAccessorImpl:.invoke;io/netty/channel/AbstractChannelHandlerContext:.write;io/netty/channel/AbstractChannelHandlerContext:.write;org/vertx/java/core/http/impl/VertxHttpHandler:.write;io/netty/channel/AbstractChannelHandlerContext:.write;io/netty/handler/codec/MessageToMessageEncoder:.write;io/netty/handler/codec/http/HttpObjectEncoder:.encode;java/util/ArrayList:.add 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vhello_js_1:.call;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/NativeJavaMethod:.call;org/mozilla/javascript/MemberBox:.invoke;sun/reflect/DelegatingMethodAccessorImpl:.invoke;io/netty/channel/AbstractChannelHandlerContext:.write;io/netty/channel/AbstractChannelHandlerContext:.write;org/vertx/java/core/http/impl/VertxHttpHandler:.write;io/netty/channel/AbstractChannelHandlerContext:.write;io/netty/handler/codec/MessageToMessageEncoder:.write;io/netty/util/internal/RecyclableArrayList:.newInstance;io/netty/util/concurrent/FastThreadLocal:.get 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vhello_js_1:.call;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/NativeJavaMethod:.call;org/mozilla/javascript/MemberBox:.invoke;sun/reflect/DelegatingMethodAccessorImpl:.invoke;io/netty/channel/AbstractChannelHandlerContext:.write;io/netty/channel/AbstractChannelHandlerContext:.write;org/vertx/java/core/http/impl/VertxHttpHandler:.write;io/netty/channel/AbstractChannelHandlerContext:.write;io/netty/handler/codec/MessageToMessageEncoder:.write;java/util/ArrayList:.ensureExplicitCapacity 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vhello_js_1:.call;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/NativeJavaMethod:.call;org/mozilla/javascript/MemberBox:.invoke;sun/reflect/DelegatingMethodAccessorImpl:.invoke;io/netty/channel/AbstractChannelHandlerContext:.write;io/netty/channel/AbstractChannelHandlerContext:.write;org/vertx/java/core/http/impl/VertxHttpHandler:.write;io/netty/channel/AbstractChannelHandlerContext:.write;io/netty/handler/codec/MessageToMessageEncoder:.write;vtable chunks 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vhello_js_1:.call;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/NativeJavaMethod:.call;org/mozilla/javascript/MemberBox:.invoke;sun/reflect/DelegatingMethodAccessorImpl:.invoke;io/netty/channel/AbstractChannelHandlerContext:.write;org/vertx/java/core/http/impl/VertxHttpHandler:.write 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vhello_js_1:.call;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/NativeJavaMethod:.call;org/mozilla/javascript/MemberBox:.invoke;sun/reflect/DelegatingMethodAccessorImpl:.invoke;io/netty/handler/codec/http/DefaultHttpHeaders:.add0 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vhello_js_1:.call;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/NativeJavaMethod:.call;org/mozilla/javascript/MemberBox:.invoke;sun/reflect/DelegatingMethodAccessorImpl:.invoke;io/netty/handler/codec/http/DefaultHttpHeaders:.set 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vhello_js_1:.call;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/NativeJavaMethod:.call;org/mozilla/javascript/MemberBox:.invoke;sun/reflect/DelegatingMethodAccessorImpl:.invoke;sun/nio/cs/UTF_8$Encoder:.<init>;jbyte_disjoint_arraycopy 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/gen/file__home_bgregg_testtest_vhello_js_1:.call;org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;org/mozilla/javascript/ScriptRuntime:.name;org/mozilla/javascript/ScriptRuntime:.nameOrFunction;org/mozilla/javascript/IdScriptableObject:.get;org/mozilla/javascript/ScriptableObject$RelinkedSlot:.getValue 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/handler/codec/http/HttpObjectDecoder:.decode;io/netty/buffer/AbstractByteBuf:.forEachByteAsc0;io/netty/util/internal/AppendableCharSequence:.append 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/handler/codec/http/HttpObjectDecoder:.decode;io/netty/handler/codec/http/HttpHeaders:.isTransferEncodingChunked 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/handler/codec/http/HttpObjectDecoder:.decode;io/netty/handler/codec/http/HttpObjectDecoder:.findWhitespace 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/handler/codec/http/HttpObjectDecoder:.decode;io/netty/handler/codec/http/HttpObjectDecoder:.readHeaders 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/handler/codec/http/HttpObjectDecoder:.decode;io/netty/handler/codec/http/HttpObjectDecoder:.readHeaders;io/netty/buffer/AbstractByteBuf:.forEachByteAsc0 2
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/handler/codec/http/HttpObjectDecoder:.decode;io/netty/handler/codec/http/HttpObjectDecoder:.readHeaders;io/netty/handler/codec/http/HttpHeaders:.hash 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/handler/codec/http/HttpObjectDecoder:.decode;io/netty/handler/codec/http/HttpObjectDecoder:.readHeaders;io/netty/handler/codec/http/HttpObjectDecoder:.splitHeader 5
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/handler/codec/http/HttpObjectDecoder:.decode;io/netty/handler/codec/http/HttpObjectDecoder:.readHeaders;java/util/Arrays:.fill 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelDuplexHandler:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelOutboundHandlerAdapter:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/DefaultChannelPipeline$HeadContext:.flush;io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;io/netty/channel/nio/AbstractNioByteChannel:.doWrite 2
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelDuplexHandler:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelOutboundHandlerAdapter:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/DefaultChannelPipeline$HeadContext:.flush;io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;io/netty/channel/nio/AbstractNioByteChannel:.doWrite;io/netty/buffer/AbstractReferenceCountedByteBuf:.release 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelDuplexHandler:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelOutboundHandlerAdapter:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/DefaultChannelPipeline$HeadContext:.flush;io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;io/netty/channel/nio/AbstractNioByteChannel:.doWrite;io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;io/netty/buffer/PooledByteBuf:.internalNioBuffer 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelDuplexHandler:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelOutboundHandlerAdapter:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/DefaultChannelPipeline$HeadContext:.flush;io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;io/netty/channel/nio/AbstractNioByteChannel:.doWrite;io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;sun/nio/ch/NativeThread:.current 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelDuplexHandler:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelOutboundHandlerAdapter:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/DefaultChannelPipeline$HeadContext:.flush;io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;io/netty/channel/nio/AbstractNioByteChannel:.doWrite;io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;sun/nio/ch/SocketChannelImpl:.write;sun/nio/ch/FileDispatcherImpl:.write0 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelDuplexHandler:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelOutboundHandlerAdapter:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/DefaultChannelPipeline$HeadContext:.flush;io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;io/netty/channel/nio/AbstractNioByteChannel:.doWrite;io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;sun/nio/ch/SocketChannelImpl:.write;sun/nio/ch/FileDispatcherImpl:.write0;  3
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelDuplexHandler:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelOutboundHandlerAdapter:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/DefaultChannelPipeline$HeadContext:.flush;io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;io/netty/channel/nio/AbstractNioByteChannel:.doWrite;io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;sun/nio/ch/SocketChannelImpl:.write;sun/nio/ch/FileDispatcherImpl:.write0;Java_sun_nio_ch_FileDispatcherImpl_write0 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelDuplexHandler:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelOutboundHandlerAdapter:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/DefaultChannelPipeline$HeadContext:.flush;io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;io/netty/channel/nio/AbstractNioByteChannel:.doWrite;io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;sun/nio/ch/SocketChannelImpl:.write;sun/nio/ch/FileDispatcherImpl:.write0;write 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelDuplexHandler:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelOutboundHandlerAdapter:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/DefaultChannelPipeline$HeadContext:.flush;io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;io/netty/channel/nio/AbstractNioByteChannel:.doWrite;io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;sun/nio/ch/SocketChannelImpl:.write;sun/nio/ch/FileDispatcherImpl:.write0;write;sys_write_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelDuplexHandler:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelOutboundHandlerAdapter:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/DefaultChannelPipeline$HeadContext:.flush;io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;io/netty/channel/nio/AbstractNioByteChannel:.doWrite;io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;sun/nio/ch/SocketChannelImpl:.write;sun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath_[k];sys_write_[k];fget_light_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelDuplexHandler:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelOutboundHandlerAdapter:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/DefaultChannelPipeline$HeadContext:.flush;io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;io/netty/channel/nio/AbstractNioByteChannel:.doWrite;io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;sun/nio/ch/SocketChannelImpl:.write;sun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];__srcu_read_lock_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelDuplexHandler:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelOutboundHandlerAdapter:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/DefaultChannelPipeline$HeadContext:.flush;io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;io/netty/channel/nio/AbstractNioByteChannel:.doWrite;io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;sun/nio/ch/SocketChannelImpl:.write;sun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];do_sync_write_[k];sock_aio_write_[k];do_sock_write.isra.10_[k];inet_sendmsg_[k];__tcp_push_pending_frames_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelDuplexHandler:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelOutboundHandlerAdapter:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/DefaultChannelPipeline$HeadContext:.flush;io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;io/netty/channel/nio/AbstractNioByteChannel:.doWrite;io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;sun/nio/ch/SocketChannelImpl:.write;sun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];do_sync_write_[k];sock_aio_write_[k];do_sock_write.isra.10_[k];inet_sendmsg_[k];tcp_sendmsg_[k] 2
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelDuplexHandler:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelOutboundHandlerAdapter:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/DefaultChannelPipeline$HeadContext:.flush;io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;io/netty/channel/nio/AbstractNioByteChannel:.doWrite;io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;sun/nio/ch/SocketChannelImpl:.write;sun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];do_sync_write_[k];sock_aio_write_[k];do_sock_write.isra.10_[k];inet_sendmsg_[k];tcp_sendmsg_[k];__tcp_push_pending_frames_[k];tcp_write_xmit_[k];ktime_get_real_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelDuplexHandler:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelOutboundHandlerAdapter:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/DefaultChannelPipeline$HeadContext:.flush;io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;io/netty/channel/nio/AbstractNioByteChannel:.doWrite;io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;sun/nio/ch/SocketChannelImpl:.write;sun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];do_sync_write_[k];sock_aio_write_[k];do_sock_write.isra.10_[k];inet_sendmsg_[k];tcp_sendmsg_[k];__tcp_push_pending_frames_[k];tcp_write_xmit_[k];skb_clone_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelDuplexHandler:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelOutboundHandlerAdapter:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/DefaultChannelPipeline$HeadContext:.flush;io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;io/netty/channel/nio/AbstractNioByteChannel:.doWrite;io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;sun/nio/ch/SocketChannelImpl:.write;sun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];do_sync_write_[k];sock_aio_write_[k];do_sock_write.isra.10_[k];inet_sendmsg_[k];tcp_sendmsg_[k];__tcp_push_pending_frames_[k];tcp_write_xmit_[k];tcp_set_skb_tso_segs_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelDuplexHandler:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelOutboundHandlerAdapter:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/DefaultChannelPipeline$HeadContext:.flush;io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;io/netty/channel/nio/AbstractNioByteChannel:.doWrite;io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;sun/nio/ch/SocketChannelImpl:.write;sun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];do_sync_write_[k];sock_aio_write_[k];do_sock_write.isra.10_[k];inet_sendmsg_[k];tcp_sendmsg_[k];__tcp_push_pending_frames_[k];tcp_write_xmit_[k];tcp_transmit_skb_[k] 2
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelDuplexHandler:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelOutboundHandlerAdapter:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/DefaultChannelPipeline$HeadContext:.flush;io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;io/netty/channel/nio/AbstractNioByteChannel:.doWrite;io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;sun/nio/ch/SocketChannelImpl:.write;sun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];do_sync_write_[k];sock_aio_write_[k];do_sock_write.isra.10_[k];inet_sendmsg_[k];tcp_sendmsg_[k];__tcp_push_pending_frames_[k];tcp_write_xmit_[k];tcp_transmit_skb_[k];ip_queue_xmit_[k];ip_local_out_[k];ip_output_[k];ip_finish_output_[k];dev_hard_start_xmit_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelDuplexHandler:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelOutboundHandlerAdapter:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/DefaultChannelPipeline$HeadContext:.flush;io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;io/netty/channel/nio/AbstractNioByteChannel:.doWrite;io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;sun/nio/ch/SocketChannelImpl:.write;sun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];do_sync_write_[k];sock_aio_write_[k];do_sock_write.isra.10_[k];inet_sendmsg_[k];tcp_sendmsg_[k];__tcp_push_pending_frames_[k];tcp_write_xmit_[k];tcp_transmit_skb_[k];ip_queue_xmit_[k];ip_local_out_[k];ip_output_[k];ip_finish_output_[k];dev_pick_tx_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelDuplexHandler:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelOutboundHandlerAdapter:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/DefaultChannelPipeline$HeadContext:.flush;io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;io/netty/channel/nio/AbstractNioByteChannel:.doWrite;io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;sun/nio/ch/SocketChannelImpl:.write;sun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];do_sync_write_[k];sock_aio_write_[k];do_sock_write.isra.10_[k];inet_sendmsg_[k];tcp_sendmsg_[k];__tcp_push_pending_frames_[k];tcp_write_xmit_[k];tcp_transmit_skb_[k];ip_queue_xmit_[k];ip_local_out_[k];ip_output_[k];ip_finish_output_[k];dev_queue_xmit_[k];dev_hard_start_xmit_[k];dev_queue_xmit_nit_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelDuplexHandler:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelOutboundHandlerAdapter:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/DefaultChannelPipeline$HeadContext:.flush;io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;io/netty/channel/nio/AbstractNioByteChannel:.doWrite;io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;sun/nio/ch/SocketChannelImpl:.write;sun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];do_sync_write_[k];sock_aio_write_[k];do_sock_write.isra.10_[k];inet_sendmsg_[k];tcp_sendmsg_[k];__tcp_push_pending_frames_[k];tcp_write_xmit_[k];tcp_transmit_skb_[k];ip_queue_xmit_[k];ip_local_out_[k];ip_output_[k];ip_finish_output_[k];dev_queue_xmit_[k];dev_hard_start_xmit_[k];loopback_xmit_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelDuplexHandler:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelOutboundHandlerAdapter:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/DefaultChannelPipeline$HeadContext:.flush;io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;io/netty/channel/nio/AbstractNioByteChannel:.doWrite;io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;sun/nio/ch/SocketChannelImpl:.write;sun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];do_sync_write_[k];sock_aio_write_[k];do_sock_write.isra.10_[k];inet_sendmsg_[k];tcp_sendmsg_[k];__tcp_push_pending_frames_[k];tcp_write_xmit_[k];tcp_transmit_skb_[k];ip_queue_xmit_[k];ip_local_out_[k];ip_output_[k];ip_finish_output_[k];dev_queue_xmit_[k];dev_hard_start_xmit_[k];loopback_xmit_[k];netif_rx_[k];netif_rx.part.82_[k];xen_restore_fl_direct_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelDuplexHandler:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelOutboundHandlerAdapter:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/DefaultChannelPipeline$HeadContext:.flush;io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;io/netty/channel/nio/AbstractNioByteChannel:.doWrite;io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;sun/nio/ch/SocketChannelImpl:.write;sun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];do_sync_write_[k];sock_aio_write_[k];do_sock_write.isra.10_[k];inet_sendmsg_[k];tcp_sendmsg_[k];__tcp_push_pending_frames_[k];tcp_write_xmit_[k];tcp_transmit_skb_[k];ip_queue_xmit_[k];ip_local_out_[k];ip_output_[k];ip_finish_output_[k];dev_queue_xmit_[k];dev_hard_start_xmit_[k];loopback_xmit_[k];netif_rx_[k];netif_rx.part.82_[k];xen_restore_fl_direct_end_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelDuplexHandler:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelOutboundHandlerAdapter:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/DefaultChannelPipeline$HeadContext:.flush;io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;io/netty/channel/nio/AbstractNioByteChannel:.doWrite;io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;sun/nio/ch/SocketChannelImpl:.write;sun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];do_sync_write_[k];sock_aio_write_[k];do_sock_write.isra.10_[k];inet_sendmsg_[k];tcp_sendmsg_[k];__tcp_push_pending_frames_[k];tcp_write_xmit_[k];tcp_transmit_skb_[k];ip_queue_xmit_[k];ip_local_out_[k];ip_output_[k];ip_finish_output_[k];dev_queue_xmit_[k];local_bh_enable_[k];do_softirq_[k];call_softirq_[k];__do_softirq_[k];net_rx_action_[k];dma_issue_pending_all_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelDuplexHandler:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelOutboundHandlerAdapter:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/DefaultChannelPipeline$HeadContext:.flush;io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;io/netty/channel/nio/AbstractNioByteChannel:.doWrite;io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;sun/nio/ch/SocketChannelImpl:.write;sun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];do_sync_write_[k];sock_aio_write_[k];do_sock_write.isra.10_[k];inet_sendmsg_[k];tcp_sendmsg_[k];__tcp_push_pending_frames_[k];tcp_write_xmit_[k];tcp_transmit_skb_[k];ip_queue_xmit_[k];ip_local_out_[k];ip_output_[k];ip_finish_output_[k];dev_queue_xmit_[k];local_bh_enable_[k];do_softirq_[k];call_softirq_[k];__do_softirq_[k];net_rx_action_[k];process_backlog_[k];__netif_receive_skb_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelDuplexHandler:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelOutboundHandlerAdapter:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/DefaultChannelPipeline$HeadContext:.flush;io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;io/netty/channel/nio/AbstractNioByteChannel:.doWrite;io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;sun/nio/ch/SocketChannelImpl:.write;sun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];do_sync_write_[k];sock_aio_write_[k];do_sock_write.isra.10_[k];inet_sendmsg_[k];tcp_sendmsg_[k];__tcp_push_pending_frames_[k];tcp_write_xmit_[k];tcp_transmit_skb_[k];ip_queue_xmit_[k];ip_local_out_[k];ip_output_[k];ip_finish_output_[k];dev_queue_xmit_[k];local_bh_enable_[k];do_softirq_[k];call_softirq_[k];__do_softirq_[k];net_rx_action_[k];process_backlog_[k];__netif_receive_skb_[k];ip_rcv_[k];ip_rcv_finish_[k];ip_local_deliver_[k];ip_local_deliver_finish_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelDuplexHandler:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelOutboundHandlerAdapter:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/DefaultChannelPipeline$HeadContext:.flush;io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;io/netty/channel/nio/AbstractNioByteChannel:.doWrite;io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;sun/nio/ch/SocketChannelImpl:.write;sun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];do_sync_write_[k];sock_aio_write_[k];do_sock_write.isra.10_[k];inet_sendmsg_[k];tcp_sendmsg_[k];__tcp_push_pending_frames_[k];tcp_write_xmit_[k];tcp_transmit_skb_[k];ip_queue_xmit_[k];ip_local_out_[k];ip_output_[k];ip_finish_output_[k];dev_queue_xmit_[k];local_bh_enable_[k];do_softirq_[k];call_softirq_[k];__do_softirq_[k];net_rx_action_[k];process_backlog_[k];__netif_receive_skb_[k];ip_rcv_[k];ip_rcv_finish_[k];ip_local_deliver_[k];ip_local_deliver_finish_[k];tcp_v4_rcv_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelDuplexHandler:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelOutboundHandlerAdapter:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/DefaultChannelPipeline$HeadContext:.flush;io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;io/netty/channel/nio/AbstractNioByteChannel:.doWrite;io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;sun/nio/ch/SocketChannelImpl:.write;sun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];do_sync_write_[k];sock_aio_write_[k];do_sock_write.isra.10_[k];inet_sendmsg_[k];tcp_sendmsg_[k];__tcp_push_pending_frames_[k];tcp_write_xmit_[k];tcp_transmit_skb_[k];ip_queue_xmit_[k];ip_local_out_[k];ip_output_[k];ip_finish_output_[k];dev_queue_xmit_[k];local_bh_enable_[k];do_softirq_[k];call_softirq_[k];__do_softirq_[k];net_rx_action_[k];process_backlog_[k];__netif_receive_skb_[k];ip_rcv_[k];ip_rcv_finish_[k];ip_local_deliver_[k];ip_local_deliver_finish_[k];tcp_v4_rcv_[k];__inet_lookup_established_[k] 3
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelDuplexHandler:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelOutboundHandlerAdapter:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/DefaultChannelPipeline$HeadContext:.flush;io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;io/netty/channel/nio/AbstractNioByteChannel:.doWrite;io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;sun/nio/ch/SocketChannelImpl:.write;sun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];do_sync_write_[k];sock_aio_write_[k];do_sock_write.isra.10_[k];inet_sendmsg_[k];tcp_sendmsg_[k];__tcp_push_pending_frames_[k];tcp_write_xmit_[k];tcp_transmit_skb_[k];ip_queue_xmit_[k];ip_local_out_[k];ip_output_[k];ip_finish_output_[k];dev_queue_xmit_[k];local_bh_enable_[k];do_softirq_[k];call_softirq_[k];__do_softirq_[k];net_rx_action_[k];process_backlog_[k];__netif_receive_skb_[k];ip_rcv_[k];ip_rcv_finish_[k];ip_local_deliver_[k];ip_local_deliver_finish_[k];tcp_v4_rcv_[k];tcp_v4_do_rcv_[k];tcp_event_data_recv_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelDuplexHandler:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelOutboundHandlerAdapter:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/DefaultChannelPipeline$HeadContext:.flush;io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;io/netty/channel/nio/AbstractNioByteChannel:.doWrite;io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;sun/nio/ch/SocketChannelImpl:.write;sun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];do_sync_write_[k];sock_aio_write_[k];do_sock_write.isra.10_[k];inet_sendmsg_[k];tcp_sendmsg_[k];__tcp_push_pending_frames_[k];tcp_write_xmit_[k];tcp_transmit_skb_[k];ip_queue_xmit_[k];ip_local_out_[k];ip_output_[k];ip_finish_output_[k];dev_queue_xmit_[k];local_bh_enable_[k];do_softirq_[k];call_softirq_[k];__do_softirq_[k];net_rx_action_[k];process_backlog_[k];__netif_receive_skb_[k];ip_rcv_[k];ip_rcv_finish_[k];ip_local_deliver_[k];ip_local_deliver_finish_[k];tcp_v4_rcv_[k];tcp_v4_do_rcv_[k];tcp_rcv_established_[k];sock_def_readable_[k];__wake_up_sync_key_[k];check_events_[k];hypercall_page_[k] 19
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelDuplexHandler:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelOutboundHandlerAdapter:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/DefaultChannelPipeline$HeadContext:.flush;io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;io/netty/channel/nio/AbstractNioByteChannel:.doWrite;io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;sun/nio/ch/SocketChannelImpl:.write;sun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];do_sync_write_[k];sock_aio_write_[k];do_sock_write.isra.10_[k];inet_sendmsg_[k];tcp_sendmsg_[k];__tcp_push_pending_frames_[k];tcp_write_xmit_[k];tcp_transmit_skb_[k];ip_queue_xmit_[k];ip_local_out_[k];ip_output_[k];ip_finish_output_[k];dev_queue_xmit_[k];local_bh_enable_[k];do_softirq_[k];call_softirq_[k];__do_softirq_[k];net_rx_action_[k];process_backlog_[k];__netif_receive_skb_[k];ip_rcv_[k];ip_rcv_finish_[k];ip_local_deliver_[k];ip_local_deliver_finish_[k];tcp_v4_rcv_[k];tcp_v4_do_rcv_[k];tcp_rcv_established_[k];tcp_ack_[k] 3
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelDuplexHandler:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelOutboundHandlerAdapter:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/DefaultChannelPipeline$HeadContext:.flush;io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;io/netty/channel/nio/AbstractNioByteChannel:.doWrite;io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;sun/nio/ch/SocketChannelImpl:.write;sun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];do_sync_write_[k];sock_aio_write_[k];do_sock_write.isra.10_[k];inet_sendmsg_[k];tcp_sendmsg_[k];__tcp_push_pending_frames_[k];tcp_write_xmit_[k];tcp_transmit_skb_[k];ip_queue_xmit_[k];ip_local_out_[k];ip_output_[k];ip_finish_output_[k];dev_queue_xmit_[k];local_bh_enable_[k];do_softirq_[k];call_softirq_[k];__do_softirq_[k];net_rx_action_[k];process_backlog_[k];__netif_receive_skb_[k];ip_rcv_[k];ip_rcv_finish_[k];ip_local_deliver_[k];ip_local_deliver_finish_[k];tcp_v4_rcv_[k];tcp_v4_do_rcv_[k];tcp_rcv_established_[k];tcp_ack_[k];tcp_clean_rtx_queue_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelDuplexHandler:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelOutboundHandlerAdapter:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/DefaultChannelPipeline$HeadContext:.flush;io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;io/netty/channel/nio/AbstractNioByteChannel:.doWrite;io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;sun/nio/ch/SocketChannelImpl:.write;sun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];do_sync_write_[k];sock_aio_write_[k];do_sock_write.isra.10_[k];inet_sendmsg_[k];tcp_sendmsg_[k];__tcp_push_pending_frames_[k];tcp_write_xmit_[k];tcp_transmit_skb_[k];ip_queue_xmit_[k];ip_local_out_[k];ip_output_[k];ip_finish_output_[k];dev_queue_xmit_[k];local_bh_enable_[k];do_softirq_[k];call_softirq_[k];__do_softirq_[k];net_rx_action_[k];process_backlog_[k];__netif_receive_skb_[k];ip_rcv_[k];ip_rcv_finish_[k];ip_local_deliver_[k];ip_local_deliver_finish_[k];tcp_v4_rcv_[k];tcp_v4_do_rcv_[k];tcp_rcv_established_[k];tcp_ack_[k];tcp_clean_rtx_queue_[k];bictcp_acked_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelDuplexHandler:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelOutboundHandlerAdapter:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/DefaultChannelPipeline$HeadContext:.flush;io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;io/netty/channel/nio/AbstractNioByteChannel:.doWrite;io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;sun/nio/ch/SocketChannelImpl:.write;sun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];do_sync_write_[k];sock_aio_write_[k];do_sock_write.isra.10_[k];inet_sendmsg_[k];tcp_sendmsg_[k];__tcp_push_pending_frames_[k];tcp_write_xmit_[k];tcp_transmit_skb_[k];ip_queue_xmit_[k];ip_local_out_[k];ip_output_[k];ip_finish_output_[k];dev_queue_xmit_[k];local_bh_enable_[k];do_softirq_[k];call_softirq_[k];__do_softirq_[k];net_rx_action_[k];process_backlog_[k];__netif_receive_skb_[k];ip_rcv_[k];ip_rcv_finish_[k];ip_local_deliver_[k];ip_local_deliver_finish_[k];tcp_v4_rcv_[k];tcp_v4_do_rcv_[k];tcp_rcv_established_[k];tcp_ack_[k];tcp_clean_rtx_queue_[k];ktime_get_real_[k];getnstimeofday_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelDuplexHandler:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelOutboundHandlerAdapter:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/DefaultChannelPipeline$HeadContext:.flush;io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;io/netty/channel/nio/AbstractNioByteChannel:.doWrite;io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;sun/nio/ch/SocketChannelImpl:.write;sun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];do_sync_write_[k];sock_aio_write_[k];do_sock_write.isra.10_[k];inet_sendmsg_[k];tcp_sendmsg_[k];__tcp_push_pending_frames_[k];tcp_write_xmit_[k];tcp_transmit_skb_[k];ip_queue_xmit_[k];ip_local_out_[k];ip_output_[k];ip_finish_output_[k];dev_queue_xmit_[k];local_bh_enable_[k];do_softirq_[k];call_softirq_[k];__do_softirq_[k];net_rx_action_[k];process_backlog_[k];__netif_receive_skb_[k];ip_rcv_[k];ip_rcv_finish_[k];ip_local_deliver_[k];ip_local_deliver_finish_[k];tcp_v4_rcv_[k];tcp_v4_do_rcv_[k];tcp_rcv_established_[k];tcp_ack_[k];tcp_clean_rtx_queue_[k];ktime_get_real_[k];getnstimeofday_[k];xen_clocksource_get_cycles_[k];xen_clocksource_read_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelDuplexHandler:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelOutboundHandlerAdapter:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/DefaultChannelPipeline$HeadContext:.flush;io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;io/netty/channel/nio/AbstractNioByteChannel:.doWrite;io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;sun/nio/ch/SocketChannelImpl:.write;sun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];do_sync_write_[k];sock_aio_write_[k];do_sock_write.isra.10_[k];inet_sendmsg_[k];tcp_sendmsg_[k];__tcp_push_pending_frames_[k];tcp_write_xmit_[k];tcp_transmit_skb_[k];ip_queue_xmit_[k];ip_local_out_[k];ip_output_[k];ip_finish_output_[k];dev_queue_xmit_[k];local_bh_enable_[k];do_softirq_[k];call_softirq_[k];__do_softirq_[k];net_rx_action_[k];process_backlog_[k];__netif_receive_skb_[k];ip_rcv_[k];ip_rcv_finish_[k];ip_local_deliver_[k];ip_local_deliver_finish_[k];tcp_v4_rcv_[k];tcp_v4_do_rcv_[k];tcp_rcv_established_[k];tcp_ack_[k];tcp_clean_rtx_queue_[k];tcp_rtt_estimator_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelDuplexHandler:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelOutboundHandlerAdapter:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/DefaultChannelPipeline$HeadContext:.flush;io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;io/netty/channel/nio/AbstractNioByteChannel:.doWrite;io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;sun/nio/ch/SocketChannelImpl:.write;sun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];do_sync_write_[k];sock_aio_write_[k];do_sock_write.isra.10_[k];inet_sendmsg_[k];tcp_sendmsg_[k];__tcp_push_pending_frames_[k];tcp_write_xmit_[k];tcp_transmit_skb_[k];ip_queue_xmit_[k];ip_local_out_[k];ip_output_[k];ip_finish_output_[k];dev_queue_xmit_[k];local_bh_enable_[k];do_softirq_[k];call_softirq_[k];__do_softirq_[k];net_rx_action_[k];process_backlog_[k];__netif_receive_skb_[k];ip_rcv_[k];ip_rcv_finish_[k];ip_local_deliver_[k];ip_local_deliver_finish_[k];tcp_v4_rcv_[k];tcp_v4_do_rcv_[k];tcp_rcv_established_[k];tcp_ack_[k];tcp_clean_rtx_queue_[k];tcp_valid_rtt_meas_[k];tcp_rtt_estimator_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelDuplexHandler:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelOutboundHandlerAdapter:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/DefaultChannelPipeline$HeadContext:.flush;io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;io/netty/channel/nio/AbstractNioByteChannel:.doWrite;io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;sun/nio/ch/SocketChannelImpl:.write;sun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];do_sync_write_[k];sock_aio_write_[k];do_sock_write.isra.10_[k];inet_sendmsg_[k];tcp_sendmsg_[k];__tcp_push_pending_frames_[k];tcp_write_xmit_[k];tcp_transmit_skb_[k];ip_queue_xmit_[k];ip_local_out_[k];ip_output_[k];ip_finish_output_[k];dev_queue_xmit_[k];local_bh_enable_[k];do_softirq_[k];call_softirq_[k];__do_softirq_[k];net_rx_action_[k];process_backlog_[k];__netif_receive_skb_[k];ip_rcv_[k];ip_rcv_finish_[k];ip_local_deliver_finish_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelDuplexHandler:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelOutboundHandlerAdapter:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/DefaultChannelPipeline$HeadContext:.flush;io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;io/netty/channel/nio/AbstractNioByteChannel:.doWrite;io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;sun/nio/ch/SocketChannelImpl:.write;sun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];do_sync_write_[k];sock_aio_write_[k];do_sock_write.isra.10_[k];inet_sendmsg_[k];tcp_sendmsg_[k];__tcp_push_pending_frames_[k];tcp_write_xmit_[k];tcp_transmit_skb_[k];ip_queue_xmit_[k];ip_local_out_[k];ip_output_[k];ip_finish_output_[k];dev_queue_xmit_[k];local_bh_enable_[k];do_softirq_[k];call_softirq_[k];rcu_bh_qs_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelDuplexHandler:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelOutboundHandlerAdapter:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/DefaultChannelPipeline$HeadContext:.flush;io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;io/netty/channel/nio/AbstractNioByteChannel:.doWrite;io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;sun/nio/ch/SocketChannelImpl:.write;sun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];do_sync_write_[k];sock_aio_write_[k];do_sock_write.isra.10_[k];inet_sendmsg_[k];tcp_sendmsg_[k];__tcp_push_pending_frames_[k];tcp_write_xmit_[k];tcp_transmit_skb_[k];ip_queue_xmit_[k];ip_local_out_[k];ip_output_[k];ip_finish_output_[k];dev_queue_xmit_[k];netif_skb_features_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelDuplexHandler:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelOutboundHandlerAdapter:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/DefaultChannelPipeline$HeadContext:.flush;io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;io/netty/channel/nio/AbstractNioByteChannel:.doWrite;io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;sun/nio/ch/SocketChannelImpl:.write;sun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];do_sync_write_[k];sock_aio_write_[k];do_sock_write.isra.10_[k];inet_sendmsg_[k];tcp_sendmsg_[k];__tcp_push_pending_frames_[k];tcp_write_xmit_[k];tcp_transmit_skb_[k];ip_queue_xmit_[k];ip_output_[k] 2
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelDuplexHandler:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelOutboundHandlerAdapter:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/DefaultChannelPipeline$HeadContext:.flush;io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;io/netty/channel/nio/AbstractNioByteChannel:.doWrite;io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;sun/nio/ch/SocketChannelImpl:.write;sun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];do_sync_write_[k];sock_aio_write_[k];do_sock_write.isra.10_[k];inet_sendmsg_[k];tcp_sendmsg_[k];__tcp_push_pending_frames_[k];tcp_write_xmit_[k];tcp_transmit_skb_[k];ktime_get_real_[k];getnstimeofday_[k];xen_clocksource_get_cycles_[k];pvclock_clocksource_read_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelDuplexHandler:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelOutboundHandlerAdapter:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/DefaultChannelPipeline$HeadContext:.flush;io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;io/netty/channel/nio/AbstractNioByteChannel:.doWrite;io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;sun/nio/ch/SocketChannelImpl:.write;sun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];do_sync_write_[k];sock_aio_write_[k];do_sock_write.isra.10_[k];inet_sendmsg_[k];tcp_sendmsg_[k];__tcp_push_pending_frames_[k];tcp_write_xmit_[k];tcp_transmit_skb_[k];ktime_get_real_[k];getnstimeofday_[k];xen_clocksource_get_cycles_[k];xen_clocksource_read_[k];pvclock_clocksource_read_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelDuplexHandler:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelOutboundHandlerAdapter:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/DefaultChannelPipeline$HeadContext:.flush;io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;io/netty/channel/nio/AbstractNioByteChannel:.doWrite;io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;sun/nio/ch/SocketChannelImpl:.write;sun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];do_sync_write_[k];sock_aio_write_[k];do_sock_write.isra.10_[k];inet_sendmsg_[k];tcp_sendmsg_[k];__tcp_push_pending_frames_[k];tcp_write_xmit_[k];tcp_transmit_skb_[k];ktime_get_real_[k];xen_clocksource_get_cycles_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelDuplexHandler:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelOutboundHandlerAdapter:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/DefaultChannelPipeline$HeadContext:.flush;io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;io/netty/channel/nio/AbstractNioByteChannel:.doWrite;io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;sun/nio/ch/SocketChannelImpl:.write;sun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];do_sync_write_[k];sock_aio_write_[k];do_sock_write.isra.10_[k];inet_sendmsg_[k];tcp_sendmsg_[k];__tcp_push_pending_frames_[k];tcp_write_xmit_[k];tcp_transmit_skb_[k];skb_dst_set_noref_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelDuplexHandler:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelOutboundHandlerAdapter:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/DefaultChannelPipeline$HeadContext:.flush;io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;io/netty/channel/nio/AbstractNioByteChannel:.doWrite;io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;sun/nio/ch/SocketChannelImpl:.write;sun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];do_sync_write_[k];sock_aio_write_[k];do_sock_write.isra.10_[k];inet_sendmsg_[k];tcp_sendmsg_[k];lock_sock_nested_[k];_raw_spin_lock_bh_[k];local_bh_disable_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelDuplexHandler:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelOutboundHandlerAdapter:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/DefaultChannelPipeline$HeadContext:.flush;io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;io/netty/channel/nio/AbstractNioByteChannel:.doWrite;io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;sun/nio/ch/SocketChannelImpl:.write;sun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];do_sync_write_[k];sock_aio_write_[k];do_sock_write.isra.10_[k];inet_sendmsg_[k];tcp_sendmsg_[k];sk_stream_alloc_skb_[k];__alloc_skb_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelDuplexHandler:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelOutboundHandlerAdapter:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/DefaultChannelPipeline$HeadContext:.flush;io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;io/netty/channel/nio/AbstractNioByteChannel:.doWrite;io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;sun/nio/ch/SocketChannelImpl:.write;sun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];do_sync_write_[k];sock_aio_write_[k];do_sock_write.isra.10_[k];inet_sendmsg_[k];tcp_sendmsg_[k];sk_stream_alloc_skb_[k];__alloc_skb_[k];__kmalloc_node_track_caller_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelDuplexHandler:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelOutboundHandlerAdapter:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/DefaultChannelPipeline$HeadContext:.flush;io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;io/netty/channel/nio/AbstractNioByteChannel:.doWrite;io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;sun/nio/ch/SocketChannelImpl:.write;sun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];do_sync_write_[k];sock_aio_write_[k];do_sock_write.isra.10_[k];inet_sendmsg_[k];tcp_sendmsg_[k];sk_stream_alloc_skb_[k];__alloc_skb_[k];__kmalloc_node_track_caller_[k];arch_local_irq_save_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelDuplexHandler:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelOutboundHandlerAdapter:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/DefaultChannelPipeline$HeadContext:.flush;io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;io/netty/channel/nio/AbstractNioByteChannel:.doWrite;io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;sun/nio/ch/SocketChannelImpl:.write;sun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];do_sync_write_[k];sock_aio_write_[k];do_sock_write.isra.10_[k];inet_sendmsg_[k];tcp_sendmsg_[k];sk_stream_alloc_skb_[k];__alloc_skb_[k];__phys_addr_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelDuplexHandler:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelOutboundHandlerAdapter:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/DefaultChannelPipeline$HeadContext:.flush;io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;io/netty/channel/nio/AbstractNioByteChannel:.doWrite;io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;sun/nio/ch/SocketChannelImpl:.write;sun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];do_sync_write_[k];sock_aio_write_[k];do_sock_write.isra.10_[k];inet_sendmsg_[k];tcp_sendmsg_[k];sk_stream_alloc_skb_[k];__alloc_skb_[k];get_slab_[k] 2
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelDuplexHandler:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelOutboundHandlerAdapter:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/DefaultChannelPipeline$HeadContext:.flush;io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;io/netty/channel/nio/AbstractNioByteChannel:.doWrite;io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;sun/nio/ch/SocketChannelImpl:.write;sun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];do_sync_write_[k];sock_aio_write_[k];do_sock_write.isra.10_[k];inet_sendmsg_[k];tcp_sendmsg_[k];sk_stream_alloc_skb_[k];__alloc_skb_[k];kmem_cache_alloc_node_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelDuplexHandler:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelOutboundHandlerAdapter:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/DefaultChannelPipeline$HeadContext:.flush;io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;io/netty/channel/nio/AbstractNioByteChannel:.doWrite;io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;sun/nio/ch/SocketChannelImpl:.write;sun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];do_sync_write_[k];sock_aio_write_[k];do_sock_write.isra.10_[k];inet_sendmsg_[k];tcp_sendmsg_[k];sk_stream_alloc_skb_[k];ksize_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelDuplexHandler:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelOutboundHandlerAdapter:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/DefaultChannelPipeline$HeadContext:.flush;io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;io/netty/channel/nio/AbstractNioByteChannel:.doWrite;io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;sun/nio/ch/SocketChannelImpl:.write;sun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];do_sync_write_[k];sock_aio_write_[k];do_sock_write.isra.10_[k];inet_sendmsg_[k];tcp_sendmsg_[k];tcp_send_mss_[k];tcp_current_mss_[k];ipv4_mtu_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelDuplexHandler:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelOutboundHandlerAdapter:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/DefaultChannelPipeline$HeadContext:.flush;io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;io/netty/channel/nio/AbstractNioByteChannel:.doWrite;io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;sun/nio/ch/SocketChannelImpl:.write;sun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];do_sync_write_[k];sock_aio_write_[k];do_sock_write.isra.10_[k];inet_sendmsg_[k];tcp_sendmsg_[k];tcp_send_mss_[k];tcp_current_mss_[k];tcp_established_options_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelDuplexHandler:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelOutboundHandlerAdapter:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/DefaultChannelPipeline$HeadContext:.flush;io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;io/netty/channel/nio/AbstractNioByteChannel:.doWrite;io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;sun/nio/ch/SocketChannelImpl:.write;sun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];do_sync_write_[k];sock_aio_write_[k];do_sock_write.isra.10_[k];inet_sendmsg_[k];tcp_sendmsg_[k];tcp_send_mss_[k];tcp_xmit_size_goal_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelDuplexHandler:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelOutboundHandlerAdapter:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/DefaultChannelPipeline$HeadContext:.flush;io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;io/netty/channel/nio/AbstractNioByteChannel:.doWrite;io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;sun/nio/ch/SocketChannelImpl:.write;sun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];do_sync_write_[k];sock_aio_write_[k];do_sock_write.isra.10_[k];inet_sendmsg_[k];tcp_sendmsg_[k];tcp_xmit_size_goal_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelDuplexHandler:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelOutboundHandlerAdapter:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/DefaultChannelPipeline$HeadContext:.flush;io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;io/netty/channel/nio/AbstractNioByteChannel:.doWrite;io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;sun/nio/ch/SocketChannelImpl:.write;sun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];fsnotify_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelDuplexHandler:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelOutboundHandlerAdapter:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/DefaultChannelPipeline$HeadContext:.flush;io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;io/netty/channel/nio/AbstractNioByteChannel:.doWrite;io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;sun/nio/ch/SocketChannelImpl:.write;sun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];fsnotify_[k];__srcu_read_lock_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelDuplexHandler:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelOutboundHandlerAdapter:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/DefaultChannelPipeline$HeadContext:.flush;io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;io/netty/channel/nio/AbstractNioByteChannel:.doWrite;io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;sun/nio/ch/SocketChannelImpl:.write;sun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];rw_verify_area_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelDuplexHandler:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelOutboundHandlerAdapter:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/DefaultChannelPipeline$HeadContext:.flush;io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;io/netty/channel/nio/AbstractNioByteChannel:.doWrite;io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;sun/nio/ch/SocketChannelImpl:.write;sun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];rw_verify_area_[k];apparmor_file_permission_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelDuplexHandler:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelOutboundHandlerAdapter:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/DefaultChannelPipeline$HeadContext:.flush;io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;io/netty/channel/nio/AbstractNioByteChannel:.doWrite;io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;sun/nio/ch/SocketChannelImpl:.write;sun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];rw_verify_area_[k];security_file_permission_[k];apparmor_file_permission_[k];common_file_perm_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelDuplexHandler:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelOutboundHandlerAdapter:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/DefaultChannelPipeline$HeadContext:.flush;io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;io/netty/channel/nio/AbstractNioByteChannel:.doWrite;io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;sun/nio/ch/SocketChannelImpl:.write;sun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath_[k];sys_write_[k];vfs_write_[k];sock_aio_write_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelDuplexHandler:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelOutboundHandlerAdapter:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/DefaultChannelPipeline$HeadContext:.flush;io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;io/netty/channel/nio/AbstractNioByteChannel:.doWrite;io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;sun/nio/ch/SocketChannelImpl:.write;sun/nio/ch/SocketChannelImpl:.writerCleanup 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelDuplexHandler:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelOutboundHandlerAdapter:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/DefaultChannelPipeline$HeadContext:.flush;io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;io/netty/channel/nio/AbstractNioByteChannel:.doWrite;io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;sun/nio/ch/SocketChannelImpl:.writerCleanup 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelDuplexHandler:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/ChannelOutboundHandlerAdapter:.flush;io/netty/channel/AbstractChannelHandlerContext:.flush;io/netty/channel/DefaultChannelPipeline$HeadContext:.flush;io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;io/netty/channel/nio/AbstractNioByteChannel:.doWrite;io/netty/util/Recycler:.recycle 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;io/netty/channel/ChannelDuplexHandler:.flush 2
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/socket/nio/NioSocketChannel:.doReadBytes;sun/nio/ch/SocketChannelImpl:.read;java/nio/channels/spi/AbstractInterruptibleChannel:.end 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/socket/nio/NioSocketChannel:.doReadBytes;sun/nio/ch/SocketChannelImpl:.read;sun/nio/ch/FileDispatcherImpl:.read0;read 2
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/socket/nio/NioSocketChannel:.doReadBytes;sun/nio/ch/SocketChannelImpl:.read;sun/nio/ch/FileDispatcherImpl:.read0;read;sys_read_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/socket/nio/NioSocketChannel:.doReadBytes;sun/nio/ch/SocketChannelImpl:.read;sun/nio/ch/FileDispatcherImpl:.read0;read;system_call_fastpath_[k];sys_read_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/socket/nio/NioSocketChannel:.doReadBytes;sun/nio/ch/SocketChannelImpl:.read;sun/nio/ch/FileDispatcherImpl:.read0;read;system_call_fastpath_[k];sys_read_[k];do_sync_read_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/socket/nio/NioSocketChannel:.doReadBytes;sun/nio/ch/SocketChannelImpl:.read;sun/nio/ch/FileDispatcherImpl:.read0;read;system_call_fastpath_[k];sys_read_[k];vfs_read_[k];do_sync_read_[k];sock_aio_read_[k];sock_aio_read.part.13_[k];do_sock_read.isra.12_[k];inet_recvmsg_[k];__kfree_skb_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/socket/nio/NioSocketChannel:.doReadBytes;sun/nio/ch/SocketChannelImpl:.read;sun/nio/ch/FileDispatcherImpl:.read0;read;system_call_fastpath_[k];sys_read_[k];vfs_read_[k];do_sync_read_[k];sock_aio_read_[k];sock_aio_read.part.13_[k];do_sock_read.isra.12_[k];inet_recvmsg_[k];tcp_rcv_space_adjust_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/socket/nio/NioSocketChannel:.doReadBytes;sun/nio/ch/SocketChannelImpl:.read;sun/nio/ch/FileDispatcherImpl:.read0;read;system_call_fastpath_[k];sys_read_[k];vfs_read_[k];do_sync_read_[k];sock_aio_read_[k];sock_aio_read.part.13_[k];do_sock_read.isra.12_[k];inet_recvmsg_[k];tcp_recvmsg_[k];__kfree_skb_[k];skb_release_data_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/socket/nio/NioSocketChannel:.doReadBytes;sun/nio/ch/SocketChannelImpl:.read;sun/nio/ch/FileDispatcherImpl:.read0;read;system_call_fastpath_[k];sys_read_[k];vfs_read_[k];do_sync_read_[k];sock_aio_read_[k];sock_aio_read.part.13_[k];do_sock_read.isra.12_[k];inet_recvmsg_[k];tcp_recvmsg_[k];__kfree_skb_[k];skb_release_head_state_[k];dst_release_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/socket/nio/NioSocketChannel:.doReadBytes;sun/nio/ch/SocketChannelImpl:.read;sun/nio/ch/FileDispatcherImpl:.read0;read;system_call_fastpath_[k];sys_read_[k];vfs_read_[k];do_sync_read_[k];sock_aio_read_[k];sock_aio_read.part.13_[k];do_sock_read.isra.12_[k];inet_recvmsg_[k];tcp_recvmsg_[k];_raw_spin_lock_bh_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/socket/nio/NioSocketChannel:.doReadBytes;sun/nio/ch/SocketChannelImpl:.read;sun/nio/ch/FileDispatcherImpl:.read0;read;system_call_fastpath_[k];sys_read_[k];vfs_read_[k];do_sync_read_[k];sock_aio_read_[k];sock_aio_read.part.13_[k];do_sock_read.isra.12_[k];inet_recvmsg_[k];tcp_recvmsg_[k];skb_copy_datagram_iovec_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/socket/nio/NioSocketChannel:.doReadBytes;sun/nio/ch/SocketChannelImpl:.read;sun/nio/ch/FileDispatcherImpl:.read0;read;system_call_fastpath_[k];sys_read_[k];vfs_read_[k];do_sync_read_[k];sock_aio_read_[k];sock_aio_read.part.13_[k];do_sock_read.isra.12_[k];inet_recvmsg_[k];tcp_recvmsg_[k];skb_copy_datagram_iovec_[k];copy_user_enhanced_fast_string_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/socket/nio/NioSocketChannel:.doReadBytes;sun/nio/ch/SocketChannelImpl:.read;sun/nio/ch/FileDispatcherImpl:.read0;read;system_call_fastpath_[k];sys_read_[k];vfs_read_[k];do_sync_read_[k];sock_aio_read_[k];sock_aio_read.part.13_[k];do_sock_read.isra.12_[k];inet_recvmsg_[k];tcp_recvmsg_[k];tcp_cleanup_rbuf_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/socket/nio/NioSocketChannel:.doReadBytes;sun/nio/ch/SocketChannelImpl:.read;sun/nio/ch/FileDispatcherImpl:.read0;read;system_call_fastpath_[k];sys_read_[k];vfs_read_[k];do_sync_read_[k];sock_aio_read_[k];sock_aio_read.part.13_[k];do_sock_read.isra.12_[k];inet_recvmsg_[k];tcp_recvmsg_[k];tcp_cleanup_rbuf_[k];__tcp_select_window_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/socket/nio/NioSocketChannel:.doReadBytes;sun/nio/ch/SocketChannelImpl:.read;sun/nio/ch/FileDispatcherImpl:.read0;read;system_call_fastpath_[k];sys_read_[k];vfs_read_[k];rw_verify_area_[k] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeys;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/socket/nio/NioSocketChannel:.doReadBytes 1
java;start_thread;java_start;VMThread::run;VMThread::loop;VMThread::evaluate_operation;VM_Operation::evaluate;VM_ParallelGCFailedAllocation::doit;ParallelScavengeHeap::failed_mem_allocate;PSScavenge::invoke;PSScavenge::invoke_no_policy;PSIsAliveClosure::do_object_b 1
java;start_thread;java_start;VMThread::run;VMThread::loop;VMThread::evaluate_operation;VM_Operation::evaluate;VM_ParallelGCFailedAllocation::doit;ParallelScavengeHeap::failed_mem_allocate;PSScavenge::invoke;PSScavenge::invoke_no_policy;StringTable::unlink_or_oops_do 2
java;start_thread;java_start;VMThread::run;VMThread::loop;VMThread::evaluate_operation;VM_Operation::evaluate;VM_ParallelGCFailedAllocation::doit;ParallelScavengeHeap::failed_mem_allocate;PSScavenge::invoke;PSScavenge::invoke_no_policy;pthread_cond_signal@@GLIBC_2.3.2;system_call_fastpath_[k];sys_futex_[k];do_futex_[k];futex_wake_op_[k] 1
java;write;check_events_[k];hypercall_page_[k] 3

ab-?;[unknown];__write_nocancel;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;inet_sendmsg;call_function_single_interrupt;smp_call_function_single_interrupt;generic_smp_call_function_single_interrupt;remote_function;__perf_event_enable;group_sched_in;x86_pmu_commit_txn;perf_pmu_enable;x86_pmu_enable;intel_pmu_enable_all;native_write_msr_safe 4
ab-?;[unknown];__write_nocancel;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;inet_sendmsg;tcp_sendmsg;__tcp_push_pending_frames;tcp_write_xmit;tcp_transmit_skb;ip_queue_xmit;ip_local_out;ip_output;ip_finish_output;local_bh_enable;do_softirq;do_softirq_own_stack;__do_softirq;net_rx_action;process_backlog;__netif_receive_skb;__netif_receive_skb_core;ip_rcv;ip_rcv_finish;ip_local_deliver;ip_local_deliver_finish;tcp_v4_rcv;tcp_v4_do_rcv;tcp_rcv_established;tcp_ack;tcp_clean_rtx_queue;__kfree_skb 1
ab-?;[unknown];__write_nocancel;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;inet_sendmsg;tcp_sendmsg;sk_stream_alloc_skb;__alloc_skb;__kmalloc_reserve.isra.26 1
ab-?;[unknown];__write_nocancel;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;inet_sendmsg;tcp_sendmsg;tcp_send_mss;tcp_current_mss;tcp_v4_md5_lookup 1
java-?;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/buffer/AbstractByteBuf:.writeBytes;Lsun/nio/ch/SocketChannelImpl:.read 1
java-?;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/buffer/AbstractByteBuf:.writeBytes;Lsun/nio/ch/SocketChannelImpl:.read;Ljava/lang/Thread:.blockedOn 1
java-?;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/DefaultChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/DefaultChannelHandlerContext:.fireChannelRead 1
java-?;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/DefaultChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/DefaultChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/VertxHttpHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/vertx/java/core/http/impl/ServerConnection:.handleMessage 1
java-?;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/DefaultChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/DefaultChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/VertxHttpHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/vertx/java/core/http/impl/ServerConnection:.handleMessage;Lorg/mozilla/javascript/WrapFactory:.wrap;call_function_single_interrupt;smp_call_function_single_interrupt;generic_smp_call_function_single_interrupt;remote_function;__perf_event_enable;group_sched_in;x86_pmu_commit_txn;perf_pmu_enable;x86_pmu_enable;intel_pmu_enable_all;native_write_msr_safe 4
java-?;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/DefaultChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/DefaultChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/VertxHttpHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/vertx/java/core/http/impl/ServerConnection:.handleMessage;Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_80:.call;Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_80:.call;Lorg/mozilla/javascript/BaseFunction:.construct;Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_80:.call;Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_80:._c_anonymous_3;Lorg/mozilla/javascript/BaseFunction:.construct;Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_80:.call;Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_80:._c_anonymous_21;Lorg/mozilla/javascript/optimizer/OptRuntime:.call2;Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_streams_js_31:.call;Lorg/mozilla/javascript/ScriptRuntime:.setObjectProp;Lorg/mozilla/javascript/IdScriptableObject:.has 1
java-?;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/DefaultChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/DefaultChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/VertxHttpHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/vertx/java/core/http/impl/ServerConnection:.handleMessage;Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_90:.call;Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_90:.call;Lorg/mozilla/javascript/BaseFunction:.construct;Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_90:.call;Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_90:._c_anonymous_3;Lorg/mozilla/javascript/ScriptRuntime:.getPropFunctionAndThis 1
java-?;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/DefaultChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/DefaultChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/VertxHttpHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/vertx/java/core/http/impl/ServerConnection:.handleMessage;Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_90:.call;Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_90:.call;Lorg/mozilla/javascript/BaseFunction:.construct;Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_90:.call;Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_90:._c_anonymous_3;Lorg/mozilla/javascript/ScriptRuntime:.nameOrFunction;Lorg/mozilla/javascript/IdScriptableObject:.get;Lorg/mozilla/javascript/ScriptableObject:.getSlot 1
java-?;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/DefaultChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/DefaultChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/VertxHttpHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/vertx/java/core/http/impl/ServerConnection:.handleMessage;Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_90:.call;Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_90:.call;Lorg/mozilla/javascript/BaseFunction:.construct;Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_90:.call;Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_90:._c_anonymous_3;Lorg/mozilla/javascript/ScriptRuntime:.setObjectProp;Lorg/mozilla/javascript/IdScriptableObject:.put;Lorg/mozilla/javascript/ScriptableObject:.getSlot;Lorg/mozilla/javascript/ScriptableObject:.createSlot 1
java-?;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/DefaultChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/DefaultChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/VertxHttpHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/vertx/java/core/http/impl/ServerConnection:.handleMessage;Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_90:.call;Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_90:.call;Lorg/mozilla/javascript/gen/file__home_bgregg_bench_Server_js_js_4:.call;Lorg/mozilla/javascript/gen/file__home_bgregg_bench_Server_js_js_4:._c_anonymous_1;Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_90:.call;Lorg/mozilla/javascript/NativeJavaMethod:.call;Lorg/mozilla/javascript/MemberBox:.invoke;Ljava/lang/reflect/Method:.invoke 1
java-?;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/DefaultChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/DefaultChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/VertxHttpHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/vertx/java/core/http/impl/ServerConnection:.handleMessage;Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_91:.call;Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_91:.call;Lorg/mozilla/javascript/BaseFunction:.construct;Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_91:.call;Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_91:._c_anonymous_3;Lorg/mozilla/javascript/BaseFunction:.construct;Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_91:.call;Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_91:._c_anonymous_21;Lorg/mozilla/javascript/optimizer/OptRuntime:.call2;Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_streams_js_49:.call;Lorg/mozilla/javascript/optimizer/OptRuntime:.call2;Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_streams_js_49:.call;Lorg/mozilla/javascript/ScriptRuntime:.setObjectProp;Lorg/mozilla/javascript/ScriptableObject:.getSlot 1
java-?;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/DefaultChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/DefaultChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/VertxHttpHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/vertx/java/core/http/impl/ServerConnection:.handleMessage;Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_91:.call;Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_91:.call;Lorg/mozilla/javascript/BaseFunction:.construct;Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_91:.call;Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_91:._c_anonymous_3;Lorg/mozilla/javascript/ScriptRuntime:.setObjectProp;Lorg/mozilla/javascript/IdScriptableObject:.has 1
java-?;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/DefaultChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/DefaultChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/VertxHttpHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/vertx/java/core/http/impl/ServerConnection:.handleMessage;Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_91:.call;Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_91:.call;Lorg/mozilla/javascript/BaseFunction:.construct;Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_91:.call;Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_91:._c_anonymous_3;Lorg/mozilla/javascript/ScriptRuntime:.setObjectProp;Lorg/mozilla/javascript/IdScriptableObject:.put;Lorg/mozilla/javascript/ScriptableObject:.getSlot;Lorg/mozilla/javascript/ScriptableObject:.createSlot 1
java-?;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/DefaultChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/DefaultChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/VertxHttpHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/vertx/java/core/http/impl/ServerConnection:.handleMessage;Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_91:.call;Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_91:.call;Lorg/mozilla/javascript/gen/file__home_bgregg_bench_Server_js_js_2:.call;Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_91:.call;Lorg/mozilla/javascript/NativeJavaMethod:.call;Lorg/mozilla/javascript/MemberBox:.invoke;Ljava/lang/reflect/Method:.invoke;Lio/netty/handler/codec/http/DefaultHttpHeaders:.add0 1
java-?;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/DefaultChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/DefaultChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/VertxHttpHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/vertx/java/core/http/impl/ServerConnection:.handleMessage;Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93:.call;Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93:.call 1
java-?;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/DefaultChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/DefaultChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/VertxHttpHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/vertx/java/core/http/impl/ServerConnection:.handleMessage;Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93:.call;Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93:.call;Lorg/mozilla/javascript/BaseFunction:.construct;Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93:.call;Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93:._c_anonymous_3;Lorg/mozilla/javascript/BaseFunction:.construct;Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93:.call;Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93:._c_anonymous_21;Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93:.getParamAndVarCount 1
java-?;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/DefaultChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/DefaultChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/VertxHttpHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/vertx/java/core/http/impl/ServerConnection:.handleMessage;Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93:.call;Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93:.call;Lorg/mozilla/javascript/BaseFunction:.construct;Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93:.call;Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93:._c_anonymous_3;Lorg/mozilla/javascript/ScriptRuntime:.setObjectProp;Lorg/mozilla/javascript/IdScriptableObject:.has;Lorg/mozilla/javascript/ScriptableObject:.getSlot 1
java-?;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/DefaultChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/DefaultChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/VertxHttpHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/vertx/java/core/http/impl/ServerConnection:.handleMessage;Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93:.call;Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93:.call;Lorg/mozilla/javascript/BaseFunction:.construct;Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93:.call;Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93:._c_anonymous_3;Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93:.<init>;Lorg/mozilla/javascript/NativeFunction:.initScriptFunction;Lorg/mozilla/javascript/TopLevel:.getBuiltinPrototype;call_function_single_interrupt;smp_call_function_single_interrupt;generic_smp_call_function_single_interrupt;remote_function;__perf_event_enable;group_sched_in;x86_pmu_commit_txn;perf_pmu_enable;x86_pmu_enable;intel_pmu_enable_all;native_write_msr_safe 4
java-?;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/DefaultChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/DefaultChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/VertxHttpHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/vertx/java/core/http/impl/ServerConnection:.handleMessage;Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93:.call;Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93:.call;Lorg/mozilla/javascript/BaseFunction:.construct;Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93:.call;Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93:._c_anonymous_3;Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93:.getParamCount 1
java-?;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/DefaultChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/DefaultChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/VertxHttpHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/vertx/java/core/http/impl/ServerConnection:.handleMessage;Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93:.call;Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93:.call;Lorg/mozilla/javascript/BaseFunction:.construct;Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93:.call;Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93:._c_anonymous_3;Lorg/mozilla/javascript/optimizer/OptRuntime:.call2;Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_streams_js_47:.call;Lorg/mozilla/javascript/ScriptRuntime:.setObjectProp;Lorg/mozilla/javascript/ScriptableObject:.getSlot 1
java-?;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/DefaultChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/handler/codec/http/HttpObjectDecoder:.decode;Lio/netty/handler/codec/http/HttpMethod:.valueOf 1
java-?;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/DefaultChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/handler/codec/http/HttpObjectDecoder:.decode;Lio/netty/handler/codec/http/HttpObjectDecoder:.findWhitespace 1
java-?;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/DefaultChannelHandlerContext:.fireChannelReadComplete;Lio/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;Lio/netty/channel/DefaultChannelHandlerContext:.fireChannelReadComplete;Lorg/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;Lio/netty/channel/DefaultChannelHandlerContext:.flush;Lio/netty/channel/ChannelDuplexHandler:.flush;Lio/netty/channel/DefaultChannelHandlerContext:.flush;Lio/netty/channel/ChannelOutboundHandlerAdapter:.flush;Lio/netty/channel/DefaultChannelHandlerContext:.flush;Lio/netty/channel/DefaultChannelPipeline$HeadHandler:.flush;Lio/netty/channel/nio/AbstractNioByteChannel:.doWrite;Lio/netty/buffer/PooledUnsafeDirectByteBuf:.getBytes;Lsun/nio/ch/SocketChannelImpl:.write;Lsun/nio/ch/FileDispatcherImpl:.write0;[libpthread-2.19.so];system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;inet_sendmsg;tcp_sendmsg;__tcp_push_pending_frames;tcp_write_xmit;tcp_transmit_skb;ip_queue_xmit;ip_local_out;ip_output;ip_finish_output;local_bh_enable;do_softirq;do_softirq_own_stack;__do_softirq;net_rx_action;process_backlog;__netif_receive_skb;__netif_receive_skb_core;ip_rcv;ip_rcv_finish;ip_local_deliver;ip_local_deliver_finish;tcp_v4_rcv;tcp_v4_do_rcv;tcp_rcv_established;tcp_data_queue;sock_def_readable;__wake_up_sync_key;__wake_up_common;ep_poll_callback;__wake_up_locked;__wake_up_common;default_wake_function;try_to_wake_up;_raw_spin_unlock_irqrestore 1
java-?;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/DefaultChannelHandlerContext:.fireChannelReadComplete;Lio/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;Lio/netty/channel/DefaultChannelHandlerContext:.fireChannelReadComplete;Lorg/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;Lio/netty/channel/DefaultChannelHandlerContext:.flush;Lio/netty/channel/ChannelDuplexHandler:.flush;Lio/netty/channel/DefaultChannelHandlerContext:.flush;Lio/netty/channel/ChannelOutboundHandlerAdapter:.flush;Lio/netty/channel/DefaultChannelHandlerContext:.flush;Lio/netty/channel/DefaultChannelPipeline$HeadHandler:.flush;Lio/netty/channel/nio/AbstractNioByteChannel:.doWrite;Lio/netty/buffer/PooledUnsafeDirectByteBuf:.getBytes;Lsun/nio/ch/SocketChannelImpl:.write;Lsun/nio/ch/FileDispatcherImpl:.write0;[libpthread-2.19.so];system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;inet_sendmsg;tcp_sendmsg;__tcp_push_pending_frames;tcp_write_xmit;tcp_transmit_skb;tcp_v4_send_check;__tcp_v4_send_check 1
java-?;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/DefaultChannelHandlerContext:.fireChannelReadComplete;Lio/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;Lio/netty/channel/DefaultChannelHandlerContext:.fireChannelReadComplete;Lorg/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;Lio/netty/channel/DefaultChannelHandlerContext:.flush;Lio/netty/channel/ChannelDuplexHandler:.flush;Lio/netty/channel/DefaultChannelHandlerContext:.flush;Lio/netty/channel/ChannelOutboundHandlerAdapter:.flush;Lio/netty/channel/DefaultChannelHandlerContext:.flush;Lio/netty/channel/DefaultChannelPipeline$HeadHandler:.flush;Lio/netty/channel/nio/AbstractNioByteChannel:.doWrite;Lio/netty/buffer/PooledUnsafeDirectByteBuf:.getBytes;Lsun/nio/ch/SocketChannelImpl:.write;pthread_self 1
java-?;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.select;Lsun/nio/ch/SelectorImpl:.lockAndDoSelect;Lsun/nio/ch/EPollSelectorImpl:.doSelect;Lsun/nio/ch/EPollArrayWrapper:.poll;Lsun/nio/ch/EPollArrayWrapper:.epollWait;__libc_enable_asynccancel 1
perf-?;__libc_start_main;[perf];[perf];[perf];__GI___ioctl;system_call_fastpath;sys_ioctl;do_vfs_ioctl;perf_ioctl;perf_event_for_each_child;perf_event_enable;cpu_function_call;smp_call_function_single;remote_function;__perf_event_enable;group_sched_in;x86_pmu_commit_txn;perf_pmu_enable;x86_pmu_enable;intel_pmu_enable_all;native_write_msr_safe 4
perf-?;__libc_start_main;[perf];[perf];[perf];page_fault 1
swapper-?;x86_64_start_kernel;x86_64_start_reservations;start_kernel;rest_init;cpu_startup_entry;rcu_idle_enter 1

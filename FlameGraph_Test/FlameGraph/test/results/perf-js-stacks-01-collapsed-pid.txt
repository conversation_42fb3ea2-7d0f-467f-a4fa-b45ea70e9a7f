node-v011-?;__libc_start_main;node::Start;uv_run;uv__io_poll;uv__async_io;uv__async_event;uv__work_done;node::After;v8::Function::Call;v8::internal::Execution::Call;v8::internal::Invoke;Stub:JSEntryStub;Builtin:A builtin from the snapshot;LazyCompile:_tickDomainCallback node.js:387;LazyCompile:*Async$consumeFunctionBuffer /apps/node/webapp/node_modules/xxxxx/js/main/async.js:39;RegExp:\bFoo ?Bar 1
node-v011-?;__libc_start_main;node::Start;uv_run;uv__io_poll;uv__async_io;uv__async_event;uv__work_done;node::After;v8::Function::Call;v8::internal::Execution::Call;v8::internal::Invoke;Stub:JSEntryStub;Builtin:A builtin from the snapshot;LazyCompile:_tickDomainCallback node.js:387;LazyCompile:*Async$consumeFunctionBuffer /apps/node/webapp/node_modules/xxxxx/js/main/async.js:39;v8::internal::Execution::Call;LazyCompile:~body_0 evalmachine.<anonymous>:1;RegExp:[&<>\\] 1

<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" width="1200" height="338" onload="init(evt)" viewBox="0 0 1200 338" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<!-- Flame graph stack visualization. See https://github.com/brendangregg/FlameGraph for latest version, and http://www.brendangregg.com/flamegraphs.html for examples. -->
<defs >
	<linearGradient id="background" y1="0" y2="1" x1="0" x2="0" >
		<stop stop-color="#eeeeee" offset="5%" />
		<stop stop-color="#eeeeb0" offset="95%" />
	</linearGradient>
</defs>
<style type="text/css">
	.func_g:hover { stroke:black; stroke-width:0.5; cursor:pointer; }
</style>
<script type="text/ecmascript">
<![CDATA[
	var details, svg;
	function init(evt) { 
		details = document.getElementById("details").firstChild; 
		svg = document.getElementsByTagName("svg")[0];
		searching = 0;
	}

	// mouse-over for info
	function s(info) { details.nodeValue = "Function: " + info; }
	function c() { details.nodeValue = ' '; }

	// functions
	function find_child(parent, name, attr) {
		var children = parent.childNodes;
		for (var i=0; i<children.length;i++) {
			if (children[i].tagName == name)
				return (attr != undefined) ? children[i].attributes[attr].value : children[i];
		}
		return;
	}
	function orig_save(e, attr, val) {
		if (e.attributes["_orig_"+attr] != undefined) return;
		if (e.attributes[attr] == undefined) return;
		if (val == undefined) val = e.attributes[attr].value;
		e.setAttribute("_orig_"+attr, val);
	}
	function orig_load(e, attr) {
		if (e.attributes["_orig_"+attr] == undefined) return;
		e.attributes[attr].value = e.attributes["_orig_"+attr].value;
		e.removeAttribute("_orig_"+attr);
	}
	function update_text(e) {
		var r = find_child(e, "rect");
		var t = find_child(e, "text");
		var w = parseFloat(r.attributes["width"].value) -3;
		var txt = find_child(e, "title").textContent.replace(/\([^(]*\)/,"");
		t.attributes["x"].value = parseFloat(r.attributes["x"].value) +3;
		
		// Smaller than this size won't fit anything
		if (w < 2*12*0.59) {
			t.textContent = "";
			return;
		}
		
		t.textContent = txt;
		// Fit in full text width
		if (/^ *$/.test(txt) || t.getSubStringLength(0, txt.length) < w)
			return;
		
		for (var x=txt.length-2; x>0; x--) {
			if (t.getSubStringLength(0, x+2) <= w) { 
				t.textContent = txt.substring(0,x) + "..";
				return;
			}
		}
		t.textContent = "";
	}

	// zoom
	function zoom_reset(e) {
		if (e.attributes != undefined) {
			orig_load(e, "x");
			orig_load(e, "width");
		}
		if (e.childNodes == undefined) return;
		for(var i=0, c=e.childNodes; i<c.length; i++) {
			zoom_reset(c[i]);
		}
	}
	function zoom_child(e, x, ratio) {
		if (e.attributes != undefined) {
			if (e.attributes["x"] != undefined) {
				orig_save(e, "x");
				e.attributes["x"].value = (parseFloat(e.attributes["x"].value) - x - 10) * ratio + 10;
				if(e.tagName == "text") e.attributes["x"].value = find_child(e.parentNode, "rect", "x") + 3;
			}
			if (e.attributes["width"] != undefined) {
				orig_save(e, "width");
				e.attributes["width"].value = parseFloat(e.attributes["width"].value) * ratio;
			}
		}
		
		if (e.childNodes == undefined) return;
		for(var i=0, c=e.childNodes; i<c.length; i++) {
			zoom_child(c[i], x-10, ratio);
		}
	}
	function zoom_parent(e) {
		if (e.attributes) {
			if (e.attributes["x"] != undefined) {
				orig_save(e, "x");
				e.attributes["x"].value = 10;
			}
			if (e.attributes["width"] != undefined) {
				orig_save(e, "width");
				e.attributes["width"].value = parseInt(svg.width.baseVal.value) - (10*2);
			}
		}
		if (e.childNodes == undefined) return;
		for(var i=0, c=e.childNodes; i<c.length; i++) {
			zoom_parent(c[i]);
		}
	}
	function zoom(node) { 
		var attr = find_child(node, "rect").attributes;
		var width = parseFloat(attr["width"].value);
		var xmin = parseFloat(attr["x"].value);
		var xmax = parseFloat(xmin + width);
		var ymin = parseFloat(attr["y"].value);
		var ratio = (svg.width.baseVal.value - 2*10) / width;
		
		// XXX: Workaround for JavaScript float issues (fix me)
		var fudge = 0.0001;
		
		var unzoombtn = document.getElementById("unzoom");
		unzoombtn.style["opacity"] = "1.0";
		
		var el = document.getElementsByTagName("g");
		for(var i=0;i<el.length;i++){
			var e = el[i];
			var a = find_child(e, "rect").attributes;
			var ex = parseFloat(a["x"].value);
			var ew = parseFloat(a["width"].value);
			// Is it an ancestor
			if (0 == 0) {
				var upstack = parseFloat(a["y"].value) > ymin;
			} else {
				var upstack = parseFloat(a["y"].value) < ymin;
			}
			if (upstack) {
				// Direct ancestor
				if (ex <= xmin && (ex+ew+fudge) >= xmax) {
					e.style["opacity"] = "0.5";
					zoom_parent(e);
					e.onclick = function(e){unzoom(); zoom(this);};
					update_text(e);
				}
				// not in current path
				else
					e.style["display"] = "none";
			}
			// Children maybe
			else {
				// no common path
				if (ex < xmin || ex + fudge >= xmax) {
					e.style["display"] = "none";
				}
				else {
					zoom_child(e, xmin, ratio);
					e.onclick = function(e){zoom(this);};
					update_text(e);
				}
			}
		}
	}
	function unzoom() {
		var unzoombtn = document.getElementById("unzoom");
		unzoombtn.style["opacity"] = "0.0";
		
		var el = document.getElementsByTagName("g");
		for(i=0;i<el.length;i++) {
			el[i].style["display"] = "block";
			el[i].style["opacity"] = "1";
			zoom_reset(el[i]);
			update_text(el[i]);
		}
	}	

	// search
	function reset_search() {
		var el = document.getElementsByTagName("rect");
		for (var i=0; i < el.length; i++){
			orig_load(el[i], "fill")
		}
	}
	function search_prompt() {
		if (!searching) {
			var term = prompt("Enter a search term (regexp " +
			    "allowed, eg: ^ext4_)", "");
			if (term != null) {
				search(term)
			}
		} else {
			reset_search();
			searching = 0;
			var searchbtn = document.getElementById("search");
			searchbtn.style["opacity"] = "0.1";
			searchbtn.firstChild.nodeValue = "Search"
		}
	}
	function search(term) {
		var re = new RegExp(term);
		var el = document.getElementsByTagName("g");
		for (var i=0; i < el.length; i++){
			var e = el[i];
			if (e.attributes["class"].value == "func_g") {
				// Scrape the function name from the onmouseover
				// callback text. This is a little dirty.
				var func = e.attributes["onmouseover"].value;
				if (func != null) {
					func = func.substr(3);
					func = func.replace(/ .*/, "");
					var r = find_child(e, "rect");
				}
				if (func != null && r != null &&
				    func.match(re)) {
					orig_save(r, "fill");
					r.attributes["fill"].value =
					    "rgb(230,0,230)";
					searching = 1;
				}
			}
		}
		if (searching) {
			var searchbtn = document.getElementById("search");
			searchbtn.style["opacity"] = "1.0";
			searchbtn.firstChild.nodeValue = "Reset Search"
		}
	}
	function searchover(e) {
		var searchbtn = document.getElementById("search");
		searchbtn.style["opacity"] = "1.0";
	}
	function searchout(e) {
		var searchbtn = document.getElementById("search");
		if (searching) {
			searchbtn.style["opacity"] = "1.0";
		} else {
			searchbtn.style["opacity"] = "0.1";
		}
	}
]]>
</script>
<rect x="0.0" y="0" width="1200.0" height="338.0" fill="url(#background)"  />
<text text-anchor="middle" x="600.00" y="24" font-size="17" font-family="Verdana" fill="rgb(0,0,0)"  >Flame Graph</text>
<text text-anchor="" x="10.00" y="321" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" id="details" > </text>
<text text-anchor="" x="10.00" y="24" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" id="unzoom" onclick="unzoom()" style="opacity:0.0;cursor:pointer" >Reset Zoom</text>
<text text-anchor="" x="1090.00" y="24" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" id="search" onmouseover="searchover()" onmouseout="searchout()" onclick="search_prompt()" style="opacity:0.1;cursor:pointer" >Search</text>
<g class="func_g" onmouseover="s('unix`mutex_enter (195 samples, 0.34%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`mutex_enter (195 samples, 0.34%)</title><rect x="912.7" y="65" width="4.0" height="15.0" fill="rgb(244,114,54)" rx="2" ry="2" />
<text text-anchor="" x="915.65" y="75.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`as_fault (12 samples, 0.02%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`as_fault (12 samples, 0.02%)</title><rect x="12.4" y="225" width="0.3" height="15.0" fill="rgb(214,58,31)" rx="2" ry="2" />
<text text-anchor="" x="15.42" y="235.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`disp_lock_exit (27 samples, 0.05%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`disp_lock_exit (27 samples, 0.05%)</title><rect x="24.3" y="257" width="0.6" height="15.0" fill="rgb(224,14,33)" rx="2" ry="2" />
<text text-anchor="" x="27.34" y="267.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`vsd_free (17 samples, 0.03%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`vsd_free (17 samples, 0.03%)</title><rect x="1018.1" y="65" width="0.4" height="15.0" fill="rgb(233,146,12)" rx="2" ry="2" />
<text text-anchor="" x="1021.12" y="75.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`pn_fixslash (44 samples, 0.08%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`pn_fixslash (44 samples, 0.08%)</title><rect x="825.4" y="129" width="0.9" height="15.0" fill="rgb(244,46,9)" rx="2" ry="2" />
<text text-anchor="" x="828.43" y="139.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`mutex_exit (105 samples, 0.18%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`mutex_exit (105 samples, 0.18%)</title><rect x="470.3" y="49" width="2.1" height="15.0" fill="rgb(209,131,47)" rx="2" ry="2" />
<text text-anchor="" x="473.26" y="59.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`falloc (1,363 samples, 2.37%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`falloc (1,363 samples, 2.37%)</title><rect x="147.1" y="209" width="28.0" height="15.0" fill="rgb(221,183,48)" rx="2" ry="2" />
<text text-anchor="" x="150.12" y="219.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >g..</text>
</g>
<g class="func_g" onmouseover="s('genunix`traverse (30 samples, 0.05%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`traverse (30 samples, 0.05%)</title><rect x="254.0" y="113" width="0.7" height="15.0" fill="rgb(230,87,3)" rx="2" ry="2" />
<text text-anchor="" x="257.05" y="123.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`fop_lookup (55 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`fop_lookup (55 samples, 0.10%)</title><rect x="208.8" y="145" width="1.1" height="15.0" fill="rgb(243,68,6)" rx="2" ry="2" />
<text text-anchor="" x="211.77" y="155.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`kmem_cache_free (29 samples, 0.05%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`kmem_cache_free (29 samples, 0.05%)</title><rect x="964.7" y="81" width="0.6" height="15.0" fill="rgb(244,22,21)" rx="2" ry="2" />
<text text-anchor="" x="967.71" y="91.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('lofs`makelonode (39 samples, 0.07%)')" onmouseout="c()" onclick="zoom(this)">
<title>lofs`makelonode (39 samples, 0.07%)</title><rect x="704.2" y="113" width="0.8" height="15.0" fill="rgb(227,161,51)" rx="2" ry="2" />
<text text-anchor="" x="707.22" y="123.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`vsd_free (155 samples, 0.27%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`vsd_free (155 samples, 0.27%)</title><rect x="634.3" y="33" width="3.2" height="15.0" fill="rgb(214,51,47)" rx="2" ry="2" />
<text text-anchor="" x="637.34" y="43.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`strlen (2,659 samples, 4.63%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`strlen (2,659 samples, 4.63%)</title><rect x="749.8" y="113" width="54.6" height="15.0" fill="rgb(240,97,40)" rx="2" ry="2" />
<text text-anchor="" x="752.81" y="123.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >unix`..</text>
</g>
<g class="func_g" onmouseover="s('unix`clear_int_flag (180 samples, 0.31%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`clear_int_flag (180 samples, 0.31%)</title><rect x="79.4" y="225" width="3.7" height="15.0" fill="rgb(218,135,40)" rx="2" ry="2" />
<text text-anchor="" x="82.35" y="235.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`mutex_exit (38 samples, 0.07%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`mutex_exit (38 samples, 0.07%)</title><rect x="1154.7" y="209" width="0.8" height="15.0" fill="rgb(231,115,42)" rx="2" ry="2" />
<text text-anchor="" x="1157.73" y="219.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`kmem_cpu_reload (5 samples, 0.01%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`kmem_cpu_reload (5 samples, 0.01%)</title><rect x="988.7" y="49" width="0.1" height="15.0" fill="rgb(239,50,50)" rx="2" ry="2" />
<text text-anchor="" x="991.66" y="59.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`mutex_exit (26 samples, 0.05%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`mutex_exit (26 samples, 0.05%)</title><rect x="703.7" y="97" width="0.5" height="15.0" fill="rgb(248,133,9)" rx="2" ry="2" />
<text text-anchor="" x="706.69" y="107.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`vn_vfslocks_getlock (47 samples, 0.08%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`vn_vfslocks_getlock (47 samples, 0.08%)</title><rect x="846.1" y="113" width="1.0" height="15.0" fill="rgb(254,215,28)" rx="2" ry="2" />
<text text-anchor="" x="849.09" y="123.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`bzero (8 samples, 0.01%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`bzero (8 samples, 0.01%)</title><rect x="155.8" y="177" width="0.1" height="15.0" fill="rgb(249,146,38)" rx="2" ry="2" />
<text text-anchor="" x="158.75" y="187.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`vn_exists (50 samples, 0.09%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`vn_exists (50 samples, 0.09%)</title><rect x="647.3" y="81" width="1.0" height="15.0" fill="rgb(220,163,19)" rx="2" ry="2" />
<text text-anchor="" x="650.26" y="91.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`mutex_enter (727 samples, 1.27%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`mutex_enter (727 samples, 1.27%)</title><rect x="500.3" y="65" width="14.9" height="15.0" fill="rgb(208,192,12)" rx="2" ry="2" />
<text text-anchor="" x="503.30" y="75.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`kmem_cache_alloc (179 samples, 0.31%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`kmem_cache_alloc (179 samples, 0.31%)</title><rect x="624.7" y="65" width="3.7" height="15.0" fill="rgb(205,9,0)" rx="2" ry="2" />
<text text-anchor="" x="627.74" y="75.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`mutex_enter (905 samples, 1.58%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`mutex_enter (905 samples, 1.58%)</title><rect x="1024.5" y="65" width="18.6" height="15.0" fill="rgb(224,223,38)" rx="2" ry="2" />
<text text-anchor="" x="1027.53" y="75.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`ufalloc (10 samples, 0.02%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`ufalloc (10 samples, 0.02%)</title><rect x="181.3" y="209" width="0.2" height="15.0" fill="rgb(224,148,24)" rx="2" ry="2" />
<text text-anchor="" x="184.33" y="219.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`vn_rele (25 samples, 0.04%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`vn_rele (25 samples, 0.04%)</title><rect x="966.2" y="81" width="0.6" height="15.0" fill="rgb(225,52,33)" rx="2" ry="2" />
<text text-anchor="" x="969.25" y="91.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`vn_exists (17 samples, 0.03%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`vn_exists (17 samples, 0.03%)</title><rect x="604.1" y="97" width="0.3" height="15.0" fill="rgb(213,118,14)" rx="2" ry="2" />
<text text-anchor="" x="607.10" y="107.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`lock_try (778 samples, 1.35%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`lock_try (778 samples, 1.35%)</title><rect x="93.0" y="241" width="15.9" height="15.0" fill="rgb(245,112,27)" rx="2" ry="2" />
<text text-anchor="" x="95.95" y="251.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`rwst_enter_common (314 samples, 0.55%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`rwst_enter_common (314 samples, 0.55%)</title><rect x="850.5" y="81" width="6.5" height="15.0" fill="rgb(231,20,27)" rx="2" ry="2" />
<text text-anchor="" x="853.53" y="91.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`fsop_root (62 samples, 0.11%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`fsop_root (62 samples, 0.11%)</title><rect x="824.2" y="129" width="1.2" height="15.0" fill="rgb(233,198,54)" rx="2" ry="2" />
<text text-anchor="" x="827.15" y="139.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('lofs`table_lock_enter (44 samples, 0.08%)')" onmouseout="c()" onclick="zoom(this)">
<title>lofs`table_lock_enter (44 samples, 0.08%)</title><rect x="1075.9" y="81" width="0.9" height="15.0" fill="rgb(234,13,28)" rx="2" ry="2" />
<text text-anchor="" x="1078.91" y="91.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`mutex_exit (138 samples, 0.24%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`mutex_exit (138 samples, 0.24%)</title><rect x="692.4" y="81" width="2.8" height="15.0" fill="rgb(222,87,49)" rx="2" ry="2" />
<text text-anchor="" x="695.35" y="91.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`mutex_enter (316 samples, 0.55%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`mutex_enter (316 samples, 0.55%)</title><rect x="544.9" y="33" width="6.5" height="15.0" fill="rgb(231,92,21)" rx="2" ry="2" />
<text text-anchor="" x="547.89" y="43.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`kmem_cache_free (5 samples, 0.01%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`kmem_cache_free (5 samples, 0.01%)</title><rect x="175.5" y="209" width="0.1" height="15.0" fill="rgb(248,89,7)" rx="2" ry="2" />
<text text-anchor="" x="178.47" y="219.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`preempt (14 samples, 0.02%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`preempt (14 samples, 0.02%)</title><rect x="109.1" y="241" width="0.2" height="15.0" fill="rgb(216,85,17)" rx="2" ry="2" />
<text text-anchor="" x="112.06" y="251.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`vn_alloc (1,189 samples, 2.07%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`vn_alloc (1,189 samples, 2.07%)</title><rect x="622.8" y="81" width="24.5" height="15.0" fill="rgb(242,127,14)" rx="2" ry="2" />
<text text-anchor="" x="625.83" y="91.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >g..</text>
</g>
<g class="func_g" onmouseover="s('genunix`kmem_cache_alloc (126 samples, 0.22%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`kmem_cache_alloc (126 samples, 0.22%)</title><rect x="868.6" y="65" width="2.6" height="15.0" fill="rgb(247,0,50)" rx="2" ry="2" />
<text text-anchor="" x="871.61" y="75.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`vfs_getops (157 samples, 0.27%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`vfs_getops (157 samples, 0.27%)</title><rect x="597.9" y="81" width="3.3" height="15.0" fill="rgb(250,224,46)" rx="2" ry="2" />
<text text-anchor="" x="600.94" y="91.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('lofs`lsave (27 samples, 0.05%)')" onmouseout="c()" onclick="zoom(this)">
<title>lofs`lsave (27 samples, 0.05%)</title><rect x="607.5" y="97" width="0.6" height="15.0" fill="rgb(215,171,23)" rx="2" ry="2" />
<text text-anchor="" x="610.53" y="107.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`tsc_read (160 samples, 0.28%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`tsc_read (160 samples, 0.28%)</title><rect x="118.0" y="225" width="3.3" height="15.0" fill="rgb(250,170,12)" rx="2" ry="2" />
<text text-anchor="" x="121.01" y="235.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('lofs`lfind (26 samples, 0.05%)')" onmouseout="c()" onclick="zoom(this)">
<title>lofs`lfind (26 samples, 0.05%)</title><rect x="607.0" y="97" width="0.5" height="15.0" fill="rgb(232,132,26)" rx="2" ry="2" />
<text text-anchor="" x="609.99" y="107.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`atomic_add_64 (205 samples, 0.36%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`atomic_add_64 (205 samples, 0.36%)</title><rect x="1185.8" y="257" width="4.2" height="15.0" fill="rgb(237,141,4)" rx="2" ry="2" />
<text text-anchor="" x="1188.79" y="267.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`mutex_enter (320 samples, 0.56%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`mutex_enter (320 samples, 0.56%)</title><rect x="1137.6" y="161" width="6.6" height="15.0" fill="rgb(218,71,41)" rx="2" ry="2" />
<text text-anchor="" x="1140.62" y="171.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`traverse (17 samples, 0.03%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`traverse (17 samples, 0.03%)</title><rect x="1119.9" y="145" width="0.3" height="15.0" fill="rgb(229,119,38)" rx="2" ry="2" />
<text text-anchor="" x="1122.89" y="155.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`mutex_enter (197 samples, 0.34%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`mutex_enter (197 samples, 0.34%)</title><rect x="164.1" y="177" width="4.0" height="15.0" fill="rgb(206,55,14)" rx="2" ry="2" />
<text text-anchor="" x="167.09" y="187.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`vn_mountedvfs (20 samples, 0.03%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`vn_mountedvfs (20 samples, 0.03%)</title><rect x="845.3" y="113" width="0.4" height="15.0" fill="rgb(240,130,2)" rx="2" ry="2" />
<text text-anchor="" x="848.29" y="123.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`audit_unfalloc (340 samples, 0.59%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`audit_unfalloc (340 samples, 0.59%)</title><rect x="182.8" y="193" width="7.0" height="15.0" fill="rgb(219,106,48)" rx="2" ry="2" />
<text text-anchor="" x="185.79" y="203.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`kmem_cache_free (209 samples, 0.36%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`kmem_cache_free (209 samples, 0.36%)</title><rect x="991.2" y="33" width="4.3" height="15.0" fill="rgb(247,92,2)" rx="2" ry="2" />
<text text-anchor="" x="994.17" y="43.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`kmem_zalloc (13 samples, 0.02%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`kmem_zalloc (13 samples, 0.02%)</title><rect x="157.1" y="193" width="0.3" height="15.0" fill="rgb(221,130,20)" rx="2" ry="2" />
<text text-anchor="" x="160.15" y="203.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`thread_lock (33 samples, 0.06%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`thread_lock (33 samples, 0.06%)</title><rect x="112.1" y="257" width="0.7" height="15.0" fill="rgb(225,209,33)" rx="2" ry="2" />
<text text-anchor="" x="115.10" y="267.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`tsc_read (186 samples, 0.32%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`tsc_read (186 samples, 0.32%)</title><rect x="16.9" y="225" width="3.9" height="15.0" fill="rgb(229,160,21)" rx="2" ry="2" />
<text text-anchor="" x="19.94" y="235.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`vn_vfsrlock (12 samples, 0.02%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`vn_vfsrlock (12 samples, 0.02%)</title><rect x="1094.5" y="129" width="0.3" height="15.0" fill="rgb(237,37,26)" rx="2" ry="2" />
<text text-anchor="" x="1097.52" y="139.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('lofs`lo_inactive (21 samples, 0.04%)')" onmouseout="c()" onclick="zoom(this)">
<title>lofs`lo_inactive (21 samples, 0.04%)</title><rect x="1092.9" y="113" width="0.4" height="15.0" fill="rgb(251,142,3)" rx="2" ry="2" />
<text text-anchor="" x="1095.89" y="123.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`rwst_destroy (20 samples, 0.03%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`rwst_destroy (20 samples, 0.03%)</title><rect x="899.0" y="97" width="0.4" height="15.0" fill="rgb(232,69,14)" rx="2" ry="2" />
<text text-anchor="" x="902.01" y="107.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`mutex_enter (379 samples, 0.66%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`mutex_enter (379 samples, 0.66%)</title><rect x="482.9" y="33" width="7.8" height="15.0" fill="rgb(248,210,39)" rx="2" ry="2" />
<text text-anchor="" x="485.94" y="43.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`vn_setops (41 samples, 0.07%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`vn_setops (41 samples, 0.07%)</title><rect x="605.1" y="97" width="0.8" height="15.0" fill="rgb(210,84,39)" rx="2" ry="2" />
<text text-anchor="" x="608.06" y="107.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`vn_recycle (33 samples, 0.06%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`vn_recycle (33 samples, 0.06%)</title><rect x="628.4" y="65" width="0.7" height="15.0" fill="rgb(212,29,26)" rx="2" ry="2" />
<text text-anchor="" x="631.42" y="75.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('lofs`lo_inactive (6,307 samples, 10.98%)')" onmouseout="c()" onclick="zoom(this)">
<title>lofs`lo_inactive (6,307 samples, 10.98%)</title><rect x="963.3" y="97" width="129.6" height="15.0" fill="rgb(205,84,7)" rx="2" ry="2" />
<text text-anchor="" x="966.33" y="107.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >lofs`lo_inactive</text>
</g>
<g class="func_g" onmouseover="s('lofs`table_lock_enter (220 samples, 0.38%)')" onmouseout="c()" onclick="zoom(this)">
<title>lofs`table_lock_enter (220 samples, 0.38%)</title><rect x="663.3" y="81" width="4.5" height="15.0" fill="rgb(254,47,23)" rx="2" ry="2" />
<text text-anchor="" x="666.28" y="91.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`cv_broadcast (25 samples, 0.04%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`cv_broadcast (25 samples, 0.04%)</title><rect x="180.1" y="193" width="0.5" height="15.0" fill="rgb(252,61,44)" rx="2" ry="2" />
<text text-anchor="" x="183.09" y="203.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`mutex_exit (358 samples, 0.62%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`mutex_exit (358 samples, 0.62%)</title><rect x="1043.1" y="65" width="7.4" height="15.0" fill="rgb(219,126,43)" rx="2" ry="2" />
<text text-anchor="" x="1046.12" y="75.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`kmem_cache_alloc (234 samples, 0.41%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`kmem_cache_alloc (234 samples, 0.41%)</title><rect x="618.0" y="81" width="4.8" height="15.0" fill="rgb(212,141,13)" rx="2" ry="2" />
<text text-anchor="" x="621.01" y="91.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`rw_enter (525 samples, 0.91%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`rw_enter (525 samples, 0.91%)</title><rect x="427.0" y="65" width="10.7" height="15.0" fill="rgb(244,160,2)" rx="2" ry="2" />
<text text-anchor="" x="429.96" y="75.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`membar_consumer (237 samples, 0.41%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`membar_consumer (237 samples, 0.41%)</title><rect x="674.3" y="81" width="4.9" height="15.0" fill="rgb(230,55,11)" rx="2" ry="2" />
<text text-anchor="" x="677.33" y="91.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`swtch (5 samples, 0.01%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`swtch (5 samples, 0.01%)</title><rect x="109.2" y="225" width="0.1" height="15.0" fill="rgb(229,130,31)" rx="2" ry="2" />
<text text-anchor="" x="112.24" y="235.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`rwst_enter_common (32 samples, 0.06%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`rwst_enter_common (32 samples, 0.06%)</title><rect x="456.1" y="65" width="0.7" height="15.0" fill="rgb(220,176,32)" rx="2" ry="2" />
<text text-anchor="" x="459.11" y="75.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('lofs`freelonode (5,313 samples, 9.25%)')" onmouseout="c()" onclick="zoom(this)">
<title>lofs`freelonode (5,313 samples, 9.25%)</title><rect x="966.8" y="81" width="109.1" height="15.0" fill="rgb(221,220,42)" rx="2" ry="2" />
<text text-anchor="" x="969.76" y="91.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >lofs`freelonode</text>
</g>
<g class="func_g" onmouseover="s('genunix`vn_openat (46,342 samples, 80.68%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`vn_openat (46,342 samples, 80.68%)</title><rect x="196.5" y="209" width="952.0" height="15.0" fill="rgb(209,93,50)" rx="2" ry="2" />
<text text-anchor="" x="199.51" y="219.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >genunix`vn_openat</text>
</g>
<g class="func_g" onmouseover="s('genunix`vn_rele (19 samples, 0.03%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`vn_rele (19 samples, 0.03%)</title><rect x="845.7" y="113" width="0.4" height="15.0" fill="rgb(214,32,17)" rx="2" ry="2" />
<text text-anchor="" x="848.70" y="123.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`proc_exit (5 samples, 0.01%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`proc_exit (5 samples, 0.01%)</title><rect x="1158.2" y="225" width="0.1" height="15.0" fill="rgb(252,28,3)" rx="2" ry="2" />
<text text-anchor="" x="1161.24" y="235.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`mutex_exit (512 samples, 0.89%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`mutex_exit (512 samples, 0.89%)</title><rect x="585.8" y="65" width="10.6" height="15.0" fill="rgb(211,166,49)" rx="2" ry="2" />
<text text-anchor="" x="588.84" y="75.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`kmem_free (35 samples, 0.06%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`kmem_free (35 samples, 0.06%)</title><rect x="524.7" y="65" width="0.7" height="15.0" fill="rgb(209,91,7)" rx="2" ry="2" />
<text text-anchor="" x="527.70" y="75.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`mutex_enter (252 samples, 0.44%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`mutex_enter (252 samples, 0.44%)</title><rect x="871.2" y="65" width="5.2" height="15.0" fill="rgb(245,115,6)" rx="2" ry="2" />
<text text-anchor="" x="874.20" y="75.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`rwst_exit (12 samples, 0.02%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`rwst_exit (12 samples, 0.02%)</title><rect x="844.3" y="113" width="0.2" height="15.0" fill="rgb(254,161,44)" rx="2" ry="2" />
<text text-anchor="" x="847.28" y="123.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`crgetuid (22 samples, 0.04%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`crgetuid (22 samples, 0.04%)</title><rect x="393.9" y="49" width="0.4" height="15.0" fill="rgb(247,109,47)" rx="2" ry="2" />
<text text-anchor="" x="396.86" y="59.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`kmem_free (17 samples, 0.03%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`kmem_free (17 samples, 0.03%)</title><rect x="898.7" y="97" width="0.3" height="15.0" fill="rgb(232,3,38)" rx="2" ry="2" />
<text text-anchor="" x="901.66" y="107.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`mutex_init (53 samples, 0.09%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`mutex_init (53 samples, 0.09%)</title><rect x="498.3" y="33" width="1.1" height="15.0" fill="rgb(251,75,45)" rx="2" ry="2" />
<text text-anchor="" x="501.26" y="43.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('ufs`ufs_iaccess (648 samples, 1.13%)')" onmouseout="c()" onclick="zoom(this)">
<title>ufs`ufs_iaccess (648 samples, 1.13%)</title><rect x="385.5" y="65" width="13.3" height="15.0" fill="rgb(225,207,54)" rx="2" ry="2" />
<text text-anchor="" x="388.46" y="75.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('all (57,441 samples, 100%)')" onmouseout="c()" onclick="zoom(this)">
<title>all (57,441 samples, 100%)</title><rect x="10.0" y="289" width="1180.0" height="15.0" fill="rgb(234,182,42)" rx="2" ry="2" />
<text text-anchor="" x="13.00" y="299.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`fop_inactive (6,689 samples, 11.64%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`fop_inactive (6,689 samples, 11.64%)</title><rect x="955.5" y="113" width="137.4" height="15.0" fill="rgb(250,9,27)" rx="2" ry="2" />
<text text-anchor="" x="958.48" y="123.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >genunix`fop_inact..</text>
</g>
<g class="func_g" onmouseover="s('genunix`kmem_cache_alloc (9 samples, 0.02%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`kmem_cache_alloc (9 samples, 0.02%)</title><rect x="175.3" y="209" width="0.2" height="15.0" fill="rgb(233,137,8)" rx="2" ry="2" />
<text text-anchor="" x="178.29" y="219.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`kmem_cache_free (184 samples, 0.32%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`kmem_cache_free (184 samples, 0.32%)</title><rect x="975.5" y="65" width="3.8" height="15.0" fill="rgb(212,182,34)" rx="2" ry="2" />
<text text-anchor="" x="978.53" y="75.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`pn_get_buf (13 samples, 0.02%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`pn_get_buf (13 samples, 0.02%)</title><rect x="1147.8" y="177" width="0.3" height="15.0" fill="rgb(216,84,2)" rx="2" ry="2" />
<text text-anchor="" x="1150.78" y="187.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`strlen (107 samples, 0.19%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`strlen (107 samples, 0.19%)</title><rect x="1116.1" y="129" width="2.2" height="15.0" fill="rgb(238,134,54)" rx="2" ry="2" />
<text text-anchor="" x="1119.11" y="139.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`mutex_exit (46 samples, 0.08%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`mutex_exit (46 samples, 0.08%)</title><rect x="154.8" y="161" width="1.0" height="15.0" fill="rgb(206,147,41)" rx="2" ry="2" />
<text text-anchor="" x="157.81" y="171.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`post_syscall (12 samples, 0.02%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`post_syscall (12 samples, 0.02%)</title><rect x="10.3" y="273" width="0.3" height="15.0" fill="rgb(243,35,40)" rx="2" ry="2" />
<text text-anchor="" x="13.35" y="283.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`mutex_init (38 samples, 0.07%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`mutex_init (38 samples, 0.07%)</title><rect x="881.4" y="65" width="0.8" height="15.0" fill="rgb(239,154,51)" rx="2" ry="2" />
<text text-anchor="" x="884.45" y="75.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`rw_exit (439 samples, 0.76%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`rw_exit (439 samples, 0.76%)</title><rect x="437.7" y="65" width="9.1" height="15.0" fill="rgb(222,170,41)" rx="2" ry="2" />
<text text-anchor="" x="440.74" y="75.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('lofs`lo_lookup (65 samples, 0.11%)')" onmouseout="c()" onclick="zoom(this)">
<title>lofs`lo_lookup (65 samples, 0.11%)</title><rect x="1095.2" y="129" width="1.3" height="15.0" fill="rgb(209,132,29)" rx="2" ry="2" />
<text text-anchor="" x="1098.17" y="139.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`clear_stale_fd (44 samples, 0.08%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`clear_stale_fd (44 samples, 0.08%)</title><rect x="33.5" y="241" width="0.9" height="15.0" fill="rgb(206,68,8)" rx="2" ry="2" />
<text text-anchor="" x="36.52" y="251.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`mutex_enter (238 samples, 0.41%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`mutex_enter (238 samples, 0.41%)</title><rect x="857.0" y="81" width="4.9" height="15.0" fill="rgb(245,109,36)" rx="2" ry="2" />
<text text-anchor="" x="859.98" y="91.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`pn_get_buf (687 samples, 1.20%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`pn_get_buf (687 samples, 1.20%)</title><rect x="1123.1" y="161" width="14.1" height="15.0" fill="rgb(229,141,32)" rx="2" ry="2" />
<text text-anchor="" x="1126.13" y="171.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`vn_free (1,663 samples, 2.90%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`vn_free (1,663 samples, 2.90%)</title><rect x="981.7" y="65" width="34.1" height="15.0" fill="rgb(244,189,40)" rx="2" ry="2" />
<text text-anchor="" x="984.68" y="75.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >ge..</text>
</g>
<g class="func_g" onmouseover="s('unix`mutex_enter (980 samples, 1.71%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`mutex_enter (980 samples, 1.71%)</title><rect x="399.6" y="65" width="20.2" height="15.0" fill="rgb(236,72,53)" rx="2" ry="2" />
<text text-anchor="" x="402.64" y="75.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`crhold (5 samples, 0.01%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`crhold (5 samples, 0.01%)</title><rect x="146.7" y="209" width="0.1" height="15.0" fill="rgb(223,214,29)" rx="2" ry="2" />
<text text-anchor="" x="149.69" y="219.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`mutex_exit (59 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`mutex_exit (59 samples, 0.10%)</title><rect x="843.1" y="97" width="1.2" height="15.0" fill="rgb(232,207,41)" rx="2" ry="2" />
<text text-anchor="" x="846.07" y="107.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`vn_reinit (48 samples, 0.08%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`vn_reinit (48 samples, 0.08%)</title><rect x="648.3" y="81" width="1.0" height="15.0" fill="rgb(224,145,2)" rx="2" ry="2" />
<text text-anchor="" x="651.29" y="91.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`vfs_getops (21 samples, 0.04%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`vfs_getops (21 samples, 0.04%)</title><rect x="596.4" y="97" width="0.4" height="15.0" fill="rgb(239,182,34)" rx="2" ry="2" />
<text text-anchor="" x="599.35" y="107.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`open (49,669 samples, 86.47%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`open (49,669 samples, 86.47%)</title><rect x="137.5" y="257" width="1020.4" height="15.0" fill="rgb(245,165,45)" rx="2" ry="2" />
<text text-anchor="" x="140.51" y="267.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >genunix`open</text>
</g>
<g class="func_g" onmouseover="s('genunix`kmem_cache_alloc (39 samples, 0.07%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`kmem_cache_alloc (39 samples, 0.07%)</title><rect x="446.8" y="97" width="0.8" height="15.0" fill="rgb(239,10,28)" rx="2" ry="2" />
<text text-anchor="" x="449.76" y="107.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`vn_vfslocks_getlock (79 samples, 0.14%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`vn_vfslocks_getlock (79 samples, 0.14%)</title><rect x="901.7" y="97" width="1.6" height="15.0" fill="rgb(234,13,17)" rx="2" ry="2" />
<text text-anchor="" x="904.68" y="107.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`clear_int_flag (39 samples, 0.07%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`clear_int_flag (39 samples, 0.07%)</title><rect x="35.7" y="225" width="0.8" height="15.0" fill="rgb(233,184,25)" rx="2" ry="2" />
<text text-anchor="" x="38.72" y="235.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`kmem_cache_free (215 samples, 0.37%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`kmem_cache_free (215 samples, 0.37%)</title><rect x="984.2" y="49" width="4.5" height="15.0" fill="rgb(212,35,12)" rx="2" ry="2" />
<text text-anchor="" x="987.24" y="59.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`mutex_destroy (53 samples, 0.09%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`mutex_destroy (53 samples, 0.09%)</title><rect x="920.4" y="65" width="1.1" height="15.0" fill="rgb(249,123,33)" rx="2" ry="2" />
<text text-anchor="" x="923.42" y="75.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`vn_vfsunlock (3,578 samples, 6.23%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`vn_vfsunlock (3,578 samples, 6.23%)</title><rect x="522.9" y="81" width="73.5" height="15.0" fill="rgb(223,210,44)" rx="2" ry="2" />
<text text-anchor="" x="525.85" y="91.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >genunix`..</text>
</g>
<g class="func_g" onmouseover="s('genunix`dnlc_lookup (1,843 samples, 3.21%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`dnlc_lookup (1,843 samples, 3.21%)</title><rect x="345.3" y="65" width="37.9" height="15.0" fill="rgb(209,187,22)" rx="2" ry="2" />
<text text-anchor="" x="348.32" y="75.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >gen..</text>
</g>
<g class="func_g" onmouseover="s('genunix`lookupnameatcred (45,978 samples, 80.04%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`lookupnameatcred (45,978 samples, 80.04%)</title><rect x="203.0" y="177" width="944.5" height="15.0" fill="rgb(220,213,18)" rx="2" ry="2" />
<text text-anchor="" x="206.02" y="187.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >genunix`lookupnameatcred</text>
</g>
<g class="func_g" onmouseover="s('genunix`crgetmapped (41 samples, 0.07%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`crgetmapped (41 samples, 0.07%)</title><rect x="961.8" y="97" width="0.8" height="15.0" fill="rgb(241,216,23)" rx="2" ry="2" />
<text text-anchor="" x="964.77" y="107.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`anon_zero (7 samples, 0.01%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`anon_zero (7 samples, 0.01%)</title><rect x="12.5" y="177" width="0.1" height="15.0" fill="rgb(237,222,42)" rx="2" ry="2" />
<text text-anchor="" x="15.47" y="187.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`rwst_tryenter (628 samples, 1.09%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`rwst_tryenter (628 samples, 1.09%)</title><rect x="850.0" y="97" width="12.9" height="15.0" fill="rgb(254,105,34)" rx="2" ry="2" />
<text text-anchor="" x="852.98" y="107.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`mutex_enter (309 samples, 0.54%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`mutex_enter (309 samples, 0.54%)</title><rect x="813.6" y="97" width="6.3" height="15.0" fill="rgb(223,53,37)" rx="2" ry="2" />
<text text-anchor="" x="816.59" y="107.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`vn_rele (14 samples, 0.02%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`vn_rele (14 samples, 0.02%)</title><rect x="333.7" y="81" width="0.3" height="15.0" fill="rgb(234,123,51)" rx="2" ry="2" />
<text text-anchor="" x="336.69" y="91.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`vn_setpath (1,969 samples, 3.43%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`vn_setpath (1,969 samples, 3.43%)</title><rect x="255.2" y="113" width="40.5" height="15.0" fill="rgb(207,29,19)" rx="2" ry="2" />
<text text-anchor="" x="258.24" y="123.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >gen..</text>
</g>
<g class="func_g" onmouseover="s('unix`mutex_enter (111 samples, 0.19%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`mutex_enter (111 samples, 0.19%)</title><rect x="186.4" y="161" width="2.2" height="15.0" fill="rgb(226,32,41)" rx="2" ry="2" />
<text text-anchor="" x="189.36" y="171.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`cv_broadcast (40 samples, 0.07%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`cv_broadcast (40 samples, 0.07%)</title><rect x="528.7" y="49" width="0.8" height="15.0" fill="rgb(205,172,3)" rx="2" ry="2" />
<text text-anchor="" x="531.69" y="59.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`kmem_cache_alloc (66 samples, 0.11%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`kmem_cache_alloc (66 samples, 0.11%)</title><rect x="150.9" y="161" width="1.4" height="15.0" fill="rgb(213,11,38)" rx="2" ry="2" />
<text text-anchor="" x="153.94" y="171.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`audit_getstate (21 samples, 0.04%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`audit_getstate (21 samples, 0.04%)</title><rect x="221.6" y="129" width="0.4" height="15.0" fill="rgb(241,187,17)" rx="2" ry="2" />
<text text-anchor="" x="224.61" y="139.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`vn_setpath (58 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`vn_setpath (58 samples, 0.10%)</title><rect x="1093.3" y="129" width="1.2" height="15.0" fill="rgb(246,88,46)" rx="2" ry="2" />
<text text-anchor="" x="1096.33" y="139.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`open (17 samples, 0.03%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`open (17 samples, 0.03%)</title><rect x="10.0" y="273" width="0.3" height="15.0" fill="rgb(222,212,9)" rx="2" ry="2" />
<text text-anchor="" x="13.00" y="283.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`bcopy (896 samples, 1.56%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`bcopy (896 samples, 1.56%)</title><rect x="705.0" y="113" width="18.4" height="15.0" fill="rgb(247,92,11)" rx="2" ry="2" />
<text text-anchor="" x="708.03" y="123.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`mutex_enter (99 samples, 0.17%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`mutex_enter (99 samples, 0.17%)</title><rect x="171.9" y="193" width="2.0" height="15.0" fill="rgb(213,180,51)" rx="2" ry="2" />
<text text-anchor="" x="174.90" y="203.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`traverse (5,557 samples, 9.67%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`traverse (5,557 samples, 9.67%)</title><rect x="835.7" y="129" width="114.1" height="15.0" fill="rgb(218,13,37)" rx="2" ry="2" />
<text text-anchor="" x="838.66" y="139.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >genunix`traverse</text>
</g>
<g class="func_g" onmouseover="s('genunix`pn_getcomponent (41 samples, 0.07%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`pn_getcomponent (41 samples, 0.07%)</title><rect x="1119.0" y="145" width="0.9" height="15.0" fill="rgb(253,186,34)" rx="2" ry="2" />
<text text-anchor="" x="1122.05" y="155.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`mutex_enter (640 samples, 1.11%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`mutex_enter (640 samples, 1.11%)</title><rect x="679.2" y="81" width="13.2" height="15.0" fill="rgb(231,183,12)" rx="2" ry="2" />
<text text-anchor="" x="682.20" y="91.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`mutex_destroy (176 samples, 0.31%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`mutex_destroy (176 samples, 0.31%)</title><rect x="556.9" y="33" width="3.6" height="15.0" fill="rgb(209,85,19)" rx="2" ry="2" />
<text text-anchor="" x="559.89" y="43.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`lwp_getdatamodel (6 samples, 0.01%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`lwp_getdatamodel (6 samples, 0.01%)</title><rect x="108.9" y="241" width="0.2" height="15.0" fill="rgb(246,22,47)" rx="2" ry="2" />
<text text-anchor="" x="111.93" y="251.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`unfalloc (39 samples, 0.07%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`unfalloc (39 samples, 0.07%)</title><rect x="1156.8" y="225" width="0.8" height="15.0" fill="rgb(231,46,33)" rx="2" ry="2" />
<text text-anchor="" x="1159.76" y="235.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`syscall_mstate (355 samples, 0.62%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`syscall_mstate (355 samples, 0.62%)</title><rect x="13.7" y="257" width="7.3" height="15.0" fill="rgb(222,65,49)" rx="2" ry="2" />
<text text-anchor="" x="16.74" y="267.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`cv_init (65 samples, 0.11%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`cv_init (65 samples, 0.11%)</title><rect x="496.9" y="33" width="1.4" height="15.0" fill="rgb(221,113,21)" rx="2" ry="2" />
<text text-anchor="" x="499.93" y="43.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`mutex_enter (95 samples, 0.17%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`mutex_enter (95 samples, 0.17%)</title><rect x="841.1" y="97" width="2.0" height="15.0" fill="rgb(205,40,54)" rx="2" ry="2" />
<text text-anchor="" x="844.12" y="107.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`bcmp (42 samples, 0.07%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`bcmp (42 samples, 0.07%)</title><rect x="398.8" y="65" width="0.8" height="15.0" fill="rgb(210,85,17)" rx="2" ry="2" />
<text text-anchor="" x="401.77" y="75.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`mutex_exit (350 samples, 0.61%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`mutex_exit (350 samples, 0.61%)</title><rect x="419.8" y="65" width="7.2" height="15.0" fill="rgb(226,63,15)" rx="2" ry="2" />
<text text-anchor="" x="422.77" y="75.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`kmem_free (288 samples, 0.50%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`kmem_free (288 samples, 0.50%)</title><rect x="183.9" y="177" width="5.9" height="15.0" fill="rgb(233,187,43)" rx="2" ry="2" />
<text text-anchor="" x="186.85" y="187.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`mutex_exit (58 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`mutex_exit (58 samples, 0.10%)</title><rect x="173.9" y="193" width="1.2" height="15.0" fill="rgb(248,22,29)" rx="2" ry="2" />
<text text-anchor="" x="176.93" y="203.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`kmem_alloc (32 samples, 0.06%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`kmem_alloc (32 samples, 0.06%)</title><rect x="455.5" y="65" width="0.6" height="15.0" fill="rgb(215,132,47)" rx="2" ry="2" />
<text text-anchor="" x="458.45" y="75.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`mutex_exit (356 samples, 0.62%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`mutex_exit (356 samples, 0.62%)</title><rect x="938.8" y="97" width="7.3" height="15.0" fill="rgb(232,181,19)" rx="2" ry="2" />
<text text-anchor="" x="941.76" y="107.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`mutex_init (46 samples, 0.08%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`mutex_init (46 samples, 0.08%)</title><rect x="499.4" y="49" width="0.9" height="15.0" fill="rgb(223,197,16)" rx="2" ry="2" />
<text text-anchor="" x="502.35" y="59.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`rwst_init (173 samples, 0.30%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`rwst_init (173 samples, 0.30%)</title><rect x="878.7" y="81" width="3.5" height="15.0" fill="rgb(212,93,42)" rx="2" ry="2" />
<text text-anchor="" x="881.67" y="91.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`rwst_enter_common (28 samples, 0.05%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`rwst_enter_common (28 samples, 0.05%)</title><rect x="849.1" y="97" width="0.6" height="15.0" fill="rgb(233,0,32)" rx="2" ry="2" />
<text text-anchor="" x="852.13" y="107.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`openat (49,647 samples, 86.43%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`openat (49,647 samples, 86.43%)</title><rect x="138.0" y="241" width="1019.9" height="15.0" fill="rgb(228,97,16)" rx="2" ry="2" />
<text text-anchor="" x="140.96" y="251.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >genunix`openat</text>
</g>
<g class="func_g" onmouseover="s('unix`mutex_enter (303 samples, 0.53%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`mutex_enter (303 samples, 0.53%)</title><rect x="1148.5" y="209" width="6.2" height="15.0" fill="rgb(240,210,53)" rx="2" ry="2" />
<text text-anchor="" x="1151.50" y="219.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('lofs`lfind (278 samples, 0.48%)')" onmouseout="c()" onclick="zoom(this)">
<title>lofs`lfind (278 samples, 0.48%)</title><rect x="652.6" y="81" width="5.7" height="15.0" fill="rgb(240,139,26)" rx="2" ry="2" />
<text text-anchor="" x="655.56" y="91.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`mutex_exit (90 samples, 0.16%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`mutex_exit (90 samples, 0.16%)</title><rect x="916.7" y="65" width="1.8" height="15.0" fill="rgb(208,14,49)" rx="2" ry="2" />
<text text-anchor="" x="919.66" y="75.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`cv_init (49 samples, 0.09%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`cv_init (49 samples, 0.09%)</title><rect x="880.4" y="65" width="1.0" height="15.0" fill="rgb(214,193,27)" rx="2" ry="2" />
<text text-anchor="" x="883.44" y="75.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`tsc_gethrtimeunscaled (43 samples, 0.07%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`tsc_gethrtimeunscaled (43 samples, 0.07%)</title><rect x="1176.2" y="225" width="0.8" height="15.0" fill="rgb(212,61,3)" rx="2" ry="2" />
<text text-anchor="" x="1179.15" y="235.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`rwst_tryenter (32 samples, 0.06%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`rwst_tryenter (32 samples, 0.06%)</title><rect x="451.0" y="81" width="0.7" height="15.0" fill="rgb(247,126,29)" rx="2" ry="2" />
<text text-anchor="" x="454.01" y="91.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`pn_fixslash (14 samples, 0.02%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`pn_fixslash (14 samples, 0.02%)</title><rect x="1118.8" y="145" width="0.2" height="15.0" fill="rgb(247,6,7)" rx="2" ry="2" />
<text text-anchor="" x="1121.76" y="155.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`gethrtime_unscaled (420 samples, 0.73%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`gethrtime_unscaled (420 samples, 0.73%)</title><rect x="1175.9" y="241" width="8.7" height="15.0" fill="rgb(215,153,45)" rx="2" ry="2" />
<text text-anchor="" x="1178.95" y="251.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`post_syscall (4,245 samples, 7.39%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`post_syscall (4,245 samples, 7.39%)</title><rect x="24.9" y="257" width="87.2" height="15.0" fill="rgb(251,42,41)" rx="2" ry="2" />
<text text-anchor="" x="27.89" y="267.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >genunix`po..</text>
</g>
<g class="func_g" onmouseover="s('genunix`kmem_zalloc (280 samples, 0.49%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`kmem_zalloc (280 samples, 0.49%)</title><rect x="150.0" y="177" width="5.8" height="15.0" fill="rgb(226,151,28)" rx="2" ry="2" />
<text text-anchor="" x="153.00" y="187.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`vn_alloc (20 samples, 0.03%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`vn_alloc (20 samples, 0.03%)</title><rect x="603.7" y="97" width="0.4" height="15.0" fill="rgb(254,75,22)" rx="2" ry="2" />
<text text-anchor="" x="606.69" y="107.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`vn_mountedvfs (43 samples, 0.07%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`vn_mountedvfs (43 samples, 0.07%)</title><rect x="949.8" y="129" width="0.9" height="15.0" fill="rgb(244,118,1)" rx="2" ry="2" />
<text text-anchor="" x="952.81" y="139.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`audit_getstate (15 samples, 0.03%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`audit_getstate (15 samples, 0.03%)</title><rect x="33.2" y="241" width="0.3" height="15.0" fill="rgb(254,128,15)" rx="2" ry="2" />
<text text-anchor="" x="36.21" y="251.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('zfs`zfs_lookup (22 samples, 0.04%)')" onmouseout="c()" onclick="zoom(this)">
<title>zfs`zfs_lookup (22 samples, 0.04%)</title><rect x="1118.3" y="129" width="0.5" height="15.0" fill="rgb(237,44,25)" rx="2" ry="2" />
<text text-anchor="" x="1121.31" y="139.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`crgetuid (6 samples, 0.01%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`crgetuid (6 samples, 0.01%)</title><rect x="807.8" y="97" width="0.2" height="15.0" fill="rgb(250,226,10)" rx="2" ry="2" />
<text text-anchor="" x="810.84" y="107.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`copystr (598 samples, 1.04%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`copystr (598 samples, 1.04%)</title><rect x="1125.0" y="145" width="12.2" height="15.0" fill="rgb(231,208,19)" rx="2" ry="2" />
<text text-anchor="" x="1127.96" y="155.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`i_ddi_splhigh (23 samples, 0.04%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`i_ddi_splhigh (23 samples, 0.04%)</title><rect x="91.9" y="241" width="0.5" height="15.0" fill="rgb(238,76,22)" rx="2" ry="2" />
<text text-anchor="" x="94.90" y="251.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`trap (13 samples, 0.02%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`trap (13 samples, 0.02%)</title><rect x="12.4" y="257" width="0.3" height="15.0" fill="rgb(234,30,10)" rx="2" ry="2" />
<text text-anchor="" x="15.42" y="267.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`audit_getstate (27 samples, 0.05%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`audit_getstate (27 samples, 0.05%)</title><rect x="23.6" y="257" width="0.5" height="15.0" fill="rgb(217,76,35)" rx="2" ry="2" />
<text text-anchor="" x="26.58" y="267.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`vn_mountedvfs (56 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`vn_mountedvfs (56 samples, 0.10%)</title><rect x="1120.2" y="145" width="1.2" height="15.0" fill="rgb(241,90,54)" rx="2" ry="2" />
<text text-anchor="" x="1123.24" y="155.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`mutex_destroy (17 samples, 0.03%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`mutex_destroy (17 samples, 0.03%)</title><rect x="921.5" y="81" width="0.4" height="15.0" fill="rgb(235,224,46)" rx="2" ry="2" />
<text text-anchor="" x="924.51" y="91.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`cv_broadcast (14 samples, 0.02%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`cv_broadcast (14 samples, 0.02%)</title><rect x="898.4" y="97" width="0.3" height="15.0" fill="rgb(237,1,17)" rx="2" ry="2" />
<text text-anchor="" x="901.37" y="107.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`segvn_fault (11 samples, 0.02%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`segvn_fault (11 samples, 0.02%)</title><rect x="12.4" y="209" width="0.3" height="15.0" fill="rgb(208,154,37)" rx="2" ry="2" />
<text text-anchor="" x="15.44" y="219.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`vn_rele (39 samples, 0.07%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`vn_rele (39 samples, 0.07%)</title><rect x="384.7" y="65" width="0.8" height="15.0" fill="rgb(231,129,14)" rx="2" ry="2" />
<text text-anchor="" x="387.66" y="75.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`kmem_free (457 samples, 0.80%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`kmem_free (457 samples, 0.80%)</title><rect x="909.1" y="81" width="9.4" height="15.0" fill="rgb(240,102,3)" rx="2" ry="2" />
<text text-anchor="" x="912.12" y="91.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`vn_vfsunlock (20 samples, 0.03%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`vn_vfsunlock (20 samples, 0.03%)</title><rect x="1094.8" y="129" width="0.4" height="15.0" fill="rgb(226,211,34)" rx="2" ry="2" />
<text text-anchor="" x="1097.76" y="139.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`vn_vfslocks_rele (34 samples, 0.06%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`vn_vfslocks_rele (34 samples, 0.06%)</title><rect x="847.1" y="113" width="0.7" height="15.0" fill="rgb(237,134,28)" rx="2" ry="2" />
<text text-anchor="" x="850.06" y="123.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`atomic_cas_64 (318 samples, 0.55%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`atomic_cas_64 (318 samples, 0.55%)</title><rect x="667.8" y="81" width="6.5" height="15.0" fill="rgb(231,163,23)" rx="2" ry="2" />
<text text-anchor="" x="670.80" y="91.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`mutex_enter (337 samples, 0.59%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`mutex_enter (337 samples, 0.59%)</title><rect x="463.3" y="49" width="7.0" height="15.0" fill="rgb(223,66,40)" rx="2" ry="2" />
<text text-anchor="" x="466.34" y="59.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`do_splx (31 samples, 0.05%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`do_splx (31 samples, 0.05%)</title><rect x="91.3" y="241" width="0.6" height="15.0" fill="rgb(238,62,27)" rx="2" ry="2" />
<text text-anchor="" x="94.27" y="251.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`ufalloc_file (20 samples, 0.03%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`ufalloc_file (20 samples, 0.03%)</title><rect x="168.7" y="193" width="0.4" height="15.0" fill="rgb(250,54,26)" rx="2" ry="2" />
<text text-anchor="" x="171.73" y="203.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`fd_reserve (35 samples, 0.06%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`fd_reserve (35 samples, 0.06%)</title><rect x="180.6" y="193" width="0.7" height="15.0" fill="rgb(248,30,36)" rx="2" ry="2" />
<text text-anchor="" x="183.61" y="203.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`copen (49,444 samples, 86.08%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`copen (49,444 samples, 86.08%)</title><rect x="139.8" y="225" width="1015.7" height="15.0" fill="rgb(215,150,8)" rx="2" ry="2" />
<text text-anchor="" x="142.79" y="235.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >genunix`copen</text>
</g>
<g class="func_g" onmouseover="s('unix`mutex_enter (279 samples, 0.49%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`mutex_enter (279 samples, 0.49%)</title><rect x="1082.8" y="81" width="5.7" height="15.0" fill="rgb(228,71,50)" rx="2" ry="2" />
<text text-anchor="" x="1085.81" y="91.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`0xfffffffffb800c91 (4,361 samples, 7.59%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`0xfffffffffb800c91 (4,361 samples, 7.59%)</title><rect x="23.3" y="273" width="89.6" height="15.0" fill="rgb(237,84,0)" rx="2" ry="2" />
<text text-anchor="" x="26.29" y="283.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >unix`0xfff..</text>
</g>
<g class="func_g" onmouseover="s('genunix`crgetmapped (55 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`crgetmapped (55 samples, 0.10%)</title><rect x="222.0" y="129" width="1.2" height="15.0" fill="rgb(237,4,9)" rx="2" ry="2" />
<text text-anchor="" x="225.04" y="139.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`cv_init (56 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`cv_init (56 samples, 0.10%)</title><rect x="866.2" y="81" width="1.2" height="15.0" fill="rgb(250,5,38)" rx="2" ry="2" />
<text text-anchor="" x="869.22" y="91.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`dnlc_lookup (26 samples, 0.05%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`dnlc_lookup (26 samples, 0.05%)</title><rect x="250.3" y="113" width="0.5" height="15.0" fill="rgb(215,48,53)" rx="2" ry="2" />
<text text-anchor="" x="253.27" y="123.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`kmem_alloc (11 samples, 0.02%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`kmem_alloc (11 samples, 0.02%)</title><rect x="848.9" y="97" width="0.2" height="15.0" fill="rgb(232,8,25)" rx="2" ry="2" />
<text text-anchor="" x="851.91" y="107.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`cv_init (53 samples, 0.09%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`cv_init (53 samples, 0.09%)</title><rect x="476.5" y="49" width="1.1" height="15.0" fill="rgb(254,91,51)" rx="2" ry="2" />
<text text-anchor="" x="479.49" y="59.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`copyinstr (25 samples, 0.04%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`copyinstr (25 samples, 0.04%)</title><rect x="1124.4" y="145" width="0.6" height="15.0" fill="rgb(224,213,13)" rx="2" ry="2" />
<text text-anchor="" x="1127.45" y="155.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`gethrtime_unscaled (203 samples, 0.35%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`gethrtime_unscaled (203 samples, 0.35%)</title><rect x="16.6" y="241" width="4.2" height="15.0" fill="rgb(227,216,48)" rx="2" ry="2" />
<text text-anchor="" x="19.59" y="251.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`kmem_cache_alloc (11 samples, 0.02%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`kmem_cache_alloc (11 samples, 0.02%)</title><rect x="149.8" y="177" width="0.2" height="15.0" fill="rgb(205,33,21)" rx="2" ry="2" />
<text text-anchor="" x="152.77" y="187.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`vn_free (26 samples, 0.05%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`vn_free (26 samples, 0.05%)</title><rect x="965.3" y="81" width="0.5" height="15.0" fill="rgb(207,173,48)" rx="2" ry="2" />
<text text-anchor="" x="968.30" y="91.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`mutex_exit (149 samples, 0.26%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`mutex_exit (149 samples, 0.26%)</title><rect x="292.0" y="81" width="3.0" height="15.0" fill="rgb(207,206,6)" rx="2" ry="2" />
<text text-anchor="" x="294.97" y="91.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`vn_recycle (319 samples, 0.56%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`vn_recycle (319 samples, 0.56%)</title><rect x="631.0" y="49" width="6.5" height="15.0" fill="rgb(250,202,8)" rx="2" ry="2" />
<text text-anchor="" x="633.97" y="59.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`vn_rele (64 samples, 0.11%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`vn_rele (64 samples, 0.11%)</title><rect x="1016.8" y="65" width="1.3" height="15.0" fill="rgb(253,45,27)" rx="2" ry="2" />
<text text-anchor="" x="1019.80" y="75.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`bcmp (11 samples, 0.02%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`bcmp (11 samples, 0.02%)</title><rect x="813.4" y="97" width="0.2" height="15.0" fill="rgb(207,14,10)" rx="2" ry="2" />
<text text-anchor="" x="816.37" y="107.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`kmem_cache_free (154 samples, 0.27%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`kmem_cache_free (154 samples, 0.27%)</title><rect x="541.7" y="33" width="3.2" height="15.0" fill="rgb(244,99,40)" rx="2" ry="2" />
<text text-anchor="" x="544.73" y="43.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`lock_clear_splx (28 samples, 0.05%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`lock_clear_splx (28 samples, 0.05%)</title><rect x="92.4" y="241" width="0.6" height="15.0" fill="rgb(223,140,46)" rx="2" ry="2" />
<text text-anchor="" x="95.38" y="251.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`unfalloc (729 samples, 1.27%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`unfalloc (729 samples, 1.27%)</title><rect x="181.5" y="209" width="15.0" height="15.0" fill="rgb(212,171,37)" rx="2" ry="2" />
<text text-anchor="" x="184.53" y="219.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`fop_lookup (85 samples, 0.15%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`fop_lookup (85 samples, 0.15%)</title><rect x="250.8" y="113" width="1.7" height="15.0" fill="rgb(243,132,49)" rx="2" ry="2" />
<text text-anchor="" x="253.80" y="123.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('zfs`specvp_check (10 samples, 0.02%)')" onmouseout="c()" onclick="zoom(this)">
<title>zfs`specvp_check (10 samples, 0.02%)</title><rect x="804.4" y="113" width="0.2" height="15.0" fill="rgb(221,54,13)" rx="2" ry="2" />
<text text-anchor="" x="807.43" y="123.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`lookupnameatcred (22 samples, 0.04%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`lookupnameatcred (22 samples, 0.04%)</title><rect x="1148.1" y="193" width="0.4" height="15.0" fill="rgb(209,140,13)" rx="2" ry="2" />
<text text-anchor="" x="1151.05" y="203.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`tsc_read (367 samples, 0.64%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`tsc_read (367 samples, 0.64%)</title><rect x="1177.0" y="225" width="7.6" height="15.0" fill="rgb(214,65,44)" rx="2" ry="2" />
<text text-anchor="" x="1180.04" y="235.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`memcmp (38 samples, 0.07%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`memcmp (38 samples, 0.07%)</title><rect x="812.6" y="65" width="0.8" height="15.0" fill="rgb(214,80,8)" rx="2" ry="2" />
<text text-anchor="" x="815.59" y="75.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`splx (6 samples, 0.01%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`splx (6 samples, 0.01%)</title><rect x="112.0" y="241" width="0.1" height="15.0" fill="rgb(242,155,24)" rx="2" ry="2" />
<text text-anchor="" x="114.97" y="251.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`mutex_exit (95 samples, 0.17%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`mutex_exit (95 samples, 0.17%)</title><rect x="876.4" y="65" width="1.9" height="15.0" fill="rgb(247,60,11)" rx="2" ry="2" />
<text text-anchor="" x="879.37" y="75.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`gethrtime_unscaled (7 samples, 0.01%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`gethrtime_unscaled (7 samples, 0.01%)</title><rect x="112.9" y="257" width="0.2" height="15.0" fill="rgb(247,19,51)" rx="2" ry="2" />
<text text-anchor="" x="115.94" y="267.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`rwst_init (13 samples, 0.02%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`rwst_init (13 samples, 0.02%)</title><rect x="849.7" y="97" width="0.3" height="15.0" fill="rgb(245,206,5)" rx="2" ry="2" />
<text text-anchor="" x="852.71" y="107.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`audit_getstate (31 samples, 0.05%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`audit_getstate (31 samples, 0.05%)</title><rect x="179.5" y="193" width="0.6" height="15.0" fill="rgb(241,47,37)" rx="2" ry="2" />
<text text-anchor="" x="182.46" y="203.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`kmem_cache_alloc (32 samples, 0.06%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`kmem_cache_alloc (32 samples, 0.06%)</title><rect x="295.0" y="97" width="0.7" height="15.0" fill="rgb(224,125,48)" rx="2" ry="2" />
<text text-anchor="" x="298.03" y="107.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`disp_lock_exit (2,096 samples, 3.65%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`disp_lock_exit (2,096 samples, 3.65%)</title><rect x="34.4" y="241" width="43.1" height="15.0" fill="rgb(233,191,28)" rx="2" ry="2" />
<text text-anchor="" x="37.43" y="251.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >genu..</text>
</g>
<g class="func_g" onmouseover="s('unix`mutex_exit (49 samples, 0.09%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`mutex_exit (49 samples, 0.09%)</title><rect x="861.9" y="81" width="1.0" height="15.0" fill="rgb(231,106,51)" rx="2" ry="2" />
<text text-anchor="" x="864.87" y="91.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`copyinstr (18 samples, 0.03%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`copyinstr (18 samples, 0.03%)</title><rect x="1137.2" y="161" width="0.4" height="15.0" fill="rgb(252,20,2)" rx="2" ry="2" />
<text text-anchor="" x="1140.25" y="171.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('ufs`ufs_lookup (46 samples, 0.08%)')" onmouseout="c()" onclick="zoom(this)">
<title>ufs`ufs_lookup (46 samples, 0.08%)</title><rect x="696.1" y="97" width="0.9" height="15.0" fill="rgb(223,173,21)" rx="2" ry="2" />
<text text-anchor="" x="699.07" y="107.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`clear_stale_fd (10 samples, 0.02%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`clear_stale_fd (10 samples, 0.02%)</title><rect x="24.1" y="257" width="0.2" height="15.0" fill="rgb(223,156,13)" rx="2" ry="2" />
<text text-anchor="" x="27.13" y="267.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`rwst_destroy (296 samples, 0.52%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`rwst_destroy (296 samples, 0.52%)</title><rect x="554.4" y="49" width="6.1" height="15.0" fill="rgb(239,87,40)" rx="2" ry="2" />
<text text-anchor="" x="557.43" y="59.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`syscall_mstate (1,336 samples, 2.33%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`syscall_mstate (1,336 samples, 2.33%)</title><rect x="1158.3" y="257" width="27.5" height="15.0" fill="rgb(239,185,30)" rx="2" ry="2" />
<text text-anchor="" x="1161.34" y="267.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >g..</text>
</g>
<g class="func_g" onmouseover="s('genunix`kmem_alloc (934 samples, 1.63%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`kmem_alloc (934 samples, 1.63%)</title><rect x="275.8" y="97" width="19.2" height="15.0" fill="rgb(250,123,35)" rx="2" ry="2" />
<text text-anchor="" x="278.84" y="107.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`atomic_add_32 (325 samples, 0.57%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`atomic_add_32 (325 samples, 0.57%)</title><rect x="697.0" y="97" width="6.7" height="15.0" fill="rgb(221,31,30)" rx="2" ry="2" />
<text text-anchor="" x="700.01" y="107.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`mutex_enter (947 samples, 1.65%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`mutex_enter (947 samples, 1.65%)</title><rect x="723.4" y="113" width="19.5" height="15.0" fill="rgb(228,167,54)" rx="2" ry="2" />
<text text-anchor="" x="726.43" y="123.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`mutex_exit (56 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`mutex_exit (56 samples, 0.10%)</title><rect x="195.4" y="193" width="1.1" height="15.0" fill="rgb(237,18,19)" rx="2" ry="2" />
<text text-anchor="" x="198.36" y="203.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`mutex_enter (318 samples, 0.55%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`mutex_enter (318 samples, 0.55%)</title><rect x="637.8" y="65" width="6.5" height="15.0" fill="rgb(231,78,54)" rx="2" ry="2" />
<text text-anchor="" x="640.81" y="75.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('lofs`lo_root (80 samples, 0.14%)')" onmouseout="c()" onclick="zoom(this)">
<title>lofs`lo_root (80 samples, 0.14%)</title><rect x="839.5" y="97" width="1.6" height="15.0" fill="rgb(247,129,52)" rx="2" ry="2" />
<text text-anchor="" x="842.48" y="107.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`lookuppnvp (44,242 samples, 77.02%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`lookuppnvp (44,242 samples, 77.02%)</title><rect x="209.9" y="145" width="908.9" height="15.0" fill="rgb(212,166,3)" rx="2" ry="2" />
<text text-anchor="" x="212.90" y="155.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >genunix`lookuppnvp</text>
</g>
<g class="func_g" onmouseover="s('genunix`lookupnameat (46,075 samples, 80.21%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`lookupnameat (46,075 samples, 80.21%)</title><rect x="201.5" y="193" width="946.6" height="15.0" fill="rgb(246,102,0)" rx="2" ry="2" />
<text text-anchor="" x="204.54" y="203.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >genunix`lookupnameat</text>
</g>
<g class="func_g" onmouseover="s('unix`setbackdq (5 samples, 0.01%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`setbackdq (5 samples, 0.01%)</title><rect x="109.1" y="209" width="0.1" height="15.0" fill="rgb(241,157,31)" rx="2" ry="2" />
<text text-anchor="" x="112.12" y="219.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('lofs`lo_root (31 samples, 0.05%)')" onmouseout="c()" onclick="zoom(this)">
<title>lofs`lo_root (31 samples, 0.05%)</title><rect x="946.1" y="113" width="0.6" height="15.0" fill="rgb(225,57,16)" rx="2" ry="2" />
<text text-anchor="" x="949.07" y="123.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`kmem_cache_alloc (17 samples, 0.03%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`kmem_cache_alloc (17 samples, 0.03%)</title><rect x="878.3" y="81" width="0.4" height="15.0" fill="rgb(233,92,3)" rx="2" ry="2" />
<text text-anchor="" x="881.32" y="91.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`mutex_exit (212 samples, 0.37%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`mutex_exit (212 samples, 0.37%)</title><rect x="1088.5" y="81" width="4.4" height="15.0" fill="rgb(249,190,29)" rx="2" ry="2" />
<text text-anchor="" x="1091.54" y="91.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`vn_vfsrlock (2,414 samples, 4.20%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`vn_vfsrlock (2,414 samples, 4.20%)</title><rect x="847.8" y="113" width="49.5" height="15.0" fill="rgb(219,179,3)" rx="2" ry="2" />
<text text-anchor="" x="850.76" y="123.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >genun..</text>
</g>
<g class="func_g" onmouseover="s('genunix`vfs_matchops (28 samples, 0.05%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`vfs_matchops (28 samples, 0.05%)</title><rect x="254.7" y="113" width="0.5" height="15.0" fill="rgb(227,205,5)" rx="2" ry="2" />
<text text-anchor="" x="257.66" y="123.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`prunstop (36 samples, 0.06%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`prunstop (36 samples, 0.06%)</title><rect x="109.3" y="241" width="0.8" height="15.0" fill="rgb(238,99,48)" rx="2" ry="2" />
<text text-anchor="" x="112.35" y="251.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`mutex_exit (155 samples, 0.27%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`mutex_exit (155 samples, 0.27%)</title><rect x="490.7" y="33" width="3.2" height="15.0" fill="rgb(225,177,54)" rx="2" ry="2" />
<text text-anchor="" x="493.72" y="43.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`mutex_init (31 samples, 0.05%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`mutex_init (31 samples, 0.05%)</title><rect x="882.2" y="81" width="0.7" height="15.0" fill="rgb(205,198,41)" rx="2" ry="2" />
<text text-anchor="" x="885.23" y="91.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`atomic_add_32_nv (100 samples, 0.17%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`atomic_add_32_nv (100 samples, 0.17%)</title><rect x="191.3" y="193" width="2.1" height="15.0" fill="rgb(207,14,46)" rx="2" ry="2" />
<text text-anchor="" x="194.31" y="203.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`lookupnameat (69 samples, 0.12%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`lookupnameat (69 samples, 0.12%)</title><rect x="175.6" y="209" width="1.4" height="15.0" fill="rgb(212,229,16)" rx="2" ry="2" />
<text text-anchor="" x="178.58" y="219.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`_sys_rtt (6 samples, 0.01%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`_sys_rtt (6 samples, 0.01%)</title><rect x="123.5" y="273" width="0.1" height="15.0" fill="rgb(234,7,47)" rx="2" ry="2" />
<text text-anchor="" x="126.50" y="283.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`kmem_cache_alloc (49 samples, 0.09%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`kmem_cache_alloc (49 samples, 0.09%)</title><rect x="156.1" y="193" width="1.0" height="15.0" fill="rgb(237,108,16)" rx="2" ry="2" />
<text text-anchor="" x="159.14" y="203.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`tsc_gethrtimeunscaled (17 samples, 0.03%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`tsc_gethrtimeunscaled (17 samples, 0.03%)</title><rect x="117.7" y="225" width="0.3" height="15.0" fill="rgb(230,32,35)" rx="2" ry="2" />
<text text-anchor="" x="120.66" y="235.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`fop_lookup (29,216 samples, 50.86%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`fop_lookup (29,216 samples, 50.86%)</title><rect x="224.0" y="129" width="600.2" height="15.0" fill="rgb(233,134,19)" rx="2" ry="2" />
<text text-anchor="" x="226.97" y="139.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >genunix`fop_lookup</text>
</g>
<g class="func_g" onmouseover="s('unix`mutex_exit (142 samples, 0.25%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`mutex_exit (142 samples, 0.25%)</title><rect x="644.3" y="65" width="3.0" height="15.0" fill="rgb(245,192,45)" rx="2" ry="2" />
<text text-anchor="" x="647.34" y="75.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`crgetmapped (31 samples, 0.05%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`crgetmapped (31 samples, 0.05%)</title><rect x="954.8" y="113" width="0.7" height="15.0" fill="rgb(223,16,30)" rx="2" ry="2" />
<text text-anchor="" x="957.85" y="123.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`do_splx (1,993 samples, 3.47%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`do_splx (1,993 samples, 3.47%)</title><rect x="36.5" y="225" width="41.0" height="15.0" fill="rgb(242,203,17)" rx="2" ry="2" />
<text text-anchor="" x="39.52" y="235.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >uni..</text>
</g>
<g class="func_g" onmouseover="s('genunix`kmem_cache_free (22 samples, 0.04%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`kmem_cache_free (22 samples, 0.04%)</title><rect x="539.7" y="49" width="0.5" height="15.0" fill="rgb(222,32,40)" rx="2" ry="2" />
<text text-anchor="" x="542.74" y="59.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`mutex_enter (95 samples, 0.17%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`mutex_enter (95 samples, 0.17%)</title><rect x="946.7" y="113" width="2.0" height="15.0" fill="rgb(232,215,33)" rx="2" ry="2" />
<text text-anchor="" x="949.71" y="123.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`crhold (11 samples, 0.02%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`crhold (11 samples, 0.02%)</title><rect x="155.9" y="193" width="0.2" height="15.0" fill="rgb(207,151,52)" rx="2" ry="2" />
<text text-anchor="" x="158.92" y="203.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`mutex_enter (823 samples, 1.43%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`mutex_enter (823 samples, 1.43%)</title><rect x="921.9" y="97" width="16.9" height="15.0" fill="rgb(216,129,32)" rx="2" ry="2" />
<text text-anchor="" x="924.85" y="107.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`mutex_exit (29 samples, 0.05%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`mutex_exit (29 samples, 0.05%)</title><rect x="168.1" y="177" width="0.6" height="15.0" fill="rgb(217,5,44)" rx="2" ry="2" />
<text text-anchor="" x="171.14" y="187.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`vn_vfsrlock (3,342 samples, 5.82%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`vn_vfsrlock (3,342 samples, 5.82%)</title><rect x="454.2" y="81" width="68.7" height="15.0" fill="rgb(246,78,4)" rx="2" ry="2" />
<text text-anchor="" x="457.20" y="91.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >genunix..</text>
</g>
<g class="func_g" onmouseover="s('unix`tsc_gethrtimeunscaled (13 samples, 0.02%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`tsc_gethrtimeunscaled (13 samples, 0.02%)</title><rect x="20.8" y="241" width="0.2" height="15.0" fill="rgb(226,14,35)" rx="2" ry="2" />
<text text-anchor="" x="23.76" y="251.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`vn_rele (73 samples, 0.13%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`vn_rele (73 samples, 0.13%)</title><rect x="1121.4" y="145" width="1.5" height="15.0" fill="rgb(205,53,7)" rx="2" ry="2" />
<text text-anchor="" x="1124.39" y="155.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`mutex_exit (337 samples, 0.59%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`mutex_exit (337 samples, 0.59%)</title><rect x="742.9" y="113" width="6.9" height="15.0" fill="rgb(248,164,47)" rx="2" ry="2" />
<text text-anchor="" x="745.89" y="123.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`vn_vfslocks_getlock (973 samples, 1.69%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`vn_vfslocks_getlock (973 samples, 1.69%)</title><rect x="862.9" y="97" width="20.0" height="15.0" fill="rgb(213,83,46)" rx="2" ry="2" />
<text text-anchor="" x="865.88" y="107.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('zfs`specvp_check (20 samples, 0.03%)')" onmouseout="c()" onclick="zoom(this)">
<title>zfs`specvp_check (20 samples, 0.03%)</title><rect x="822.7" y="97" width="0.4" height="15.0" fill="rgb(205,137,1)" rx="2" ry="2" />
<text text-anchor="" x="825.71" y="107.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`vsd_free (14 samples, 0.02%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`vsd_free (14 samples, 0.02%)</title><rect x="637.5" y="49" width="0.3" height="15.0" fill="rgb(253,90,49)" rx="2" ry="2" />
<text text-anchor="" x="640.52" y="59.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`mutex_enter (314 samples, 0.55%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`mutex_enter (314 samples, 0.55%)</title><rect x="1005.9" y="49" width="6.4" height="15.0" fill="rgb(235,150,49)" rx="2" ry="2" />
<text text-anchor="" x="1008.87" y="59.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`cv_destroy (81 samples, 0.14%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`cv_destroy (81 samples, 0.14%)</title><rect x="907.1" y="81" width="1.6" height="15.0" fill="rgb(218,178,46)" rx="2" ry="2" />
<text text-anchor="" x="910.08" y="91.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`cv_broadcast (25 samples, 0.04%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`cv_broadcast (25 samples, 0.04%)</title><rect x="524.2" y="65" width="0.5" height="15.0" fill="rgb(243,96,5)" rx="2" ry="2" />
<text text-anchor="" x="527.19" y="75.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`mutex_enter (122 samples, 0.21%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`mutex_enter (122 samples, 0.21%)</title><rect x="152.3" y="161" width="2.5" height="15.0" fill="rgb(235,42,20)" rx="2" ry="2" />
<text text-anchor="" x="155.30" y="171.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`mutex_exit (55 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`mutex_exit (55 samples, 0.10%)</title><rect x="188.6" y="161" width="1.2" height="15.0" fill="rgb(213,47,21)" rx="2" ry="2" />
<text text-anchor="" x="191.64" y="171.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`set_errno (24 samples, 0.04%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`set_errno (24 samples, 0.04%)</title><rect x="177.0" y="209" width="0.5" height="15.0" fill="rgb(237,100,4)" rx="2" ry="2" />
<text text-anchor="" x="179.99" y="219.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`cv_destroy (42 samples, 0.07%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`cv_destroy (42 samples, 0.07%)</title><rect x="556.0" y="33" width="0.9" height="15.0" fill="rgb(247,122,7)" rx="2" ry="2" />
<text text-anchor="" x="559.03" y="43.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`fd_find (13 samples, 0.02%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`fd_find (13 samples, 0.02%)</title><rect x="157.6" y="177" width="0.3" height="15.0" fill="rgb(225,80,43)" rx="2" ry="2" />
<text text-anchor="" x="160.60" y="187.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`vn_invalid (47 samples, 0.08%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`vn_invalid (47 samples, 0.08%)</title><rect x="1015.8" y="65" width="1.0" height="15.0" fill="rgb(245,201,3)" rx="2" ry="2" />
<text text-anchor="" x="1018.84" y="75.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`vfs_matchops (336 samples, 0.58%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`vfs_matchops (336 samples, 0.58%)</title><rect x="596.8" y="97" width="6.9" height="15.0" fill="rgb(223,137,26)" rx="2" ry="2" />
<text text-anchor="" x="599.79" y="107.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`tsc_gethrtimeunscaled (59 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`tsc_gethrtimeunscaled (59 samples, 0.10%)</title><rect x="1184.6" y="241" width="1.2" height="15.0" fill="rgb(250,111,9)" rx="2" ry="2" />
<text text-anchor="" x="1187.58" y="251.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`fop_inactive (39 samples, 0.07%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`fop_inactive (39 samples, 0.07%)</title><rect x="223.2" y="129" width="0.8" height="15.0" fill="rgb(238,39,51)" rx="2" ry="2" />
<text text-anchor="" x="226.17" y="139.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`kmem_free (693 samples, 1.21%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`kmem_free (693 samples, 1.21%)</title><rect x="540.2" y="49" width="14.2" height="15.0" fill="rgb(251,181,40)" rx="2" ry="2" />
<text text-anchor="" x="543.19" y="59.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`syscall_mstate (412 samples, 0.72%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`syscall_mstate (412 samples, 0.72%)</title><rect x="113.1" y="257" width="8.4" height="15.0" fill="rgb(254,177,49)" rx="2" ry="2" />
<text text-anchor="" x="116.08" y="267.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`thread_lock (670 samples, 1.17%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`thread_lock (670 samples, 1.17%)</title><rect x="77.5" y="241" width="13.8" height="15.0" fill="rgb(247,196,0)" rx="2" ry="2" />
<text text-anchor="" x="80.50" y="251.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('lofs`lsave (162 samples, 0.28%)')" onmouseout="c()" onclick="zoom(this)">
<title>lofs`lsave (162 samples, 0.28%)</title><rect x="658.3" y="81" width="3.3" height="15.0" fill="rgb(241,194,43)" rx="2" ry="2" />
<text text-anchor="" x="661.27" y="91.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`atomic_add_64 (95 samples, 0.17%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`atomic_add_64 (95 samples, 0.17%)</title><rect x="121.5" y="257" width="2.0" height="15.0" fill="rgb(252,68,24)" rx="2" ry="2" />
<text text-anchor="" x="124.55" y="267.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`audit_getstate (66 samples, 0.11%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`audit_getstate (66 samples, 0.11%)</title><rect x="144.5" y="209" width="1.3" height="15.0" fill="rgb(218,74,2)" rx="2" ry="2" />
<text text-anchor="" x="147.49" y="219.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`dnlc_lookup (70 samples, 0.12%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`dnlc_lookup (70 samples, 0.12%)</title><rect x="332.3" y="81" width="1.4" height="15.0" fill="rgb(247,147,12)" rx="2" ry="2" />
<text text-anchor="" x="335.26" y="91.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`vn_mountedvfs (30 samples, 0.05%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`vn_mountedvfs (30 samples, 0.05%)</title><rect x="604.4" y="97" width="0.7" height="15.0" fill="rgb(213,193,41)" rx="2" ry="2" />
<text text-anchor="" x="607.45" y="107.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`cv_broadcast (19 samples, 0.03%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`cv_broadcast (19 samples, 0.03%)</title><rect x="901.3" y="81" width="0.4" height="15.0" fill="rgb(249,182,12)" rx="2" ry="2" />
<text text-anchor="" x="904.29" y="91.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`kmem_alloc (533 samples, 0.93%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`kmem_alloc (533 samples, 0.93%)</title><rect x="867.4" y="81" width="10.9" height="15.0" fill="rgb(248,87,22)" rx="2" ry="2" />
<text text-anchor="" x="870.38" y="91.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`mutex_exit (160 samples, 0.28%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`mutex_exit (160 samples, 0.28%)</title><rect x="1001.6" y="33" width="3.3" height="15.0" fill="rgb(242,1,27)" rx="2" ry="2" />
<text text-anchor="" x="1004.60" y="43.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`memcmp (38 samples, 0.07%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`memcmp (38 samples, 0.07%)</title><rect x="376.3" y="49" width="0.8" height="15.0" fill="rgb(252,137,40)" rx="2" ry="2" />
<text text-anchor="" x="379.34" y="59.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`strlen (1,238 samples, 2.16%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`strlen (1,238 samples, 2.16%)</title><rect x="1050.5" y="65" width="25.4" height="15.0" fill="rgb(208,64,14)" rx="2" ry="2" />
<text text-anchor="" x="1053.47" y="75.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >u..</text>
</g>
<g class="func_g" onmouseover="s('genunix`lookuppnatcred (12 samples, 0.02%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`lookuppnatcred (12 samples, 0.02%)</title><rect x="1147.5" y="177" width="0.3" height="15.0" fill="rgb(250,114,16)" rx="2" ry="2" />
<text text-anchor="" x="1150.54" y="187.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`crfree (13 samples, 0.02%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`crfree (13 samples, 0.02%)</title><rect x="189.8" y="193" width="0.2" height="15.0" fill="rgb(221,12,6)" rx="2" ry="2" />
<text text-anchor="" x="192.77" y="203.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('lofs`table_lock_enter (43 samples, 0.07%)')" onmouseout="c()" onclick="zoom(this)">
<title>lofs`table_lock_enter (43 samples, 0.07%)</title><rect x="695.2" y="97" width="0.9" height="15.0" fill="rgb(218,97,33)" rx="2" ry="2" />
<text text-anchor="" x="698.19" y="107.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`rwst_exit (18 samples, 0.03%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`rwst_exit (18 samples, 0.03%)</title><rect x="450.6" y="81" width="0.4" height="15.0" fill="rgb(229,190,16)" rx="2" ry="2" />
<text text-anchor="" x="453.64" y="91.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`cv_destroy (31 samples, 0.05%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`cv_destroy (31 samples, 0.05%)</title><rect x="919.8" y="65" width="0.6" height="15.0" fill="rgb(215,139,38)" rx="2" ry="2" />
<text text-anchor="" x="922.78" y="75.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`rwst_init (236 samples, 0.41%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`rwst_init (236 samples, 0.41%)</title><rect x="494.5" y="49" width="4.9" height="15.0" fill="rgb(210,173,45)" rx="2" ry="2" />
<text text-anchor="" x="497.50" y="59.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`vn_vfslocks_rele (1,420 samples, 2.47%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`vn_vfslocks_rele (1,420 samples, 2.47%)</title><rect x="532.0" y="65" width="29.1" height="15.0" fill="rgb(230,124,39)" rx="2" ry="2" />
<text text-anchor="" x="534.97" y="75.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >ge..</text>
</g>
<g class="func_g" onmouseover="s('genunix`falloc (36 samples, 0.06%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`falloc (36 samples, 0.06%)</title><rect x="1155.5" y="225" width="0.7" height="15.0" fill="rgb(254,194,53)" rx="2" ry="2" />
<text text-anchor="" x="1158.51" y="235.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`setf (187 samples, 0.33%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`setf (187 samples, 0.33%)</title><rect x="177.5" y="209" width="3.8" height="15.0" fill="rgb(226,209,31)" rx="2" ry="2" />
<text text-anchor="" x="180.49" y="219.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('zfs`zfs_fastaccesschk_execute (50 samples, 0.09%)')" onmouseout="c()" onclick="zoom(this)">
<title>zfs`zfs_fastaccesschk_execute (50 samples, 0.09%)</title><rect x="823.1" y="97" width="1.1" height="15.0" fill="rgb(254,144,3)" rx="2" ry="2" />
<text text-anchor="" x="826.13" y="107.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`vn_vfslocks_getlock (120 samples, 0.21%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`vn_vfslocks_getlock (120 samples, 0.21%)</title><rect x="529.5" y="65" width="2.5" height="15.0" fill="rgb(225,219,12)" rx="2" ry="2" />
<text text-anchor="" x="532.51" y="75.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`fd_reserve (9 samples, 0.02%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`fd_reserve (9 samples, 0.02%)</title><rect x="157.9" y="177" width="0.2" height="15.0" fill="rgb(222,178,46)" rx="2" ry="2" />
<text text-anchor="" x="160.87" y="187.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`vn_setops (160 samples, 0.28%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`vn_setops (160 samples, 0.28%)</title><rect x="649.3" y="81" width="3.3" height="15.0" fill="rgb(216,62,33)" rx="2" ry="2" />
<text text-anchor="" x="652.27" y="91.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`sys_syscall (51,908 samples, 90.37%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`sys_syscall (51,908 samples, 90.37%)</title><rect x="123.7" y="273" width="1066.3" height="15.0" fill="rgb(212,14,30)" rx="2" ry="2" />
<text text-anchor="" x="126.66" y="283.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >unix`sys_syscall</text>
</g>
<g class="func_g" onmouseover="s('genunix`kmem_free (115 samples, 0.20%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`kmem_free (115 samples, 0.20%)</title><rect x="979.3" y="65" width="2.4" height="15.0" fill="rgb(207,30,6)" rx="2" ry="2" />
<text text-anchor="" x="982.31" y="75.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`vsd_free (48 samples, 0.08%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`vsd_free (48 samples, 0.08%)</title><rect x="1004.9" y="49" width="1.0" height="15.0" fill="rgb(227,186,13)" rx="2" ry="2" />
<text text-anchor="" x="1007.89" y="59.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`rexit (5 samples, 0.01%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`rexit (5 samples, 0.01%)</title><rect x="1158.2" y="257" width="0.1" height="15.0" fill="rgb(221,25,27)" rx="2" ry="2" />
<text text-anchor="" x="1161.24" y="267.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`vn_mountedvfs (11 samples, 0.02%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`vn_mountedvfs (11 samples, 0.02%)</title><rect x="451.7" y="81" width="0.2" height="15.0" fill="rgb(219,201,29)" rx="2" ry="2" />
<text text-anchor="" x="454.67" y="91.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`lookuppnatcred (44,681 samples, 77.79%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`lookuppnatcred (44,681 samples, 77.79%)</title><rect x="205.1" y="161" width="917.8" height="15.0" fill="rgb(250,143,22)" rx="2" ry="2" />
<text text-anchor="" x="208.05" y="171.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >genunix`lookuppnatcred</text>
</g>
<g class="func_g" onmouseover="s('unix`splr (92 samples, 0.16%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`splr (92 samples, 0.16%)</title><rect x="110.1" y="241" width="1.9" height="15.0" fill="rgb(213,115,37)" rx="2" ry="2" />
<text text-anchor="" x="113.08" y="251.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`vn_vfsrlock (13 samples, 0.02%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`vn_vfsrlock (13 samples, 0.02%)</title><rect x="605.9" y="97" width="0.3" height="15.0" fill="rgb(205,33,30)" rx="2" ry="2" />
<text text-anchor="" x="608.91" y="107.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`mutex_exit (371 samples, 0.65%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`mutex_exit (371 samples, 0.65%)</title><rect x="515.2" y="65" width="7.7" height="15.0" fill="rgb(239,68,12)" rx="2" ry="2" />
<text text-anchor="" x="518.23" y="75.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`kmem_cache_free (5 samples, 0.01%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`kmem_cache_free (5 samples, 0.01%)</title><rect x="183.8" y="177" width="0.1" height="15.0" fill="rgb(238,178,39)" rx="2" ry="2" />
<text text-anchor="" x="186.75" y="187.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`dnlc_lookup (263 samples, 0.46%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`dnlc_lookup (263 samples, 0.46%)</title><rect x="808.0" y="97" width="5.4" height="15.0" fill="rgb(224,229,10)" rx="2" ry="2" />
<text text-anchor="" x="810.97" y="107.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`audit_unfalloc (32 samples, 0.06%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`audit_unfalloc (32 samples, 0.06%)</title><rect x="145.8" y="209" width="0.7" height="15.0" fill="rgb(211,114,19)" rx="2" ry="2" />
<text text-anchor="" x="148.85" y="219.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`0xfffffffffb8001d6 (13 samples, 0.02%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`0xfffffffffb8001d6 (13 samples, 0.02%)</title><rect x="12.4" y="273" width="0.3" height="15.0" fill="rgb(210,131,54)" rx="2" ry="2" />
<text text-anchor="" x="15.42" y="283.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`rwst_destroy (146 samples, 0.25%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`rwst_destroy (146 samples, 0.25%)</title><rect x="918.5" y="81" width="3.0" height="15.0" fill="rgb(243,169,11)" rx="2" ry="2" />
<text text-anchor="" x="921.51" y="91.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`gethrtime_unscaled (182 samples, 0.32%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`gethrtime_unscaled (182 samples, 0.32%)</title><rect x="117.6" y="241" width="3.7" height="15.0" fill="rgb(235,226,22)" rx="2" ry="2" />
<text text-anchor="" x="120.56" y="251.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`mutex_enter (575 samples, 1.00%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`mutex_enter (575 samples, 1.00%)</title><rect x="1096.5" y="129" width="11.8" height="15.0" fill="rgb(220,94,34)" rx="2" ry="2" />
<text text-anchor="" x="1099.51" y="139.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`mutex_exit (148 samples, 0.26%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`mutex_exit (148 samples, 0.26%)</title><rect x="551.4" y="33" width="3.0" height="15.0" fill="rgb(227,116,17)" rx="2" ry="2" />
<text text-anchor="" x="554.39" y="43.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`ufalloc_file (294 samples, 0.51%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`ufalloc_file (294 samples, 0.51%)</title><rect x="158.1" y="177" width="6.0" height="15.0" fill="rgb(234,86,49)" rx="2" ry="2" />
<text text-anchor="" x="161.05" y="187.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`mutex_exit (163 samples, 0.28%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`mutex_exit (163 samples, 0.28%)</title><rect x="1144.2" y="161" width="3.3" height="15.0" fill="rgb(227,126,44)" rx="2" ry="2" />
<text text-anchor="" x="1147.19" y="171.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`membar_consumer (106 samples, 0.18%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`membar_consumer (106 samples, 0.18%)</title><rect x="1022.4" y="65" width="2.1" height="15.0" fill="rgb(207,125,0)" rx="2" ry="2" />
<text text-anchor="" x="1025.35" y="75.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`crgetmapped (36 samples, 0.06%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`crgetmapped (36 samples, 0.06%)</title><rect x="313.1" y="97" width="0.7" height="15.0" fill="rgb(215,83,38)" rx="2" ry="2" />
<text text-anchor="" x="316.11" y="107.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`memcmp (277 samples, 0.48%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`memcmp (277 samples, 0.48%)</title><rect x="377.5" y="33" width="5.7" height="15.0" fill="rgb(225,179,33)" rx="2" ry="2" />
<text text-anchor="" x="380.49" y="43.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`cv_destroy (77 samples, 0.13%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`cv_destroy (77 samples, 0.13%)</title><rect x="538.2" y="49" width="1.5" height="15.0" fill="rgb(227,34,15)" rx="2" ry="2" />
<text text-anchor="" x="541.16" y="59.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`kmem_cache_free (116 samples, 0.20%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`kmem_cache_free (116 samples, 0.20%)</title><rect x="910.3" y="65" width="2.4" height="15.0" fill="rgb(250,59,39)" rx="2" ry="2" />
<text text-anchor="" x="913.27" y="75.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`kmem_cache_alloc (29 samples, 0.05%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`kmem_cache_alloc (29 samples, 0.05%)</title><rect x="493.9" y="49" width="0.6" height="15.0" fill="rgb(206,121,52)" rx="2" ry="2" />
<text text-anchor="" x="496.91" y="59.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`fd_reserve (8 samples, 0.01%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`fd_reserve (8 samples, 0.01%)</title><rect x="175.1" y="209" width="0.2" height="15.0" fill="rgb(218,184,52)" rx="2" ry="2" />
<text text-anchor="" x="178.12" y="219.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('zfs`zfs_lookup (946 samples, 1.65%)')" onmouseout="c()" onclick="zoom(this)">
<title>zfs`zfs_lookup (946 samples, 1.65%)</title><rect x="804.7" y="113" width="19.5" height="15.0" fill="rgb(227,94,37)" rx="2" ry="2" />
<text text-anchor="" x="807.72" y="123.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`kmem_alloc (795 samples, 1.38%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`kmem_alloc (795 samples, 1.38%)</title><rect x="477.6" y="49" width="16.3" height="15.0" fill="rgb(220,147,8)" rx="2" ry="2" />
<text text-anchor="" x="480.58" y="59.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`tsc_gethrtimeunscaled (11 samples, 0.02%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`tsc_gethrtimeunscaled (11 samples, 0.02%)</title><rect x="16.7" y="225" width="0.2" height="15.0" fill="rgb(206,36,22)" rx="2" ry="2" />
<text text-anchor="" x="19.72" y="235.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`segvn_faultpage (7 samples, 0.01%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`segvn_faultpage (7 samples, 0.01%)</title><rect x="12.5" y="193" width="0.1" height="15.0" fill="rgb(233,134,49)" rx="2" ry="2" />
<text text-anchor="" x="15.47" y="203.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`set_errno (9 samples, 0.02%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`set_errno (9 samples, 0.02%)</title><rect x="1156.2" y="225" width="0.2" height="15.0" fill="rgb(234,116,51)" rx="2" ry="2" />
<text text-anchor="" x="1159.25" y="235.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`splr (400 samples, 0.70%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`splr (400 samples, 0.70%)</title><rect x="83.1" y="225" width="8.2" height="15.0" fill="rgb(253,39,34)" rx="2" ry="2" />
<text text-anchor="" x="86.05" y="235.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`rwst_destroy (32 samples, 0.06%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`rwst_destroy (32 samples, 0.06%)</title><rect x="525.4" y="65" width="0.7" height="15.0" fill="rgb(243,10,19)" rx="2" ry="2" />
<text text-anchor="" x="528.42" y="75.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`rwst_init (28 samples, 0.05%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`rwst_init (28 samples, 0.05%)</title><rect x="456.8" y="65" width="0.5" height="15.0" fill="rgb(220,150,11)" rx="2" ry="2" />
<text text-anchor="" x="459.77" y="75.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`atomic_add_32 (292 samples, 0.51%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`atomic_add_32 (292 samples, 0.51%)</title><rect x="1076.8" y="81" width="6.0" height="15.0" fill="rgb(239,0,30)" rx="2" ry="2" />
<text text-anchor="" x="1079.81" y="91.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`0xfffffffffb800ca0 (517 samples, 0.90%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`0xfffffffffb800ca0 (517 samples, 0.90%)</title><rect x="112.9" y="273" width="10.6" height="15.0" fill="rgb(206,16,0)" rx="2" ry="2" />
<text text-anchor="" x="115.88" y="283.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`syscall_mstate (89 samples, 0.15%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`syscall_mstate (89 samples, 0.15%)</title><rect x="10.6" y="273" width="1.8" height="15.0" fill="rgb(226,201,28)" rx="2" ry="2" />
<text text-anchor="" x="13.60" y="283.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`kmem_alloc (73 samples, 0.13%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`kmem_alloc (73 samples, 0.13%)</title><rect x="252.5" y="113" width="1.5" height="15.0" fill="rgb(206,156,23)" rx="2" ry="2" />
<text text-anchor="" x="255.55" y="123.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`vn_vfsunlock (40 samples, 0.07%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`vn_vfsunlock (40 samples, 0.07%)</title><rect x="606.2" y="97" width="0.8" height="15.0" fill="rgb(237,22,53)" rx="2" ry="2" />
<text text-anchor="" x="609.17" y="107.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`mutex_enter (1,202 samples, 2.09%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`mutex_enter (1,202 samples, 2.09%)</title><rect x="561.1" y="65" width="24.7" height="15.0" fill="rgb(215,129,31)" rx="2" ry="2" />
<text text-anchor="" x="564.14" y="75.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >u..</text>
</g>
<g class="func_g" onmouseover="s('lofs`makelfsnode (28 samples, 0.05%)')" onmouseout="c()" onclick="zoom(this)">
<title>lofs`makelfsnode (28 samples, 0.05%)</title><rect x="608.1" y="97" width="0.6" height="15.0" fill="rgb(211,52,34)" rx="2" ry="2" />
<text text-anchor="" x="611.08" y="107.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`0xfffffffffb800c86 (472 samples, 0.82%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`0xfffffffffb800c86 (472 samples, 0.82%)</title><rect x="13.6" y="273" width="9.7" height="15.0" fill="rgb(214,120,44)" rx="2" ry="2" />
<text text-anchor="" x="16.59" y="283.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`vn_rele (6,943 samples, 12.09%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`vn_rele (6,943 samples, 12.09%)</title><rect x="950.7" y="129" width="142.6" height="15.0" fill="rgb(252,60,15)" rx="2" ry="2" />
<text text-anchor="" x="953.70" y="139.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >genunix`vn_rele</text>
</g>
<g class="func_g" onmouseover="s('unix`mutex_exit (56 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`mutex_exit (56 samples, 0.10%)</title><rect x="948.7" y="113" width="1.1" height="15.0" fill="rgb(254,30,8)" rx="2" ry="2" />
<text text-anchor="" x="951.66" y="123.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`kmem_cache_free (51 samples, 0.09%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`kmem_cache_free (51 samples, 0.09%)</title><rect x="190.0" y="193" width="1.1" height="15.0" fill="rgb(205,40,41)" rx="2" ry="2" />
<text text-anchor="" x="193.04" y="203.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`gethrtime_unscaled (11 samples, 0.02%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`gethrtime_unscaled (11 samples, 0.02%)</title><rect x="137.2" y="257" width="0.2" height="15.0" fill="rgb(214,153,30)" rx="2" ry="2" />
<text text-anchor="" x="140.20" y="267.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`pagefault (13 samples, 0.02%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`pagefault (13 samples, 0.02%)</title><rect x="12.4" y="241" width="0.3" height="15.0" fill="rgb(241,45,25)" rx="2" ry="2" />
<text text-anchor="" x="15.42" y="251.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`secpolicy_vnode_access2 (217 samples, 0.38%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`secpolicy_vnode_access2 (217 samples, 0.38%)</title><rect x="394.3" y="49" width="4.5" height="15.0" fill="rgb(229,93,34)" rx="2" ry="2" />
<text text-anchor="" x="397.32" y="59.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`vn_vfslocks_getlock (1,357 samples, 2.36%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`vn_vfslocks_getlock (1,357 samples, 2.36%)</title><rect x="472.4" y="65" width="27.9" height="15.0" fill="rgb(230,84,26)" rx="2" ry="2" />
<text text-anchor="" x="475.42" y="75.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >g..</text>
</g>
<g class="func_g" onmouseover="s('unix`bcmp (295 samples, 0.51%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`bcmp (295 samples, 0.51%)</title><rect x="377.1" y="49" width="6.1" height="15.0" fill="rgb(242,198,31)" rx="2" ry="2" />
<text text-anchor="" x="380.12" y="59.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`mutex_enter (97 samples, 0.17%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`mutex_enter (97 samples, 0.17%)</title><rect x="193.4" y="193" width="2.0" height="15.0" fill="rgb(213,6,50)" rx="2" ry="2" />
<text text-anchor="" x="196.37" y="203.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`membar_consumer (123 samples, 0.21%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`membar_consumer (123 samples, 0.21%)</title><rect x="601.2" y="81" width="2.5" height="15.0" fill="rgb(243,61,8)" rx="2" ry="2" />
<text text-anchor="" x="604.16" y="91.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`audit_getstate (16 samples, 0.03%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`audit_getstate (16 samples, 0.03%)</title><rect x="208.4" y="145" width="0.4" height="15.0" fill="rgb(216,4,21)" rx="2" ry="2" />
<text text-anchor="" x="211.44" y="155.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`mutex_enter (455 samples, 0.79%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`mutex_enter (455 samples, 0.79%)</title><rect x="882.9" y="97" width="9.3" height="15.0" fill="rgb(208,168,52)" rx="2" ry="2" />
<text text-anchor="" x="885.86" y="107.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('lofs`makelonode (4,212 samples, 7.33%)')" onmouseout="c()" onclick="zoom(this)">
<title>lofs`makelonode (4,212 samples, 7.33%)</title><rect x="608.7" y="97" width="86.5" height="15.0" fill="rgb(243,91,47)" rx="2" ry="2" />
<text text-anchor="" x="611.66" y="107.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >lofs`makel..</text>
</g>
<g class="func_g" onmouseover="s('genunix`kmem_cache_alloc (168 samples, 0.29%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`kmem_cache_alloc (168 samples, 0.29%)</title><rect x="479.5" y="33" width="3.4" height="15.0" fill="rgb(221,118,18)" rx="2" ry="2" />
<text text-anchor="" x="482.49" y="43.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`vn_vfslocks_getlock (62 samples, 0.11%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`vn_vfslocks_getlock (62 samples, 0.11%)</title><rect x="451.9" y="81" width="1.3" height="15.0" fill="rgb(240,186,54)" rx="2" ry="2" />
<text text-anchor="" x="454.90" y="91.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`secpolicy_vnode_access2 (72 samples, 0.13%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`secpolicy_vnode_access2 (72 samples, 0.13%)</title><rect x="383.2" y="65" width="1.5" height="15.0" fill="rgb(222,115,24)" rx="2" ry="2" />
<text text-anchor="" x="386.18" y="75.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`kmem_cache_free (73 samples, 0.13%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`kmem_cache_free (73 samples, 0.13%)</title><rect x="184.9" y="161" width="1.5" height="15.0" fill="rgb(245,176,42)" rx="2" ry="2" />
<text text-anchor="" x="187.86" y="171.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`vn_reinit (424 samples, 0.74%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`vn_reinit (424 samples, 0.74%)</title><rect x="629.1" y="65" width="8.7" height="15.0" fill="rgb(240,37,51)" rx="2" ry="2" />
<text text-anchor="" x="632.10" y="75.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`pn_getcomponent (454 samples, 0.79%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`pn_getcomponent (454 samples, 0.79%)</title><rect x="826.3" y="129" width="9.4" height="15.0" fill="rgb(250,85,12)" rx="2" ry="2" />
<text text-anchor="" x="829.33" y="139.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`fsop_root (297 samples, 0.52%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`fsop_root (297 samples, 0.52%)</title><rect x="838.2" y="113" width="6.1" height="15.0" fill="rgb(230,79,49)" rx="2" ry="2" />
<text text-anchor="" x="841.18" y="123.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`crgetuid (30 samples, 0.05%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`crgetuid (30 samples, 0.05%)</title><rect x="344.7" y="65" width="0.6" height="15.0" fill="rgb(223,149,47)" rx="2" ry="2" />
<text text-anchor="" x="347.70" y="75.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`kmem_free (785 samples, 1.37%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`kmem_free (785 samples, 1.37%)</title><rect x="988.8" y="49" width="16.1" height="15.0" fill="rgb(228,138,16)" rx="2" ry="2" />
<text text-anchor="" x="991.76" y="59.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`mutex_exit (171 samples, 0.30%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`mutex_exit (171 samples, 0.30%)</title><rect x="1012.3" y="49" width="3.5" height="15.0" fill="rgb(212,200,49)" rx="2" ry="2" />
<text text-anchor="" x="1015.33" y="59.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`crgetmapped (58 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`crgetmapped (58 samples, 0.10%)</title><rect x="331.1" y="81" width="1.2" height="15.0" fill="rgb(224,216,12)" rx="2" ry="2" />
<text text-anchor="" x="334.06" y="91.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`mutex_enter (299 samples, 0.52%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`mutex_enter (299 samples, 0.52%)</title><rect x="995.5" y="33" width="6.1" height="15.0" fill="rgb(205,125,8)" rx="2" ry="2" />
<text text-anchor="" x="998.46" y="43.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`rwst_exit (167 samples, 0.29%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`rwst_exit (167 samples, 0.29%)</title><rect x="526.1" y="65" width="3.4" height="15.0" fill="rgb(254,66,0)" rx="2" ry="2" />
<text text-anchor="" x="529.08" y="75.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`audit_falloc (8 samples, 0.01%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`audit_falloc (8 samples, 0.01%)</title><rect x="144.3" y="209" width="0.2" height="15.0" fill="rgb(228,11,39)" rx="2" ry="2" />
<text text-anchor="" x="147.33" y="219.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`rwst_exit (110 samples, 0.19%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`rwst_exit (110 samples, 0.19%)</title><rect x="899.4" y="97" width="2.3" height="15.0" fill="rgb(206,144,16)" rx="2" ry="2" />
<text text-anchor="" x="902.42" y="107.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`exit (5 samples, 0.01%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`exit (5 samples, 0.01%)</title><rect x="1158.2" y="241" width="0.1" height="15.0" fill="rgb(216,220,47)" rx="2" ry="2" />
<text text-anchor="" x="1161.24" y="251.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`mutex_exit (250 samples, 0.44%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`mutex_exit (250 samples, 0.44%)</title><rect x="892.2" y="97" width="5.1" height="15.0" fill="rgb(237,8,31)" rx="2" ry="2" />
<text text-anchor="" x="895.21" y="107.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('lofs`freelonode (35 samples, 0.06%)')" onmouseout="c()" onclick="zoom(this)">
<title>lofs`freelonode (35 samples, 0.06%)</title><rect x="962.6" y="97" width="0.7" height="15.0" fill="rgb(230,130,30)" rx="2" ry="2" />
<text text-anchor="" x="965.61" y="107.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`rwst_tryenter (37 samples, 0.06%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`rwst_tryenter (37 samples, 0.06%)</title><rect x="844.5" y="113" width="0.8" height="15.0" fill="rgb(245,181,9)" rx="2" ry="2" />
<text text-anchor="" x="847.53" y="123.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('ufs`ufs_iaccess (91 samples, 0.16%)')" onmouseout="c()" onclick="zoom(this)">
<title>ufs`ufs_iaccess (91 samples, 0.16%)</title><rect x="334.0" y="81" width="1.9" height="15.0" fill="rgb(223,226,13)" rx="2" ry="2" />
<text text-anchor="" x="336.98" y="91.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`tsc_gethrtimeunscaled (12 samples, 0.02%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`tsc_gethrtimeunscaled (12 samples, 0.02%)</title><rect x="121.3" y="241" width="0.2" height="15.0" fill="rgb(250,61,12)" rx="2" ry="2" />
<text text-anchor="" x="124.30" y="251.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`kmem_cache_alloc (241 samples, 0.42%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`kmem_cache_alloc (241 samples, 0.42%)</title><rect x="279.5" y="81" width="5.0" height="15.0" fill="rgb(242,133,12)" rx="2" ry="2" />
<text text-anchor="" x="282.50" y="91.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('FSS`fss_preempt (8 samples, 0.01%)')" onmouseout="c()" onclick="zoom(this)">
<title>FSS`fss_preempt (8 samples, 0.01%)</title><rect x="109.1" y="225" width="0.1" height="15.0" fill="rgb(212,8,16)" rx="2" ry="2" />
<text text-anchor="" x="112.06" y="235.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`fd_reserve (15 samples, 0.03%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`fd_reserve (15 samples, 0.03%)</title><rect x="163.8" y="161" width="0.3" height="15.0" fill="rgb(248,64,12)" rx="2" ry="2" />
<text text-anchor="" x="166.78" y="171.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`cv_broadcast (16 samples, 0.03%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`cv_broadcast (16 samples, 0.03%)</title><rect x="146.8" y="209" width="0.3" height="15.0" fill="rgb(249,99,1)" rx="2" ry="2" />
<text text-anchor="" x="149.79" y="219.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`crgetmapped (57 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`crgetmapped (57 samples, 0.10%)</title><rect x="249.1" y="113" width="1.2" height="15.0" fill="rgb(231,201,26)" rx="2" ry="2" />
<text text-anchor="" x="252.10" y="123.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`mutex_exit (379 samples, 0.66%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`mutex_exit (379 samples, 0.66%)</title><rect x="1108.3" y="129" width="7.8" height="15.0" fill="rgb(226,178,4)" rx="2" ry="2" />
<text text-anchor="" x="1111.32" y="139.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`mutex_destroy (31 samples, 0.05%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`mutex_destroy (31 samples, 0.05%)</title><rect x="560.5" y="49" width="0.6" height="15.0" fill="rgb(238,186,49)" rx="2" ry="2" />
<text text-anchor="" x="563.51" y="59.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('lofs`table_lock_enter (189 samples, 0.33%)')" onmouseout="c()" onclick="zoom(this)">
<title>lofs`table_lock_enter (189 samples, 0.33%)</title><rect x="1018.5" y="65" width="3.9" height="15.0" fill="rgb(206,126,49)" rx="2" ry="2" />
<text text-anchor="" x="1021.47" y="75.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`rwst_enter_common (264 samples, 0.46%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`rwst_enter_common (264 samples, 0.46%)</title><rect x="457.9" y="49" width="5.4" height="15.0" fill="rgb(238,16,32)" rx="2" ry="2" />
<text text-anchor="" x="460.92" y="59.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`kmem_free (11 samples, 0.02%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`kmem_free (11 samples, 0.02%)</title><rect x="191.1" y="193" width="0.2" height="15.0" fill="rgb(237,143,48)" rx="2" ry="2" />
<text text-anchor="" x="194.08" y="203.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`atomic_add_32 (134 samples, 0.23%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`atomic_add_32 (134 samples, 0.23%)</title><rect x="169.1" y="193" width="2.8" height="15.0" fill="rgb(218,64,21)" rx="2" ry="2" />
<text text-anchor="" x="172.15" y="203.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`ufalloc (551 samples, 0.96%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`ufalloc (551 samples, 0.96%)</title><rect x="157.4" y="193" width="11.3" height="15.0" fill="rgb(226,122,53)" rx="2" ry="2" />
<text text-anchor="" x="160.42" y="203.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`audit_falloc (313 samples, 0.54%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`audit_falloc (313 samples, 0.54%)</title><rect x="149.5" y="193" width="6.4" height="15.0" fill="rgb(223,204,27)" rx="2" ry="2" />
<text text-anchor="" x="152.49" y="203.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('lofs`lo_lookup (19,887 samples, 34.62%)')" onmouseout="c()" onclick="zoom(this)">
<title>lofs`lo_lookup (19,887 samples, 34.62%)</title><rect x="295.7" y="113" width="408.5" height="15.0" fill="rgb(207,93,4)" rx="2" ry="2" />
<text text-anchor="" x="298.69" y="123.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >lofs`lo_lookup</text>
</g>
<g class="func_g" onmouseover="s('unix`atomic_add_64 (110 samples, 0.19%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`atomic_add_64 (110 samples, 0.19%)</title><rect x="21.0" y="257" width="2.3" height="15.0" fill="rgb(211,92,11)" rx="2" ry="2" />
<text text-anchor="" x="24.03" y="267.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`vn_vfsunlock (2,372 samples, 4.13%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`vn_vfsunlock (2,372 samples, 4.13%)</title><rect x="897.3" y="113" width="48.8" height="15.0" fill="rgb(236,99,22)" rx="2" ry="2" />
<text text-anchor="" x="900.35" y="123.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >genu..</text>
</g>
<g class="func_g" onmouseover="s('genunix`openat (17 samples, 0.03%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`openat (17 samples, 0.03%)</title><rect x="1157.9" y="257" width="0.3" height="15.0" fill="rgb(231,105,33)" rx="2" ry="2" />
<text text-anchor="" x="1160.85" y="267.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`bcmp (45 samples, 0.08%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`bcmp (45 samples, 0.08%)</title><rect x="812.4" y="81" width="1.0" height="15.0" fill="rgb(207,97,23)" rx="2" ry="2" />
<text text-anchor="" x="815.44" y="91.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`audit_getstate (62 samples, 0.11%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`audit_getstate (62 samples, 0.11%)</title><rect x="138.5" y="225" width="1.3" height="15.0" fill="rgb(229,74,37)" rx="2" ry="2" />
<text text-anchor="" x="141.52" y="235.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`crfree (9 samples, 0.02%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`crfree (9 samples, 0.02%)</title><rect x="146.5" y="209" width="0.2" height="15.0" fill="rgb(208,192,15)" rx="2" ry="2" />
<text text-anchor="" x="149.51" y="219.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`kmem_cache_free (18 samples, 0.03%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`kmem_cache_free (18 samples, 0.03%)</title><rect x="908.7" y="81" width="0.4" height="15.0" fill="rgb(238,106,39)" rx="2" ry="2" />
<text text-anchor="" x="911.75" y="91.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`vn_vfslocks_rele (903 samples, 1.57%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`vn_vfslocks_rele (903 samples, 1.57%)</title><rect x="903.3" y="97" width="18.6" height="15.0" fill="rgb(234,114,47)" rx="2" ry="2" />
<text text-anchor="" x="906.30" y="107.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`vn_invalid (20 samples, 0.03%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`vn_invalid (20 samples, 0.03%)</title><rect x="965.8" y="81" width="0.4" height="15.0" fill="rgb(210,115,47)" rx="2" ry="2" />
<text text-anchor="" x="968.84" y="91.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`vn_vfslocks_rele (50 samples, 0.09%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`vn_vfslocks_rele (50 samples, 0.09%)</title><rect x="453.2" y="81" width="1.0" height="15.0" fill="rgb(232,100,41)" rx="2" ry="2" />
<text text-anchor="" x="456.17" y="91.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`lookuppnvp (10 samples, 0.02%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`lookuppnvp (10 samples, 0.02%)</title><rect x="1122.9" y="161" width="0.2" height="15.0" fill="rgb(219,219,30)" rx="2" ry="2" />
<text text-anchor="" x="1125.93" y="171.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`fd_find (161 samples, 0.28%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`fd_find (161 samples, 0.28%)</title><rect x="160.5" y="161" width="3.3" height="15.0" fill="rgb(221,98,19)" rx="2" ry="2" />
<text text-anchor="" x="163.48" y="171.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('ufs`ufs_lookup (5,399 samples, 9.40%)')" onmouseout="c()" onclick="zoom(this)">
<title>ufs`ufs_lookup (5,399 samples, 9.40%)</title><rect x="335.9" y="81" width="110.9" height="15.0" fill="rgb(238,53,50)" rx="2" ry="2" />
<text text-anchor="" x="338.85" y="91.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >ufs`ufs_lookup</text>
</g>
<g class="func_g" onmouseover="s('unix`0xfffffffffb800c7c (42 samples, 0.07%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`0xfffffffffb800c7c (42 samples, 0.07%)</title><rect x="12.7" y="273" width="0.9" height="15.0" fill="rgb(233,122,33)" rx="2" ry="2" />
<text text-anchor="" x="15.69" y="283.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`vn_openat (14 samples, 0.02%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`vn_openat (14 samples, 0.02%)</title><rect x="1157.6" y="225" width="0.3" height="15.0" fill="rgb(254,17,22)" rx="2" ry="2" />
<text text-anchor="" x="1160.56" y="235.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`setf (16 samples, 0.03%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`setf (16 samples, 0.03%)</title><rect x="1156.4" y="225" width="0.4" height="15.0" fill="rgb(251,118,19)" rx="2" ry="2" />
<text text-anchor="" x="1159.43" y="235.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`traverse (7,243 samples, 12.61%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`traverse (7,243 samples, 12.61%)</title><rect x="447.6" y="97" width="148.8" height="15.0" fill="rgb(249,18,37)" rx="2" ry="2" />
<text text-anchor="" x="450.56" y="107.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >genunix`traverse</text>
</g>
<g class="func_g" onmouseover="s('genunix`rwst_tryenter (734 samples, 1.28%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`rwst_tryenter (734 samples, 1.28%)</title><rect x="457.3" y="65" width="15.1" height="15.0" fill="rgb(232,17,15)" rx="2" ry="2" />
<text text-anchor="" x="460.34" y="75.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unix`mutex_enter (366 samples, 0.64%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`mutex_enter (366 samples, 0.64%)</title><rect x="284.5" y="81" width="7.5" height="15.0" fill="rgb(213,91,3)" rx="2" ry="2" />
<text text-anchor="" x="287.45" y="91.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`fop_lookup (6,470 samples, 11.26%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`fop_lookup (6,470 samples, 11.26%)</title><rect x="313.8" y="97" width="133.0" height="15.0" fill="rgb(249,6,23)" rx="2" ry="2" />
<text text-anchor="" x="316.85" y="107.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >genunix`fop_lookup</text>
</g>
<g class="func_g" onmouseover="s('unix`mutex_exit (135 samples, 0.24%)')" onmouseout="c()" onclick="zoom(this)">
<title>unix`mutex_exit (135 samples, 0.24%)</title><rect x="819.9" y="97" width="2.8" height="15.0" fill="rgb(228,196,44)" rx="2" ry="2" />
<text text-anchor="" x="822.94" y="107.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('lofs`makelfsnode (82 samples, 0.14%)')" onmouseout="c()" onclick="zoom(this)">
<title>lofs`makelfsnode (82 samples, 0.14%)</title><rect x="661.6" y="81" width="1.7" height="15.0" fill="rgb(239,175,9)" rx="2" ry="2" />
<text text-anchor="" x="664.60" y="91.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('genunix`copen (7 samples, 0.01%)')" onmouseout="c()" onclick="zoom(this)">
<title>genunix`copen (7 samples, 0.01%)</title><rect x="137.8" y="241" width="0.2" height="15.0" fill="rgb(236,69,31)" rx="2" ry="2" />
<text text-anchor="" x="140.82" y="251.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
</svg>

<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" width="1200" height="676" onload="init(evt)" viewBox="0 0 1200 676" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<defs >
	<linearGradient id="background" y1="0" y2="1" x1="0" x2="0" >
		<stop stop-color="#eeeeee" offset="5%" />
		<stop stop-color="#eeeeb0" offset="95%" />
	</linearGradient>
</defs>
<style type="text/css">
	.func_g:hover { stroke:black; stroke-width:0.5; }
</style>
<script type="text/ecmascript">
<![CDATA[
	var details;
	function init(evt) { details = document.getElementById("details").firstChild; }
	function s(info) { details.nodeValue = "Function: " + info; }
	function c() { details.nodeValue = ' '; }
]]>
</script>
<rect x="0.0" y="0" width="1200.0" height="676.0" fill="url(#background)"  />
<text text-anchor="middle" x="600" y="40" font-size="25" font-family="Verdana" fill="rgb(0,0,0)"  >Flame Graph</text>
<text text-anchor="" x="10" y="651" font-size="20" font-family="Verdana" fill="rgb(0,0,0)" id="details" > </text>
<g class="func_g" onmouseover="s('sch_direct_xmit (84 samples, 2.85%)')" onmouseout="c()">
<title>sch_direct_xmit (84 samples, 2.85%)</title><rect x="1154.4" y="185" width="33.6" height="25.0" fill="rgb(235,35,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('call_filldir (58 samples, 1.96%)')" onmouseout="c()">
<title>call_filldir (58 samples, 1.96%)</title><rect x="138.7" y="471" width="23.2" height="25.0" fill="rgb(250,50,36)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('do_exit (6 samples, 0.20%)')" onmouseout="c()">
<title>do_exit (6 samples, 0.20%)</title><rect x="111.1" y="497" width="2.4" height="25.0" fill="rgb(230,65,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('sys_mmap_pgoff (2 samples, 0.07%)')" onmouseout="c()">
<title>sys_mmap_pgoff (2 samples, 0.07%)</title><rect x="357.4" y="523" width="0.8" height="25.0" fill="rgb(206,19,8)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__alloc_pages_nodemask (3 samples, 0.10%)')" onmouseout="c()">
<title>__alloc_pages_nodemask (3 samples, 0.10%)</title><rect x="19.6" y="445" width="1.2" height="25.0" fill="rgb(239,157,17)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('user_path_at_empty (1,177 samples, 39.87%)')" onmouseout="c()">
<title>user_path_at_empty (1,177 samples, 39.87%)</title><rect x="477.3" y="471" width="470.5" height="25.0" fill="rgb(253,93,42)" rx="2" ry="2" />
<text text-anchor="" x="480.283197831978" y="490.5" font-size="20" font-family="Verdana" fill="rgb(0,0,0)"  >user_path_at_empty</text>
</g>
<g class="func_g" onmouseover="s('_copy_to_user (1 samples, 0.03%)')" onmouseout="c()">
<title>_copy_to_user (1 samples, 0.03%)</title><rect x="364.2" y="497" width="0.4" height="25.0" fill="rgb(215,113,24)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('read_cache_page_async (4 samples, 0.14%)')" onmouseout="c()">
<title>read_cache_page_async (4 samples, 0.14%)</title><rect x="1117.2" y="419" width="1.6" height="25.0" fill="rgb(240,97,18)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('do_last (57 samples, 1.93%)')" onmouseout="c()">
<title>do_last (57 samples, 1.93%)</title><rect x="1028.5" y="445" width="22.8" height="25.0" fill="rgb(213,26,14)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__list_add (3 samples, 0.10%)')" onmouseout="c()">
<title>__list_add (3 samples, 0.10%)</title><rect x="1044.9" y="341" width="1.2" height="25.0" fill="rgb(235,91,14)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('find_get_page (17 samples, 0.58%)')" onmouseout="c()">
<title>find_get_page (17 samples, 0.58%)</title><rect x="188.7" y="289" width="6.8" height="25.0" fill="rgb(232,122,31)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('cap_file_alloc_security (1 samples, 0.03%)')" onmouseout="c()">
<title>cap_file_alloc_security (1 samples, 0.03%)</title><rect x="1093.7" y="393" width="0.4" height="25.0" fill="rgb(222,33,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('user_path_at (1,184 samples, 40.11%)')" onmouseout="c()">
<title>user_path_at (1,184 samples, 40.11%)</title><rect x="474.5" y="497" width="473.3" height="25.0" fill="rgb(212,198,5)" rx="2" ry="2" />
<text text-anchor="" x="477.485094850949" y="516.5" font-size="20" font-family="Verdana" fill="rgb(0,0,0)"  >user_path_at</text>
</g>
<g class="func_g" onmouseover="s('path_init (17 samples, 0.58%)')" onmouseout="c()">
<title>path_init (17 samples, 0.58%)</title><rect x="1140.4" y="445" width="6.8" height="25.0" fill="rgb(242,59,39)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('flush_tlb_page (1 samples, 0.03%)')" onmouseout="c()">
<title>flush_tlb_page (1 samples, 0.03%)</title><rect x="21.2" y="419" width="0.4" height="25.0" fill="rgb(216,218,42)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__do_softirq (1 samples, 0.03%)')" onmouseout="c()">
<title>__do_softirq (1 samples, 0.03%)</title><rect x="10.0" y="445" width="0.4" height="25.0" fill="rgb(219,39,15)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unmap_region (2 samples, 0.07%)')" onmouseout="c()">
<title>unmap_region (2 samples, 0.07%)</title><rect x="358.2" y="497" width="0.8" height="25.0" fill="rgb(252,6,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__ext4_check_dir_entry (25 samples, 0.85%)')" onmouseout="c()">
<title>__ext4_check_dir_entry (25 samples, 0.85%)</title><rect x="170.3" y="419" width="10.0" height="25.0" fill="rgb(232,188,28)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vfsmount_lock_local_lock (1 samples, 0.03%)')" onmouseout="c()">
<title>vfsmount_lock_local_lock (1 samples, 0.03%)</title><rect x="1129.6" y="445" width="0.4" height="25.0" fill="rgb(236,41,19)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('path_init (209 samples, 7.08%)')" onmouseout="c()">
<title>path_init (209 samples, 7.08%)</title><rect x="687.9" y="393" width="83.6" height="25.0" fill="rgb(219,81,10)" rx="2" ry="2" />
<text text-anchor="" x="690.940379403794" y="412.5" font-size="20" font-family="Verdana" fill="rgb(0,0,0)"  >path_..</text>
</g>
<g class="func_g" onmouseover="s('n_tty_write (1 samples, 0.03%)')" onmouseout="c()">
<title>n_tty_write (1 samples, 0.03%)</title><rect x="1189.6" y="471" width="0.4" height="25.0" fill="rgb(238,53,47)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('path_put (5 samples, 0.17%)')" onmouseout="c()">
<title>path_put (5 samples, 0.17%)</title><rect x="1049.3" y="419" width="2.0" height="25.0" fill="rgb(249,105,6)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('mem_cgroup_count_vm_event (2 samples, 0.07%)')" onmouseout="c()">
<title>mem_cgroup_count_vm_event (2 samples, 0.07%)</title><rect x="22.4" y="497" width="0.8" height="25.0" fill="rgb(229,191,39)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('page_add_file_rmap (1 samples, 0.03%)')" onmouseout="c()">
<title>page_add_file_rmap (1 samples, 0.03%)</title><rect x="19.2" y="445" width="0.4" height="25.0" fill="rgb(246,69,20)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('copy_page (9 samples, 0.30%)')" onmouseout="c()">
<title>copy_page (9 samples, 0.30%)</title><rect x="10.8" y="471" width="3.6" height="25.0" fill="rgb(228,158,14)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('jiffies_to_usecs (1 samples, 0.03%)')" onmouseout="c()">
<title>jiffies_to_usecs (1 samples, 0.03%)</title><rect x="1188.4" y="289" width="0.4" height="25.0" fill="rgb(244,93,53)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__phys_addr (7 samples, 0.24%)')" onmouseout="c()">
<title>__phys_addr (7 samples, 0.24%)</title><rect x="1151.2" y="445" width="2.8" height="25.0" fill="rgb(217,181,5)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('security_inode_permission (3 samples, 0.10%)')" onmouseout="c()">
<title>security_inode_permission (3 samples, 0.10%)</title><rect x="686.7" y="341" width="1.2" height="25.0" fill="rgb(213,131,14)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('mntget (1 samples, 0.03%)')" onmouseout="c()">
<title>mntget (1 samples, 0.03%)</title><rect x="1048.9" y="367" width="0.4" height="25.0" fill="rgb(246,136,22)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kern_path (1 samples, 0.03%)')" onmouseout="c()">
<title>kern_path (1 samples, 0.03%)</title><rect x="110.7" y="471" width="0.4" height="25.0" fill="rgb(221,134,10)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__strncpy_from_user (2 samples, 0.07%)')" onmouseout="c()">
<title>__strncpy_from_user (2 samples, 0.07%)</title><rect x="1103.3" y="419" width="0.8" height="25.0" fill="rgb(229,85,27)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('security_inode_permission (74 samples, 2.51%)')" onmouseout="c()">
<title>security_inode_permission (74 samples, 2.51%)</title><rect x="735.9" y="341" width="29.6" height="25.0" fill="rgb(227,220,44)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('writeback_single_inode (1 samples, 0.03%)')" onmouseout="c()">
<title>writeback_single_inode (1 samples, 0.03%)</title><rect x="10.4" y="393" width="0.4" height="25.0" fill="rgb(244,8,10)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('htree_dirblock_to_tree (408 samples, 13.82%)')" onmouseout="c()">
<title>htree_dirblock_to_tree (408 samples, 13.82%)</title><rect x="162.7" y="445" width="163.1" height="25.0" fill="rgb(211,93,30)" rx="2" ry="2" />
<text text-anchor="" x="165.69647696477" y="464.5" font-size="20" font-family="Verdana" fill="rgb(0,0,0)"  >htree_dirbl..</text>
</g>
<g class="func_g" onmouseover="s('__do_softirq (1 samples, 0.03%)')" onmouseout="c()">
<title>__do_softirq (1 samples, 0.03%)</title><rect x="14.4" y="497" width="0.4" height="25.0" fill="rgb(230,165,32)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('fd_install (3 samples, 0.10%)')" onmouseout="c()">
<title>fd_install (3 samples, 0.10%)</title><rect x="1098.9" y="497" width="1.2" height="25.0" fill="rgb(244,142,50)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('sys_openat (230 samples, 7.79%)')" onmouseout="c()">
<title>sys_openat (230 samples, 7.79%)</title><rect x="1018.1" y="549" width="92.0" height="25.0" fill="rgb(233,1,42)" rx="2" ry="2" />
<text text-anchor="" x="1021.11653116531" y="568.5" font-size="20" font-family="Verdana" fill="rgb(0,0,0)"  >sys_o..</text>
</g>
<g class="func_g" onmouseover="s('kfree (47 samples, 1.59%)')" onmouseout="c()">
<title>kfree (47 samples, 1.59%)</title><rect x="80.8" y="393" width="18.7" height="25.0" fill="rgb(222,112,47)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('generic_file_aio_read (2 samples, 0.07%)')" onmouseout="c()">
<title>generic_file_aio_read (2 samples, 0.07%)</title><rect x="1110.5" y="471" width="0.8" height="25.0" fill="rgb(209,48,8)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('mntput_no_expire (2 samples, 0.07%)')" onmouseout="c()">
<title>mntput_no_expire (2 samples, 0.07%)</title><rect x="105.5" y="445" width="0.8" height="25.0" fill="rgb(220,213,32)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('copy_pte_range (1 samples, 0.03%)')" onmouseout="c()">
<title>copy_pte_range (1 samples, 0.03%)</title><rect x="23.6" y="419" width="0.4" height="25.0" fill="rgb(245,56,24)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('module_put (1 samples, 0.03%)')" onmouseout="c()">
<title>module_put (1 samples, 0.03%)</title><rect x="106.3" y="471" width="0.4" height="25.0" fill="rgb(222,135,45)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('get_empty_filp (107 samples, 3.62%)')" onmouseout="c()">
<title>get_empty_filp (107 samples, 3.62%)</title><rect x="1051.3" y="445" width="42.8" height="25.0" fill="rgb(205,47,39)" rx="2" ry="2" />
<text text-anchor="" x="1054.29403794038" y="464.5" font-size="20" font-family="Verdana" fill="rgb(0,0,0)"  >g..</text>
</g>
<g class="func_g" onmouseover="s('__rb_rotate_right (3 samples, 0.10%)')" onmouseout="c()">
<title>__rb_rotate_right (3 samples, 0.10%)</title><rect x="263.0" y="367" width="1.2" height="25.0" fill="rgb(232,153,36)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('virtqueue_kick (84 samples, 2.85%)')" onmouseout="c()">
<title>virtqueue_kick (84 samples, 2.85%)</title><rect x="1154.4" y="107" width="33.6" height="25.0" fill="rgb(216,65,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('do_sync_read (2 samples, 0.07%)')" onmouseout="c()">
<title>do_sync_read (2 samples, 0.07%)</title><rect x="1110.5" y="497" width="0.8" height="25.0" fill="rgb(227,59,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__lookup_tag (1 samples, 0.03%)')" onmouseout="c()">
<title>__lookup_tag (1 samples, 0.03%)</title><rect x="10.4" y="211" width="0.4" height="25.0" fill="rgb(211,201,44)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('page_put_link (1 samples, 0.03%)')" onmouseout="c()">
<title>page_put_link (1 samples, 0.03%)</title><rect x="1119.2" y="497" width="0.4" height="25.0" fill="rgb(210,182,54)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('cp_new_stat (88 samples, 2.98%)')" onmouseout="c()">
<title>cp_new_stat (88 samples, 2.98%)</title><rect x="381.3" y="523" width="35.2" height="25.0" fill="rgb(225,107,16)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('do_fork (2 samples, 0.07%)')" onmouseout="c()">
<title>do_fork (2 samples, 0.07%)</title><rect x="23.2" y="523" width="0.8" height="25.0" fill="rgb(207,155,33)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('putname (9 samples, 0.30%)')" onmouseout="c()">
<title>putname (9 samples, 0.30%)</title><rect x="1150.4" y="497" width="3.6" height="25.0" fill="rgb(235,36,2)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rb_insert_color (38 samples, 1.29%)')" onmouseout="c()">
<title>rb_insert_color (38 samples, 1.29%)</title><rect x="249.0" y="393" width="15.2" height="25.0" fill="rgb(249,15,14)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('exit_mmap (6 samples, 0.20%)')" onmouseout="c()">
<title>exit_mmap (6 samples, 0.20%)</title><rect x="111.1" y="419" width="2.4" height="25.0" fill="rgb(209,157,23)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ext4_htree_fill_tree (410 samples, 13.89%)')" onmouseout="c()">
<title>ext4_htree_fill_tree (410 samples, 13.89%)</title><rect x="161.9" y="471" width="163.9" height="25.0" fill="rgb(208,213,1)" rx="2" ry="2" />
<text text-anchor="" x="164.89701897019" y="490.5" font-size="20" font-family="Verdana" fill="rgb(0,0,0)"  >ext4_htree_..</text>
</g>
<g class="func_g" onmouseover="s('____pagevec_lru_add (2 samples, 0.07%)')" onmouseout="c()">
<title>____pagevec_lru_add (2 samples, 0.07%)</title><rect x="60.0" y="445" width="0.8" height="25.0" fill="rgb(246,217,45)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('do_lookup (217 samples, 7.35%)')" onmouseout="c()">
<title>do_lookup (217 samples, 7.35%)</title><rect x="554.0" y="393" width="86.8" height="25.0" fill="rgb(242,186,5)" rx="2" ry="2" />
<text text-anchor="" x="557.031165311653" y="412.5" font-size="20" font-family="Verdana" fill="rgb(0,0,0)"  >do_lo..</text>
</g>
<g class="func_g" onmouseover="s('inet_sendmsg (89 samples, 3.01%)')" onmouseout="c()">
<title>inet_sendmsg (89 samples, 3.01%)</title><rect x="1154.0" y="445" width="35.6" height="25.0" fill="rgb(216,211,21)" rx="2" ry="2" />
<text text-anchor="" x="1157.0243902439" y="464.5" font-size="20" font-family="Verdana" fill="rgb(0,0,0)"  >i..</text>
</g>
<g class="func_g" onmouseover="s('complete_walk (4 samples, 0.14%)')" onmouseout="c()">
<title>complete_walk (4 samples, 0.14%)</title><rect x="1132.8" y="445" width="1.6" height="25.0" fill="rgb(205,29,20)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('touch_atime (1 samples, 0.03%)')" onmouseout="c()">
<title>touch_atime (1 samples, 0.03%)</title><rect x="1110.9" y="445" width="0.4" height="25.0" fill="rgb(231,84,16)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__dentry_open (23 samples, 0.78%)')" onmouseout="c()">
<title>__dentry_open (23 samples, 0.78%)</title><rect x="1038.5" y="393" width="9.2" height="25.0" fill="rgb(232,164,5)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('file_ra_state_init (5 samples, 0.17%)')" onmouseout="c()">
<title>file_ra_state_init (5 samples, 0.17%)</title><rect x="1041.3" y="367" width="2.0" height="25.0" fill="rgb(247,37,30)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('sys_readlinkat (107 samples, 3.62%)')" onmouseout="c()">
<title>sys_readlinkat (107 samples, 3.62%)</title><rect x="1111.3" y="549" width="42.7" height="25.0" fill="rgb(242,46,43)" rx="2" ry="2" />
<text text-anchor="" x="1114.25338753388" y="568.5" font-size="20" font-family="Verdana" fill="rgb(0,0,0)"  >s..</text>
</g>
<g class="func_g" onmouseover="s('security_file_fcntl (3 samples, 0.10%)')" onmouseout="c()">
<title>security_file_fcntl (3 samples, 0.10%)</title><rect x="119.1" y="523" width="1.2" height="25.0" fill="rgb(253,56,21)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_cond_resched (3 samples, 0.10%)')" onmouseout="c()">
<title>_cond_resched (3 samples, 0.10%)</title><rect x="871.0" y="367" width="1.2" height="25.0" fill="rgb(213,158,17)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('writeback_sb_inodes (1 samples, 0.03%)')" onmouseout="c()">
<title>writeback_sb_inodes (1 samples, 0.03%)</title><rect x="10.4" y="419" width="0.4" height="25.0" fill="rgb(224,183,30)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('generic_fillattr (1 samples, 0.03%)')" onmouseout="c()">
<title>generic_fillattr (1 samples, 0.03%)</title><rect x="375.8" y="471" width="0.4" height="25.0" fill="rgb(206,143,24)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__writeback_inodes_wb (1 samples, 0.03%)')" onmouseout="c()">
<title>__writeback_inodes_wb (1 samples, 0.03%)</title><rect x="10.4" y="445" width="0.4" height="25.0" fill="rgb(215,135,21)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_raw_spin_lock (1 samples, 0.03%)')" onmouseout="c()">
<title>_raw_spin_lock (1 samples, 0.03%)</title><rect x="1134.0" y="419" width="0.4" height="25.0" fill="rgb(220,105,22)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix_find_other (1 samples, 0.03%)')" onmouseout="c()">
<title>unix_find_other (1 samples, 0.03%)</title><rect x="110.7" y="497" width="0.4" height="25.0" fill="rgb(220,46,9)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__list_del_entry (5 samples, 0.17%)')" onmouseout="c()">
<title>__list_del_entry (5 samples, 0.17%)</title><rect x="100.7" y="445" width="2.0" height="25.0" fill="rgb(212,65,30)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__phys_addr (9 samples, 0.30%)')" onmouseout="c()">
<title>__phys_addr (9 samples, 0.30%)</title><rect x="91.9" y="367" width="3.6" height="25.0" fill="rgb(206,119,18)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ip_finish_output (84 samples, 2.85%)')" onmouseout="c()">
<title>ip_finish_output (84 samples, 2.85%)</title><rect x="1154.4" y="237" width="33.6" height="25.0" fill="rgb(245,0,44)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('sys_clone (2 samples, 0.07%)')" onmouseout="c()">
<title>sys_clone (2 samples, 0.07%)</title><rect x="23.2" y="549" width="0.8" height="25.0" fill="rgb(241,50,48)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vfsmount_lock_local_unlock (8 samples, 0.27%)')" onmouseout="c()">
<title>vfsmount_lock_local_unlock (8 samples, 0.27%)</title><rect x="550.8" y="367" width="3.2" height="25.0" fill="rgb(226,217,48)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('wb_writeback (1 samples, 0.03%)')" onmouseout="c()">
<title>wb_writeback (1 samples, 0.03%)</title><rect x="10.4" y="471" width="0.4" height="25.0" fill="rgb(230,200,15)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('radix_tree_lookup_slot (1 samples, 0.03%)')" onmouseout="c()">
<title>radix_tree_lookup_slot (1 samples, 0.03%)</title><rect x="1118.4" y="341" width="0.4" height="25.0" fill="rgb(236,60,30)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('copy_user_highpage (9 samples, 0.30%)')" onmouseout="c()">
<title>copy_user_highpage (9 samples, 0.30%)</title><rect x="10.8" y="497" width="3.6" height="25.0" fill="rgb(238,98,34)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('cap_file_permission (2 samples, 0.07%)')" onmouseout="c()">
<title>cap_file_permission (2 samples, 0.07%)</title><rect x="351.0" y="471" width="0.8" height="25.0" fill="rgb(222,116,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('alloc_pages_current (2 samples, 0.07%)')" onmouseout="c()">
<title>alloc_pages_current (2 samples, 0.07%)</title><rect x="1088.5" y="341" width="0.8" height="25.0" fill="rgb(229,187,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('free_rb_tree_fname (70 samples, 2.37%)')" onmouseout="c()">
<title>free_rb_tree_fname (70 samples, 2.37%)</title><rect x="71.6" y="419" width="27.9" height="25.0" fill="rgb(245,61,52)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('touch_atime (11 samples, 0.37%)')" onmouseout="c()">
<title>touch_atime (11 samples, 0.37%)</title><rect x="352.6" y="497" width="4.4" height="25.0" fill="rgb(214,3,20)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('find_get_page (2 samples, 0.07%)')" onmouseout="c()">
<title>find_get_page (2 samples, 0.07%)</title><rect x="18.4" y="419" width="0.8" height="25.0" fill="rgb(225,120,37)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('alloc_fd (15 samples, 0.51%)')" onmouseout="c()">
<title>alloc_fd (15 samples, 0.51%)</title><rect x="1020.5" y="497" width="6.0" height="25.0" fill="rgb(210,91,10)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kfree (3 samples, 0.10%)')" onmouseout="c()">
<title>kfree (3 samples, 0.10%)</title><rect x="99.5" y="419" width="1.2" height="25.0" fill="rgb(214,50,46)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('page_add_new_anon_rmap (2 samples, 0.07%)')" onmouseout="c()">
<title>page_add_new_anon_rmap (2 samples, 0.07%)</title><rect x="21.6" y="471" width="0.8" height="25.0" fill="rgb(224,81,8)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ip_output (84 samples, 2.85%)')" onmouseout="c()">
<title>ip_output (84 samples, 2.85%)</title><rect x="1154.4" y="263" width="33.6" height="25.0" fill="rgb(227,172,50)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('sys_close (124 samples, 4.20%)')" onmouseout="c()">
<title>sys_close (124 samples, 4.20%)</title><rect x="61.2" y="549" width="49.5" height="25.0" fill="rgb(228,35,24)" rx="2" ry="2" />
<text text-anchor="" x="64.1653116531165" y="568.5" font-size="20" font-family="Verdana" fill="rgb(0,0,0)"  >sy..</text>
</g>
<g class="func_g" onmouseover="s('sys_munmap (2 samples, 0.07%)')" onmouseout="c()">
<title>sys_munmap (2 samples, 0.07%)</title><rect x="358.2" y="549" width="0.8" height="25.0" fill="rgb(246,50,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('path_lookupat (1 samples, 0.03%)')" onmouseout="c()">
<title>path_lookupat (1 samples, 0.03%)</title><rect x="110.7" y="419" width="0.4" height="25.0" fill="rgb(228,137,44)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tcp_transmit_skb (85 samples, 2.88%)')" onmouseout="c()">
<title>tcp_transmit_skb (85 samples, 2.88%)</title><rect x="1154.4" y="341" width="34.0" height="25.0" fill="rgb(246,141,46)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tlb_flush_mmu (1 samples, 0.03%)')" onmouseout="c()">
<title>tlb_flush_mmu (1 samples, 0.03%)</title><rect x="358.6" y="445" width="0.4" height="25.0" fill="rgb(226,29,34)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('security_file_free (2 samples, 0.07%)')" onmouseout="c()">
<title>security_file_free (2 samples, 0.07%)</title><rect x="107.1" y="471" width="0.8" height="25.0" fill="rgb(207,152,32)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('lru_add_drain (2 samples, 0.07%)')" onmouseout="c()">
<title>lru_add_drain (2 samples, 0.07%)</title><rect x="60.0" y="471" width="0.8" height="25.0" fill="rgb(223,129,26)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('put_page (5 samples, 0.17%)')" onmouseout="c()">
<title>put_page (5 samples, 0.17%)</title><rect x="111.1" y="315" width="2.0" height="25.0" fill="rgb(208,160,46)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__d_alloc (1 samples, 0.03%)')" onmouseout="c()">
<title>__d_alloc (1 samples, 0.03%)</title><rect x="110.7" y="289" width="0.4" height="25.0" fill="rgb(220,19,44)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('strncpy_from_user (6 samples, 0.20%)')" onmouseout="c()">
<title>strncpy_from_user (6 samples, 0.20%)</title><rect x="1101.7" y="445" width="2.4" height="25.0" fill="rgb(251,30,36)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('put_pid (1 samples, 0.03%)')" onmouseout="c()">
<title>put_pid (1 samples, 0.03%)</title><rect x="106.7" y="471" width="0.4" height="25.0" fill="rgb(252,97,31)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tcp_rcv_established (1 samples, 0.03%)')" onmouseout="c()">
<title>tcp_rcv_established (1 samples, 0.03%)</title><rect x="1188.4" y="341" width="0.4" height="25.0" fill="rgb(234,68,1)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_copy_to_user (2 samples, 0.07%)')" onmouseout="c()">
<title>_copy_to_user (2 samples, 0.07%)</title><rect x="154.7" y="419" width="0.8" height="25.0" fill="rgb(222,177,19)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tlb_finish_mmu (1 samples, 0.03%)')" onmouseout="c()">
<title>tlb_finish_mmu (1 samples, 0.03%)</title><rect x="358.6" y="471" width="0.4" height="25.0" fill="rgb(231,208,7)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('path_lookupat (716 samples, 24.25%)')" onmouseout="c()">
<title>path_lookupat (716 samples, 24.25%)</title><rect x="485.3" y="419" width="286.2" height="25.0" fill="rgb(247,118,38)" rx="2" ry="2" />
<text text-anchor="" x="488.277777777778" y="438.5" font-size="20" font-family="Verdana" fill="rgb(0,0,0)"  >path_lookupat</text>
</g>
<g class="func_g" onmouseover="s('__page_cache_release.part.1 (2 samples, 0.07%)')" onmouseout="c()">
<title>__page_cache_release.part.1 (2 samples, 0.07%)</title><rect x="111.5" y="263" width="0.8" height="25.0" fill="rgb(213,138,21)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vfs_readlink (23 samples, 0.78%)')" onmouseout="c()">
<title>vfs_readlink (23 samples, 0.78%)</title><rect x="1119.6" y="497" width="9.2" height="25.0" fill="rgb(228,55,1)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('smp_apic_timer_interrupt (1 samples, 0.03%)')" onmouseout="c()">
<title>smp_apic_timer_interrupt (1 samples, 0.03%)</title><rect x="10.0" y="549" width="0.4" height="25.0" fill="rgb(243,71,23)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('radix_tree_lookup_element (11 samples, 0.37%)')" onmouseout="c()">
<title>radix_tree_lookup_element (11 samples, 0.37%)</title><rect x="191.1" y="237" width="4.4" height="25.0" fill="rgb(220,147,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('mntput_no_expire (2 samples, 0.07%)')" onmouseout="c()">
<title>mntput_no_expire (2 samples, 0.07%)</title><rect x="1129.2" y="471" width="0.8" height="25.0" fill="rgb(215,79,16)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('do_read_cache_page (4 samples, 0.14%)')" onmouseout="c()">
<title>do_read_cache_page (4 samples, 0.14%)</title><rect x="1117.2" y="393" width="1.6" height="25.0" fill="rgb(250,158,40)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('handle_mm_fault (14 samples, 0.47%)')" onmouseout="c()">
<title>handle_mm_fault (14 samples, 0.47%)</title><rect x="17.6" y="523" width="5.6" height="25.0" fill="rgb(244,77,10)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tcp_v4_md5_lookup (1 samples, 0.03%)')" onmouseout="c()">
<title>tcp_v4_md5_lookup (1 samples, 0.03%)</title><rect x="1189.2" y="315" width="0.4" height="25.0" fill="rgb(205,161,6)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kmem_cache_free (187 samples, 6.33%)')" onmouseout="c()">
<title>kmem_cache_free (187 samples, 6.33%)</title><rect x="873.0" y="419" width="74.8" height="25.0" fill="rgb(206,203,47)" rx="2" ry="2" />
<text text-anchor="" x="876.014905149052" y="438.5" font-size="20" font-family="Verdana" fill="rgb(0,0,0)"  >kmem..</text>
</g>
<g class="func_g" onmouseover="s('locks_remove_posix (7 samples, 0.24%)')" onmouseout="c()">
<title>locks_remove_posix (7 samples, 0.24%)</title><rect x="107.9" y="497" width="2.8" height="25.0" fill="rgb(249,24,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('path_init (7 samples, 0.24%)')" onmouseout="c()">
<title>path_init (7 samples, 0.24%)</title><rect x="1096.1" y="445" width="2.8" height="25.0" fill="rgb(245,58,54)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('system_call_after_swapgs (79 samples, 2.68%)')" onmouseout="c()">
<title>system_call_after_swapgs (79 samples, 2.68%)</title><rect x="25.6" y="575" width="31.6" height="25.0" fill="rgb(207,108,49)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('putname (15 samples, 0.51%)')" onmouseout="c()">
<title>putname (15 samples, 0.51%)</title><rect x="1104.1" y="497" width="6.0" height="25.0" fill="rgb(218,212,23)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_cond_resched (1 samples, 0.03%)')" onmouseout="c()">
<title>_cond_resched (1 samples, 0.03%)</title><rect x="363.8" y="497" width="0.4" height="25.0" fill="rgb(208,172,9)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('locks_remove_flock (2 samples, 0.07%)')" onmouseout="c()">
<title>locks_remove_flock (2 samples, 0.07%)</title><rect x="104.3" y="471" width="0.8" height="25.0" fill="rgb(240,142,6)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('path_put (3 samples, 0.10%)')" onmouseout="c()">
<title>path_put (3 samples, 0.10%)</title><rect x="1128.8" y="523" width="1.2" height="25.0" fill="rgb(246,117,9)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vfs_getattr (174 samples, 5.89%)')" onmouseout="c()">
<title>vfs_getattr (174 samples, 5.89%)</title><rect x="947.8" y="497" width="69.5" height="25.0" fill="rgb(220,193,23)" rx="2" ry="2" />
<text text-anchor="" x="950.764227642276" y="516.5" font-size="20" font-family="Verdana" fill="rgb(0,0,0)"  >vfs..</text>
</g>
<g class="func_g" onmouseover="s('ext4_getblk (61 samples, 2.07%)')" onmouseout="c()">
<title>ext4_getblk (61 samples, 2.07%)</title><rect x="180.7" y="393" width="24.4" height="25.0" fill="rgb(250,25,45)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('files_lglock_local_lock_cpu (2 samples, 0.07%)')" onmouseout="c()">
<title>files_lglock_local_lock_cpu (2 samples, 0.07%)</title><rect x="102.7" y="445" width="0.8" height="25.0" fill="rgb(215,45,6)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('copy_user_generic_unrolled (11 samples, 0.37%)')" onmouseout="c()">
<title>copy_user_generic_unrolled (11 samples, 0.37%)</title><rect x="364.6" y="497" width="4.4" height="25.0" fill="rgb(248,167,42)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('do_sys_open (229 samples, 7.76%)')" onmouseout="c()">
<title>do_sys_open (229 samples, 7.76%)</title><rect x="1018.5" y="523" width="91.6" height="25.0" fill="rgb(217,60,17)" rx="2" ry="2" />
<text text-anchor="" x="1021.5162601626" y="542.5" font-size="20" font-family="Verdana" fill="rgb(0,0,0)"  >do_sy..</text>
</g>
<g class="func_g" onmouseover="s('__d_lookup_rcu (3 samples, 0.10%)')" onmouseout="c()">
<title>__d_lookup_rcu (3 samples, 0.10%)</title><rect x="1034.5" y="393" width="1.2" height="25.0" fill="rgb(243,145,1)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_raw_spin_unlock_irqrestore (1 samples, 0.03%)')" onmouseout="c()">
<title>_raw_spin_unlock_irqrestore (1 samples, 0.03%)</title><rect x="1189.6" y="289" width="0.4" height="25.0" fill="rgb(222,21,30)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('sys_ioctl (1 samples, 0.03%)')" onmouseout="c()">
<title>sys_ioctl (1 samples, 0.03%)</title><rect x="357.0" y="549" width="0.4" height="25.0" fill="rgb(230,84,47)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ext4_bread (62 samples, 2.10%)')" onmouseout="c()">
<title>ext4_bread (62 samples, 2.10%)</title><rect x="180.3" y="419" width="24.8" height="25.0" fill="rgb(213,192,1)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__slab_alloc (1 samples, 0.03%)')" onmouseout="c()">
<title>__slab_alloc (1 samples, 0.03%)</title><rect x="110.7" y="237" width="0.4" height="25.0" fill="rgb(226,65,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('do_lookup (5 samples, 0.17%)')" onmouseout="c()">
<title>do_lookup (5 samples, 0.17%)</title><rect x="1033.7" y="419" width="2.0" height="25.0" fill="rgb(239,223,31)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('all (2,952 samples, 100%)')" onmouseout="c()">
<title>all (2,952 samples, 100%)</title><rect x="10.0" y="601" width="1180.0" height="25.0" fill="rgb(232,90,23)" rx="2" ry="2" />
<text text-anchor="" x="13" y="620.5" font-size="20" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('__tcp_push_pending_frames (86 samples, 2.91%)')" onmouseout="c()">
<title>__tcp_push_pending_frames (86 samples, 2.91%)</title><rect x="1154.0" y="393" width="34.4" height="25.0" fill="rgb(245,165,6)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('do_path_lookup (728 samples, 24.66%)')" onmouseout="c()">
<title>do_path_lookup (728 samples, 24.66%)</title><rect x="480.5" y="445" width="291.0" height="25.0" fill="rgb(207,82,53)" rx="2" ry="2" />
<text text-anchor="" x="483.481029810298" y="464.5" font-size="20" font-family="Verdana" fill="rgb(0,0,0)"  >do_path_lookup</text>
</g>
<g class="func_g" onmouseover="s('_raw_spin_unlock_irqrestore (2 samples, 0.07%)')" onmouseout="c()">
<title>_raw_spin_unlock_irqrestore (2 samples, 0.07%)</title><rect x="111.5" y="237" width="0.8" height="25.0" fill="rgb(230,75,10)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('pty_write (1 samples, 0.03%)')" onmouseout="c()">
<title>pty_write (1 samples, 0.03%)</title><rect x="1189.6" y="445" width="0.4" height="25.0" fill="rgb(206,10,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('copy_user_generic_unrolled (24 samples, 0.81%)')" onmouseout="c()">
<title>copy_user_generic_unrolled (24 samples, 0.81%)</title><rect x="406.9" y="497" width="9.6" height="25.0" fill="rgb(211,182,29)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('pagevec_lru_move_fn (1 samples, 0.03%)')" onmouseout="c()">
<title>pagevec_lru_move_fn (1 samples, 0.03%)</title><rect x="60.4" y="419" width="0.4" height="25.0" fill="rgb(242,192,24)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('filp_close (119 samples, 4.03%)')" onmouseout="c()">
<title>filp_close (119 samples, 4.03%)</title><rect x="63.2" y="523" width="47.5" height="25.0" fill="rgb(210,89,2)" rx="2" ry="2" />
<text text-anchor="" x="66.1639566395664" y="542.5" font-size="20" font-family="Verdana" fill="rgb(0,0,0)"  >fi..</text>
</g>
<g class="func_g" onmouseover="s('sys_newfstatat (1,598 samples, 54.13%)')" onmouseout="c()">
<title>sys_newfstatat (1,598 samples, 54.13%)</title><rect x="378.6" y="549" width="638.7" height="25.0" fill="rgb(210,200,19)" rx="2" ry="2" />
<text text-anchor="" x="381.550135501355" y="568.5" font-size="20" font-family="Verdana" fill="rgb(0,0,0)"  >sys_newfstatat</text>
</g>
<g class="func_g" onmouseover="s('cap_inode_getattr (4 samples, 0.14%)')" onmouseout="c()">
<title>cap_inode_getattr (4 samples, 0.14%)</title><rect x="1015.7" y="445" width="1.6" height="25.0" fill="rgb(206,109,7)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('fsnotify_find_inode_mark (2 samples, 0.07%)')" onmouseout="c()">
<title>fsnotify_find_inode_mark (2 samples, 0.07%)</title><rect x="64.4" y="471" width="0.8" height="25.0" fill="rgb(225,172,50)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('filemap_fault (2 samples, 0.07%)')" onmouseout="c()">
<title>filemap_fault (2 samples, 0.07%)</title><rect x="18.4" y="445" width="0.8" height="25.0" fill="rgb(230,227,20)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('read_cache_page (6 samples, 0.20%)')" onmouseout="c()">
<title>read_cache_page (6 samples, 0.20%)</title><rect x="1116.8" y="445" width="2.4" height="25.0" fill="rgb(209,219,26)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('get_slab (12 samples, 0.41%)')" onmouseout="c()">
<title>get_slab (12 samples, 0.41%)</title><rect x="236.2" y="367" width="4.8" height="25.0" fill="rgb(236,69,4)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tcp_write_xmit (86 samples, 2.91%)')" onmouseout="c()">
<title>tcp_write_xmit (86 samples, 2.91%)</title><rect x="1154.0" y="367" width="34.4" height="25.0" fill="rgb(240,100,51)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kmem_cache_alloc (95 samples, 3.22%)')" onmouseout="c()">
<title>kmem_cache_alloc (95 samples, 3.22%)</title><rect x="1051.7" y="419" width="38.0" height="25.0" fill="rgb(246,95,34)" rx="2" ry="2" />
<text text-anchor="" x="1054.69376693767" y="438.5" font-size="20" font-family="Verdana" fill="rgb(0,0,0)"  >k..</text>
</g>
<g class="func_g" onmouseover="s('kthread (13 samples, 0.44%)')" onmouseout="c()">
<title>kthread (13 samples, 0.44%)</title><rect x="10.4" y="549" width="5.2" height="25.0" fill="rgb(230,133,11)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('find_get_pages_tag (1 samples, 0.03%)')" onmouseout="c()">
<title>find_get_pages_tag (1 samples, 0.03%)</title><rect x="10.4" y="263" width="0.4" height="25.0" fill="rgb(232,142,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__kmalloc (49 samples, 1.66%)')" onmouseout="c()">
<title>__kmalloc (49 samples, 1.66%)</title><rect x="224.3" y="393" width="19.5" height="25.0" fill="rgb(228,202,8)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__strncpy_from_user (5 samples, 0.17%)')" onmouseout="c()">
<title>__strncpy_from_user (5 samples, 0.17%)</title><rect x="1148.4" y="445" width="2.0" height="25.0" fill="rgb(251,47,54)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('radix_tree_gang_lookup_tag_slot (1 samples, 0.03%)')" onmouseout="c()">
<title>radix_tree_gang_lookup_tag_slot (1 samples, 0.03%)</title><rect x="10.4" y="237" width="0.4" height="25.0" fill="rgb(222,41,17)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('run_timer_softirq (1 samples, 0.03%)')" onmouseout="c()">
<title>run_timer_softirq (1 samples, 0.03%)</title><rect x="10.0" y="419" width="0.4" height="25.0" fill="rgb(252,124,23)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('start_xmit (84 samples, 2.85%)')" onmouseout="c()">
<title>start_xmit (84 samples, 2.85%)</title><rect x="1154.4" y="133" width="33.6" height="25.0" fill="rgb(230,17,36)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('copy_thread (1 samples, 0.03%)')" onmouseout="c()">
<title>copy_thread (1 samples, 0.03%)</title><rect x="23.2" y="471" width="0.4" height="25.0" fill="rgb(208,142,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__d_lookup_rcu (5 samples, 0.17%)')" onmouseout="c()">
<title>__d_lookup_rcu (5 samples, 0.17%)</title><rect x="1135.2" y="419" width="2.0" height="25.0" fill="rgb(235,229,35)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('generic_permission (1 samples, 0.03%)')" onmouseout="c()">
<title>generic_permission (1 samples, 0.03%)</title><rect x="1095.3" y="393" width="0.4" height="25.0" fill="rgb(235,162,47)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vfs_write (90 samples, 3.05%)')" onmouseout="c()">
<title>vfs_write (90 samples, 3.05%)</title><rect x="1154.0" y="523" width="36.0" height="25.0" fill="rgb(216,151,48)" rx="2" ry="2" />
<text text-anchor="" x="1157.0243902439" y="542.5" font-size="20" font-family="Verdana" fill="rgb(0,0,0)"  >v..</text>
</g>
<g class="func_g" onmouseover="s('generic_fillattr (78 samples, 2.64%)')" onmouseout="c()">
<title>generic_fillattr (78 samples, 2.64%)</title><rect x="975.7" y="445" width="31.2" height="25.0" fill="rgb(245,115,15)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('filldir (38 samples, 1.29%)')" onmouseout="c()">
<title>filldir (38 samples, 1.29%)</title><rect x="146.7" y="445" width="15.2" height="25.0" fill="rgb(228,155,18)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_cond_resched (1 samples, 0.03%)')" onmouseout="c()">
<title>_cond_resched (1 samples, 0.03%)</title><rect x="17.2" y="523" width="0.4" height="25.0" fill="rgb(218,30,42)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('generic_permission (3 samples, 0.10%)')" onmouseout="c()">
<title>generic_permission (3 samples, 0.10%)</title><rect x="1142.8" y="393" width="1.2" height="25.0" fill="rgb(216,52,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('fget_raw_light (4 samples, 0.14%)')" onmouseout="c()">
<title>fget_raw_light (4 samples, 0.14%)</title><rect x="1141.2" y="419" width="1.6" height="25.0" fill="rgb(226,113,0)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('current_kernel_time (1 samples, 0.03%)')" onmouseout="c()">
<title>current_kernel_time (1 samples, 0.03%)</title><rect x="1110.9" y="393" width="0.4" height="25.0" fill="rgb(240,17,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('mntget (2 samples, 0.07%)')" onmouseout="c()">
<title>mntget (2 samples, 0.07%)</title><rect x="1032.9" y="393" width="0.8" height="25.0" fill="rgb(242,205,47)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('do_path_lookup (1 samples, 0.03%)')" onmouseout="c()">
<title>do_path_lookup (1 samples, 0.03%)</title><rect x="110.7" y="445" width="0.4" height="25.0" fill="rgb(211,82,6)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ip_local_out (84 samples, 2.85%)')" onmouseout="c()">
<title>ip_local_out (84 samples, 2.85%)</title><rect x="1154.4" y="289" width="33.6" height="25.0" fill="rgb(218,102,36)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('____pagevec_lru_add (1 samples, 0.03%)')" onmouseout="c()">
<title>____pagevec_lru_add (1 samples, 0.03%)</title><rect x="357.4" y="367" width="0.4" height="25.0" fill="rgb(230,166,21)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('dput (3 samples, 0.10%)')" onmouseout="c()">
<title>dput (3 samples, 0.10%)</title><rect x="1050.1" y="393" width="1.2" height="25.0" fill="rgb(242,96,28)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('lru_add_drain (1 samples, 0.03%)')" onmouseout="c()">
<title>lru_add_drain (1 samples, 0.03%)</title><rect x="358.2" y="471" width="0.4" height="25.0" fill="rgb(212,38,4)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kmem_cache_alloc (51 samples, 1.73%)')" onmouseout="c()">
<title>kmem_cache_alloc (51 samples, 1.73%)</title><rect x="795.9" y="419" width="20.4" height="25.0" fill="rgb(236,54,33)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('do_mmap_pgoff (2 samples, 0.07%)')" onmouseout="c()">
<title>do_mmap_pgoff (2 samples, 0.07%)</title><rect x="357.4" y="497" width="0.8" height="25.0" fill="rgb(210,223,54)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('inode_permission (3 samples, 0.10%)')" onmouseout="c()">
<title>inode_permission (3 samples, 0.10%)</title><rect x="1094.9" y="419" width="1.2" height="25.0" fill="rgb(230,99,40)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_raw_spin_lock (7 samples, 0.24%)')" onmouseout="c()">
<title>_raw_spin_lock (7 samples, 0.24%)</title><rect x="543.6" y="367" width="2.8" height="25.0" fill="rgb(240,7,16)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('perf_event_mmap (1 samples, 0.03%)')" onmouseout="c()">
<title>perf_event_mmap (1 samples, 0.03%)</title><rect x="357.8" y="445" width="0.4" height="25.0" fill="rgb(205,22,26)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__tlb_remove_page (5 samples, 0.17%)')" onmouseout="c()">
<title>__tlb_remove_page (5 samples, 0.17%)</title><rect x="111.1" y="367" width="2.0" height="25.0" fill="rgb(227,213,54)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('cap_file_fcntl (2 samples, 0.07%)')" onmouseout="c()">
<title>cap_file_fcntl (2 samples, 0.07%)</title><rect x="119.5" y="497" width="0.8" height="25.0" fill="rgb(215,226,4)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('queue_work (1 samples, 0.03%)')" onmouseout="c()">
<title>queue_work (1 samples, 0.03%)</title><rect x="1189.6" y="367" width="0.4" height="25.0" fill="rgb(229,73,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('mntput_no_expire (23 samples, 0.78%)')" onmouseout="c()">
<title>mntput_no_expire (23 samples, 0.78%)</title><rect x="465.3" y="445" width="9.2" height="25.0" fill="rgb(228,168,4)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('fsnotify (2 samples, 0.07%)')" onmouseout="c()">
<title>fsnotify (2 samples, 0.07%)</title><rect x="103.5" y="471" width="0.8" height="25.0" fill="rgb(234,134,41)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rb_first (2 samples, 0.07%)')" onmouseout="c()">
<title>rb_first (2 samples, 0.07%)</title><rect x="338.6" y="471" width="0.8" height="25.0" fill="rgb(250,183,8)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('do_softirq (1 samples, 0.03%)')" onmouseout="c()">
<title>do_softirq (1 samples, 0.03%)</title><rect x="10.0" y="497" width="0.4" height="25.0" fill="rgb(208,168,27)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kmem_cache_alloc (1 samples, 0.03%)')" onmouseout="c()">
<title>kmem_cache_alloc (1 samples, 0.03%)</title><rect x="110.7" y="263" width="0.4" height="25.0" fill="rgb(233,26,17)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('mutex_lock_killable (2 samples, 0.07%)')" onmouseout="c()">
<title>mutex_lock_killable (2 samples, 0.07%)</title><rect x="346.6" y="497" width="0.8" height="25.0" fill="rgb(212,143,26)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('bdi_writeback_thread (1 samples, 0.03%)')" onmouseout="c()">
<title>bdi_writeback_thread (1 samples, 0.03%)</title><rect x="10.4" y="523" width="0.4" height="25.0" fill="rgb(220,226,1)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__do_fault (4 samples, 0.14%)')" onmouseout="c()">
<title>__do_fault (4 samples, 0.14%)</title><rect x="18.0" y="471" width="1.6" height="25.0" fill="rgb(210,228,19)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('handle_pte_fault (11 samples, 0.37%)')" onmouseout="c()">
<title>handle_pte_fault (11 samples, 0.37%)</title><rect x="18.0" y="497" width="4.4" height="25.0" fill="rgb(241,92,26)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('security_inode_permission (8 samples, 0.27%)')" onmouseout="c()">
<title>security_inode_permission (8 samples, 0.27%)</title><rect x="1144.0" y="393" width="3.2" height="25.0" fill="rgb(228,16,34)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('inode_permission (11 samples, 0.37%)')" onmouseout="c()">
<title>inode_permission (11 samples, 0.37%)</title><rect x="1142.8" y="419" width="4.4" height="25.0" fill="rgb(237,190,20)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kmem_cache_alloc (1 samples, 0.03%)')" onmouseout="c()">
<title>kmem_cache_alloc (1 samples, 0.03%)</title><rect x="1101.3" y="445" width="0.4" height="25.0" fill="rgb(230,139,33)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('get_page_from_freelist (1 samples, 0.03%)')" onmouseout="c()">
<title>get_page_from_freelist (1 samples, 0.03%)</title><rect x="1088.9" y="289" width="0.4" height="25.0" fill="rgb(223,96,45)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_cond_resched (6 samples, 0.20%)')" onmouseout="c()">
<title>_cond_resched (6 samples, 0.20%)</title><rect x="459.7" y="445" width="2.4" height="25.0" fill="rgb(226,59,10)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ioread8 (1 samples, 0.03%)')" onmouseout="c()">
<title>ioread8 (1 samples, 0.03%)</title><rect x="14.8" y="419" width="0.4" height="25.0" fill="rgb(216,169,12)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('schedule (1 samples, 0.03%)')" onmouseout="c()">
<title>schedule (1 samples, 0.03%)</title><rect x="15.2" y="497" width="0.4" height="25.0" fill="rgb(205,195,10)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__schedule (3 samples, 0.10%)')" onmouseout="c()">
<title>__schedule (3 samples, 0.10%)</title><rect x="24.0" y="523" width="1.2" height="25.0" fill="rgb(222,137,40)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('copy_user_generic_unrolled (4 samples, 0.14%)')" onmouseout="c()">
<title>copy_user_generic_unrolled (4 samples, 0.14%)</title><rect x="1120.4" y="471" width="1.6" height="25.0" fill="rgb(209,191,5)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('touch_atime (1 samples, 0.03%)')" onmouseout="c()">
<title>touch_atime (1 samples, 0.03%)</title><rect x="1130.0" y="523" width="0.4" height="25.0" fill="rgb(223,40,8)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tcp_v4_md5_do_lookup (1 samples, 0.03%)')" onmouseout="c()">
<title>tcp_v4_md5_do_lookup (1 samples, 0.03%)</title><rect x="1188.0" y="263" width="0.4" height="25.0" fill="rgb(220,81,34)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('dev_queue_xmit (84 samples, 2.85%)')" onmouseout="c()">
<title>dev_queue_xmit (84 samples, 2.85%)</title><rect x="1154.4" y="211" width="33.6" height="25.0" fill="rgb(230,9,46)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__slab_free (10 samples, 0.34%)')" onmouseout="c()">
<title>__slab_free (10 samples, 0.34%)</title><rect x="95.5" y="367" width="4.0" height="25.0" fill="rgb(207,149,46)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('path_openat (180 samples, 6.10%)')" onmouseout="c()">
<title>path_openat (180 samples, 6.10%)</title><rect x="1026.9" y="471" width="72.0" height="25.0" fill="rgb(211,33,0)" rx="2" ry="2" />
<text text-anchor="" x="1029.91056910569" y="490.5" font-size="20" font-family="Verdana" fill="rgb(0,0,0)"  >path..</text>
</g>
<g class="func_g" onmouseover="s('sys_newfstat (49 samples, 1.66%)')" onmouseout="c()">
<title>sys_newfstat (49 samples, 1.66%)</title><rect x="359.0" y="549" width="19.6" height="25.0" fill="rgb(250,68,51)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('schedule (3 samples, 0.10%)')" onmouseout="c()">
<title>schedule (3 samples, 0.10%)</title><rect x="24.0" y="549" width="1.2" height="25.0" fill="rgb(215,52,49)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('system_call_fastpath (2,834 samples, 96.00%)')" onmouseout="c()">
<title>system_call_fastpath (2,834 samples, 96.00%)</title><rect x="57.2" y="575" width="1132.8" height="25.0" fill="rgb(231,166,20)" rx="2" ry="2" />
<text text-anchor="" x="60.1680216802168" y="594.5" font-size="20" font-family="Verdana" fill="rgb(0,0,0)"  >system_call_fastpath</text>
</g>
<g class="func_g" onmouseover="s('kmem_cache_free (14 samples, 0.47%)')" onmouseout="c()">
<title>kmem_cache_free (14 samples, 0.47%)</title><rect x="1104.5" y="471" width="5.6" height="25.0" fill="rgb(249,226,24)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('getname_flags (7 samples, 0.24%)')" onmouseout="c()">
<title>getname_flags (7 samples, 0.24%)</title><rect x="1101.3" y="471" width="2.8" height="25.0" fill="rgb(220,146,47)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('putname (189 samples, 6.40%)')" onmouseout="c()">
<title>putname (189 samples, 6.40%)</title><rect x="872.2" y="445" width="75.6" height="25.0" fill="rgb(250,143,35)" rx="2" ry="2" />
<text text-anchor="" x="875.215447154472" y="464.5" font-size="20" font-family="Verdana" fill="rgb(0,0,0)"  >putn..</text>
</g>
<g class="func_g" onmouseover="s('up_read (1 samples, 0.03%)')" onmouseout="c()">
<title>up_read (1 samples, 0.03%)</title><rect x="204.7" y="341" width="0.4" height="25.0" fill="rgb(240,98,0)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('get_page_from_freelist (2 samples, 0.07%)')" onmouseout="c()">
<title>get_page_from_freelist (2 samples, 0.07%)</title><rect x="20.0" y="419" width="0.8" height="25.0" fill="rgb(214,190,4)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__find_get_block (38 samples, 1.29%)')" onmouseout="c()">
<title>__find_get_block (38 samples, 1.29%)</title><rect x="181.1" y="341" width="15.2" height="25.0" fill="rgb(252,21,47)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('user_path_at_empty (59 samples, 2.00%)')" onmouseout="c()">
<title>user_path_at_empty (59 samples, 2.00%)</title><rect x="1130.4" y="523" width="23.6" height="25.0" fill="rgb(242,19,47)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('security_inode_getattr (9 samples, 0.30%)')" onmouseout="c()">
<title>security_inode_getattr (9 samples, 0.30%)</title><rect x="1013.7" y="471" width="3.6" height="25.0" fill="rgb(237,61,37)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('link_path_walk (118 samples, 4.00%)')" onmouseout="c()">
<title>link_path_walk (118 samples, 4.00%)</title><rect x="640.8" y="393" width="47.1" height="25.0" fill="rgb(229,224,45)" rx="2" ry="2" />
<text text-anchor="" x="643.772357723577" y="412.5" font-size="20" font-family="Verdana" fill="rgb(0,0,0)"  >l..</text>
</g>
<g class="func_g" onmouseover="s('finish_task_switch (1 samples, 0.03%)')" onmouseout="c()">
<title>finish_task_switch (1 samples, 0.03%)</title><rect x="15.2" y="445" width="0.4" height="25.0" fill="rgb(213,164,15)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('d_alloc (1 samples, 0.03%)')" onmouseout="c()">
<title>d_alloc (1 samples, 0.03%)</title><rect x="110.7" y="315" width="0.4" height="25.0" fill="rgb(208,194,35)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('fget (9 samples, 0.30%)')" onmouseout="c()">
<title>fget (9 samples, 0.30%)</title><rect x="369.4" y="497" width="3.6" height="25.0" fill="rgb(235,222,22)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('pagevec_lookup_tag (1 samples, 0.03%)')" onmouseout="c()">
<title>pagevec_lookup_tag (1 samples, 0.03%)</title><rect x="10.4" y="289" width="0.4" height="25.0" fill="rgb(216,93,22)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kernel_thread_helper (13 samples, 0.44%)')" onmouseout="c()">
<title>kernel_thread_helper (13 samples, 0.44%)</title><rect x="10.4" y="575" width="5.2" height="25.0" fill="rgb(232,106,49)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vfs_getattr (11 samples, 0.37%)')" onmouseout="c()">
<title>vfs_getattr (11 samples, 0.37%)</title><rect x="374.2" y="497" width="4.4" height="25.0" fill="rgb(215,89,32)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__alloc_pages_nodemask (2 samples, 0.07%)')" onmouseout="c()">
<title>__alloc_pages_nodemask (2 samples, 0.07%)</title><rect x="1088.5" y="315" width="0.8" height="25.0" fill="rgb(238,165,45)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_cond_resched (2 samples, 0.07%)')" onmouseout="c()">
<title>_cond_resched (2 samples, 0.07%)</title><rect x="815.5" y="393" width="0.8" height="25.0" fill="rgb(239,36,41)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('nameidata_to_filp (30 samples, 1.02%)')" onmouseout="c()">
<title>nameidata_to_filp (30 samples, 1.02%)</title><rect x="1037.3" y="419" width="12.0" height="25.0" fill="rgb(219,219,32)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('strlen (17 samples, 0.58%)')" onmouseout="c()">
<title>strlen (17 samples, 0.58%)</title><rect x="1122.0" y="471" width="6.8" height="25.0" fill="rgb(226,162,21)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vfsmount_lock_local_lock (15 samples, 0.51%)')" onmouseout="c()">
<title>vfsmount_lock_local_lock (15 samples, 0.51%)</title><rect x="765.5" y="367" width="6.0" height="25.0" fill="rgb(237,45,19)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ptep_clear_flush (1 samples, 0.03%)')" onmouseout="c()">
<title>ptep_clear_flush (1 samples, 0.03%)</title><rect x="21.2" y="445" width="0.4" height="25.0" fill="rgb(250,40,1)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tty_write (1 samples, 0.03%)')" onmouseout="c()">
<title>tty_write (1 samples, 0.03%)</title><rect x="1189.6" y="497" width="0.4" height="25.0" fill="rgb(221,95,21)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('link_path_walk (5 samples, 0.17%)')" onmouseout="c()">
<title>link_path_walk (5 samples, 0.17%)</title><rect x="1094.1" y="445" width="2.0" height="25.0" fill="rgb(209,175,19)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vfs_readdir (577 samples, 19.55%)')" onmouseout="c()">
<title>vfs_readdir (577 samples, 19.55%)</title><rect x="126.3" y="523" width="230.7" height="25.0" fill="rgb(253,81,24)" rx="2" ry="2" />
<text text-anchor="" x="129.321138211382" y="542.5" font-size="20" font-family="Verdana" fill="rgb(0,0,0)"  >vfs_readdir</text>
</g>
<g class="func_g" onmouseover="s('str2hashbuf_signed (59 samples, 2.00%)')" onmouseout="c()">
<title>str2hashbuf_signed (59 samples, 2.00%)</title><rect x="302.2" y="393" width="23.6" height="25.0" fill="rgb(245,50,48)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tcp_ack (1 samples, 0.03%)')" onmouseout="c()">
<title>tcp_ack (1 samples, 0.03%)</title><rect x="1188.4" y="315" width="0.4" height="25.0" fill="rgb(220,138,48)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ext4_htree_free_dir_info (73 samples, 2.47%)')" onmouseout="c()">
<title>ext4_htree_free_dir_info (73 samples, 2.47%)</title><rect x="71.6" y="445" width="29.1" height="25.0" fill="rgb(240,115,9)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__getblk (38 samples, 1.29%)')" onmouseout="c()">
<title>__getblk (38 samples, 1.29%)</title><rect x="181.1" y="367" width="15.2" height="25.0" fill="rgb(242,93,30)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('release_sock (1 samples, 0.03%)')" onmouseout="c()">
<title>release_sock (1 samples, 0.03%)</title><rect x="1188.4" y="393" width="0.4" height="25.0" fill="rgb(229,85,49)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('fsnotify (3 samples, 0.10%)')" onmouseout="c()">
<title>fsnotify (3 samples, 0.10%)</title><rect x="1046.5" y="341" width="1.2" height="25.0" fill="rgb(205,227,9)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('do_vfs_ioctl (1 samples, 0.03%)')" onmouseout="c()">
<title>do_vfs_ioctl (1 samples, 0.03%)</title><rect x="357.0" y="523" width="0.4" height="25.0" fill="rgb(249,199,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('do_munmap (1 samples, 0.03%)')" onmouseout="c()">
<title>do_munmap (1 samples, 0.03%)</title><rect x="357.4" y="445" width="0.4" height="25.0" fill="rgb(223,54,29)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('do_filp_open (181 samples, 6.13%)')" onmouseout="c()">
<title>do_filp_open (181 samples, 6.13%)</title><rect x="1026.5" y="497" width="72.4" height="25.0" fill="rgb(218,5,32)" rx="2" ry="2" />
<text text-anchor="" x="1029.5108401084" y="516.5" font-size="20" font-family="Verdana" fill="rgb(0,0,0)"  >do_f..</text>
</g>
<g class="func_g" onmouseover="s('generic_readlink (40 samples, 1.36%)')" onmouseout="c()">
<title>generic_readlink (40 samples, 1.36%)</title><rect x="1112.9" y="523" width="15.9" height="25.0" fill="rgb(205,193,49)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('sys_mmap (2 samples, 0.07%)')" onmouseout="c()">
<title>sys_mmap (2 samples, 0.07%)</title><rect x="357.4" y="549" width="0.8" height="25.0" fill="rgb(238,167,40)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('inode_permission (4 samples, 0.14%)')" onmouseout="c()">
<title>inode_permission (4 samples, 0.14%)</title><rect x="1035.7" y="419" width="1.6" height="25.0" fill="rgb(212,195,28)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('sys_open (2 samples, 0.07%)')" onmouseout="c()">
<title>sys_open (2 samples, 0.07%)</title><rect x="1017.3" y="549" width="0.8" height="25.0" fill="rgb(207,92,4)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('memset (7 samples, 0.24%)')" onmouseout="c()">
<title>memset (7 samples, 0.24%)</title><rect x="241.0" y="367" width="2.8" height="25.0" fill="rgb(237,203,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__percpu_counter_add (3 samples, 0.10%)')" onmouseout="c()">
<title>__percpu_counter_add (3 samples, 0.10%)</title><rect x="66.8" y="471" width="1.2" height="25.0" fill="rgb(253,110,26)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('sock_aio_write (89 samples, 3.01%)')" onmouseout="c()">
<title>sock_aio_write (89 samples, 3.01%)</title><rect x="1154.0" y="471" width="35.6" height="25.0" fill="rgb(207,223,8)" rx="2" ry="2" />
<text text-anchor="" x="1157.0243902439" y="490.5" font-size="20" font-family="Verdana" fill="rgb(0,0,0)"  >s..</text>
</g>
<g class="func_g" onmouseover="s('khugepaged (9 samples, 0.30%)')" onmouseout="c()">
<title>khugepaged (9 samples, 0.30%)</title><rect x="10.8" y="523" width="3.6" height="25.0" fill="rgb(253,204,24)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('generic_permission (8 samples, 0.27%)')" onmouseout="c()">
<title>generic_permission (8 samples, 0.27%)</title><rect x="732.7" y="341" width="3.2" height="25.0" fill="rgb(237,21,47)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__rb_rotate_left (1 samples, 0.03%)')" onmouseout="c()">
<title>__rb_rotate_left (1 samples, 0.03%)</title><rect x="262.6" y="367" width="0.4" height="25.0" fill="rgb(239,27,26)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('path_lookupat (37 samples, 1.25%)')" onmouseout="c()">
<title>path_lookupat (37 samples, 1.25%)</title><rect x="1132.4" y="471" width="14.8" height="25.0" fill="rgb(241,117,27)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ext4_da_writepages (1 samples, 0.03%)')" onmouseout="c()">
<title>ext4_da_writepages (1 samples, 0.03%)</title><rect x="10.4" y="341" width="0.4" height="25.0" fill="rgb(215,223,41)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('security_file_alloc (11 samples, 0.37%)')" onmouseout="c()">
<title>security_file_alloc (11 samples, 0.37%)</title><rect x="1089.7" y="419" width="4.4" height="25.0" fill="rgb(216,23,19)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('complete_walk (37 samples, 1.25%)')" onmouseout="c()">
<title>complete_walk (37 samples, 1.25%)</title><rect x="539.2" y="393" width="14.8" height="25.0" fill="rgb(226,190,54)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ip_queue_xmit (84 samples, 2.85%)')" onmouseout="c()">
<title>ip_queue_xmit (84 samples, 2.85%)</title><rect x="1154.4" y="315" width="33.6" height="25.0" fill="rgb(238,72,18)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tcp_send_mss (2 samples, 0.07%)')" onmouseout="c()">
<title>tcp_send_mss (2 samples, 0.07%)</title><rect x="1188.8" y="393" width="0.8" height="25.0" fill="rgb(220,51,53)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ext4_getattr (101 samples, 3.42%)')" onmouseout="c()">
<title>ext4_getattr (101 samples, 3.42%)</title><rect x="966.6" y="471" width="40.3" height="25.0" fill="rgb(227,69,42)" rx="2" ry="2" />
<text text-anchor="" x="969.551490514905" y="490.5" font-size="20" font-family="Verdana" fill="rgb(0,0,0)"  >e..</text>
</g>
<g class="func_g" onmouseover="s('unmap_region (3 samples, 0.10%)')" onmouseout="c()">
<title>unmap_region (3 samples, 0.10%)</title><rect x="60.0" y="497" width="1.2" height="25.0" fill="rgb(211,82,26)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix_stream_connect (1 samples, 0.03%)')" onmouseout="c()">
<title>unix_stream_connect (1 samples, 0.03%)</title><rect x="110.7" y="523" width="0.4" height="25.0" fill="rgb(207,140,9)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('sys_read (2 samples, 0.07%)')" onmouseout="c()">
<title>sys_read (2 samples, 0.07%)</title><rect x="1110.5" y="549" width="0.8" height="25.0" fill="rgb(218,109,53)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__fsnotify_parent (1 samples, 0.03%)')" onmouseout="c()">
<title>__fsnotify_parent (1 samples, 0.03%)</title><rect x="66.4" y="471" width="0.4" height="25.0" fill="rgb(207,21,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('radix_tree_lookup_slot (11 samples, 0.37%)')" onmouseout="c()">
<title>radix_tree_lookup_slot (11 samples, 0.37%)</title><rect x="191.1" y="263" width="4.4" height="25.0" fill="rgb(243,105,1)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('fget_raw_light (3 samples, 0.10%)')" onmouseout="c()">
<title>fget_raw_light (3 samples, 0.10%)</title><rect x="1097.7" y="419" width="1.2" height="25.0" fill="rgb(227,164,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('dput (45 samples, 1.52%)')" onmouseout="c()">
<title>dput (45 samples, 1.52%)</title><rect x="444.9" y="471" width="18.0" height="25.0" fill="rgb(229,166,24)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__lru_cache_add (1 samples, 0.03%)')" onmouseout="c()">
<title>__lru_cache_add (1 samples, 0.03%)</title><rect x="22.0" y="419" width="0.4" height="25.0" fill="rgb(239,13,0)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('n_tty_ioctl_helper (1 samples, 0.03%)')" onmouseout="c()">
<title>n_tty_ioctl_helper (1 samples, 0.03%)</title><rect x="357.0" y="445" width="0.4" height="25.0" fill="rgb(211,188,53)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('fget_raw_light (38 samples, 1.29%)')" onmouseout="c()">
<title>fget_raw_light (38 samples, 1.29%)</title><rect x="714.7" y="367" width="15.2" height="25.0" fill="rgb(238,166,4)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('free_hot_cold_page (2 samples, 0.07%)')" onmouseout="c()">
<title>free_hot_cold_page (2 samples, 0.07%)</title><rect x="112.3" y="263" width="0.8" height="25.0" fill="rgb(209,24,27)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('sys_getdents (592 samples, 20.05%)')" onmouseout="c()">
<title>sys_getdents (592 samples, 20.05%)</title><rect x="120.3" y="549" width="236.7" height="25.0" fill="rgb(236,70,51)" rx="2" ry="2" />
<text text-anchor="" x="123.325203252033" y="568.5" font-size="20" font-family="Verdana" fill="rgb(0,0,0)"  >sys_getdents</text>
</g>
<g class="func_g" onmouseover="s('do_wp_page (1 samples, 0.03%)')" onmouseout="c()">
<title>do_wp_page (1 samples, 0.03%)</title><rect x="21.2" y="471" width="0.4" height="25.0" fill="rgb(238,196,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('do_lookup (7 samples, 0.24%)')" onmouseout="c()">
<title>do_lookup (7 samples, 0.24%)</title><rect x="1134.4" y="445" width="2.8" height="25.0" fill="rgb(232,161,36)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('mem_cgroup_pgfault (1 samples, 0.03%)')" onmouseout="c()">
<title>mem_cgroup_pgfault (1 samples, 0.03%)</title><rect x="22.8" y="471" width="0.4" height="25.0" fill="rgb(212,152,53)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tcp_established_options (1 samples, 0.03%)')" onmouseout="c()">
<title>tcp_established_options (1 samples, 0.03%)</title><rect x="1188.0" y="315" width="0.4" height="25.0" fill="rgb(228,115,18)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('copy_user_generic_unrolled (16 samples, 0.54%)')" onmouseout="c()">
<title>copy_user_generic_unrolled (16 samples, 0.54%)</title><rect x="155.5" y="419" width="6.4" height="25.0" fill="rgb(214,30,49)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('current_fs_time (1 samples, 0.03%)')" onmouseout="c()">
<title>current_fs_time (1 samples, 0.03%)</title><rect x="1110.9" y="419" width="0.4" height="25.0" fill="rgb(217,100,5)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('clocksource_watchdog (1 samples, 0.03%)')" onmouseout="c()">
<title>clocksource_watchdog (1 samples, 0.03%)</title><rect x="10.0" y="393" width="0.4" height="25.0" fill="rgb(219,13,40)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('path_get (4 samples, 0.14%)')" onmouseout="c()">
<title>path_get (4 samples, 0.14%)</title><rect x="1047.7" y="393" width="1.6" height="25.0" fill="rgb(224,30,9)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('new_slab (2 samples, 0.07%)')" onmouseout="c()">
<title>new_slab (2 samples, 0.07%)</title><rect x="1088.5" y="367" width="0.8" height="25.0" fill="rgb(237,118,37)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('path_openat (2 samples, 0.07%)')" onmouseout="c()">
<title>path_openat (2 samples, 0.07%)</title><rect x="1017.3" y="471" width="0.8" height="25.0" fill="rgb(253,12,21)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('call_softirq (1 samples, 0.03%)')" onmouseout="c()">
<title>call_softirq (1 samples, 0.03%)</title><rect x="10.0" y="471" width="0.4" height="25.0" fill="rgb(218,220,28)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('wait_on_page_read (1 samples, 0.03%)')" onmouseout="c()">
<title>wait_on_page_read (1 samples, 0.03%)</title><rect x="1118.8" y="419" width="0.4" height="25.0" fill="rgb(245,12,36)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_cond_resched (1 samples, 0.03%)')" onmouseout="c()">
<title>_cond_resched (1 samples, 0.03%)</title><rect x="235.8" y="367" width="0.4" height="25.0" fill="rgb(240,199,5)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('do_munmap (6 samples, 0.20%)')" onmouseout="c()">
<title>do_munmap (6 samples, 0.20%)</title><rect x="58.8" y="523" width="2.4" height="25.0" fill="rgb(245,42,1)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('link_path_walk (8 samples, 0.27%)')" onmouseout="c()">
<title>link_path_walk (8 samples, 0.27%)</title><rect x="1137.2" y="445" width="3.2" height="25.0" fill="rgb(244,133,49)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ext4fs_dirhash (154 samples, 5.22%)')" onmouseout="c()">
<title>ext4fs_dirhash (154 samples, 5.22%)</title><rect x="264.2" y="419" width="61.6" height="25.0" fill="rgb(238,176,43)" rx="2" ry="2" />
<text text-anchor="" x="267.227642276423" y="438.5" font-size="20" font-family="Verdana" fill="rgb(0,0,0)"  >ext..</text>
</g>
<g class="func_g" onmouseover="s('cp_new_stat (23 samples, 0.78%)')" onmouseout="c()">
<title>cp_new_stat (23 samples, 0.78%)</title><rect x="359.8" y="523" width="9.2" height="25.0" fill="rgb(242,216,34)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('inode_permission (22 samples, 0.75%)')" onmouseout="c()">
<title>inode_permission (22 samples, 0.75%)</title><rect x="679.1" y="367" width="8.8" height="25.0" fill="rgb(236,37,32)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('cap_inode_permission (2 samples, 0.07%)')" onmouseout="c()">
<title>cap_inode_permission (2 samples, 0.07%)</title><rect x="687.1" y="315" width="0.8" height="25.0" fill="rgb(205,72,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_raw_spin_unlock_irqrestore (1 samples, 0.03%)')" onmouseout="c()">
<title>_raw_spin_unlock_irqrestore (1 samples, 0.03%)</title><rect x="358.2" y="393" width="0.4" height="25.0" fill="rgb(246,105,28)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('sys_write (90 samples, 3.05%)')" onmouseout="c()">
<title>sys_write (90 samples, 3.05%)</title><rect x="1154.0" y="549" width="36.0" height="25.0" fill="rgb(239,69,54)" rx="2" ry="2" />
<text text-anchor="" x="1157.0243902439" y="568.5" font-size="20" font-family="Verdana" fill="rgb(0,0,0)"  >s..</text>
</g>
<g class="func_g" onmouseover="s('half_md4_transform (86 samples, 2.91%)')" onmouseout="c()">
<title>half_md4_transform (86 samples, 2.91%)</title><rect x="267.8" y="393" width="34.4" height="25.0" fill="rgb(245,197,29)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_raw_spin_lock (2 samples, 0.07%)')" onmouseout="c()">
<title>_raw_spin_lock (2 samples, 0.07%)</title><rect x="62.4" y="523" width="0.8" height="25.0" fill="rgb(243,27,29)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('d_alloc_and_lookup (1 samples, 0.03%)')" onmouseout="c()">
<title>d_alloc_and_lookup (1 samples, 0.03%)</title><rect x="110.7" y="341" width="0.4" height="25.0" fill="rgb(205,172,36)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_cond_resched (1 samples, 0.03%)')" onmouseout="c()">
<title>_cond_resched (1 samples, 0.03%)</title><rect x="1089.3" y="393" width="0.4" height="25.0" fill="rgb(247,122,30)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('mem_cgroup_update_page_stat (1 samples, 0.03%)')" onmouseout="c()">
<title>mem_cgroup_update_page_stat (1 samples, 0.03%)</title><rect x="19.2" y="419" width="0.4" height="25.0" fill="rgb(220,151,46)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('exit_mm (6 samples, 0.20%)')" onmouseout="c()">
<title>exit_mm (6 samples, 0.20%)</title><rect x="111.1" y="471" width="2.4" height="25.0" fill="rgb(242,106,2)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('current_fs_time (6 samples, 0.20%)')" onmouseout="c()">
<title>current_fs_time (6 samples, 0.20%)</title><rect x="354.6" y="471" width="2.4" height="25.0" fill="rgb(234,197,16)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('mark_page_accessed (2 samples, 0.07%)')" onmouseout="c()">
<title>mark_page_accessed (2 samples, 0.07%)</title><rect x="195.5" y="315" width="0.8" height="25.0" fill="rgb(254,105,42)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ext4_ext_map_blocks (15 samples, 0.51%)')" onmouseout="c()">
<title>ext4_ext_map_blocks (15 samples, 0.51%)</title><rect x="198.7" y="341" width="6.0" height="25.0" fill="rgb(228,23,12)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('sys_fcntl (17 samples, 0.58%)')" onmouseout="c()">
<title>sys_fcntl (17 samples, 0.58%)</title><rect x="113.5" y="549" width="6.8" height="25.0" fill="rgb(238,134,13)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('mntput (3 samples, 0.10%)')" onmouseout="c()">
<title>mntput (3 samples, 0.10%)</title><rect x="105.1" y="471" width="1.2" height="25.0" fill="rgb(227,192,37)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('put_page (1 samples, 0.03%)')" onmouseout="c()">
<title>put_page (1 samples, 0.03%)</title><rect x="1119.2" y="471" width="0.4" height="25.0" fill="rgb(217,7,24)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('fsnotify (2 samples, 0.07%)')" onmouseout="c()">
<title>fsnotify (2 samples, 0.07%)</title><rect x="351.8" y="471" width="0.8" height="25.0" fill="rgb(230,157,52)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('find_get_page (4 samples, 0.14%)')" onmouseout="c()">
<title>find_get_page (4 samples, 0.14%)</title><rect x="1117.2" y="367" width="1.6" height="25.0" fill="rgb(236,125,35)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tcp_v4_do_rcv (1 samples, 0.03%)')" onmouseout="c()">
<title>tcp_v4_do_rcv (1 samples, 0.03%)</title><rect x="1188.4" y="367" width="0.4" height="25.0" fill="rgb(222,52,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_raw_spin_lock (2 samples, 0.07%)')" onmouseout="c()">
<title>_raw_spin_lock (2 samples, 0.07%)</title><rect x="1099.3" y="471" width="0.8" height="25.0" fill="rgb(214,57,46)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_raw_spin_lock (1 samples, 0.03%)')" onmouseout="c()">
<title>_raw_spin_lock (1 samples, 0.03%)</title><rect x="1050.9" y="367" width="0.4" height="25.0" fill="rgb(215,193,0)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__d_lookup_rcu (1 samples, 0.03%)')" onmouseout="c()">
<title>__d_lookup_rcu (1 samples, 0.03%)</title><rect x="1017.7" y="393" width="0.4" height="25.0" fill="rgb(225,144,6)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('copy_page_range (1 samples, 0.03%)')" onmouseout="c()">
<title>copy_page_range (1 samples, 0.03%)</title><rect x="23.6" y="445" width="0.4" height="25.0" fill="rgb(253,16,12)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('security_file_permission (13 samples, 0.44%)')" onmouseout="c()">
<title>security_file_permission (13 samples, 0.44%)</title><rect x="347.4" y="497" width="5.2" height="25.0" fill="rgb(235,128,41)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('sys_connect (1 samples, 0.03%)')" onmouseout="c()">
<title>sys_connect (1 samples, 0.03%)</title><rect x="110.7" y="549" width="0.4" height="25.0" fill="rgb(211,85,39)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('do_path_lookup (38 samples, 1.29%)')" onmouseout="c()">
<title>do_path_lookup (38 samples, 1.29%)</title><rect x="1132.0" y="497" width="15.2" height="25.0" fill="rgb(215,183,9)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_raw_spin_unlock_irqrestore (1 samples, 0.03%)')" onmouseout="c()">
<title>_raw_spin_unlock_irqrestore (1 samples, 0.03%)</title><rect x="60.4" y="393" width="0.4" height="25.0" fill="rgb(231,220,51)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('irq_exit (1 samples, 0.03%)')" onmouseout="c()">
<title>irq_exit (1 samples, 0.03%)</title><rect x="10.0" y="523" width="0.4" height="25.0" fill="rgb(207,192,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rb_erase (1 samples, 0.03%)')" onmouseout="c()">
<title>rb_erase (1 samples, 0.03%)</title><rect x="59.6" y="497" width="0.4" height="25.0" fill="rgb(245,150,15)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__memcpy (13 samples, 0.44%)')" onmouseout="c()">
<title>__memcpy (13 samples, 0.44%)</title><rect x="243.8" y="393" width="5.2" height="25.0" fill="rgb(227,30,23)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('do_lookup (1 samples, 0.03%)')" onmouseout="c()">
<title>do_lookup (1 samples, 0.03%)</title><rect x="110.7" y="367" width="0.4" height="25.0" fill="rgb(212,34,46)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('do_writepages (1 samples, 0.03%)')" onmouseout="c()">
<title>do_writepages (1 samples, 0.03%)</title><rect x="10.4" y="367" width="0.4" height="25.0" fill="rgb(221,10,22)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('current_kernel_time (1 samples, 0.03%)')" onmouseout="c()">
<title>current_kernel_time (1 samples, 0.03%)</title><rect x="356.6" y="445" width="0.4" height="25.0" fill="rgb(205,41,37)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('do_sync_write (89 samples, 3.01%)')" onmouseout="c()">
<title>do_sync_write (89 samples, 3.01%)</title><rect x="1154.0" y="497" width="35.6" height="25.0" fill="rgb(223,40,32)" rx="2" ry="2" />
<text text-anchor="" x="1157.0243902439" y="516.5" font-size="20" font-family="Verdana" fill="rgb(0,0,0)"  >d..</text>
</g>
<g class="func_g" onmouseover="s('mmput (6 samples, 0.20%)')" onmouseout="c()">
<title>mmput (6 samples, 0.20%)</title><rect x="111.1" y="445" width="2.4" height="25.0" fill="rgb(225,31,13)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ext4_htree_store_dirent (148 samples, 5.01%)')" onmouseout="c()">
<title>ext4_htree_store_dirent (148 samples, 5.01%)</title><rect x="205.1" y="419" width="59.1" height="25.0" fill="rgb(216,172,6)" rx="2" ry="2" />
<text text-anchor="" x="208.067750677507" y="438.5" font-size="20" font-family="Verdana" fill="rgb(0,0,0)"  >ext..</text>
</g>
<g class="func_g" onmouseover="s('_cond_resched (1 samples, 0.03%)')" onmouseout="c()">
<title>_cond_resched (1 samples, 0.03%)</title><rect x="1119.6" y="471" width="0.4" height="25.0" fill="rgb(235,191,48)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('generic_permission (8 samples, 0.27%)')" onmouseout="c()">
<title>generic_permission (8 samples, 0.27%)</title><rect x="683.5" y="341" width="3.2" height="25.0" fill="rgb(216,88,6)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vp_notify (84 samples, 2.85%)')" onmouseout="c()">
<title>vp_notify (84 samples, 2.85%)</title><rect x="1154.4" y="81" width="33.6" height="25.0" fill="rgb(251,120,34)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('security_inode_getattr (6 samples, 0.20%)')" onmouseout="c()">
<title>security_inode_getattr (6 samples, 0.20%)</title><rect x="376.2" y="471" width="2.4" height="25.0" fill="rgb(216,9,19)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('security_inode_permission (1 samples, 0.03%)')" onmouseout="c()">
<title>security_inode_permission (1 samples, 0.03%)</title><rect x="1095.7" y="393" width="0.4" height="25.0" fill="rgb(243,140,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unmap_vmas (1 samples, 0.03%)')" onmouseout="c()">
<title>unmap_vmas (1 samples, 0.03%)</title><rect x="60.8" y="471" width="0.4" height="25.0" fill="rgb(211,35,16)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__find_get_block_slow (25 samples, 0.85%)')" onmouseout="c()">
<title>__find_get_block_slow (25 samples, 0.85%)</title><rect x="185.5" y="315" width="10.0" height="25.0" fill="rgb(218,171,8)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_raw_spin_lock (1 samples, 0.03%)')" onmouseout="c()">
<title>_raw_spin_lock (1 samples, 0.03%)</title><rect x="1022.9" y="471" width="0.4" height="25.0" fill="rgb(237,138,42)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('do_group_exit (6 samples, 0.20%)')" onmouseout="c()">
<title>do_group_exit (6 samples, 0.20%)</title><rect x="111.1" y="523" width="2.4" height="25.0" fill="rgb(221,185,29)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_raw_spin_lock (1 samples, 0.03%)')" onmouseout="c()">
<title>_raw_spin_lock (1 samples, 0.03%)</title><rect x="204.3" y="289" width="0.4" height="25.0" fill="rgb(254,23,9)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('mark_page_accessed (1 samples, 0.03%)')" onmouseout="c()">
<title>mark_page_accessed (1 samples, 0.03%)</title><rect x="113.1" y="367" width="0.4" height="25.0" fill="rgb(241,12,27)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('schedule_work (1 samples, 0.03%)')" onmouseout="c()">
<title>schedule_work (1 samples, 0.03%)</title><rect x="1189.6" y="393" width="0.4" height="25.0" fill="rgb(217,135,47)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('fput (107 samples, 3.62%)')" onmouseout="c()">
<title>fput (107 samples, 3.62%)</title><rect x="65.2" y="497" width="42.7" height="25.0" fill="rgb(240,130,14)" rx="2" ry="2" />
<text text-anchor="" x="68.1626016260163" y="516.5" font-size="20" font-family="Verdana" fill="rgb(0,0,0)"  >f..</text>
</g>
<g class="func_g" onmouseover="s('link_path_walk (1 samples, 0.03%)')" onmouseout="c()">
<title>link_path_walk (1 samples, 0.03%)</title><rect x="110.7" y="393" width="0.4" height="25.0" fill="rgb(245,185,18)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('do_lookup (1 samples, 0.03%)')" onmouseout="c()">
<title>do_lookup (1 samples, 0.03%)</title><rect x="1017.7" y="419" width="0.4" height="25.0" fill="rgb(215,210,33)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('alloc_pages_vma (4 samples, 0.14%)')" onmouseout="c()">
<title>alloc_pages_vma (4 samples, 0.14%)</title><rect x="19.6" y="471" width="1.6" height="25.0" fill="rgb(243,142,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('getname (7 samples, 0.24%)')" onmouseout="c()">
<title>getname (7 samples, 0.24%)</title><rect x="1101.3" y="497" width="2.8" height="25.0" fill="rgb(243,112,53)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_raw_spin_lock (1 samples, 0.03%)')" onmouseout="c()">
<title>_raw_spin_lock (1 samples, 0.03%)</title><rect x="64.8" y="445" width="0.4" height="25.0" fill="rgb(219,63,44)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__strncpy_from_user (59 samples, 2.00%)')" onmouseout="c()">
<title>__strncpy_from_user (59 samples, 2.00%)</title><rect x="848.6" y="393" width="23.6" height="25.0" fill="rgb(208,168,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('fget (7 samples, 0.24%)')" onmouseout="c()">
<title>fget (7 samples, 0.24%)</title><rect x="123.1" y="523" width="2.8" height="25.0" fill="rgb(217,28,21)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__fsnotify_parent (1 samples, 0.03%)')" onmouseout="c()">
<title>__fsnotify_parent (1 samples, 0.03%)</title><rect x="350.6" y="471" width="0.4" height="25.0" fill="rgb(228,43,54)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__phys_addr (7 samples, 0.24%)')" onmouseout="c()">
<title>__phys_addr (7 samples, 0.24%)</title><rect x="1107.3" y="445" width="2.8" height="25.0" fill="rgb(207,60,51)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('process_one_work (1 samples, 0.03%)')" onmouseout="c()">
<title>process_one_work (1 samples, 0.03%)</title><rect x="14.8" y="497" width="0.4" height="25.0" fill="rgb(223,68,46)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('generic_permission (2 samples, 0.07%)')" onmouseout="c()">
<title>generic_permission (2 samples, 0.07%)</title><rect x="1035.7" y="393" width="0.8" height="25.0" fill="rgb(223,88,21)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('page_fault (19 samples, 0.64%)')" onmouseout="c()">
<title>page_fault (19 samples, 0.64%)</title><rect x="15.6" y="575" width="7.6" height="25.0" fill="rgb(249,140,16)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('clear_page (2 samples, 0.07%)')" onmouseout="c()">
<title>clear_page (2 samples, 0.07%)</title><rect x="20.0" y="393" width="0.8" height="25.0" fill="rgb(213,196,7)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tcp_established_options (1 samples, 0.03%)')" onmouseout="c()">
<title>tcp_established_options (1 samples, 0.03%)</title><rect x="1189.2" y="341" width="0.4" height="25.0" fill="rgb(220,68,46)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kmem_cache_alloc_trace (23 samples, 0.78%)')" onmouseout="c()">
<title>kmem_cache_alloc_trace (23 samples, 0.78%)</title><rect x="329.4" y="471" width="9.2" height="25.0" fill="rgb(239,186,4)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tty_ioctl (1 samples, 0.03%)')" onmouseout="c()">
<title>tty_ioctl (1 samples, 0.03%)</title><rect x="357.0" y="497" width="0.4" height="25.0" fill="rgb(247,98,1)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('complete_walk (2 samples, 0.07%)')" onmouseout="c()">
<title>complete_walk (2 samples, 0.07%)</title><rect x="1032.9" y="419" width="0.8" height="25.0" fill="rgb(213,28,21)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ext4_map_blocks (22 samples, 0.75%)')" onmouseout="c()">
<title>ext4_map_blocks (22 samples, 0.75%)</title><rect x="196.3" y="367" width="8.8" height="25.0" fill="rgb(205,151,14)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tcp_v4_md5_lookup (1 samples, 0.03%)')" onmouseout="c()">
<title>tcp_v4_md5_lookup (1 samples, 0.03%)</title><rect x="1188.0" y="289" width="0.4" height="25.0" fill="rgb(223,124,11)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('sys_exit_group (6 samples, 0.20%)')" onmouseout="c()">
<title>sys_exit_group (6 samples, 0.20%)</title><rect x="111.1" y="549" width="2.4" height="25.0" fill="rgb(248,171,23)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('worker_thread (2 samples, 0.07%)')" onmouseout="c()">
<title>worker_thread (2 samples, 0.07%)</title><rect x="14.8" y="523" width="0.8" height="25.0" fill="rgb(209,127,21)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('file_sb_list_add (7 samples, 0.24%)')" onmouseout="c()">
<title>file_sb_list_add (7 samples, 0.24%)</title><rect x="1043.3" y="367" width="2.8" height="25.0" fill="rgb(246,143,19)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vfs_fstatat (1,503 samples, 50.91%)')" onmouseout="c()">
<title>vfs_fstatat (1,503 samples, 50.91%)</title><rect x="416.5" y="523" width="600.8" height="25.0" fill="rgb(244,8,31)" rx="2" ry="2" />
<text text-anchor="" x="419.524390243902" y="542.5" font-size="20" font-family="Verdana" fill="rgb(0,0,0)"  >vfs_fstatat</text>
</g>
<g class="func_g" onmouseover="s('mmap_region (2 samples, 0.07%)')" onmouseout="c()">
<title>mmap_region (2 samples, 0.07%)</title><rect x="357.4" y="471" width="0.8" height="25.0" fill="rgb(244,200,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tty_flip_buffer_push (1 samples, 0.03%)')" onmouseout="c()">
<title>tty_flip_buffer_push (1 samples, 0.03%)</title><rect x="1189.6" y="419" width="0.4" height="25.0" fill="rgb(215,137,31)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('lru_cache_add_lru (2 samples, 0.07%)')" onmouseout="c()">
<title>lru_cache_add_lru (2 samples, 0.07%)</title><rect x="21.6" y="445" width="0.8" height="25.0" fill="rgb(226,208,4)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('pagevec_lru_move_fn (1 samples, 0.03%)')" onmouseout="c()">
<title>pagevec_lru_move_fn (1 samples, 0.03%)</title><rect x="358.2" y="419" width="0.4" height="25.0" fill="rgb(212,73,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('do_sys_open (2 samples, 0.07%)')" onmouseout="c()">
<title>do_sys_open (2 samples, 0.07%)</title><rect x="1017.3" y="523" width="0.8" height="25.0" fill="rgb(250,182,52)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('getname_flags (252 samples, 8.54%)')" onmouseout="c()">
<title>getname_flags (252 samples, 8.54%)</title><rect x="771.5" y="445" width="100.7" height="25.0" fill="rgb(249,141,3)" rx="2" ry="2" />
<text text-anchor="" x="774.483739837398" y="464.5" font-size="20" font-family="Verdana" fill="rgb(0,0,0)"  >getnam..</text>
</g>
<g class="func_g" onmouseover="s('mntput (29 samples, 0.98%)')" onmouseout="c()">
<title>mntput (29 samples, 0.98%)</title><rect x="462.9" y="471" width="11.6" height="25.0" fill="rgb(208,70,5)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('dup_mm (1 samples, 0.03%)')" onmouseout="c()">
<title>dup_mm (1 samples, 0.03%)</title><rect x="23.6" y="471" width="0.4" height="25.0" fill="rgb(233,39,42)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__slab_free (4 samples, 0.14%)')" onmouseout="c()">
<title>__slab_free (4 samples, 0.14%)</title><rect x="327.8" y="419" width="1.6" height="25.0" fill="rgb(225,123,37)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('getname_flags (8 samples, 0.27%)')" onmouseout="c()">
<title>getname_flags (8 samples, 0.27%)</title><rect x="1147.2" y="497" width="3.2" height="25.0" fill="rgb(212,49,48)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('sys_ppoll (1 samples, 0.03%)')" onmouseout="c()">
<title>sys_ppoll (1 samples, 0.03%)</title><rect x="1110.1" y="549" width="0.4" height="25.0" fill="rgb(253,11,2)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('sysret_check (1 samples, 0.03%)')" onmouseout="c()">
<title>sysret_check (1 samples, 0.03%)</title><rect x="25.2" y="575" width="0.4" height="25.0" fill="rgb(241,12,29)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('free_rb_tree_fname (9 samples, 0.30%)')" onmouseout="c()">
<title>free_rb_tree_fname (9 samples, 0.30%)</title><rect x="325.8" y="471" width="3.6" height="25.0" fill="rgb(239,44,49)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('copy_process (2 samples, 0.07%)')" onmouseout="c()">
<title>copy_process (2 samples, 0.07%)</title><rect x="23.2" y="497" width="0.8" height="25.0" fill="rgb(232,212,41)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_cond_resched (1 samples, 0.03%)')" onmouseout="c()">
<title>_cond_resched (1 samples, 0.03%)</title><rect x="154.3" y="419" width="0.4" height="25.0" fill="rgb(214,217,1)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unmap_region (1 samples, 0.03%)')" onmouseout="c()">
<title>unmap_region (1 samples, 0.03%)</title><rect x="357.4" y="419" width="0.4" height="25.0" fill="rgb(209,215,37)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rb_next (18 samples, 0.61%)')" onmouseout="c()">
<title>rb_next (18 samples, 0.61%)</title><rect x="339.4" y="471" width="7.2" height="25.0" fill="rgb(249,18,9)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('____pagevec_lru_add (1 samples, 0.03%)')" onmouseout="c()">
<title>____pagevec_lru_add (1 samples, 0.03%)</title><rect x="358.2" y="445" width="0.4" height="25.0" fill="rgb(249,134,1)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('queue_work_on (1 samples, 0.03%)')" onmouseout="c()">
<title>queue_work_on (1 samples, 0.03%)</title><rect x="1189.6" y="341" width="0.4" height="25.0" fill="rgb(247,190,19)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_copy_to_user (6 samples, 0.20%)')" onmouseout="c()">
<title>_copy_to_user (6 samples, 0.20%)</title><rect x="404.5" y="497" width="2.4" height="25.0" fill="rgb(222,203,9)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_cond_resched (5 samples, 0.17%)')" onmouseout="c()">
<title>_cond_resched (5 samples, 0.17%)</title><rect x="402.5" y="497" width="2.0" height="25.0" fill="rgb(234,165,16)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('mntget (11 samples, 0.37%)')" onmouseout="c()">
<title>mntget (11 samples, 0.37%)</title><rect x="546.4" y="367" width="4.4" height="25.0" fill="rgb(224,5,1)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('generic_fillattr (17 samples, 0.58%)')" onmouseout="c()">
<title>generic_fillattr (17 samples, 0.58%)</title><rect x="1006.9" y="471" width="6.8" height="25.0" fill="rgb(245,99,21)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('relay_file_poll (1 samples, 0.03%)')" onmouseout="c()">
<title>relay_file_poll (1 samples, 0.03%)</title><rect x="1110.1" y="497" width="0.4" height="25.0" fill="rgb(213,6,30)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('path_put (135 samples, 4.57%)')" onmouseout="c()">
<title>path_put (135 samples, 4.57%)</title><rect x="420.5" y="497" width="54.0" height="25.0" fill="rgb(251,226,0)" rx="2" ry="2" />
<text text-anchor="" x="423.521680216802" y="516.5" font-size="20" font-family="Verdana" fill="rgb(0,0,0)"  >pa..</text>
</g>
<g class="func_g" onmouseover="s('__schedule (1 samples, 0.03%)')" onmouseout="c()">
<title>__schedule (1 samples, 0.03%)</title><rect x="15.2" y="471" width="0.4" height="25.0" fill="rgb(212,22,13)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kfree (5 samples, 0.17%)')" onmouseout="c()">
<title>kfree (5 samples, 0.17%)</title><rect x="327.4" y="445" width="2.0" height="25.0" fill="rgb(221,59,16)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vfs_fstat (24 samples, 0.81%)')" onmouseout="c()">
<title>vfs_fstat (24 samples, 0.81%)</title><rect x="369.0" y="523" width="9.6" height="25.0" fill="rgb(238,6,22)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('current_fs_time (1 samples, 0.03%)')" onmouseout="c()">
<title>current_fs_time (1 samples, 0.03%)</title><rect x="1130.0" y="497" width="0.4" height="25.0" fill="rgb(229,224,21)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('mntput (2 samples, 0.07%)')" onmouseout="c()">
<title>mntput (2 samples, 0.07%)</title><rect x="1129.2" y="497" width="0.8" height="25.0" fill="rgb(217,108,9)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('cap_inode_permission (2 samples, 0.07%)')" onmouseout="c()">
<title>cap_inode_permission (2 samples, 0.07%)</title><rect x="1036.5" y="367" width="0.8" height="25.0" fill="rgb(241,212,40)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('fput (3 samples, 0.10%)')" onmouseout="c()">
<title>fput (3 samples, 0.10%)</title><rect x="373.0" y="497" width="1.2" height="25.0" fill="rgb(221,188,13)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('sysret_careful (3 samples, 0.10%)')" onmouseout="c()">
<title>sysret_careful (3 samples, 0.10%)</title><rect x="24.0" y="575" width="1.2" height="25.0" fill="rgb(220,139,4)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('pagevec_lru_move_fn (1 samples, 0.03%)')" onmouseout="c()">
<title>pagevec_lru_move_fn (1 samples, 0.03%)</title><rect x="357.4" y="341" width="0.4" height="25.0" fill="rgb(216,140,33)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('wb_do_writeback (1 samples, 0.03%)')" onmouseout="c()">
<title>wb_do_writeback (1 samples, 0.03%)</title><rect x="10.4" y="497" width="0.4" height="25.0" fill="rgb(232,184,24)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('do_munmap (2 samples, 0.07%)')" onmouseout="c()">
<title>do_munmap (2 samples, 0.07%)</title><rect x="358.2" y="523" width="0.8" height="25.0" fill="rgb(224,27,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('dnotify_flush (2 samples, 0.07%)')" onmouseout="c()">
<title>dnotify_flush (2 samples, 0.07%)</title><rect x="64.4" y="497" width="0.8" height="25.0" fill="rgb(242,11,8)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('mark_page_accessed (1 samples, 0.03%)')" onmouseout="c()">
<title>mark_page_accessed (1 samples, 0.03%)</title><rect x="1110.5" y="445" width="0.4" height="25.0" fill="rgb(230,128,27)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('get_vma_policy (1 samples, 0.03%)')" onmouseout="c()">
<title>get_vma_policy (1 samples, 0.03%)</title><rect x="20.8" y="445" width="0.4" height="25.0" fill="rgb(208,217,42)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('expand_files (4 samples, 0.14%)')" onmouseout="c()">
<title>expand_files (4 samples, 0.14%)</title><rect x="1023.3" y="471" width="1.6" height="25.0" fill="rgb(215,64,13)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__phys_addr (66 samples, 2.24%)')" onmouseout="c()">
<title>__phys_addr (66 samples, 2.24%)</title><rect x="921.4" y="393" width="26.4" height="25.0" fill="rgb(222,165,0)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('cap_inode_permission (7 samples, 0.24%)')" onmouseout="c()">
<title>cap_inode_permission (7 samples, 0.24%)</title><rect x="762.7" y="315" width="2.8" height="25.0" fill="rgb(248,229,32)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('run_ksoftirqd (1 samples, 0.03%)')" onmouseout="c()">
<title>run_ksoftirqd (1 samples, 0.03%)</title><rect x="14.4" y="523" width="0.4" height="25.0" fill="rgb(220,88,7)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ext4_ext_check_cache (4 samples, 0.14%)')" onmouseout="c()">
<title>ext4_ext_check_cache (4 samples, 0.14%)</title><rect x="203.1" y="315" width="1.6" height="25.0" fill="rgb(207,24,29)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('link_path_walk (1 samples, 0.03%)')" onmouseout="c()">
<title>link_path_walk (1 samples, 0.03%)</title><rect x="1017.7" y="445" width="0.4" height="25.0" fill="rgb(244,136,11)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__put_single_page (4 samples, 0.14%)')" onmouseout="c()">
<title>__put_single_page (4 samples, 0.14%)</title><rect x="111.5" y="289" width="1.6" height="25.0" fill="rgb(221,188,32)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unmap_vmas (6 samples, 0.20%)')" onmouseout="c()">
<title>unmap_vmas (6 samples, 0.20%)</title><rect x="111.1" y="393" width="2.4" height="25.0" fill="rgb(248,196,52)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('memset (9 samples, 0.30%)')" onmouseout="c()">
<title>memset (9 samples, 0.30%)</title><rect x="335.0" y="445" width="3.6" height="25.0" fill="rgb(211,57,32)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('security_inode_permission (2 samples, 0.07%)')" onmouseout="c()">
<title>security_inode_permission (2 samples, 0.07%)</title><rect x="1036.5" y="393" width="0.8" height="25.0" fill="rgb(250,69,21)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('n_tty_ioctl (1 samples, 0.03%)')" onmouseout="c()">
<title>n_tty_ioctl (1 samples, 0.03%)</title><rect x="357.0" y="471" width="0.4" height="25.0" fill="rgb(250,0,40)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_copy_to_user (1 samples, 0.03%)')" onmouseout="c()">
<title>_copy_to_user (1 samples, 0.03%)</title><rect x="1120.0" y="471" width="0.4" height="25.0" fill="rgb(238,96,31)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('find_next_zero_bit (4 samples, 0.14%)')" onmouseout="c()">
<title>find_next_zero_bit (4 samples, 0.14%)</title><rect x="1024.9" y="471" width="1.6" height="25.0" fill="rgb(221,64,0)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('sys_brk (6 samples, 0.20%)')" onmouseout="c()">
<title>sys_brk (6 samples, 0.20%)</title><rect x="58.8" y="549" width="2.4" height="25.0" fill="rgb(217,159,51)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('free_page_and_swap_cache (5 samples, 0.17%)')" onmouseout="c()">
<title>free_page_and_swap_cache (5 samples, 0.17%)</title><rect x="111.1" y="341" width="2.0" height="25.0" fill="rgb(242,172,47)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('fput (1 samples, 0.03%)')" onmouseout="c()">
<title>fput (1 samples, 0.03%)</title><rect x="125.9" y="523" width="0.4" height="25.0" fill="rgb(252,171,31)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ext4_data_block_valid (3 samples, 0.10%)')" onmouseout="c()">
<title>ext4_data_block_valid (3 samples, 0.10%)</title><rect x="197.5" y="341" width="1.2" height="25.0" fill="rgb(232,121,9)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_raw_spin_unlock_irqrestore (1 samples, 0.03%)')" onmouseout="c()">
<title>_raw_spin_unlock_irqrestore (1 samples, 0.03%)</title><rect x="357.4" y="315" width="0.4" height="25.0" fill="rgb(247,156,48)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tcp_sendmsg (89 samples, 3.01%)')" onmouseout="c()">
<title>tcp_sendmsg (89 samples, 3.01%)</title><rect x="1154.0" y="419" width="35.6" height="25.0" fill="rgb(237,27,1)" rx="2" ry="2" />
<text text-anchor="" x="1157.0243902439" y="438.5" font-size="20" font-family="Verdana" fill="rgb(0,0,0)"  >t..</text>
</g>
<g class="func_g" onmouseover="s('__put_user_8 (1 samples, 0.03%)')" onmouseout="c()">
<title>__put_user_8 (1 samples, 0.03%)</title><rect x="122.7" y="523" width="0.4" height="25.0" fill="rgb(233,108,21)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vfsmount_lock_local_lock (9 samples, 0.30%)')" onmouseout="c()">
<title>vfsmount_lock_local_lock (9 samples, 0.30%)</title><rect x="470.9" y="419" width="3.6" height="25.0" fill="rgb(237,220,17)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kmem_cache_free (9 samples, 0.30%)')" onmouseout="c()">
<title>kmem_cache_free (9 samples, 0.30%)</title><rect x="1150.4" y="471" width="3.6" height="25.0" fill="rgb(245,100,23)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('do_filp_open (2 samples, 0.07%)')" onmouseout="c()">
<title>do_filp_open (2 samples, 0.07%)</title><rect x="1017.3" y="497" width="0.8" height="25.0" fill="rgb(214,221,24)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('anon_vma_clone (1 samples, 0.03%)')" onmouseout="c()">
<title>anon_vma_clone (1 samples, 0.03%)</title><rect x="59.2" y="471" width="0.4" height="25.0" fill="rgb(249,175,18)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('do_page_fault (19 samples, 0.64%)')" onmouseout="c()">
<title>do_page_fault (19 samples, 0.64%)</title><rect x="15.6" y="549" width="7.6" height="25.0" fill="rgb(253,34,30)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__fsnotify_parent (1 samples, 0.03%)')" onmouseout="c()">
<title>__fsnotify_parent (1 samples, 0.03%)</title><rect x="1046.1" y="341" width="0.4" height="25.0" fill="rgb(254,226,46)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_raw_spin_lock (2 samples, 0.07%)')" onmouseout="c()">
<title>_raw_spin_lock (2 samples, 0.07%)</title><rect x="462.1" y="445" width="0.8" height="25.0" fill="rgb(213,68,5)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__queue_work (1 samples, 0.03%)')" onmouseout="c()">
<title>__queue_work (1 samples, 0.03%)</title><rect x="1189.6" y="315" width="0.4" height="25.0" fill="rgb(243,86,17)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('iowrite16 (84 samples, 2.85%)')" onmouseout="c()">
<title>iowrite16 (84 samples, 2.85%)</title><rect x="1154.4" y="55" width="33.6" height="25.0" fill="rgb(252,90,47)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('dput (1 samples, 0.03%)')" onmouseout="c()">
<title>dput (1 samples, 0.03%)</title><rect x="1128.8" y="497" width="0.4" height="25.0" fill="rgb(228,188,12)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('strncpy_from_user (8 samples, 0.27%)')" onmouseout="c()">
<title>strncpy_from_user (8 samples, 0.27%)</title><rect x="1147.2" y="471" width="3.2" height="25.0" fill="rgb(251,94,20)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('fsnotify (3 samples, 0.10%)')" onmouseout="c()">
<title>fsnotify (3 samples, 0.10%)</title><rect x="1100.1" y="497" width="1.2" height="25.0" fill="rgb(236,11,47)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('page_follow_link_light (13 samples, 0.44%)')" onmouseout="c()">
<title>page_follow_link_light (13 samples, 0.44%)</title><rect x="1114.1" y="497" width="5.1" height="25.0" fill="rgb(214,115,41)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('finish_task_switch (3 samples, 0.10%)')" onmouseout="c()">
<title>finish_task_switch (3 samples, 0.10%)</title><rect x="24.0" y="497" width="1.2" height="25.0" fill="rgb(246,203,10)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('security_dentry_open (4 samples, 0.14%)')" onmouseout="c()">
<title>security_dentry_open (4 samples, 0.14%)</title><rect x="1046.1" y="367" width="1.6" height="25.0" fill="rgb(216,194,28)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__slab_alloc (4 samples, 0.14%)')" onmouseout="c()">
<title>__slab_alloc (4 samples, 0.14%)</title><rect x="234.2" y="367" width="1.6" height="25.0" fill="rgb(231,86,37)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tcp_current_mss (1 samples, 0.03%)')" onmouseout="c()">
<title>tcp_current_mss (1 samples, 0.03%)</title><rect x="1189.2" y="367" width="0.4" height="25.0" fill="rgb(215,65,32)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('dev_hard_start_xmit (84 samples, 2.85%)')" onmouseout="c()">
<title>dev_hard_start_xmit (84 samples, 2.85%)</title><rect x="1154.4" y="159" width="33.6" height="25.0" fill="rgb(227,193,20)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('fget_raw (4 samples, 0.14%)')" onmouseout="c()">
<title>fget_raw (4 samples, 0.14%)</title><rect x="116.7" y="523" width="1.6" height="25.0" fill="rgb(205,161,45)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('do_sys_poll (1 samples, 0.03%)')" onmouseout="c()">
<title>do_sys_poll (1 samples, 0.03%)</title><rect x="1110.1" y="523" width="0.4" height="25.0" fill="rgb(248,101,0)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ext4_release_dir (73 samples, 2.47%)')" onmouseout="c()">
<title>ext4_release_dir (73 samples, 2.47%)</title><rect x="71.6" y="471" width="29.1" height="25.0" fill="rgb(248,187,28)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('stub_clone (2 samples, 0.07%)')" onmouseout="c()">
<title>stub_clone (2 samples, 0.07%)</title><rect x="23.2" y="575" width="0.8" height="25.0" fill="rgb(228,31,54)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('apic_timer_interrupt (1 samples, 0.03%)')" onmouseout="c()">
<title>apic_timer_interrupt (1 samples, 0.03%)</title><rect x="10.0" y="575" width="0.4" height="25.0" fill="rgb(239,114,20)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('lru_add_drain (1 samples, 0.03%)')" onmouseout="c()">
<title>lru_add_drain (1 samples, 0.03%)</title><rect x="357.4" y="393" width="0.4" height="25.0" fill="rgb(216,16,51)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__split_vma (1 samples, 0.03%)')" onmouseout="c()">
<title>__split_vma (1 samples, 0.03%)</title><rect x="59.2" y="497" width="0.4" height="25.0" fill="rgb(226,72,48)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ext4_num_dirty_pages (1 samples, 0.03%)')" onmouseout="c()">
<title>ext4_num_dirty_pages (1 samples, 0.03%)</title><rect x="10.4" y="315" width="0.4" height="25.0" fill="rgb(254,17,7)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('inode_permission (89 samples, 3.01%)')" onmouseout="c()">
<title>inode_permission (89 samples, 3.01%)</title><rect x="729.9" y="367" width="35.6" height="25.0" fill="rgb(211,51,54)" rx="2" ry="2" />
<text text-anchor="" x="732.911924119241" y="386.5" font-size="20" font-family="Verdana" fill="rgb(0,0,0)"  >i..</text>
</g>
<g class="func_g" onmouseover="s('fput (2 samples, 0.07%)')" onmouseout="c()">
<title>fput (2 samples, 0.07%)</title><rect x="118.3" y="523" width="0.8" height="25.0" fill="rgb(227,110,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__call_rcu (5 samples, 0.17%)')" onmouseout="c()">
<title>__call_rcu (5 samples, 0.17%)</title><rect x="69.6" y="445" width="2.0" height="25.0" fill="rgb(231,146,20)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('page_getlink (13 samples, 0.44%)')" onmouseout="c()">
<title>page_getlink (13 samples, 0.44%)</title><rect x="1114.1" y="471" width="5.1" height="25.0" fill="rgb(254,34,39)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ext4_readdir (548 samples, 18.56%)')" onmouseout="c()">
<title>ext4_readdir (548 samples, 18.56%)</title><rect x="127.5" y="497" width="219.1" height="25.0" fill="rgb(242,162,5)" rx="2" ry="2" />
<text text-anchor="" x="130.520325203252" y="516.5" font-size="20" font-family="Verdana" fill="rgb(0,0,0)"  >ext4_readdir</text>
</g>
<g class="func_g" onmouseover="s('vfs_read (2 samples, 0.07%)')" onmouseout="c()">
<title>vfs_read (2 samples, 0.07%)</title><rect x="1110.5" y="523" width="0.8" height="25.0" fill="rgb(226,9,21)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('strncpy_from_user (140 samples, 4.74%)')" onmouseout="c()">
<title>strncpy_from_user (140 samples, 4.74%)</title><rect x="816.3" y="419" width="55.9" height="25.0" fill="rgb(230,209,22)" rx="2" ry="2" />
<text text-anchor="" x="819.253387533875" y="438.5" font-size="20" font-family="Verdana" fill="rgb(0,0,0)"  >st..</text>
</g>
<g class="func_g" onmouseover="s('file_sb_list_del (7 samples, 0.24%)')" onmouseout="c()">
<title>file_sb_list_del (7 samples, 0.24%)</title><rect x="100.7" y="471" width="2.8" height="25.0" fill="rgb(246,228,47)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ata_sff_check_status (1 samples, 0.03%)')" onmouseout="c()">
<title>ata_sff_check_status (1 samples, 0.03%)</title><rect x="14.8" y="445" width="0.4" height="25.0" fill="rgb(236,146,54)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__d_lookup_rcu (202 samples, 6.84%)')" onmouseout="c()">
<title>__d_lookup_rcu (202 samples, 6.84%)</title><rect x="560.0" y="367" width="80.8" height="25.0" fill="rgb(216,124,46)" rx="2" ry="2" />
<text text-anchor="" x="563.027100271003" y="386.5" font-size="20" font-family="Verdana" fill="rgb(0,0,0)"  >__d_..</text>
</g>
<g class="func_g" onmouseover="s('__slab_alloc (91 samples, 3.08%)')" onmouseout="c()">
<title>__slab_alloc (91 samples, 3.08%)</title><rect x="1052.9" y="393" width="36.4" height="25.0" fill="rgb(247,206,5)" rx="2" ry="2" />
<text text-anchor="" x="1055.89295392954" y="412.5" font-size="20" font-family="Verdana" fill="rgb(0,0,0)"  >_..</text>
</g>
<g class="func_g" onmouseover="s('ata_sff_pio_task (1 samples, 0.03%)')" onmouseout="c()">
<title>ata_sff_pio_task (1 samples, 0.03%)</title><rect x="14.8" y="471" width="0.4" height="25.0" fill="rgb(206,18,20)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('call_rcu_sched (9 samples, 0.30%)')" onmouseout="c()">
<title>call_rcu_sched (9 samples, 0.30%)</title><rect x="68.0" y="471" width="3.6" height="25.0" fill="rgb(238,20,14)" rx="2" ry="2" />
</g>
</svg>

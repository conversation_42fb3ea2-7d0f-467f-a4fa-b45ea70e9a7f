<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" width="1200" height="530" onload="init(evt)" viewBox="0 0 1200 530" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<defs >
	<linearGradient id="background" y1="0" y2="1" x1="0" x2="0" >
		<stop stop-color="#eeeeee" offset="5%" />
		<stop stop-color="#eeeeb0" offset="95%" />
	</linearGradient>
</defs>
<style type="text/css">
	.func_g:hover { stroke:black; stroke-width:0.5; }
</style>
<script type="text/ecmascript">
<![CDATA[
	var details;
	function init(evt) { details = document.getElementById("details").firstChild; }
	function s(info) { details.nodeValue = "Function: " + info; }
	function c() { details.nodeValue = ' '; }
]]>
</script>
<rect x="0.0" y="0" width="1200.0" height="530.0" fill="url(#background)"  />
<text text-anchor="middle" x="600" y="24" font-size="17" font-family="Verdana" fill="rgb(0,0,0)"  >Flame Graph</text>
<text text-anchor="" x="10" y="513" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" id="details" > </text>
<g class="func_g" onmouseover="s('sched_clock_cpu (155 samples, 0.04%)')" onmouseout="c()">
<title>sched_clock_cpu (155 samples, 0.04%)</title><rect x="1177.6" y="401" width="0.4" height="15.0" fill="rgb(232,83,53)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('system_call_fastpath (235 samples, 0.06%)')" onmouseout="c()">
<title>system_call_fastpath (235 samples, 0.06%)</title><rect x="970.3" y="401" width="0.7" height="15.0" fill="rgb(224,162,39)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('calc_delta_mine (155 samples, 0.04%)')" onmouseout="c()">
<title>calc_delta_mine (155 samples, 0.04%)</title><rect x="634.1" y="209" width="0.5" height="15.0" fill="rgb(235,150,13)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('apic_set_eoi (1,766 samples, 0.42%)')" onmouseout="c()">
<title>apic_set_eoi (1,766 samples, 0.42%)</title><rect x="554.4" y="321" width="4.9" height="15.0" fill="rgb(250,72,20)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('printk_tick (434 samples, 0.10%)')" onmouseout="c()">
<title>printk_tick (434 samples, 0.10%)</title><rect x="172.2" y="257" width="1.3" height="15.0" fill="rgb(211,104,35)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('thread_return (24,845 samples, 5.84%)')" onmouseout="c()">
<title>thread_return (24,845 samples, 5.84%)</title><rect x="614.0" y="321" width="68.9" height="15.0" fill="rgb(241,141,48)" rx="2" ry="2" />
<text text-anchor="" x="616.96187761227" y="331.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >thread_..</text>
</g>
<g class="func_g" onmouseover="s('kvm_lapic_enabled (337 samples, 0.08%)')" onmouseout="c()">
<title>kvm_lapic_enabled (337 samples, 0.08%)</title><rect x="546.2" y="337" width="0.9" height="15.0" fill="rgb(211,200,42)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_lock_irq (218 samples, 0.05%)')" onmouseout="c()">
<title>_spin_lock_irq (218 samples, 0.05%)</title><rect x="388.0" y="241" width="0.6" height="15.0" fill="rgb(221,92,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('enqueue_entity (5,576 samples, 1.31%)')" onmouseout="c()">
<title>enqueue_entity (5,576 samples, 1.31%)</title><rect x="1004.4" y="209" width="15.4" height="15.0" fill="rgb(214,151,21)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('do_timer (71 samples, 0.02%)')" onmouseout="c()">
<title>do_timer (71 samples, 0.02%)</title><rect x="1158.4" y="401" width="0.2" height="15.0" fill="rgb(223,101,40)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('do_softirq (62 samples, 0.01%)')" onmouseout="c()">
<title>do_softirq (62 samples, 0.01%)</title><rect x="1186.9" y="305" width="0.2" height="15.0" fill="rgb(243,77,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('schedule (4,144 samples, 0.97%)')" onmouseout="c()">
<title>schedule (4,144 samples, 0.97%)</title><rect x="1122.8" y="433" width="11.5" height="15.0" fill="rgb(222,218,51)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('seq_printf (113 samples, 0.03%)')" onmouseout="c()">
<title>seq_printf (113 samples, 0.03%)</title><rect x="969.4" y="337" width="0.3" height="15.0" fill="rgb(218,83,50)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('hrtimer_run_pending (255 samples, 0.06%)')" onmouseout="c()">
<title>hrtimer_run_pending (255 samples, 0.06%)</title><rect x="352.7" y="257" width="0.7" height="15.0" fill="rgb(230,45,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rcu_bh_qs (135 samples, 0.03%)')" onmouseout="c()">
<title>rcu_bh_qs (135 samples, 0.03%)</title><rect x="353.5" y="257" width="0.3" height="15.0" fill="rgb(238,81,33)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__vmx_load_host_state (79 samples, 0.02%)')" onmouseout="c()">
<title>__vmx_load_host_state (79 samples, 0.02%)</title><rect x="687.4" y="289" width="0.2" height="15.0" fill="rgb(238,211,54)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('read_tsc (42 samples, 0.01%)')" onmouseout="c()">
<title>read_tsc (42 samples, 0.01%)</title><rect x="1039.5" y="353" width="0.2" height="15.0" fill="rgb(233,66,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('idle_cpu (181 samples, 0.04%)')" onmouseout="c()">
<title>idle_cpu (181 samples, 0.04%)</title><rect x="1036.7" y="369" width="0.5" height="15.0" fill="rgb(231,163,39)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('poll_idle (130 samples, 0.03%)')" onmouseout="c()">
<title>poll_idle (130 samples, 0.03%)</title><rect x="1112.9" y="417" width="0.3" height="15.0" fill="rgb(232,76,47)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('sys_futex (42 samples, 0.01%)')" onmouseout="c()">
<title>sys_futex (42 samples, 0.01%)</title><rect x="980.0" y="433" width="0.1" height="15.0" fill="rgb(236,194,21)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_read_tsc (37 samples, 0.01%)')" onmouseout="c()">
<title>native_read_tsc (37 samples, 0.01%)</title><rect x="1176.5" y="385" width="0.1" height="15.0" fill="rgb(249,99,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('run_rebalance_domains (60 samples, 0.01%)')" onmouseout="c()">
<title>run_rebalance_domains (60 samples, 0.01%)</title><rect x="1186.9" y="257" width="0.2" height="15.0" fill="rgb(234,111,10)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('sched_clock (78 samples, 0.02%)')" onmouseout="c()">
<title>sched_clock (78 samples, 0.02%)</title><rect x="640.1" y="257" width="0.2" height="15.0" fill="rgb(207,104,49)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ns_to_timespec (99 samples, 0.02%)')" onmouseout="c()">
<title>ns_to_timespec (99 samples, 0.02%)</title><rect x="1112.2" y="417" width="0.3" height="15.0" fill="rgb(227,143,32)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('getnstimeofday (646 samples, 0.15%)')" onmouseout="c()">
<title>getnstimeofday (646 samples, 0.15%)</title><rect x="1103.6" y="385" width="1.8" height="15.0" fill="rgb(214,199,10)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vmx_vcpu_load (356 samples, 0.08%)')" onmouseout="c()">
<title>vmx_vcpu_load (356 samples, 0.08%)</title><rect x="684.4" y="289" width="1.0" height="15.0" fill="rgb(243,164,51)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('enqueue_task_fair (6,607 samples, 1.55%)')" onmouseout="c()">
<title>enqueue_task_fair (6,607 samples, 1.55%)</title><rect x="1003.2" y="225" width="18.3" height="15.0" fill="rgb(251,134,19)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_shares (224 samples, 0.05%)')" onmouseout="c()">
<title>update_shares (224 samples, 0.05%)</title><rect x="380.9" y="225" width="0.6" height="15.0" fill="rgb(229,50,20)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('futex_wait (53 samples, 0.01%)')" onmouseout="c()">
<title>futex_wait (53 samples, 0.01%)</title><rect x="980.1" y="401" width="0.2" height="15.0" fill="rgb(215,192,35)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('nr_iowait_cpu (104 samples, 0.02%)')" onmouseout="c()">
<title>nr_iowait_cpu (104 samples, 0.02%)</title><rect x="1111.9" y="417" width="0.3" height="15.0" fill="rgb(208,117,1)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__hrtimer_start_range_ns (4,286 samples, 1.01%)')" onmouseout="c()">
<title>__hrtimer_start_range_ns (4,286 samples, 1.01%)</title><rect x="1163.8" y="401" width="11.9" height="15.0" fill="rgb(250,176,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('save_args (749 samples, 0.18%)')" onmouseout="c()">
<title>save_args (749 samples, 0.18%)</title><rect x="910.5" y="353" width="2.1" height="15.0" fill="rgb(249,138,42)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('clear_buddies (241 samples, 0.06%)')" onmouseout="c()">
<title>clear_buddies (241 samples, 0.06%)</title><rect x="1126.2" y="417" width="0.6" height="15.0" fill="rgb(216,188,17)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('iov_iter_copy_from_user_atomic (68 samples, 0.02%)')" onmouseout="c()">
<title>iov_iter_copy_from_user_atomic (68 samples, 0.02%)</title><rect x="970.7" y="273" width="0.2" height="15.0" fill="rgb(227,71,2)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('do_softirq (235 samples, 0.06%)')" onmouseout="c()">
<title>do_softirq (235 samples, 0.06%)</title><rect x="909.0" y="305" width="0.6" height="15.0" fill="rgb(243,77,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_x2apic_msr_write (643 samples, 0.15%)')" onmouseout="c()">
<title>kvm_x2apic_msr_write (643 samples, 0.15%)</title><rect x="822.9" y="289" width="1.8" height="15.0" fill="rgb(227,74,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('intel_idle (125 samples, 0.03%)')" onmouseout="c()">
<title>intel_idle (125 samples, 0.03%)</title><rect x="1120.2" y="433" width="0.4" height="15.0" fill="rgb(216,81,47)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('resched_task (67 samples, 0.02%)')" onmouseout="c()">
<title>resched_task (67 samples, 0.02%)</title><rect x="1024.1" y="241" width="0.1" height="15.0" fill="rgb(236,7,47)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('cpu_idle (3,302 samples, 0.78%)')" onmouseout="c()">
<title>cpu_idle (3,302 samples, 0.78%)</title><rect x="1180.8" y="401" width="9.1" height="15.0" fill="rgb(233,201,15)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('clockevents_program_event (44 samples, 0.01%)')" onmouseout="c()">
<title>clockevents_program_event (44 samples, 0.01%)</title><rect x="1142.9" y="321" width="0.1" height="15.0" fill="rgb(233,130,45)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('system_call_fastpath (60 samples, 0.01%)')" onmouseout="c()">
<title>system_call_fastpath (60 samples, 0.01%)</title><rect x="969.8" y="449" width="0.2" height="15.0" fill="rgb(224,162,39)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_lock_irqsave (659 samples, 0.16%)')" onmouseout="c()">
<title>_spin_lock_irqsave (659 samples, 0.16%)</title><rect x="783.5" y="193" width="1.8" height="15.0" fill="rgb(209,137,45)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('seq_vprintf (113 samples, 0.03%)')" onmouseout="c()">
<title>seq_vprintf (113 samples, 0.03%)</title><rect x="969.4" y="321" width="0.3" height="15.0" fill="rgb(210,166,31)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rb_next (51 samples, 0.01%)')" onmouseout="c()">
<title>rb_next (51 samples, 0.01%)</title><rect x="1132.0" y="369" width="0.2" height="15.0" fill="rgb(251,1,22)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__queue_work (54 samples, 0.01%)')" onmouseout="c()">
<title>__queue_work (54 samples, 0.01%)</title><rect x="388.7" y="225" width="0.2" height="15.0" fill="rgb(243,121,17)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tick_program_event (170 samples, 0.04%)')" onmouseout="c()">
<title>tick_program_event (170 samples, 0.04%)</title><rect x="1142.8" y="353" width="0.4" height="15.0" fill="rgb(219,99,4)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('find_busiest_group (363 samples, 0.09%)')" onmouseout="c()">
<title>find_busiest_group (363 samples, 0.09%)</title><rect x="1114.1" y="273" width="1.1" height="15.0" fill="rgb(221,103,29)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_lock_irqsave (56 samples, 0.01%)')" onmouseout="c()">
<title>_spin_lock_irqsave (56 samples, 0.01%)</title><rect x="1152.9" y="369" width="0.1" height="15.0" fill="rgb(209,137,45)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('x86_pmu_enable (817 samples, 0.19%)')" onmouseout="c()">
<title>x86_pmu_enable (817 samples, 0.19%)</title><rect x="218.2" y="193" width="2.3" height="15.0" fill="rgb(232,155,11)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('perf_adjust_freq_unthr_context (5,222 samples, 1.23%)')" onmouseout="c()">
<title>perf_adjust_freq_unthr_context (5,222 samples, 1.23%)</title><rect x="207.7" y="225" width="14.5" height="15.0" fill="rgb(241,147,53)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__wake_up_common (10,889 samples, 2.56%)')" onmouseout="c()">
<title>__wake_up_common (10,889 samples, 2.56%)</title><rect x="999.1" y="321" width="30.2" height="15.0" fill="rgb(214,94,27)" rx="2" ry="2" />
<text text-anchor="" x="1002.0854437302" y="331.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >__..</text>
</g>
<g class="func_g" onmouseover="s('native_apic_mem_write (47 samples, 0.01%)')" onmouseout="c()">
<title>native_apic_mem_write (47 samples, 0.01%)</title><rect x="320.0" y="241" width="0.1" height="15.0" fill="rgb(209,177,4)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_lock_irq (73 samples, 0.02%)')" onmouseout="c()">
<title>_spin_lock_irq (73 samples, 0.02%)</title><rect x="981.5" y="433" width="0.2" height="15.0" fill="rgb(221,92,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__remove_hrtimer (2,086 samples, 0.49%)')" onmouseout="c()">
<title>__remove_hrtimer (2,086 samples, 0.49%)</title><rect x="1138.8" y="385" width="5.8" height="15.0" fill="rgb(244,9,30)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('retint_restore_args (323 samples, 0.08%)')" onmouseout="c()">
<title>retint_restore_args (323 samples, 0.08%)</title><rect x="909.6" y="353" width="0.9" height="15.0" fill="rgb(235,139,19)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_local_bh_enable (37 samples, 0.01%)')" onmouseout="c()">
<title>_local_bh_enable (37 samples, 0.01%)</title><rect x="990.9" y="385" width="0.1" height="15.0" fill="rgb(210,228,4)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('sched_clock_idle_sleep_event (170 samples, 0.04%)')" onmouseout="c()">
<title>sched_clock_idle_sleep_event (170 samples, 0.04%)</title><rect x="1177.5" y="417" width="0.5" height="15.0" fill="rgb(247,203,54)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tick_dev_program_event (166 samples, 0.04%)')" onmouseout="c()">
<title>tick_dev_program_event (166 samples, 0.04%)</title><rect x="1153.4" y="369" width="0.4" height="15.0" fill="rgb(218,118,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__switch_to (583 samples, 0.14%)')" onmouseout="c()">
<title>__switch_to (583 samples, 0.14%)</title><rect x="11.1" y="433" width="1.6" height="15.0" fill="rgb(218,57,18)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_read_tsc (41 samples, 0.01%)')" onmouseout="c()">
<title>native_read_tsc (41 samples, 0.01%)</title><rect x="1177.9" y="353" width="0.1" height="15.0" fill="rgb(249,99,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('timekeeping_update (57 samples, 0.01%)')" onmouseout="c()">
<title>timekeeping_update (57 samples, 0.01%)</title><rect x="1041.7" y="321" width="0.2" height="15.0" fill="rgb(253,156,9)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('smi_event_handler (581 samples, 0.14%)')" onmouseout="c()">
<title>smi_event_handler (581 samples, 0.14%)</title><rect x="974.0" y="417" width="1.6" height="15.0" fill="rgb(222,16,11)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_lock (73 samples, 0.02%)')" onmouseout="c()">
<title>_spin_lock (73 samples, 0.02%)</title><rect x="991.0" y="385" width="0.2" height="15.0" fill="rgb(207,37,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('handle_external_interrupt (414 samples, 0.10%)')" onmouseout="c()">
<title>handle_external_interrupt (414 samples, 0.10%)</title><rect x="753.2" y="321" width="1.2" height="15.0" fill="rgb(218,128,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unmap_region (45 samples, 0.01%)')" onmouseout="c()">
<title>unmap_region (45 samples, 0.01%)</title><rect x="969.8" y="401" width="0.2" height="15.0" fill="rgb(241,81,4)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('PyEval_EvalFrameEx (54 samples, 0.01%)')" onmouseout="c()">
<title>PyEval_EvalFrameEx (54 samples, 0.01%)</title><rect x="10.0" y="465" width="0.2" height="15.0" fill="rgb(230,68,24)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_vcpu_kick (699 samples, 0.16%)')" onmouseout="c()">
<title>kvm_vcpu_kick (699 samples, 0.16%)</title><rect x="541.5" y="289" width="2.0" height="15.0" fill="rgb(217,38,48)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('resched_task (62 samples, 0.01%)')" onmouseout="c()">
<title>resched_task (62 samples, 0.01%)</title><rect x="793.2" y="81" width="0.2" height="15.0" fill="rgb(236,7,47)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('apic_has_pending_timer (1,365 samples, 0.32%)')" onmouseout="c()">
<title>apic_has_pending_timer (1,365 samples, 0.32%)</title><rect x="520.6" y="321" width="3.8" height="15.0" fill="rgb(248,212,39)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('calc_delta_mine (287 samples, 0.07%)')" onmouseout="c()">
<title>calc_delta_mine (287 samples, 0.07%)</title><rect x="231.7" y="225" width="0.8" height="15.0" fill="rgb(235,150,13)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rb_erase (237 samples, 0.06%)')" onmouseout="c()">
<title>rb_erase (237 samples, 0.06%)</title><rect x="138.5" y="289" width="0.6" height="15.0" fill="rgb(236,5,12)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('run_rebalance_domains (71 samples, 0.02%)')" onmouseout="c()">
<title>run_rebalance_domains (71 samples, 0.02%)</title><rect x="1136.6" y="321" width="0.2" height="15.0" fill="rgb(234,111,10)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('enter_idle (288 samples, 0.07%)')" onmouseout="c()">
<title>enter_idle (288 samples, 0.07%)</title><rect x="1118.3" y="433" width="0.8" height="15.0" fill="rgb(237,51,8)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_write_guest_cached (1,090 samples, 0.26%)')" onmouseout="c()">
<title>kvm_write_guest_cached (1,090 samples, 0.26%)</title><rect x="695.6" y="337" width="3.0" height="15.0" fill="rgb(227,3,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('hrtimer_try_to_cancel (57 samples, 0.01%)')" onmouseout="c()">
<title>hrtimer_try_to_cancel (57 samples, 0.01%)</title><rect x="1155.3" y="417" width="0.2" height="15.0" fill="rgb(221,57,30)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('generic_write_end (42 samples, 0.01%)')" onmouseout="c()">
<title>generic_write_end (42 samples, 0.01%)</title><rect x="970.6" y="257" width="0.1" height="15.0" fill="rgb(220,178,4)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__rb_rotate_left (261 samples, 0.06%)')" onmouseout="c()">
<title>__rb_rotate_left (261 samples, 0.06%)</title><rect x="806.7" y="177" width="0.7" height="15.0" fill="rgb(251,185,40)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rb_erase (369 samples, 0.09%)')" onmouseout="c()">
<title>rb_erase (369 samples, 0.09%)</title><rect x="996.6" y="337" width="1.0" height="15.0" fill="rgb(236,5,12)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rcu_process_gp_end (330 samples, 0.08%)')" onmouseout="c()">
<title>rcu_process_gp_end (330 samples, 0.08%)</title><rect x="379.3" y="241" width="1.0" height="15.0" fill="rgb(245,96,36)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('get_next_timer_interrupt (533 samples, 0.13%)')" onmouseout="c()">
<title>get_next_timer_interrupt (533 samples, 0.13%)</title><rect x="1162.0" y="417" width="1.4" height="15.0" fill="rgb(245,174,47)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('mutex_lock (238 samples, 0.06%)')" onmouseout="c()">
<title>mutex_lock (238 samples, 0.06%)</title><rect x="685.4" y="305" width="0.6" height="15.0" fill="rgb(238,5,22)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_cpu_has_interrupt (183 samples, 0.04%)')" onmouseout="c()">
<title>kvm_cpu_has_interrupt (183 samples, 0.04%)</title><rect x="603.2" y="321" width="0.5" height="15.0" fill="rgb(245,130,49)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('srcu_read_unlock (534 samples, 0.13%)')" onmouseout="c()">
<title>srcu_read_unlock (534 samples, 0.13%)</title><rect x="719.7" y="337" width="1.4" height="15.0" fill="rgb(241,146,12)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('account_entity_enqueue (39 samples, 0.01%)')" onmouseout="c()">
<title>account_entity_enqueue (39 samples, 0.01%)</title><rect x="241.3" y="209" width="0.1" height="15.0" fill="rgb(224,51,23)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__do_softirq (111 samples, 0.03%)')" onmouseout="c()">
<title>__do_softirq (111 samples, 0.03%)</title><rect x="1136.5" y="337" width="0.3" height="15.0" fill="rgb(208,218,33)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('enqueue_entity (129 samples, 0.03%)')" onmouseout="c()">
<title>enqueue_entity (129 samples, 0.03%)</title><rect x="1002.8" y="225" width="0.4" height="15.0" fill="rgb(214,151,21)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('select_task_rq_fair (654 samples, 0.15%)')" onmouseout="c()">
<title>select_task_rq_fair (654 samples, 0.15%)</title><rect x="1024.7" y="257" width="1.9" height="15.0" fill="rgb(210,171,2)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__wake_up (401 samples, 0.09%)')" onmouseout="c()">
<title>__wake_up (401 samples, 0.09%)</title><rect x="1181.5" y="289" width="1.1" height="15.0" fill="rgb(226,70,29)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__rb_rotate_left (294 samples, 0.07%)')" onmouseout="c()">
<title>__rb_rotate_left (294 samples, 0.07%)</title><rect x="811.0" y="161" width="0.8" height="15.0" fill="rgb(251,185,40)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__vmx_load_host_state (812 samples, 0.19%)')" onmouseout="c()">
<title>__vmx_load_host_state (812 samples, 0.19%)</title><rect x="688.3" y="273" width="2.2" height="15.0" fill="rgb(238,211,54)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('worker_thread (427 samples, 0.10%)')" onmouseout="c()">
<title>worker_thread (427 samples, 0.10%)</title><rect x="975.9" y="433" width="1.2" height="15.0" fill="rgb(216,191,6)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_curr (358 samples, 0.08%)')" onmouseout="c()">
<title>update_curr (358 samples, 0.08%)</title><rect x="241.5" y="209" width="1.0" height="15.0" fill="rgb(218,93,52)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('lock_hrtimer_base (75 samples, 0.02%)')" onmouseout="c()">
<title>lock_hrtimer_base (75 samples, 0.02%)</title><rect x="1152.8" y="385" width="0.2" height="15.0" fill="rgb(232,201,8)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('lock_hrtimer_base (65 samples, 0.02%)')" onmouseout="c()">
<title>lock_hrtimer_base (65 samples, 0.02%)</title><rect x="817.9" y="209" width="0.2" height="15.0" fill="rgb(232,201,8)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_inject_apic_timer_irqs (4,450 samples, 1.05%)')" onmouseout="c()">
<title>kvm_inject_apic_timer_irqs (4,450 samples, 1.05%)</title><rect x="531.1" y="321" width="12.4" height="15.0" fill="rgb(222,210,10)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__do_softirq (82 samples, 0.02%)')" onmouseout="c()">
<title>__do_softirq (82 samples, 0.02%)</title><rect x="1187.2" y="289" width="0.2" height="15.0" fill="rgb(208,218,33)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('all (425,157 samples, 100%)')" onmouseout="c()">
<title>all (425,157 samples, 100%)</title><rect x="10.0" y="481" width="1180.0" height="15.0" fill="rgb(213,209,9)" rx="2" ry="2" />
<text text-anchor="" x="13" y="491.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unalias_gfn_instantiation (225 samples, 0.05%)')" onmouseout="c()">
<title>unalias_gfn_instantiation (225 samples, 0.05%)</title><rect x="589.8" y="289" width="0.7" height="15.0" fill="rgb(212,78,21)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('force_quiescent_state (410 samples, 0.10%)')" onmouseout="c()">
<title>force_quiescent_state (410 samples, 0.10%)</title><rect x="378.2" y="241" width="1.1" height="15.0" fill="rgb(244,88,47)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('schedule (59 samples, 0.01%)')" onmouseout="c()">
<title>schedule (59 samples, 0.01%)</title><rect x="1179.3" y="449" width="0.2" height="15.0" fill="rgb(222,218,51)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_lock (2,269 samples, 0.53%)')" onmouseout="c()">
<title>_spin_lock (2,269 samples, 0.53%)</title><rect x="195.3" y="241" width="6.3" height="15.0" fill="rgb(207,37,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_put_guest_fpu (85 samples, 0.02%)')" onmouseout="c()">
<title>kvm_put_guest_fpu (85 samples, 0.02%)</title><rect x="687.6" y="289" width="0.2" height="15.0" fill="rgb(214,10,51)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__hrtimer_start_range_ns (146 samples, 0.03%)')" onmouseout="c()">
<title>__hrtimer_start_range_ns (146 samples, 0.03%)</title><rect x="1189.4" y="353" width="0.4" height="15.0" fill="rgb(250,176,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__remove_hrtimer (536 samples, 0.13%)')" onmouseout="c()">
<title>__remove_hrtimer (536 samples, 0.13%)</title><rect x="996.4" y="353" width="1.5" height="15.0" fill="rgb(244,9,30)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('exit_idle (158 samples, 0.04%)')" onmouseout="c()">
<title>exit_idle (158 samples, 0.04%)</title><rect x="57.2" y="321" width="0.4" height="15.0" fill="rgb(217,85,44)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('enqueue_hrtimer (2,634 samples, 0.62%)')" onmouseout="c()">
<title>enqueue_hrtimer (2,634 samples, 0.62%)</title><rect x="124.0" y="289" width="7.3" height="15.0" fill="rgb(251,179,15)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__remove_hrtimer (1,544 samples, 0.36%)')" onmouseout="c()">
<title>__remove_hrtimer (1,544 samples, 0.36%)</title><rect x="1164.9" y="385" width="4.3" height="15.0" fill="rgb(244,9,30)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_ioapic_handles_vector (303 samples, 0.07%)')" onmouseout="c()">
<title>kvm_ioapic_handles_vector (303 samples, 0.07%)</title><rect x="558.4" y="305" width="0.9" height="15.0" fill="rgb(233,138,41)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('physflat_send_IPI_mask (131 samples, 0.03%)')" onmouseout="c()">
<title>physflat_send_IPI_mask (131 samples, 0.03%)</title><rect x="201.8" y="225" width="0.4" height="15.0" fill="rgb(224,172,23)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('hrtimer_run_queues (157 samples, 0.04%)')" onmouseout="c()">
<title>hrtimer_run_queues (157 samples, 0.04%)</title><rect x="169.8" y="257" width="0.4" height="15.0" fill="rgb(250,176,31)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rb_next (39 samples, 0.01%)')" onmouseout="c()">
<title>rb_next (39 samples, 0.01%)</title><rect x="1132.3" y="385" width="0.1" height="15.0" fill="rgb(251,1,22)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('system_call_fastpath (63 samples, 0.01%)')" onmouseout="c()">
<title>system_call_fastpath (63 samples, 0.01%)</title><rect x="970.1" y="449" width="0.1" height="15.0" fill="rgb(224,162,39)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('mutex_unlock (85 samples, 0.02%)')" onmouseout="c()">
<title>mutex_unlock (85 samples, 0.02%)</title><rect x="691.1" y="305" width="0.2" height="15.0" fill="rgb(235,145,26)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('task_waking_fair (212 samples, 0.05%)')" onmouseout="c()">
<title>task_waking_fair (212 samples, 0.05%)</title><rect x="1028.3" y="257" width="0.6" height="15.0" fill="rgb(252,81,50)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__rb_rotate_left (56 samples, 0.01%)')" onmouseout="c()">
<title>__rb_rotate_left (56 samples, 0.01%)</title><rect x="1169.9" y="369" width="0.2" height="15.0" fill="rgb(251,185,40)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('handle_external_interrupt (598 samples, 0.14%)')" onmouseout="c()">
<title>handle_external_interrupt (598 samples, 0.14%)</title><rect x="488.6" y="337" width="1.7" height="15.0" fill="rgb(218,128,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ktime_get (198 samples, 0.05%)')" onmouseout="c()">
<title>ktime_get (198 samples, 0.05%)</title><rect x="1176.1" y="417" width="0.5" height="15.0" fill="rgb(213,13,52)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vmcs_writel (856 samples, 0.20%)')" onmouseout="c()">
<title>vmcs_writel (856 samples, 0.20%)</title><rect x="885.1" y="321" width="2.4" height="15.0" fill="rgb(228,19,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__GI___munmap (62 samples, 0.01%)')" onmouseout="c()">
<title>__GI___munmap (62 samples, 0.01%)</title><rect x="969.8" y="465" width="0.2" height="15.0" fill="rgb(206,229,48)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_write_guest_page (771 samples, 0.18%)')" onmouseout="c()">
<title>kvm_write_guest_page (771 samples, 0.18%)</title><rect x="693.3" y="321" width="2.1" height="15.0" fill="rgb(245,123,44)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_write_guest (1,185 samples, 0.28%)')" onmouseout="c()">
<title>kvm_write_guest (1,185 samples, 0.28%)</title><rect x="692.3" y="337" width="3.3" height="15.0" fill="rgb(222,39,12)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('group_sched_in (100 samples, 0.02%)')" onmouseout="c()">
<title>group_sched_in (100 samples, 0.02%)</title><rect x="1046.4" y="337" width="0.3" height="15.0" fill="rgb(254,64,6)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ktime_get (139 samples, 0.03%)')" onmouseout="c()">
<title>ktime_get (139 samples, 0.03%)</title><rect x="1039.2" y="353" width="0.3" height="15.0" fill="rgb(213,13,52)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_cpu_has_interrupt (1,038 samples, 0.24%)')" onmouseout="c()">
<title>kvm_cpu_has_interrupt (1,038 samples, 0.24%)</title><rect x="887.6" y="353" width="2.9" height="15.0" fill="rgb(245,130,49)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_read_tsc (99 samples, 0.02%)')" onmouseout="c()">
<title>native_read_tsc (99 samples, 0.02%)</title><rect x="287.4" y="177" width="0.3" height="15.0" fill="rgb(249,99,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('idle_cpu (101 samples, 0.02%)')" onmouseout="c()">
<title>idle_cpu (101 samples, 0.02%)</title><rect x="170.2" y="257" width="0.3" height="15.0" fill="rgb(231,163,39)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('lapic_is_periodic (875 samples, 0.21%)')" onmouseout="c()">
<title>lapic_is_periodic (875 samples, 0.21%)</title><rect x="135.7" y="289" width="2.5" height="15.0" fill="rgb(205,14,15)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_curr (699 samples, 0.16%)')" onmouseout="c()">
<title>update_curr (699 samples, 0.16%)</title><rect x="632.6" y="225" width="2.0" height="15.0" fill="rgb(218,93,52)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__wake_up_common (394 samples, 0.09%)')" onmouseout="c()">
<title>__wake_up_common (394 samples, 0.09%)</title><rect x="1181.5" y="273" width="1.1" height="15.0" fill="rgb(214,94,27)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ktime_get_update_offsets (300 samples, 0.07%)')" onmouseout="c()">
<title>ktime_get_update_offsets (300 samples, 0.07%)</title><rect x="397.3" y="321" width="0.8" height="15.0" fill="rgb(246,219,37)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ktime_get_update_offsets (2,418 samples, 0.57%)')" onmouseout="c()">
<title>ktime_get_update_offsets (2,418 samples, 0.57%)</title><rect x="292.7" y="305" width="6.7" height="15.0" fill="rgb(246,219,37)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('finish_wait (89 samples, 0.02%)')" onmouseout="c()">
<title>finish_wait (89 samples, 0.02%)</title><rect x="488.4" y="337" width="0.2" height="15.0" fill="rgb(215,130,24)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('br_handle_frame (40 samples, 0.01%)')" onmouseout="c()">
<title>br_handle_frame (40 samples, 0.01%)</title><rect x="909.4" y="145" width="0.1" height="15.0" fill="rgb(244,10,37)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__do_softirq (79 samples, 0.02%)')" onmouseout="c()">
<title>__do_softirq (79 samples, 0.02%)</title><rect x="344.1" y="289" width="0.2" height="15.0" fill="rgb(208,218,33)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('clockevents_program_event (73 samples, 0.02%)')" onmouseout="c()">
<title>clockevents_program_event (73 samples, 0.02%)</title><rect x="1166.1" y="337" width="0.2" height="15.0" fill="rgb(233,130,45)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('thread_return (37 samples, 0.01%)')" onmouseout="c()">
<title>thread_return (37 samples, 0.01%)</title><rect x="1188.2" y="385" width="0.1" height="15.0" fill="rgb(241,141,48)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('profile_tick (900 samples, 0.21%)')" onmouseout="c()">
<title>profile_tick (900 samples, 0.21%)</title><rect x="150.5" y="273" width="2.5" height="15.0" fill="rgb(220,196,40)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_vcpu_kick (1,647 samples, 0.39%)')" onmouseout="c()">
<title>kvm_vcpu_kick (1,647 samples, 0.39%)</title><rect x="790.9" y="193" width="4.6" height="15.0" fill="rgb(217,38,48)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__list_add (119 samples, 0.03%)')" onmouseout="c()">
<title>__list_add (119 samples, 0.03%)</title><rect x="1006.6" y="193" width="0.3" height="15.0" fill="rgb(223,130,16)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('hrtimer_forward (198 samples, 0.05%)')" onmouseout="c()">
<title>hrtimer_forward (198 samples, 0.05%)</title><rect x="145.4" y="273" width="0.6" height="15.0" fill="rgb(212,164,2)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_lock (362 samples, 0.09%)')" onmouseout="c()">
<title>_spin_lock (362 samples, 0.09%)</title><rect x="54.3" y="321" width="1.0" height="15.0" fill="rgb(207,37,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vmstat_update (47 samples, 0.01%)')" onmouseout="c()">
<title>vmstat_update (47 samples, 0.01%)</title><rect x="977.0" y="417" width="0.1" height="15.0" fill="rgb(252,71,5)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('pick_next_task_stop (141 samples, 0.03%)')" onmouseout="c()">
<title>pick_next_task_stop (141 samples, 0.03%)</title><rect x="674.9" y="305" width="0.4" height="15.0" fill="rgb(220,169,2)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('irq_exit (39 samples, 0.01%)')" onmouseout="c()">
<title>irq_exit (39 samples, 0.01%)</title><rect x="1183.2" y="337" width="0.1" height="15.0" fill="rgb(216,86,39)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rb_erase (189 samples, 0.04%)')" onmouseout="c()">
<title>rb_erase (189 samples, 0.04%)</title><rect x="1145.4" y="385" width="0.5" height="15.0" fill="rgb(236,5,12)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('calc_global_load (51 samples, 0.01%)')" onmouseout="c()">
<title>calc_global_load (51 samples, 0.01%)</title><rect x="158.3" y="241" width="0.1" height="15.0" fill="rgb(228,122,6)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('read_tsc (118 samples, 0.03%)')" onmouseout="c()">
<title>read_tsc (118 samples, 0.03%)</title><rect x="326.5" y="273" width="0.3" height="15.0" fill="rgb(233,66,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('force_quiescent_state (623 samples, 0.15%)')" onmouseout="c()">
<title>force_quiescent_state (623 samples, 0.15%)</title><rect x="365.8" y="225" width="1.7" height="15.0" fill="rgb(244,88,47)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('account_entity_enqueue (547 samples, 0.13%)')" onmouseout="c()">
<title>account_entity_enqueue (547 samples, 0.13%)</title><rect x="1006.9" y="193" width="1.5" height="15.0" fill="rgb(224,51,23)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tick_program_event (3,844 samples, 0.90%)')" onmouseout="c()">
<title>tick_program_event (3,844 samples, 0.90%)</title><rect x="403.6" y="321" width="10.6" height="15.0" fill="rgb(219,99,4)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('lapic_is_periodic (329 samples, 0.08%)')" onmouseout="c()">
<title>lapic_is_periodic (329 samples, 0.08%)</title><rect x="1030.6" y="353" width="0.9" height="15.0" fill="rgb(205,14,15)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_read_tsc (58 samples, 0.01%)')" onmouseout="c()">
<title>native_read_tsc (58 samples, 0.01%)</title><rect x="1105.3" y="353" width="0.1" height="15.0" fill="rgb(249,99,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('[unknown] (285 samples, 0.07%)')" onmouseout="c()">
<title>[unknown] (285 samples, 0.07%)</title><rect x="970.2" y="433" width="0.8" height="15.0" fill="rgb(229,75,51)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('place_entity (71 samples, 0.02%)')" onmouseout="c()">
<title>place_entity (71 samples, 0.02%)</title><rect x="792.0" y="33" width="0.2" height="15.0" fill="rgb(226,35,29)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ktime_get (1,511 samples, 0.36%)')" onmouseout="c()">
<title>ktime_get (1,511 samples, 0.36%)</title><rect x="321.8" y="273" width="4.2" height="15.0" fill="rgb(213,13,52)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__do_softirq (235 samples, 0.06%)')" onmouseout="c()">
<title>__do_softirq (235 samples, 0.06%)</title><rect x="909.0" y="273" width="0.6" height="15.0" fill="rgb(208,218,33)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('dequeue_entity (5,688 samples, 1.34%)')" onmouseout="c()">
<title>dequeue_entity (5,688 samples, 1.34%)</title><rect x="623.0" y="257" width="15.8" height="15.0" fill="rgb(208,108,34)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('clockevents_program_event (258 samples, 0.06%)')" onmouseout="c()">
<title>clockevents_program_event (258 samples, 0.06%)</title><rect x="1166.5" y="321" width="0.7" height="15.0" fill="rgb(233,130,45)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('place_entity (104 samples, 0.02%)')" onmouseout="c()">
<title>place_entity (104 samples, 0.02%)</title><rect x="1019.8" y="209" width="0.3" height="15.0" fill="rgb(226,35,29)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tick_sched_timer (148 samples, 0.03%)')" onmouseout="c()">
<title>tick_sched_timer (148 samples, 0.03%)</title><rect x="326.8" y="305" width="0.4" height="15.0" fill="rgb(213,15,28)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kthread (1,255 samples, 0.30%)')" onmouseout="c()">
<title>kthread (1,255 samples, 0.30%)</title><rect x="973.6" y="449" width="3.5" height="15.0" fill="rgb(254,163,7)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_read_tsc (501 samples, 0.12%)')" onmouseout="c()">
<title>native_read_tsc (501 samples, 0.12%)</title><rect x="742.0" y="321" width="1.4" height="15.0" fill="rgb(249,99,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__run_hrtimer (86 samples, 0.02%)')" onmouseout="c()">
<title>__run_hrtimer (86 samples, 0.02%)</title><rect x="1136.2" y="369" width="0.3" height="15.0" fill="rgb(208,100,50)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_read_guest (1,391 samples, 0.33%)')" onmouseout="c()">
<title>kvm_read_guest (1,391 samples, 0.33%)</title><rect x="587.1" y="337" width="3.8" height="15.0" fill="rgb(246,140,42)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_cfs_shares (67 samples, 0.02%)')" onmouseout="c()">
<title>update_cfs_shares (67 samples, 0.02%)</title><rect x="1116.6" y="257" width="0.1" height="15.0" fill="rgb(240,28,1)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('[unknown] (64 samples, 0.02%)')" onmouseout="c()">
<title>[unknown] (64 samples, 0.02%)</title><rect x="1180.0" y="417" width="0.2" height="15.0" fill="rgb(229,75,51)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vmx_get_msr (2,083 samples, 0.49%)')" onmouseout="c()">
<title>vmx_get_msr (2,083 samples, 0.49%)</title><rect x="918.9" y="353" width="5.8" height="15.0" fill="rgb(217,85,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('schedule (1,543 samples, 0.36%)')" onmouseout="c()">
<title>schedule (1,543 samples, 0.36%)</title><rect x="609.7" y="321" width="4.3" height="15.0" fill="rgb(222,218,51)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('find_busiest_group (42 samples, 0.01%)')" onmouseout="c()">
<title>find_busiest_group (42 samples, 0.01%)</title><rect x="596.5" y="321" width="0.1" height="15.0" fill="rgb(221,103,29)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('run_local_timers (1,085 samples, 0.26%)')" onmouseout="c()">
<title>run_local_timers (1,085 samples, 0.26%)</title><rect x="180.2" y="257" width="3.1" height="15.0" fill="rgb(216,154,32)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('read_tsc (37 samples, 0.01%)')" onmouseout="c()">
<title>read_tsc (37 samples, 0.01%)</title><rect x="1155.6" y="401" width="0.1" height="15.0" fill="rgb(233,66,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__rb_rotate_left (182 samples, 0.04%)')" onmouseout="c()">
<title>__rb_rotate_left (182 samples, 0.04%)</title><rect x="126.7" y="273" width="0.5" height="15.0" fill="rgb(251,185,40)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__perf_event_enable (102 samples, 0.02%)')" onmouseout="c()">
<title>__perf_event_enable (102 samples, 0.02%)</title><rect x="1046.4" y="353" width="0.3" height="15.0" fill="rgb(247,179,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('hrtimer_try_to_cancel (2,635 samples, 0.62%)')" onmouseout="c()">
<title>hrtimer_try_to_cancel (2,635 samples, 0.62%)</title><rect x="1138.6" y="401" width="7.3" height="15.0" fill="rgb(221,57,30)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('number (40 samples, 0.01%)')" onmouseout="c()">
<title>number (40 samples, 0.01%)</title><rect x="969.6" y="289" width="0.1" height="15.0" fill="rgb(218,66,21)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_apic_local_deliver (3,664 samples, 0.86%)')" onmouseout="c()">
<title>kvm_apic_local_deliver (3,664 samples, 0.86%)</title><rect x="533.3" y="305" width="10.2" height="15.0" fill="rgb(221,119,6)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rb_erase (389 samples, 0.09%)')" onmouseout="c()">
<title>rb_erase (389 samples, 0.09%)</title><rect x="1143.2" y="369" width="1.1" height="15.0" fill="rgb(236,5,12)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('napi_gro_receive (89 samples, 0.02%)')" onmouseout="c()">
<title>napi_gro_receive (89 samples, 0.02%)</title><rect x="909.1" y="209" width="0.3" height="15.0" fill="rgb(219,67,15)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_read_guest (169 samples, 0.04%)')" onmouseout="c()">
<title>kvm_read_guest (169 samples, 0.04%)</title><rect x="903.5" y="353" width="0.4" height="15.0" fill="rgb(246,140,42)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tick_do_update_jiffies64 (87 samples, 0.02%)')" onmouseout="c()">
<title>tick_do_update_jiffies64 (87 samples, 0.02%)</title><rect x="1158.3" y="417" width="0.3" height="15.0" fill="rgb(236,208,31)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__getdents64 (64 samples, 0.02%)')" onmouseout="c()">
<title>__getdents64 (64 samples, 0.02%)</title><rect x="970.1" y="465" width="0.1" height="15.0" fill="rgb(215,47,50)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('apic_timer_interrupt (51 samples, 0.01%)')" onmouseout="c()">
<title>apic_timer_interrupt (51 samples, 0.01%)</title><rect x="12.7" y="433" width="0.2" height="15.0" fill="rgb(239,200,41)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('[unknown] (98 samples, 0.02%)')" onmouseout="c()">
<title>[unknown] (98 samples, 0.02%)</title><rect x="10.7" y="449" width="0.3" height="15.0" fill="rgb(229,75,51)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('apic_update_ppr (579 samples, 0.14%)')" onmouseout="c()">
<title>apic_update_ppr (579 samples, 0.14%)</title><rect x="507.3" y="321" width="1.7" height="15.0" fill="rgb(244,88,21)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('try_to_wake_up (45 samples, 0.01%)')" onmouseout="c()">
<title>try_to_wake_up (45 samples, 0.01%)</title><rect x="388.8" y="129" width="0.1" height="15.0" fill="rgb(248,130,8)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_write_msr_safe (343 samples, 0.08%)')" onmouseout="c()">
<title>native_write_msr_safe (343 samples, 0.08%)</title><rect x="978.4" y="465" width="0.9" height="15.0" fill="rgb(215,163,22)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('x86_pmu_enable (96 samples, 0.02%)')" onmouseout="c()">
<title>x86_pmu_enable (96 samples, 0.02%)</title><rect x="1046.4" y="289" width="0.3" height="15.0" fill="rgb(232,155,11)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('netif_receive_skb (82 samples, 0.02%)')" onmouseout="c()">
<title>netif_receive_skb (82 samples, 0.02%)</title><rect x="909.1" y="177" width="0.3" height="15.0" fill="rgb(252,105,44)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('[unknown] (117 samples, 0.03%)')" onmouseout="c()">
<title>[unknown] (117 samples, 0.03%)</title><rect x="1180.0" y="433" width="0.3" height="15.0" fill="rgb(229,75,51)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('usb_hcd_poll_rh_status (54 samples, 0.01%)')" onmouseout="c()">
<title>usb_hcd_poll_rh_status (54 samples, 0.01%)</title><rect x="389.3" y="225" width="0.2" height="15.0" fill="rgb(227,186,27)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('sched_clock_cpu (283 samples, 0.07%)')" onmouseout="c()">
<title>sched_clock_cpu (283 samples, 0.07%)</title><rect x="1022.2" y="209" width="0.8" height="15.0" fill="rgb(232,83,53)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('account_entity_enqueue (119 samples, 0.03%)')" onmouseout="c()">
<title>account_entity_enqueue (119 samples, 0.03%)</title><rect x="1004.0" y="209" width="0.4" height="15.0" fill="rgb(224,51,23)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_rq_clock (429 samples, 0.10%)')" onmouseout="c()">
<title>update_rq_clock (429 samples, 0.10%)</title><rect x="681.7" y="289" width="1.2" height="15.0" fill="rgb(227,206,0)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('child_rip (1,334 samples, 0.31%)')" onmouseout="c()">
<title>child_rip (1,334 samples, 0.31%)</title><rect x="973.4" y="465" width="3.7" height="15.0" fill="rgb(211,13,52)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('enqueue_entity (193 samples, 0.05%)')" onmouseout="c()">
<title>enqueue_entity (193 samples, 0.05%)</title><rect x="1181.7" y="161" width="0.6" height="15.0" fill="rgb(214,151,21)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('raise_softirq (700 samples, 0.16%)')" onmouseout="c()">
<title>raise_softirq (700 samples, 0.16%)</title><rect x="181.3" y="241" width="2.0" height="15.0" fill="rgb(249,163,14)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__rcu_pending (397 samples, 0.09%)')" onmouseout="c()">
<title>__rcu_pending (397 samples, 0.09%)</title><rect x="160.8" y="257" width="1.1" height="15.0" fill="rgb(252,143,42)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vcpu_put (1,682 samples, 0.40%)')" onmouseout="c()">
<title>vcpu_put (1,682 samples, 0.40%)</title><rect x="687.1" y="321" width="4.6" height="15.0" fill="rgb(238,142,29)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vfs_readdir (62 samples, 0.01%)')" onmouseout="c()">
<title>vfs_readdir (62 samples, 0.01%)</title><rect x="970.1" y="417" width="0.1" height="15.0" fill="rgb(245,38,47)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('enqueue_task (254 samples, 0.06%)')" onmouseout="c()">
<title>enqueue_task (254 samples, 0.06%)</title><rect x="1181.7" y="193" width="0.7" height="15.0" fill="rgb(225,196,32)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('atomic_notifier_call_chain (99 samples, 0.02%)')" onmouseout="c()">
<title>atomic_notifier_call_chain (99 samples, 0.02%)</title><rect x="981.8" y="433" width="0.3" height="15.0" fill="rgb(249,80,22)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__wake_up_common (51 samples, 0.01%)')" onmouseout="c()">
<title>__wake_up_common (51 samples, 0.01%)</title><rect x="388.7" y="177" width="0.2" height="15.0" fill="rgb(214,94,27)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rebalance_domains (1,123 samples, 0.26%)')" onmouseout="c()">
<title>rebalance_domains (1,123 samples, 0.26%)</title><rect x="1113.7" y="289" width="3.1" height="15.0" fill="rgb(207,28,22)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('read_tsc (203 samples, 0.05%)')" onmouseout="c()">
<title>read_tsc (203 samples, 0.05%)</title><rect x="820.1" y="209" width="0.5" height="15.0" fill="rgb(233,66,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('read_tsc (708 samples, 0.17%)')" onmouseout="c()">
<title>read_tsc (708 samples, 0.17%)</title><rect x="297.5" y="289" width="1.9" height="15.0" fill="rgb(233,66,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vmcs_writel (120 samples, 0.03%)')" onmouseout="c()">
<title>vmcs_writel (120 samples, 0.03%)</title><rect x="832.5" y="321" width="0.3" height="15.0" fill="rgb(228,19,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ktime_get (1,365 samples, 0.32%)')" onmouseout="c()">
<title>ktime_get (1,365 samples, 0.32%)</title><rect x="146.5" y="273" width="3.8" height="15.0" fill="rgb(213,13,52)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('irq_exit (1,317 samples, 0.31%)')" onmouseout="c()">
<title>irq_exit (1,317 samples, 0.31%)</title><rect x="1113.4" y="369" width="3.6" height="15.0" fill="rgb(216,86,39)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_load_guest_fpu (1,143 samples, 0.27%)')" onmouseout="c()">
<title>kvm_load_guest_fpu (1,143 samples, 0.27%)</title><rect x="900.3" y="353" width="3.2" height="15.0" fill="rgb(222,228,20)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('call_function_single_interrupt (120 samples, 0.03%)')" onmouseout="c()">
<title>call_function_single_interrupt (120 samples, 0.03%)</title><rect x="1046.4" y="417" width="0.3" height="15.0" fill="rgb(233,44,17)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('autoremove_wake_function (383 samples, 0.09%)')" onmouseout="c()">
<title>autoremove_wake_function (383 samples, 0.09%)</title><rect x="1181.5" y="257" width="1.1" height="15.0" fill="rgb(226,61,12)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_apic_mem_write (170 samples, 0.04%)')" onmouseout="c()">
<title>native_apic_mem_write (170 samples, 0.04%)</title><rect x="988.4" y="401" width="0.4" height="15.0" fill="rgb(209,177,4)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('account_system_time (1,750 samples, 0.41%)')" onmouseout="c()">
<title>account_system_time (1,750 samples, 0.41%)</title><rect x="164.0" y="241" width="4.9" height="15.0" fill="rgb(248,96,10)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('sys_ioctl (331,273 samples, 77.92%)')" onmouseout="c()">
<title>sys_ioctl (331,273 samples, 77.92%)</title><rect x="15.6" y="417" width="919.4" height="15.0" fill="rgb(225,38,48)" rx="2" ry="2" />
<text text-anchor="" x="18.5619923933982" y="427.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >sys_ioctl</text>
</g>
<g class="func_g" onmouseover="s('kvm_put_guest_fpu (115 samples, 0.03%)')" onmouseout="c()">
<title>kvm_put_guest_fpu (115 samples, 0.03%)</title><rect x="690.8" y="305" width="0.3" height="15.0" fill="rgb(214,10,51)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vmx_inject_irq (758 samples, 0.18%)')" onmouseout="c()">
<title>vmx_inject_irq (758 samples, 0.18%)</title><rect x="830.7" y="337" width="2.1" height="15.0" fill="rgb(206,62,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_sched_clock (88 samples, 0.02%)')" onmouseout="c()">
<title>native_sched_clock (88 samples, 0.02%)</title><rect x="641.0" y="225" width="0.2" height="15.0" fill="rgb(233,205,14)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vmx_vcpu_run (11,418 samples, 2.69%)')" onmouseout="c()">
<title>vmx_vcpu_run (11,418 samples, 2.69%)</title><rect x="935.3" y="433" width="31.7" height="15.0" fill="rgb(235,49,53)" rx="2" ry="2" />
<text text-anchor="" x="938.291833369791" y="443.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >vm..</text>
</g>
<g class="func_g" onmouseover="s('kcs_event (567 samples, 0.13%)')" onmouseout="c()">
<title>kcs_event (567 samples, 0.13%)</title><rect x="974.0" y="401" width="1.6" height="15.0" fill="rgb(227,94,14)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('sched_clock_tick (62 samples, 0.01%)')" onmouseout="c()">
<title>sched_clock_tick (62 samples, 0.01%)</title><rect x="1042.0" y="321" width="0.2" height="15.0" fill="rgb(221,107,31)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_sched_clock (68 samples, 0.02%)')" onmouseout="c()">
<title>native_sched_clock (68 samples, 0.02%)</title><rect x="1177.8" y="369" width="0.2" height="15.0" fill="rgb(233,205,14)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vmx_interrupt_allowed (263 samples, 0.06%)')" onmouseout="c()">
<title>vmx_interrupt_allowed (263 samples, 0.06%)</title><rect x="602.4" y="305" width="0.8" height="15.0" fill="rgb(206,40,41)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ktime_get (74 samples, 0.02%)')" onmouseout="c()">
<title>ktime_get (74 samples, 0.02%)</title><rect x="1120.6" y="433" width="0.2" height="15.0" fill="rgb(213,13,52)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ntp_tick_length (360 samples, 0.08%)')" onmouseout="c()">
<title>ntp_tick_length (360 samples, 0.08%)</title><rect x="158.4" y="241" width="1.0" height="15.0" fill="rgb(212,167,37)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__remove_hrtimer (70 samples, 0.02%)')" onmouseout="c()">
<title>__remove_hrtimer (70 samples, 0.02%)</title><rect x="1188.3" y="337" width="0.2" height="15.0" fill="rgb(244,9,30)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('sem_post (49 samples, 0.01%)')" onmouseout="c()">
<title>sem_post (49 samples, 0.01%)</title><rect x="979.9" y="465" width="0.2" height="15.0" fill="rgb(221,97,22)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('bnx2_poll_msix (58 samples, 0.01%)')" onmouseout="c()">
<title>bnx2_poll_msix (58 samples, 0.01%)</title><rect x="909.4" y="241" width="0.1" height="15.0" fill="rgb(241,170,15)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_lock (44 samples, 0.01%)')" onmouseout="c()">
<title>_spin_lock (44 samples, 0.01%)</title><rect x="159.9" y="209" width="0.1" height="15.0" fill="rgb(207,37,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_unlock_irqrestore (116 samples, 0.03%)')" onmouseout="c()">
<title>_spin_unlock_irqrestore (116 samples, 0.03%)</title><rect x="1152.0" y="385" width="0.3" height="15.0" fill="rgb(250,72,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_unlock_irqrestore (62 samples, 0.01%)')" onmouseout="c()">
<title>_spin_unlock_irqrestore (62 samples, 0.01%)</title><rect x="619.6" y="305" width="0.2" height="15.0" fill="rgb(250,72,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('intel_idle (21,401 samples, 5.03%)')" onmouseout="c()">
<title>intel_idle (21,401 samples, 5.03%)</title><rect x="1046.8" y="417" width="59.4" height="15.0" fill="rgb(216,81,47)" rx="2" ry="2" />
<text text-anchor="" x="1049.7814713153" y="427.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >intel_..</text>
</g>
<g class="func_g" onmouseover="s('apic_update_ppr (194 samples, 0.05%)')" onmouseout="c()">
<title>apic_update_ppr (194 samples, 0.05%)</title><rect x="557.9" y="305" width="0.5" height="15.0" fill="rgb(244,88,21)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_read_tsc (130 samples, 0.03%)')" onmouseout="c()">
<title>native_read_tsc (130 samples, 0.03%)</title><rect x="820.3" y="193" width="0.3" height="15.0" fill="rgb(249,99,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_write_msr_safe (267 samples, 0.06%)')" onmouseout="c()">
<title>native_write_msr_safe (267 samples, 0.06%)</title><rect x="838.0" y="321" width="0.7" height="15.0" fill="rgb(215,163,22)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('sys_munmap (59 samples, 0.01%)')" onmouseout="c()">
<title>sys_munmap (59 samples, 0.01%)</title><rect x="969.8" y="433" width="0.2" height="15.0" fill="rgb(229,42,29)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('perf_adjust_freq_unthr_context (113 samples, 0.03%)')" onmouseout="c()">
<title>perf_adjust_freq_unthr_context (113 samples, 0.03%)</title><rect x="202.2" y="241" width="0.3" height="15.0" fill="rgb(241,147,53)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ktime_get_real (725 samples, 0.17%)')" onmouseout="c()">
<title>ktime_get_real (725 samples, 0.17%)</title><rect x="1106.2" y="417" width="2.0" height="15.0" fill="rgb(235,131,23)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_cond_resched (70 samples, 0.02%)')" onmouseout="c()">
<title>_cond_resched (70 samples, 0.02%)</title><rect x="593.5" y="321" width="0.2" height="15.0" fill="rgb(243,183,12)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_unlock_irqrestore (408 samples, 0.10%)')" onmouseout="c()">
<title>_spin_unlock_irqrestore (408 samples, 0.10%)</title><rect x="1154.0" y="401" width="1.1" height="15.0" fill="rgb(250,72,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('select_nohz_load_balancer (199 samples, 0.05%)')" onmouseout="c()">
<title>select_nohz_load_balancer (199 samples, 0.05%)</title><rect x="1178.0" y="417" width="0.5" height="15.0" fill="rgb(224,12,41)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('cpuidle_idle_call (49,086 samples, 11.55%)')" onmouseout="c()">
<title>cpuidle_idle_call (49,086 samples, 11.55%)</title><rect x="982.1" y="433" width="136.2" height="15.0" fill="rgb(214,193,0)" rx="2" ry="2" />
<text text-anchor="" x="985.077514894498" y="443.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >cpuidle_idle_call</text>
</g>
<g class="func_g" onmouseover="s('intel_idle (1,183 samples, 0.28%)')" onmouseout="c()">
<title>intel_idle (1,183 samples, 0.28%)</title><rect x="1183.4" y="369" width="3.3" height="15.0" fill="rgb(216,81,47)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('activate_task (42 samples, 0.01%)')" onmouseout="c()">
<title>activate_task (42 samples, 0.01%)</title><rect x="999.9" y="273" width="0.1" height="15.0" fill="rgb(233,36,1)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_lock_irq (62 samples, 0.01%)')" onmouseout="c()">
<title>_spin_lock_irq (62 samples, 0.01%)</title><rect x="1113.5" y="289" width="0.1" height="15.0" fill="rgb(221,92,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__fput (37 samples, 0.01%)')" onmouseout="c()">
<title>__fput (37 samples, 0.01%)</title><rect x="968.0" y="385" width="0.1" height="15.0" fill="rgb(230,68,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('perf_event_task_sched_out (234 samples, 0.06%)')" onmouseout="c()">
<title>perf_event_task_sched_out (234 samples, 0.06%)</title><rect x="1127.0" y="417" width="0.7" height="15.0" fill="rgb(215,151,2)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('hrtick_start_fair (51 samples, 0.01%)')" onmouseout="c()">
<title>hrtick_start_fair (51 samples, 0.01%)</title><rect x="638.8" y="257" width="0.1" height="15.0" fill="rgb(209,42,50)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ktime_get_real (796 samples, 0.19%)')" onmouseout="c()">
<title>ktime_get_real (796 samples, 0.19%)</title><rect x="1103.5" y="401" width="2.2" height="15.0" fill="rgb(235,131,23)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('start_thread (124 samples, 0.03%)')" onmouseout="c()">
<title>start_thread (124 samples, 0.03%)</title><rect x="1180.0" y="465" width="0.3" height="15.0" fill="rgb(227,62,53)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_cpu_load (190 samples, 0.04%)')" onmouseout="c()">
<title>update_cpu_load (190 samples, 0.04%)</title><rect x="288.9" y="257" width="0.5" height="15.0" fill="rgb(210,180,27)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('task_of (84 samples, 0.02%)')" onmouseout="c()">
<title>task_of (84 samples, 0.02%)</title><rect x="1020.5" y="209" width="0.2" height="15.0" fill="rgb(226,191,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('hrtimer_force_reprogram (712 samples, 0.17%)')" onmouseout="c()">
<title>hrtimer_force_reprogram (712 samples, 0.17%)</title><rect x="1165.5" y="369" width="2.0" height="15.0" fill="rgb(237,60,9)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('seq_read (303 samples, 0.07%)')" onmouseout="c()">
<title>seq_read (303 samples, 0.07%)</title><rect x="968.9" y="401" width="0.8" height="15.0" fill="rgb(237,158,19)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_load_sp0 (76 samples, 0.02%)')" onmouseout="c()">
<title>native_load_sp0 (76 samples, 0.02%)</title><rect x="12.9" y="433" width="0.2" height="15.0" fill="rgb(243,63,32)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('system_call_fastpath (37 samples, 0.01%)')" onmouseout="c()">
<title>system_call_fastpath (37 samples, 0.01%)</title><rect x="967.2" y="433" width="0.1" height="15.0" fill="rgb(224,162,39)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_cfs_shares (2,863 samples, 0.67%)')" onmouseout="c()">
<title>update_cfs_shares (2,863 samples, 0.67%)</title><rect x="626.6" y="241" width="8.0" height="15.0" fill="rgb(240,28,1)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__libc_start_main (293 samples, 0.07%)')" onmouseout="c()">
<title>__libc_start_main (293 samples, 0.07%)</title><rect x="970.2" y="465" width="0.8" height="15.0" fill="rgb(249,32,16)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('autoremove_wake_function (1,421 samples, 0.33%)')" onmouseout="c()">
<title>autoremove_wake_function (1,421 samples, 0.33%)</title><rect x="791.2" y="145" width="4.0" height="15.0" fill="rgb(226,61,12)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__write_nocancel (239 samples, 0.06%)')" onmouseout="c()">
<title>__write_nocancel (239 samples, 0.06%)</title><rect x="970.3" y="417" width="0.7" height="15.0" fill="rgb(231,158,48)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rb_insert_color (106 samples, 0.02%)')" onmouseout="c()">
<title>rb_insert_color (106 samples, 0.02%)</title><rect x="1152.5" y="369" width="0.3" height="15.0" fill="rgb(222,158,21)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_sched_clock (456 samples, 0.11%)')" onmouseout="c()">
<title>native_sched_clock (456 samples, 0.11%)</title><rect x="286.4" y="193" width="1.3" height="15.0" fill="rgb(233,205,14)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('cpuidle_idle_call (2,382 samples, 0.56%)')" onmouseout="c()">
<title>cpuidle_idle_call (2,382 samples, 0.56%)</title><rect x="1180.9" y="385" width="6.6" height="15.0" fill="rgb(214,193,0)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('cpumask_next_and (337 samples, 0.08%)')" onmouseout="c()">
<title>cpumask_next_and (337 samples, 0.08%)</title><rect x="619.9" y="305" width="0.9" height="15.0" fill="rgb(231,93,24)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('hrtick_start_fair (75 samples, 0.02%)')" onmouseout="c()">
<title>hrtick_start_fair (75 samples, 0.02%)</title><rect x="1126.8" y="417" width="0.2" height="15.0" fill="rgb(209,42,50)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_smp_send_reschedule (201 samples, 0.05%)')" onmouseout="c()">
<title>native_smp_send_reschedule (201 samples, 0.05%)</title><rect x="201.7" y="241" width="0.5" height="15.0" fill="rgb(218,74,11)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('mark_page_dirty_in_slot (210 samples, 0.05%)')" onmouseout="c()">
<title>mark_page_dirty_in_slot (210 samples, 0.05%)</title><rect x="694.8" y="305" width="0.6" height="15.0" fill="rgb(209,110,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rcu_process_callbacks (9,524 samples, 2.24%)')" onmouseout="c()">
<title>rcu_process_callbacks (9,524 samples, 2.24%)</title><rect x="353.8" y="257" width="26.5" height="15.0" fill="rgb(209,24,23)" rx="2" ry="2" />
<text text-anchor="" x="356.824939963355" y="267.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >r..</text>
</g>
<g class="func_g" onmouseover="s('rcu_sched_qs (273 samples, 0.06%)')" onmouseout="c()">
<title>rcu_sched_qs (273 samples, 0.06%)</title><rect x="709.2" y="337" width="0.8" height="15.0" fill="rgb(239,116,8)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_cfs_load (133 samples, 0.03%)')" onmouseout="c()">
<title>update_cfs_load (133 samples, 0.03%)</title><rect x="277.7" y="241" width="0.4" height="15.0" fill="rgb(242,121,52)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('proc_pid_readdir (48 samples, 0.01%)')" onmouseout="c()">
<title>proc_pid_readdir (48 samples, 0.01%)</title><rect x="970.1" y="385" width="0.1" height="15.0" fill="rgb(236,108,1)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_sched_clock (211 samples, 0.05%)')" onmouseout="c()">
<title>native_sched_clock (211 samples, 0.05%)</title><rect x="682.3" y="241" width="0.6" height="15.0" fill="rgb(233,205,14)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('lock_hrtimer_base (385 samples, 0.09%)')" onmouseout="c()">
<title>lock_hrtimer_base (385 samples, 0.09%)</title><rect x="785.3" y="225" width="1.1" height="15.0" fill="rgb(232,201,8)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('sched_clock_tick (68 samples, 0.02%)')" onmouseout="c()">
<title>sched_clock_tick (68 samples, 0.02%)</title><rect x="1042.2" y="337" width="0.2" height="15.0" fill="rgb(221,107,31)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('printk_needs_cpu (71 samples, 0.02%)')" onmouseout="c()">
<title>printk_needs_cpu (71 samples, 0.02%)</title><rect x="1121.6" y="433" width="0.2" height="15.0" fill="rgb(225,165,39)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_write_msr_safe (418 samples, 0.10%)')" onmouseout="c()">
<title>native_write_msr_safe (418 samples, 0.10%)</title><rect x="217.0" y="177" width="1.2" height="15.0" fill="rgb(215,163,22)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('msecs_to_jiffies (69 samples, 0.02%)')" onmouseout="c()">
<title>msecs_to_jiffies (69 samples, 0.02%)</title><rect x="604.8" y="321" width="0.2" height="15.0" fill="rgb(231,49,11)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('port_inb (556 samples, 0.13%)')" onmouseout="c()">
<title>port_inb (556 samples, 0.13%)</title><rect x="974.0" y="385" width="1.6" height="15.0" fill="rgb(251,85,17)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('default_wake_function (64 samples, 0.02%)')" onmouseout="c()">
<title>default_wake_function (64 samples, 0.02%)</title><rect x="1029.1" y="305" width="0.2" height="15.0" fill="rgb(208,124,20)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('finish_wait (279 samples, 0.07%)')" onmouseout="c()">
<title>finish_wait (279 samples, 0.07%)</title><rect x="597.0" y="321" width="0.8" height="15.0" fill="rgb(215,130,24)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_cfs_shares (2,182 samples, 0.51%)')" onmouseout="c()">
<title>update_cfs_shares (2,182 samples, 0.51%)</title><rect x="1011.4" y="193" width="6.0" height="15.0" fill="rgb(240,28,1)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tick_nohz_stop_idle (78 samples, 0.02%)')" onmouseout="c()">
<title>tick_nohz_stop_idle (78 samples, 0.02%)</title><rect x="1043.9" y="369" width="0.2" height="15.0" fill="rgb(210,186,7)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('perf_pmu_enable (96 samples, 0.02%)')" onmouseout="c()">
<title>perf_pmu_enable (96 samples, 0.02%)</title><rect x="1046.4" y="305" width="0.3" height="15.0" fill="rgb(238,51,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__dequeue_entity (100 samples, 0.02%)')" onmouseout="c()">
<title>__dequeue_entity (100 samples, 0.02%)</title><rect x="1129.1" y="401" width="0.3" height="15.0" fill="rgb(254,73,10)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_apic_accept_pic_intr (48 samples, 0.01%)')" onmouseout="c()">
<title>kvm_apic_accept_pic_intr (48 samples, 0.01%)</title><rect x="599.2" y="305" width="0.1" height="15.0" fill="rgb(249,176,35)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('sys_read (412 samples, 0.10%)')" onmouseout="c()">
<title>sys_read (412 samples, 0.10%)</title><rect x="968.6" y="433" width="1.1" height="15.0" fill="rgb(223,64,7)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_lock (161 samples, 0.04%)')" onmouseout="c()">
<title>_spin_lock (161 samples, 0.04%)</title><rect x="1032.0" y="369" width="0.4" height="15.0" fill="rgb(207,37,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ktime_get (84 samples, 0.02%)')" onmouseout="c()">
<title>ktime_get (84 samples, 0.02%)</title><rect x="1155.5" y="417" width="0.2" height="15.0" fill="rgb(213,13,52)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('apic_update_ppr (70 samples, 0.02%)')" onmouseout="c()">
<title>apic_update_ppr (70 samples, 0.02%)</title><rect x="495.7" y="321" width="0.2" height="15.0" fill="rgb(244,88,21)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tick_check_oneshot_broadcast (86 samples, 0.02%)')" onmouseout="c()">
<title>tick_check_oneshot_broadcast (86 samples, 0.02%)</title><rect x="1039.7" y="353" width="0.3" height="15.0" fill="rgb(232,124,28)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('pick_next_task_idle (81 samples, 0.02%)')" onmouseout="c()">
<title>pick_next_task_idle (81 samples, 0.02%)</title><rect x="606.1" y="321" width="0.2" height="15.0" fill="rgb(240,58,24)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_write_guest_page (96 samples, 0.02%)')" onmouseout="c()">
<title>kvm_write_guest_page (96 samples, 0.02%)</title><rect x="698.6" y="337" width="0.3" height="15.0" fill="rgb(245,123,44)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rebalance_domains (56 samples, 0.01%)')" onmouseout="c()">
<title>rebalance_domains (56 samples, 0.01%)</title><rect x="1187.0" y="241" width="0.1" height="15.0" fill="rgb(207,28,22)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('read_tsc (161 samples, 0.04%)')" onmouseout="c()">
<title>read_tsc (161 samples, 0.04%)</title><rect x="820.6" y="225" width="0.5" height="15.0" fill="rgb(233,66,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('hrtimer_cancel (103 samples, 0.02%)')" onmouseout="c()">
<title>hrtimer_cancel (103 samples, 0.02%)</title><rect x="1188.3" y="369" width="0.3" height="15.0" fill="rgb(211,94,12)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('notifier_call_chain (49 samples, 0.01%)')" onmouseout="c()">
<title>notifier_call_chain (49 samples, 0.01%)</title><rect x="992.0" y="369" width="0.1" height="15.0" fill="rgb(240,117,28)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('system_call_fastpath (331,273 samples, 77.92%)')" onmouseout="c()">
<title>system_call_fastpath (331,273 samples, 77.92%)</title><rect x="15.6" y="433" width="919.4" height="15.0" fill="rgb(224,162,39)" rx="2" ry="2" />
<text text-anchor="" x="18.5619923933982" y="443.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >system_call_fastpath</text>
</g>
<g class="func_g" onmouseover="s('ktime_get (318 samples, 0.07%)')" onmouseout="c()">
<title>ktime_get (318 samples, 0.07%)</title><rect x="131.5" y="289" width="0.9" height="15.0" fill="rgb(213,13,52)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_lock_irqsave (64 samples, 0.02%)')" onmouseout="c()">
<title>_spin_lock_irqsave (64 samples, 0.02%)</title><rect x="595.8" y="321" width="0.2" height="15.0" fill="rgb(209,137,45)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('start_secondary (71,954 samples, 16.92%)')" onmouseout="c()">
<title>start_secondary (71,954 samples, 16.92%)</title><rect x="980.3" y="465" width="199.7" height="15.0" fill="rgb(214,25,2)" rx="2" ry="2" />
<text text-anchor="" x="983.290128117378" y="475.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >start_secondary</text>
</g>
<g class="func_g" onmouseover="s('_spin_lock_irqsave (51 samples, 0.01%)')" onmouseout="c()">
<title>_spin_lock_irqsave (51 samples, 0.01%)</title><rect x="1145.2" y="369" width="0.2" height="15.0" fill="rgb(209,137,45)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('read_tsc (118 samples, 0.03%)')" onmouseout="c()">
<title>read_tsc (118 samples, 0.03%)</title><rect x="1105.1" y="369" width="0.3" height="15.0" fill="rgb(233,66,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ktime_get_update_offsets (140 samples, 0.03%)')" onmouseout="c()">
<title>ktime_get_update_offsets (140 samples, 0.03%)</title><rect x="1032.4" y="369" width="0.4" height="15.0" fill="rgb(246,219,37)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tick_dev_program_event (150 samples, 0.04%)')" onmouseout="c()">
<title>tick_dev_program_event (150 samples, 0.04%)</title><rect x="1142.8" y="337" width="0.4" height="15.0" fill="rgb(218,118,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ksoftirqd (44 samples, 0.01%)')" onmouseout="c()">
<title>ksoftirqd (44 samples, 0.01%)</title><rect x="975.7" y="433" width="0.1" height="15.0" fill="rgb(239,229,54)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('bnx2_poll_work (55 samples, 0.01%)')" onmouseout="c()">
<title>bnx2_poll_work (55 samples, 0.01%)</title><rect x="909.4" y="225" width="0.1" height="15.0" fill="rgb(253,180,45)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_vcpu_on_spin (472 samples, 0.11%)')" onmouseout="c()">
<title>kvm_vcpu_on_spin (472 samples, 0.11%)</title><rect x="755.2" y="305" width="1.3" height="15.0" fill="rgb(211,105,41)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('sched_clock_cpu (1,293 samples, 0.30%)')" onmouseout="c()">
<title>sched_clock_cpu (1,293 samples, 0.30%)</title><rect x="284.1" y="225" width="3.6" height="15.0" fill="rgb(232,83,53)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_apic_has_interrupt (609 samples, 0.14%)')" onmouseout="c()">
<title>kvm_apic_has_interrupt (609 samples, 0.14%)</title><rect x="493.0" y="337" width="1.7" height="15.0" fill="rgb(217,29,8)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('smp_apic_timer_interrupt (325 samples, 0.08%)')" onmouseout="c()">
<title>smp_apic_timer_interrupt (325 samples, 0.08%)</title><rect x="912.6" y="353" width="0.9" height="15.0" fill="rgb(205,22,48)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('perf_pmu_disable (2,703 samples, 0.64%)')" onmouseout="c()">
<title>perf_pmu_disable (2,703 samples, 0.64%)</title><rect x="210.7" y="209" width="7.5" height="15.0" fill="rgb(218,159,27)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('idle_cpu (90 samples, 0.02%)')" onmouseout="c()">
<title>idle_cpu (90 samples, 0.02%)</title><rect x="812.5" y="193" width="0.2" height="15.0" fill="rgb(231,163,39)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__link_path_walk (62 samples, 0.01%)')" onmouseout="c()">
<title>__link_path_walk (62 samples, 0.01%)</title><rect x="968.2" y="353" width="0.2" height="15.0" fill="rgb(241,49,13)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_emulate_halt (59 samples, 0.01%)')" onmouseout="c()">
<title>kvm_emulate_halt (59 samples, 0.01%)</title><rect x="824.7" y="321" width="0.2" height="15.0" fill="rgb(242,123,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_curr (160 samples, 0.04%)')" onmouseout="c()">
<title>update_curr (160 samples, 0.04%)</title><rect x="282.3" y="241" width="0.4" height="15.0" fill="rgb(218,93,52)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('proc_tgid_stat (161 samples, 0.04%)')" onmouseout="c()">
<title>proc_tgid_stat (161 samples, 0.04%)</title><rect x="969.2" y="369" width="0.5" height="15.0" fill="rgb(236,217,13)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('do_lookup (48 samples, 0.01%)')" onmouseout="c()">
<title>do_lookup (48 samples, 0.01%)</title><rect x="968.2" y="337" width="0.2" height="15.0" fill="rgb(230,63,4)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('enqueue_task_fair (231 samples, 0.05%)')" onmouseout="c()">
<title>enqueue_task_fair (231 samples, 0.05%)</title><rect x="1181.7" y="177" width="0.6" height="15.0" fill="rgb(251,134,19)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__netif_receive_skb (80 samples, 0.02%)')" onmouseout="c()">
<title>__netif_receive_skb (80 samples, 0.02%)</title><rect x="909.1" y="161" width="0.3" height="15.0" fill="rgb(207,148,11)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('getnstimeofday (193 samples, 0.05%)')" onmouseout="c()">
<title>getnstimeofday (193 samples, 0.05%)</title><rect x="1103.0" y="401" width="0.5" height="15.0" fill="rgb(214,199,10)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('double_rq_lock (127 samples, 0.03%)')" onmouseout="c()">
<title>double_rq_lock (127 samples, 0.03%)</title><rect x="755.9" y="273" width="0.4" height="15.0" fill="rgb(227,1,52)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('irq_enter (60 samples, 0.01%)')" onmouseout="c()">
<title>irq_enter (60 samples, 0.01%)</title><rect x="987.1" y="401" width="0.1" height="15.0" fill="rgb(247,79,15)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('raise_softirq (418 samples, 0.10%)')" onmouseout="c()">
<title>raise_softirq (418 samples, 0.10%)</title><rect x="173.5" y="257" width="1.1" height="15.0" fill="rgb(249,163,14)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('apic_timer_interrupt (143,644 samples, 33.79%)')" onmouseout="c()">
<title>apic_timer_interrupt (143,644 samples, 33.79%)</title><rect x="15.6" y="353" width="398.6" height="15.0" fill="rgb(239,200,41)" rx="2" ry="2" />
<text text-anchor="" x="18.5619923933982" y="363.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >apic_timer_interrupt</text>
</g>
<g class="func_g" onmouseover="s('tick_program_event (67 samples, 0.02%)')" onmouseout="c()">
<title>tick_program_event (67 samples, 0.02%)</title><rect x="815.2" y="193" width="0.2" height="15.0" fill="rgb(219,99,4)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('perf_event_task_tick (101 samples, 0.02%)')" onmouseout="c()">
<title>perf_event_task_tick (101 samples, 0.02%)</title><rect x="172.0" y="257" width="0.2" height="15.0" fill="rgb(224,223,5)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('calc_delta_mine (61 samples, 0.01%)')" onmouseout="c()">
<title>calc_delta_mine (61 samples, 0.01%)</title><rect x="632.5" y="225" width="0.1" height="15.0" fill="rgb(235,150,13)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_lock_irqsave (198 samples, 0.05%)')" onmouseout="c()">
<title>_spin_lock_irqsave (198 samples, 0.05%)</title><rect x="677.6" y="289" width="0.5" height="15.0" fill="rgb(209,137,45)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vfs_write (235 samples, 0.06%)')" onmouseout="c()">
<title>vfs_write (235 samples, 0.06%)</title><rect x="970.3" y="369" width="0.7" height="15.0" fill="rgb(241,195,49)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_read_guest_cached (1,179 samples, 0.28%)')" onmouseout="c()">
<title>kvm_read_guest_cached (1,179 samples, 0.28%)</title><rect x="563.0" y="321" width="3.3" height="15.0" fill="rgb(224,155,18)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_IO_vfscanf (77 samples, 0.02%)')" onmouseout="c()">
<title>_IO_vfscanf (77 samples, 0.02%)</title><rect x="967.7" y="449" width="0.2" height="15.0" fill="rgb(209,15,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tick_nohz_restart_sched_tick (313 samples, 0.07%)')" onmouseout="c()">
<title>tick_nohz_restart_sched_tick (313 samples, 0.07%)</title><rect x="1188.3" y="385" width="0.8" height="15.0" fill="rgb(210,23,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('task_of (170 samples, 0.04%)')" onmouseout="c()">
<title>task_of (170 samples, 0.04%)</title><rect x="232.5" y="225" width="0.5" height="15.0" fill="rgb(226,191,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('net_rx_action (219 samples, 0.05%)')" onmouseout="c()">
<title>net_rx_action (219 samples, 0.05%)</title><rect x="909.0" y="257" width="0.6" height="15.0" fill="rgb(234,84,46)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('deactivate_task (7,511 samples, 1.77%)')" onmouseout="c()">
<title>deactivate_task (7,511 samples, 1.77%)</title><rect x="620.8" y="305" width="20.9" height="15.0" fill="rgb(245,205,36)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('read_tsc (110 samples, 0.03%)')" onmouseout="c()">
<title>read_tsc (110 samples, 0.03%)</title><rect x="1105.4" y="385" width="0.3" height="15.0" fill="rgb(233,66,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_cfs_shares (74 samples, 0.02%)')" onmouseout="c()">
<title>update_cfs_shares (74 samples, 0.02%)</title><rect x="792.4" y="33" width="0.2" height="15.0" fill="rgb(240,28,1)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('intel_pmu_nhm_enable_all (95 samples, 0.02%)')" onmouseout="c()">
<title>intel_pmu_nhm_enable_all (95 samples, 0.02%)</title><rect x="1046.4" y="273" width="0.3" height="15.0" fill="rgb(208,11,28)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('get_next_timer_interrupt (75 samples, 0.02%)')" onmouseout="c()">
<title>get_next_timer_interrupt (75 samples, 0.02%)</title><rect x="1119.3" y="433" width="0.2" height="15.0" fill="rgb(245,174,47)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('sched_clock (85 samples, 0.02%)')" onmouseout="c()">
<title>sched_clock (85 samples, 0.02%)</title><rect x="1021.9" y="209" width="0.3" height="15.0" fill="rgb(207,104,49)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('pit_has_pending_timer (109 samples, 0.03%)')" onmouseout="c()">
<title>pit_has_pending_timer (109 samples, 0.03%)</title><rect x="603.9" y="305" width="0.3" height="15.0" fill="rgb(253,0,31)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('srcu_read_unlock (240 samples, 0.06%)')" onmouseout="c()">
<title>srcu_read_unlock (240 samples, 0.06%)</title><rect x="913.7" y="353" width="0.7" height="15.0" fill="rgb(241,146,12)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('run_rebalance_domains (1,269 samples, 0.30%)')" onmouseout="c()">
<title>run_rebalance_domains (1,269 samples, 0.30%)</title><rect x="1113.4" y="305" width="3.5" height="15.0" fill="rgb(234,111,10)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('smp_apic_timer_interrupt (46 samples, 0.01%)')" onmouseout="c()">
<title>smp_apic_timer_interrupt (46 samples, 0.01%)</title><rect x="12.8" y="417" width="0.1" height="15.0" fill="rgb(205,22,48)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tick_nohz_stop_sched_tick (284 samples, 0.07%)')" onmouseout="c()">
<title>tick_nohz_stop_sched_tick (284 samples, 0.07%)</title><rect x="1189.1" y="385" width="0.8" height="15.0" fill="rgb(217,57,10)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rcu_irq_exit (1,151 samples, 0.27%)')" onmouseout="c()">
<title>rcu_irq_exit (1,151 samples, 0.27%)</title><rect x="394.1" y="305" width="3.2" height="15.0" fill="rgb(253,130,37)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('run_posix_cpu_timers (129 samples, 0.03%)')" onmouseout="c()">
<title>run_posix_cpu_timers (129 samples, 0.03%)</title><rect x="154.0" y="273" width="0.4" height="15.0" fill="rgb(243,97,44)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tick_dev_program_event (671 samples, 0.16%)')" onmouseout="c()">
<title>tick_dev_program_event (671 samples, 0.16%)</title><rect x="303.7" y="305" width="1.9" height="15.0" fill="rgb(218,118,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('copy_from_user (66 samples, 0.02%)')" onmouseout="c()">
<title>copy_from_user (66 samples, 0.02%)</title><rect x="587.5" y="321" width="0.2" height="15.0" fill="rgb(237,136,5)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tick_dev_program_event (718 samples, 0.17%)')" onmouseout="c()">
<title>tick_dev_program_event (718 samples, 0.17%)</title><rect x="1034.1" y="353" width="2.0" height="15.0" fill="rgb(218,118,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_lock_irqsave (102 samples, 0.02%)')" onmouseout="c()">
<title>_spin_lock_irqsave (102 samples, 0.02%)</title><rect x="1041.4" y="305" width="0.2" height="15.0" fill="rgb(209,137,45)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rcu_process_gp_end (3,379 samples, 0.79%)')" onmouseout="c()">
<title>rcu_process_gp_end (3,379 samples, 0.79%)</title><rect x="367.5" y="225" width="9.4" height="15.0" fill="rgb(245,96,36)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ext4_file_write (233 samples, 0.05%)')" onmouseout="c()">
<title>ext4_file_write (233 samples, 0.05%)</title><rect x="970.3" y="337" width="0.7" height="15.0" fill="rgb(205,10,21)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_vcpu_kick (418 samples, 0.10%)')" onmouseout="c()">
<title>kvm_vcpu_kick (418 samples, 0.10%)</title><rect x="540.3" y="273" width="1.2" height="15.0" fill="rgb(217,38,48)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_lock (51 samples, 0.01%)')" onmouseout="c()">
<title>_spin_lock (51 samples, 0.01%)</title><rect x="207.5" y="225" width="0.2" height="15.0" fill="rgb(207,37,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('find_first_bit (92 samples, 0.02%)')" onmouseout="c()">
<title>find_first_bit (92 samples, 0.02%)</title><rect x="672.7" y="305" width="0.3" height="15.0" fill="rgb(218,191,53)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('hrtimer_start (4,431 samples, 1.04%)')" onmouseout="c()">
<title>hrtimer_start (4,431 samples, 1.04%)</title><rect x="1163.8" y="417" width="12.3" height="15.0" fill="rgb(252,146,16)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('menu_select (58 samples, 0.01%)')" onmouseout="c()">
<title>menu_select (58 samples, 0.01%)</title><rect x="1186.7" y="369" width="0.2" height="15.0" fill="rgb(249,45,6)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tick_check_oneshot_broadcast (52 samples, 0.01%)')" onmouseout="c()">
<title>tick_check_oneshot_broadcast (52 samples, 0.01%)</title><rect x="1043.7" y="369" width="0.2" height="15.0" fill="rgb(232,124,28)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__rb_rotate_right (248 samples, 0.06%)')" onmouseout="c()">
<title>__rb_rotate_right (248 samples, 0.06%)</title><rect x="127.2" y="273" width="0.6" height="15.0" fill="rgb(206,117,53)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vmx_vcpu_put (79 samples, 0.02%)')" onmouseout="c()">
<title>vmx_vcpu_put (79 samples, 0.02%)</title><rect x="691.5" y="305" width="0.2" height="15.0" fill="rgb(225,213,42)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('hrtimer_interrupt (97,151 samples, 22.85%)')" onmouseout="c()">
<title>hrtimer_interrupt (97,151 samples, 22.85%)</title><rect x="57.6" y="321" width="269.6" height="15.0" fill="rgb(252,20,51)" rx="2" ry="2" />
<text text-anchor="" x="60.5961115540847" y="331.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >hrtimer_interrupt</text>
</g>
<g class="func_g" onmouseover="s('tick_sched_timer (57 samples, 0.01%)')" onmouseout="c()">
<title>tick_sched_timer (57 samples, 0.01%)</title><rect x="1136.3" y="353" width="0.2" height="15.0" fill="rgb(213,15,28)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_lock_irqsave (105 samples, 0.02%)')" onmouseout="c()">
<title>_spin_lock_irqsave (105 samples, 0.02%)</title><rect x="597.2" y="305" width="0.3" height="15.0" fill="rgb(209,137,45)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('run_posix_cpu_timers (820 samples, 0.19%)')" onmouseout="c()">
<title>run_posix_cpu_timers (820 samples, 0.19%)</title><rect x="183.3" y="257" width="2.2" height="15.0" fill="rgb(243,97,44)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('read_tsc (128 samples, 0.03%)')" onmouseout="c()">
<title>read_tsc (128 samples, 0.03%)</title><rect x="150.0" y="257" width="0.3" height="15.0" fill="rgb(233,66,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_unlock_irqrestore (38 samples, 0.01%)')" onmouseout="c()">
<title>_spin_unlock_irqrestore (38 samples, 0.01%)</title><rect x="1002.4" y="257" width="0.1" height="15.0" fill="rgb(250,72,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('copy_to_user (112 samples, 0.03%)')" onmouseout="c()">
<title>copy_to_user (112 samples, 0.03%)</title><rect x="692.7" y="321" width="0.3" height="15.0" fill="rgb(206,70,41)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('apic_update_ppr (1,025 samples, 0.24%)')" onmouseout="c()">
<title>apic_update_ppr (1,025 samples, 0.24%)</title><rect x="516.8" y="305" width="2.9" height="15.0" fill="rgb(244,88,21)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('find_next_bit (801 samples, 0.19%)')" onmouseout="c()">
<title>find_next_bit (801 samples, 0.19%)</title><rect x="666.5" y="273" width="2.2" height="15.0" fill="rgb(231,134,31)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('pm_qos_requirement (54 samples, 0.01%)')" onmouseout="c()">
<title>pm_qos_requirement (54 samples, 0.01%)</title><rect x="1112.7" y="417" width="0.2" height="15.0" fill="rgb(235,16,7)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('do_timer (1,366 samples, 0.32%)')" onmouseout="c()">
<title>do_timer (1,366 samples, 0.32%)</title><rect x="156.3" y="257" width="3.8" height="15.0" fill="rgb(223,101,40)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_lock_irqsave (74 samples, 0.02%)')" onmouseout="c()">
<title>_spin_lock_irqsave (74 samples, 0.02%)</title><rect x="1029.3" y="321" width="0.2" height="15.0" fill="rgb(209,137,45)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('device_not_available (43 samples, 0.01%)')" onmouseout="c()">
<title>device_not_available (43 samples, 0.01%)</title><rect x="488.2" y="337" width="0.2" height="15.0" fill="rgb(231,72,37)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__do_softirq (61 samples, 0.01%)')" onmouseout="c()">
<title>__do_softirq (61 samples, 0.01%)</title><rect x="1186.9" y="273" width="0.2" height="15.0" fill="rgb(208,218,33)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('schedule (82 samples, 0.02%)')" onmouseout="c()">
<title>schedule (82 samples, 0.02%)</title><rect x="979.7" y="465" width="0.2" height="15.0" fill="rgb(222,218,51)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('scheduler_tick (36,458 samples, 8.58%)')" onmouseout="c()">
<title>scheduler_tick (36,458 samples, 8.58%)</title><rect x="186.5" y="257" width="101.2" height="15.0" fill="rgb(240,30,11)" rx="2" ry="2" />
<text text-anchor="" x="189.498893349986" y="267.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >scheduler_tick</text>
</g>
<g class="func_g" onmouseover="s('clockevents_program_event (505 samples, 0.12%)')" onmouseout="c()">
<title>clockevents_program_event (505 samples, 0.12%)</title><rect x="310.3" y="289" width="1.4" height="15.0" fill="rgb(233,130,45)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_lock (93 samples, 0.02%)')" onmouseout="c()">
<title>_spin_lock (93 samples, 0.02%)</title><rect x="1041.0" y="321" width="0.2" height="15.0" fill="rgb(207,37,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vmx_vcpu_run (17,456 samples, 4.11%)')" onmouseout="c()">
<title>vmx_vcpu_run (17,456 samples, 4.11%)</title><rect x="839.1" y="337" width="48.4" height="15.0" fill="rgb(235,49,53)" rx="2" ry="2" />
<text text-anchor="" x="842.05049193592" y="347.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >vmx_..</text>
</g>
<g class="func_g" onmouseover="s('update_cfs_shares (406 samples, 0.10%)')" onmouseout="c()">
<title>update_cfs_shares (406 samples, 0.10%)</title><rect x="680.6" y="289" width="1.1" height="15.0" fill="rgb(240,28,1)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_unlock_irqrestore (323 samples, 0.08%)')" onmouseout="c()">
<title>_spin_unlock_irqrestore (323 samples, 0.08%)</title><rect x="802.8" y="193" width="0.9" height="15.0" fill="rgb(250,72,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('hrtimer_force_reprogram (274 samples, 0.06%)')" onmouseout="c()">
<title>hrtimer_force_reprogram (274 samples, 0.06%)</title><rect x="1142.5" y="369" width="0.7" height="15.0" fill="rgb(237,60,9)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('irq_exit (86 samples, 0.02%)')" onmouseout="c()">
<title>irq_exit (86 samples, 0.02%)</title><rect x="1187.2" y="337" width="0.2" height="15.0" fill="rgb(216,86,39)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vmx_cache_reg (619 samples, 0.15%)')" onmouseout="c()">
<title>vmx_cache_reg (619 samples, 0.15%)</title><rect x="828.1" y="321" width="1.7" height="15.0" fill="rgb(209,28,34)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_unlock_irqrestore (81 samples, 0.02%)')" onmouseout="c()">
<title>_spin_unlock_irqrestore (81 samples, 0.02%)</title><rect x="597.5" y="305" width="0.3" height="15.0" fill="rgb(250,72,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('do_filp_open (111 samples, 0.03%)')" onmouseout="c()">
<title>do_filp_open (111 samples, 0.03%)</title><rect x="968.2" y="401" width="0.3" height="15.0" fill="rgb(244,169,17)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('mark_page_dirty_in_slot (94 samples, 0.02%)')" onmouseout="c()">
<title>mark_page_dirty_in_slot (94 samples, 0.02%)</title><rect x="584.6" y="305" width="0.3" height="15.0" fill="rgb(209,110,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_group_power (39 samples, 0.01%)')" onmouseout="c()">
<title>update_group_power (39 samples, 0.01%)</title><rect x="1115.0" y="257" width="0.2" height="15.0" fill="rgb(245,109,42)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('check_for_new_grace_period (791 samples, 0.19%)')" onmouseout="c()">
<title>check_for_new_grace_period (791 samples, 0.19%)</title><rect x="363.6" y="225" width="2.2" height="15.0" fill="rgb(217,224,28)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_lock_irq (87 samples, 0.02%)')" onmouseout="c()">
<title>_spin_lock_irq (87 samples, 0.02%)</title><rect x="611.8" y="305" width="0.2" height="15.0" fill="rgb(221,92,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_apic_accept_pic_intr (293 samples, 0.07%)')" onmouseout="c()">
<title>kvm_apic_accept_pic_intr (293 samples, 0.07%)</title><rect x="492.2" y="337" width="0.8" height="15.0" fill="rgb(249,176,35)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('irq_enter (2,883 samples, 0.68%)')" onmouseout="c()">
<title>irq_enter (2,883 samples, 0.68%)</title><rect x="1036.1" y="385" width="8.0" height="15.0" fill="rgb(247,79,15)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_timer_fn (427 samples, 0.10%)')" onmouseout="c()">
<title>kvm_timer_fn (427 samples, 0.10%)</title><rect x="1181.5" y="305" width="1.2" height="15.0" fill="rgb(249,104,46)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('apic_set_eoi (620 samples, 0.15%)')" onmouseout="c()">
<title>apic_set_eoi (620 samples, 0.15%)</title><rect x="486.2" y="337" width="1.7" height="15.0" fill="rgb(250,72,20)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_read_tsc (44 samples, 0.01%)')" onmouseout="c()">
<title>native_read_tsc (44 samples, 0.01%)</title><rect x="687.8" y="289" width="0.1" height="15.0" fill="rgb(249,99,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_read_tsc (138 samples, 0.03%)')" onmouseout="c()">
<title>native_read_tsc (138 samples, 0.03%)</title><rect x="682.5" y="225" width="0.4" height="15.0" fill="rgb(249,99,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('sys_write (235 samples, 0.06%)')" onmouseout="c()">
<title>sys_write (235 samples, 0.06%)</title><rect x="970.3" y="385" width="0.7" height="15.0" fill="rgb(205,146,45)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('gfn_to_hva (57 samples, 0.01%)')" onmouseout="c()">
<title>gfn_to_hva (57 samples, 0.01%)</title><rect x="693.6" y="305" width="0.1" height="15.0" fill="rgb(234,174,13)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('account_entity_enqueue (121 samples, 0.03%)')" onmouseout="c()">
<title>account_entity_enqueue (121 samples, 0.03%)</title><rect x="632.1" y="225" width="0.4" height="15.0" fill="rgb(224,51,23)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vmx_set_interrupt_shadow (350 samples, 0.08%)')" onmouseout="c()">
<title>vmx_set_interrupt_shadow (350 samples, 0.08%)</title><rect x="762.4" y="305" width="0.9" height="15.0" fill="rgb(218,142,39)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('set_next_entity (64 samples, 0.02%)')" onmouseout="c()">
<title>set_next_entity (64 samples, 0.02%)</title><rect x="1188.0" y="353" width="0.1" height="15.0" fill="rgb(232,112,33)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tick_program_event (66 samples, 0.02%)')" onmouseout="c()">
<title>tick_program_event (66 samples, 0.02%)</title><rect x="1046.2" y="385" width="0.2" height="15.0" fill="rgb(219,99,4)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('put_prev_task_idle (51 samples, 0.01%)')" onmouseout="c()">
<title>put_prev_task_idle (51 samples, 0.01%)</title><rect x="1121.8" y="433" width="0.2" height="15.0" fill="rgb(214,94,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('enqueue_task_fair (64 samples, 0.02%)')" onmouseout="c()">
<title>enqueue_task_fair (64 samples, 0.02%)</title><rect x="1023.0" y="241" width="0.1" height="15.0" fill="rgb(251,134,19)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('apic_update_ppr (141 samples, 0.03%)')" onmouseout="c()">
<title>apic_update_ppr (141 samples, 0.03%)</title><rect x="602.0" y="273" width="0.4" height="15.0" fill="rgb(244,88,21)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('apic_timer_interrupt (271 samples, 0.06%)')" onmouseout="c()">
<title>apic_timer_interrupt (271 samples, 0.06%)</title><rect x="1136.1" y="417" width="0.8" height="15.0" fill="rgb(239,200,41)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('find_busiest_group (105 samples, 0.02%)')" onmouseout="c()">
<title>find_busiest_group (105 samples, 0.02%)</title><rect x="380.6" y="225" width="0.2" height="15.0" fill="rgb(221,103,29)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_ts_time_stats (44 samples, 0.01%)')" onmouseout="c()">
<title>update_ts_time_stats (44 samples, 0.01%)</title><rect x="1178.6" y="433" width="0.1" height="15.0" fill="rgb(229,229,29)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tick_dev_program_event (83 samples, 0.02%)')" onmouseout="c()">
<title>tick_dev_program_event (83 samples, 0.02%)</title><rect x="1033.4" y="369" width="0.3" height="15.0" fill="rgb(218,118,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_read_msr_safe (112 samples, 0.03%)')" onmouseout="c()">
<title>native_read_msr_safe (112 samples, 0.03%)</title><rect x="700.6" y="337" width="0.3" height="15.0" fill="rgb(238,178,54)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('schedule (88 samples, 0.02%)')" onmouseout="c()">
<title>schedule (88 samples, 0.02%)</title><rect x="710.0" y="337" width="0.2" height="15.0" fill="rgb(222,218,51)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_inject_apic_timer_irqs (442 samples, 0.10%)')" onmouseout="c()">
<title>kvm_inject_apic_timer_irqs (442 samples, 0.10%)</title><rect x="528.4" y="337" width="1.2" height="15.0" fill="rgb(222,210,10)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rebalance_domains (68 samples, 0.02%)')" onmouseout="c()">
<title>rebalance_domains (68 samples, 0.02%)</title><rect x="1136.6" y="305" width="0.2" height="15.0" fill="rgb(207,28,22)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('save_args (90 samples, 0.02%)')" onmouseout="c()">
<title>save_args (90 samples, 0.02%)</title><rect x="979.5" y="465" width="0.2" height="15.0" fill="rgb(249,138,42)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('irq_enter (1,788 samples, 0.42%)')" onmouseout="c()">
<title>irq_enter (1,788 samples, 0.42%)</title><rect x="328.7" y="321" width="5.0" height="15.0" fill="rgb(247,79,15)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__timer_stats_hrtimer_set_start_info (68 samples, 0.02%)')" onmouseout="c()">
<title>__timer_stats_hrtimer_set_start_info (68 samples, 0.02%)</title><rect x="1153.8" y="401" width="0.2" height="15.0" fill="rgb(211,194,18)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ns_to_timespec (83 samples, 0.02%)')" onmouseout="c()">
<title>ns_to_timespec (83 samples, 0.02%)</title><rect x="1105.7" y="401" width="0.3" height="15.0" fill="rgb(227,143,32)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_load_tr_desc (225 samples, 0.05%)')" onmouseout="c()">
<title>native_load_tr_desc (225 samples, 0.05%)</title><rect x="689.0" y="257" width="0.7" height="15.0" fill="rgb(229,52,27)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('account_system_time (329 samples, 0.08%)')" onmouseout="c()">
<title>account_system_time (329 samples, 0.08%)</title><rect x="168.9" y="257" width="0.9" height="15.0" fill="rgb(248,96,10)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('copy_to_user (183 samples, 0.04%)')" onmouseout="c()">
<title>copy_to_user (183 samples, 0.04%)</title><rect x="559.6" y="321" width="0.5" height="15.0" fill="rgb(206,70,41)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tick_nohz_restart_sched_tick (7,717 samples, 1.82%)')" onmouseout="c()">
<title>tick_nohz_restart_sched_tick (7,717 samples, 1.82%)</title><rect x="1137.4" y="433" width="21.4" height="15.0" fill="rgb(210,23,25)" rx="2" ry="2" />
<text text-anchor="" x="1140.35535343414" y="443.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >t..</text>
</g>
<g class="func_g" onmouseover="s('kvm_resched (71 samples, 0.02%)')" onmouseout="c()">
<title>kvm_resched (71 samples, 0.02%)</title><rect x="593.5" y="337" width="0.2" height="15.0" fill="rgb(237,225,46)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('try_to_wake_up (1,398 samples, 0.33%)')" onmouseout="c()">
<title>try_to_wake_up (1,398 samples, 0.33%)</title><rect x="791.3" y="113" width="3.9" height="15.0" fill="rgb(248,130,8)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('sched_clock_cpu (318 samples, 0.07%)')" onmouseout="c()">
<title>sched_clock_cpu (318 samples, 0.07%)</title><rect x="640.3" y="257" width="0.9" height="15.0" fill="rgb(232,83,53)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vmcs_writel (630 samples, 0.15%)')" onmouseout="c()">
<title>vmcs_writel (630 samples, 0.15%)</title><rect x="730.4" y="337" width="1.8" height="15.0" fill="rgb(228,19,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tick_dev_program_event (56 samples, 0.01%)')" onmouseout="c()">
<title>tick_dev_program_event (56 samples, 0.01%)</title><rect x="815.2" y="177" width="0.2" height="15.0" fill="rgb(218,118,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('irq_exit (2,792 samples, 0.66%)')" onmouseout="c()">
<title>irq_exit (2,792 samples, 0.66%)</title><rect x="37.8" y="337" width="7.8" height="15.0" fill="rgb(216,86,39)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('apic_reg_write (444 samples, 0.10%)')" onmouseout="c()">
<title>apic_reg_write (444 samples, 0.10%)</title><rect x="774.0" y="273" width="1.2" height="15.0" fill="rgb(252,104,44)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('prepare_to_wait (628 samples, 0.15%)')" onmouseout="c()">
<title>prepare_to_wait (628 samples, 0.15%)</title><rect x="607.8" y="321" width="1.7" height="15.0" fill="rgb(227,79,27)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unalias_gfn_instantiation (177 samples, 0.04%)')" onmouseout="c()">
<title>unalias_gfn_instantiation (177 samples, 0.04%)</title><rect x="590.5" y="305" width="0.4" height="15.0" fill="rgb(212,78,21)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_cpu_has_pending_timer (2,323 samples, 0.55%)')" onmouseout="c()">
<title>kvm_cpu_has_pending_timer (2,323 samples, 0.55%)</title><rect x="519.7" y="337" width="6.4" height="15.0" fill="rgb(221,78,37)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('enqueue_task (580 samples, 0.14%)')" onmouseout="c()">
<title>enqueue_task (580 samples, 0.14%)</title><rect x="791.5" y="81" width="1.6" height="15.0" fill="rgb(225,196,32)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('lapic_is_periodic (398 samples, 0.09%)')" onmouseout="c()">
<title>lapic_is_periodic (398 samples, 0.09%)</title><rect x="134.6" y="273" width="1.1" height="15.0" fill="rgb(205,14,15)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__wake_up (11,103 samples, 2.61%)')" onmouseout="c()">
<title>__wake_up (11,103 samples, 2.61%)</title><rect x="999.0" y="337" width="30.8" height="15.0" fill="rgb(226,70,29)" rx="2" ry="2" />
<text text-anchor="" x="1001.98552769918" y="347.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >__..</text>
</g>
<g class="func_g" onmouseover="s('native_smp_send_reschedule (39 samples, 0.01%)')" onmouseout="c()">
<title>native_smp_send_reschedule (39 samples, 0.01%)</title><rect x="171.9" y="257" width="0.1" height="15.0" fill="rgb(218,74,11)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_shares (2,701 samples, 0.64%)')" onmouseout="c()">
<title>update_shares (2,701 samples, 0.64%)</title><rect x="675.4" y="305" width="7.5" height="15.0" fill="rgb(229,50,20)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('hrtimer_start (328 samples, 0.08%)')" onmouseout="c()">
<title>hrtimer_start (328 samples, 0.08%)</title><rect x="786.4" y="241" width="0.9" height="15.0" fill="rgb(252,146,16)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('do_IRQ (101 samples, 0.02%)')" onmouseout="c()">
<title>do_IRQ (101 samples, 0.02%)</title><rect x="1187.1" y="353" width="0.3" height="15.0" fill="rgb(219,133,48)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('sched_clock (234 samples, 0.06%)')" onmouseout="c()">
<title>sched_clock (234 samples, 0.06%)</title><rect x="283.4" y="225" width="0.7" height="15.0" fill="rgb(207,104,49)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_cond_resched (94 samples, 0.02%)')" onmouseout="c()">
<title>_cond_resched (94 samples, 0.02%)</title><rect x="565.0" y="305" width="0.3" height="15.0" fill="rgb(243,183,12)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_timer_fn (11,725 samples, 2.76%)')" onmouseout="c()">
<title>kvm_timer_fn (11,725 samples, 2.76%)</title><rect x="998.1" y="353" width="32.5" height="15.0" fill="rgb(249,104,46)" rx="2" ry="2" />
<text text-anchor="" x="1001.07240619348" y="363.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >kv..</text>
</g>
<g class="func_g" onmouseover="s('touch_softlockup_watchdog (86 samples, 0.02%)')" onmouseout="c()">
<title>touch_softlockup_watchdog (86 samples, 0.02%)</title><rect x="1042.4" y="337" width="0.2" height="15.0" fill="rgb(243,131,12)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_write_guest_cached (367 samples, 0.09%)')" onmouseout="c()">
<title>kvm_write_guest_cached (367 samples, 0.09%)</title><rect x="566.3" y="321" width="1.0" height="15.0" fill="rgb(227,3,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__do_softirq (16,067 samples, 3.78%)')" onmouseout="c()">
<title>__do_softirq (16,067 samples, 3.78%)</title><rect x="344.9" y="273" width="44.6" height="15.0" fill="rgb(208,218,33)" rx="2" ry="2" />
<text text-anchor="" x="347.87690429653" y="283.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >__do..</text>
</g>
<g class="func_g" onmouseover="s('update_rq_clock (69 samples, 0.02%)')" onmouseout="c()">
<title>update_rq_clock (69 samples, 0.02%)</title><rect x="289.4" y="257" width="0.2" height="15.0" fill="rgb(227,206,0)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_cpu_has_interrupt (1,065 samples, 0.25%)')" onmouseout="c()">
<title>kvm_cpu_has_interrupt (1,065 samples, 0.25%)</title><rect x="599.5" y="305" width="2.9" height="15.0" fill="rgb(245,130,49)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vcpu_load (1,484 samples, 0.35%)')" onmouseout="c()">
<title>vcpu_load (1,484 samples, 0.35%)</title><rect x="682.9" y="321" width="4.2" height="15.0" fill="rgb(220,63,17)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_IO_vsscanf (89 samples, 0.02%)')" onmouseout="c()">
<title>_IO_vsscanf (89 samples, 0.02%)</title><rect x="967.7" y="465" width="0.2" height="15.0" fill="rgb(239,121,21)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('hrtimer_interrupt (15,849 samples, 3.73%)')" onmouseout="c()">
<title>hrtimer_interrupt (15,849 samples, 3.73%)</title><rect x="992.1" y="385" width="44.0" height="15.0" fill="rgb(252,20,51)" rx="2" ry="2" />
<text text-anchor="" x="995.094097004166" y="395.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >hrti..</text>
</g>
<g class="func_g" onmouseover="s('update_vsyscall (99 samples, 0.02%)')" onmouseout="c()">
<title>update_vsyscall (99 samples, 0.02%)</title><rect x="159.7" y="225" width="0.3" height="15.0" fill="rgb(229,101,53)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('resched_task (48 samples, 0.01%)')" onmouseout="c()">
<title>resched_task (48 samples, 0.01%)</title><rect x="1024.5" y="257" width="0.1" height="15.0" fill="rgb(236,7,47)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_timer_fn (609 samples, 0.14%)')" onmouseout="c()">
<title>kvm_timer_fn (609 samples, 0.14%)</title><rect x="299.4" y="305" width="1.7" height="15.0" fill="rgb(249,104,46)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_lock (951 samples, 0.22%)')" onmouseout="c()">
<title>_spin_lock (951 samples, 0.22%)</title><rect x="289.9" y="305" width="2.7" height="15.0" fill="rgb(207,37,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('menu_select (1,315 samples, 0.31%)')" onmouseout="c()">
<title>menu_select (1,315 samples, 0.31%)</title><rect x="1108.3" y="417" width="3.6" height="15.0" fill="rgb(249,45,6)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rb_erase (44 samples, 0.01%)')" onmouseout="c()">
<title>rb_erase (44 samples, 0.01%)</title><rect x="1132.2" y="385" width="0.1" height="15.0" fill="rgb(236,5,12)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_apic_mem_write (99 samples, 0.02%)')" onmouseout="c()">
<title>native_apic_mem_write (99 samples, 0.02%)</title><rect x="1035.3" y="321" width="0.3" height="15.0" fill="rgb(209,177,4)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_cond_resched (108 samples, 0.03%)')" onmouseout="c()">
<title>_cond_resched (108 samples, 0.03%)</title><rect x="683.1" y="305" width="0.3" height="15.0" fill="rgb(243,183,12)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('copy_user_generic_string (1,910 samples, 0.45%)')" onmouseout="c()">
<title>copy_user_generic_string (1,910 samples, 0.45%)</title><rect x="575.0" y="321" width="5.3" height="15.0" fill="rgb(228,65,19)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('handle_halt (103 samples, 0.02%)')" onmouseout="c()">
<title>handle_halt (103 samples, 0.02%)</title><rect x="490.3" y="337" width="0.3" height="15.0" fill="rgb(226,182,42)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tick_do_update_jiffies64 (694 samples, 0.16%)')" onmouseout="c()">
<title>tick_do_update_jiffies64 (694 samples, 0.16%)</title><rect x="1040.0" y="353" width="1.9" height="15.0" fill="rgb(236,208,31)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('render_sigset_t (49 samples, 0.01%)')" onmouseout="c()">
<title>render_sigset_t (49 samples, 0.01%)</title><rect x="969.0" y="353" width="0.1" height="15.0" fill="rgb(236,118,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__perf_event_task_sched_out (38 samples, 0.01%)')" onmouseout="c()">
<title>__perf_event_task_sched_out (38 samples, 0.01%)</title><rect x="1125.9" y="417" width="0.1" height="15.0" fill="rgb(233,222,44)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('task_rq_lock (109 samples, 0.03%)')" onmouseout="c()">
<title>task_rq_lock (109 samples, 0.03%)</title><rect x="794.8" y="97" width="0.3" height="15.0" fill="rgb(231,149,47)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('printk_tick (86 samples, 0.02%)')" onmouseout="c()">
<title>printk_tick (86 samples, 0.02%)</title><rect x="150.3" y="273" width="0.2" height="15.0" fill="rgb(211,104,35)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_lapic_sync_to_vapic (554 samples, 0.13%)')" onmouseout="c()">
<title>kvm_lapic_sync_to_vapic (554 samples, 0.13%)</title><rect x="898.8" y="353" width="1.5" height="15.0" fill="rgb(227,46,35)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_cpu_has_interrupt (5,261 samples, 1.24%)')" onmouseout="c()">
<title>kvm_cpu_has_interrupt (5,261 samples, 1.24%)</title><rect x="505.1" y="337" width="14.6" height="15.0" fill="rgb(245,130,49)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('schedule (49 samples, 0.01%)')" onmouseout="c()">
<title>schedule (49 samples, 0.01%)</title><rect x="593.5" y="289" width="0.1" height="15.0" fill="rgb(222,218,51)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('exit_idle (302 samples, 0.07%)')" onmouseout="c()">
<title>exit_idle (302 samples, 0.07%)</title><rect x="991.3" y="385" width="0.8" height="15.0" fill="rgb(217,85,44)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rcu_needs_cpu (58 samples, 0.01%)')" onmouseout="c()">
<title>rcu_needs_cpu (58 samples, 0.01%)</title><rect x="1122.2" y="433" width="0.2" height="15.0" fill="rgb(243,9,17)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('clockevents_program_event (230 samples, 0.05%)')" onmouseout="c()">
<title>clockevents_program_event (230 samples, 0.05%)</title><rect x="1034.9" y="337" width="0.7" height="15.0" fill="rgb(233,130,45)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_cfs_shares (2,733 samples, 0.64%)')" onmouseout="c()">
<title>update_cfs_shares (2,733 samples, 0.64%)</title><rect x="234.9" y="225" width="7.6" height="15.0" fill="rgb(240,28,1)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('proc_root_readdir (49 samples, 0.01%)')" onmouseout="c()">
<title>proc_root_readdir (49 samples, 0.01%)</title><rect x="970.1" y="401" width="0.1" height="15.0" fill="rgb(236,83,22)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('idle_cpu (1,005 samples, 0.24%)')" onmouseout="c()">
<title>idle_cpu (1,005 samples, 0.24%)</title><rect x="329.3" y="305" width="2.8" height="15.0" fill="rgb(231,163,39)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_sched_clock (66 samples, 0.02%)')" onmouseout="c()">
<title>native_sched_clock (66 samples, 0.02%)</title><rect x="1022.8" y="177" width="0.2" height="15.0" fill="rgb(233,205,14)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('seq_printf (43 samples, 0.01%)')" onmouseout="c()">
<title>seq_printf (43 samples, 0.01%)</title><rect x="969.0" y="337" width="0.1" height="15.0" fill="rgb(218,83,50)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('sched_clock_cpu (47 samples, 0.01%)')" onmouseout="c()">
<title>sched_clock_cpu (47 samples, 0.01%)</title><rect x="639.8" y="273" width="0.1" height="15.0" fill="rgb(232,83,53)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_process_times (112 samples, 0.03%)')" onmouseout="c()">
<title>update_process_times (112 samples, 0.03%)</title><rect x="289.6" y="289" width="0.3" height="15.0" fill="rgb(209,13,10)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('irq_exit (235 samples, 0.06%)')" onmouseout="c()">
<title>irq_exit (235 samples, 0.06%)</title><rect x="909.0" y="321" width="0.6" height="15.0" fill="rgb(216,86,39)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_read_tsc (73 samples, 0.02%)')" onmouseout="c()">
<title>native_read_tsc (73 samples, 0.02%)</title><rect x="297.3" y="289" width="0.2" height="15.0" fill="rgb(249,99,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vmx_interrupt_allowed (196 samples, 0.05%)')" onmouseout="c()">
<title>vmx_interrupt_allowed (196 samples, 0.05%)</title><rect x="691.7" y="321" width="0.6" height="15.0" fill="rgb(206,40,41)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('finish_task_switch (56 samples, 0.01%)')" onmouseout="c()">
<title>finish_task_switch (56 samples, 0.01%)</title><rect x="1119.1" y="433" width="0.2" height="15.0" fill="rgb(221,162,9)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_inject_pending_timer_irqs (502 samples, 0.12%)')" onmouseout="c()">
<title>kvm_inject_pending_timer_irqs (502 samples, 0.12%)</title><rect x="893.7" y="353" width="1.4" height="15.0" fill="rgb(216,161,22)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('start_apic_timer (167 samples, 0.04%)')" onmouseout="c()">
<title>start_apic_timer (167 samples, 0.04%)</title><rect x="822.4" y="257" width="0.5" height="15.0" fill="rgb(251,224,41)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('physflat_send_IPI_mask (39 samples, 0.01%)')" onmouseout="c()">
<title>physflat_send_IPI_mask (39 samples, 0.01%)</title><rect x="795.4" y="161" width="0.1" height="15.0" fill="rgb(224,172,23)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__wake_up_common (67 samples, 0.02%)')" onmouseout="c()">
<title>__wake_up_common (67 samples, 0.02%)</title><rect x="1029.8" y="337" width="0.2" height="15.0" fill="rgb(214,94,27)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_unlock_irqrestore (54 samples, 0.01%)')" onmouseout="c()">
<title>_spin_unlock_irqrestore (54 samples, 0.01%)</title><rect x="1169.5" y="385" width="0.2" height="15.0" fill="rgb(250,72,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__wake_up_common (1,470 samples, 0.35%)')" onmouseout="c()">
<title>__wake_up_common (1,470 samples, 0.35%)</title><rect x="791.1" y="161" width="4.1" height="15.0" fill="rgb(214,94,27)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__GI___ioctl (344,397 samples, 81.00%)')" onmouseout="c()">
<title>__GI___ioctl (344,397 samples, 81.00%)</title><rect x="11.1" y="449" width="955.9" height="15.0" fill="rgb(227,98,38)" rx="2" ry="2" />
<text text-anchor="" x="14.1268307942713" y="459.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >__GI___ioctl</text>
</g>
<g class="func_g" onmouseover="s('hrtimer_start_range_ns (75 samples, 0.02%)')" onmouseout="c()">
<title>hrtimer_start_range_ns (75 samples, 0.02%)</title><rect x="1120.0" y="433" width="0.2" height="15.0" fill="rgb(207,72,23)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('find_busiest_queue (1,070 samples, 0.25%)')" onmouseout="c()">
<title>find_busiest_queue (1,070 samples, 0.25%)</title><rect x="669.7" y="305" width="3.0" height="15.0" fill="rgb(251,226,49)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('read_tsc (60 samples, 0.01%)')" onmouseout="c()">
<title>read_tsc (60 samples, 0.01%)</title><rect x="1176.4" y="401" width="0.2" height="15.0" fill="rgb(233,66,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('pick_next_task_fair (220 samples, 0.05%)')" onmouseout="c()">
<title>pick_next_task_fair (220 samples, 0.05%)</title><rect x="612.4" y="305" width="0.6" height="15.0" fill="rgb(241,43,16)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ext4_da_write_begin (67 samples, 0.02%)')" onmouseout="c()">
<title>ext4_da_write_begin (67 samples, 0.02%)</title><rect x="970.4" y="273" width="0.2" height="15.0" fill="rgb(210,48,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('cache_reap (250 samples, 0.06%)')" onmouseout="c()">
<title>cache_reap (250 samples, 0.06%)</title><rect x="976.0" y="417" width="0.7" height="15.0" fill="rgb(236,198,47)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_read_tsc (261 samples, 0.06%)')" onmouseout="c()">
<title>native_read_tsc (261 samples, 0.06%)</title><rect x="298.7" y="273" width="0.7" height="15.0" fill="rgb(249,99,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_read_tsc (123 samples, 0.03%)')" onmouseout="c()">
<title>native_read_tsc (123 samples, 0.03%)</title><rect x="150.0" y="241" width="0.3" height="15.0" fill="rgb(249,99,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('do_softirq (103 samples, 0.02%)')" onmouseout="c()">
<title>do_softirq (103 samples, 0.02%)</title><rect x="1044.4" y="369" width="0.3" height="15.0" fill="rgb(243,77,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('copy_user_generic (541 samples, 0.13%)')" onmouseout="c()">
<title>copy_user_generic (541 samples, 0.13%)</title><rect x="560.1" y="321" width="1.5" height="15.0" fill="rgb(230,132,27)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('br_handle_frame (59 samples, 0.01%)')" onmouseout="c()">
<title>br_handle_frame (59 samples, 0.01%)</title><rect x="909.2" y="145" width="0.1" height="15.0" fill="rgb(244,10,37)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_write_msr_safe (44 samples, 0.01%)')" onmouseout="c()">
<title>native_write_msr_safe (44 samples, 0.01%)</title><rect x="220.4" y="161" width="0.1" height="15.0" fill="rgb(215,163,22)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('save_args (636 samples, 0.15%)')" onmouseout="c()">
<title>save_args (636 samples, 0.15%)</title><rect x="13.5" y="433" width="1.8" height="15.0" fill="rgb(249,138,42)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_lapic_find_highest_irr (684 samples, 0.16%)')" onmouseout="c()">
<title>kvm_lapic_find_highest_irr (684 samples, 0.16%)</title><rect x="547.1" y="337" width="1.9" height="15.0" fill="rgb(217,222,0)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_apic_mem_write (1,298 samples, 0.31%)')" onmouseout="c()">
<title>native_apic_mem_write (1,298 samples, 0.31%)</title><rect x="45.6" y="337" width="3.6" height="15.0" fill="rgb(209,177,4)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vmx_handle_exit (31,464 samples, 7.40%)')" onmouseout="c()">
<title>vmx_handle_exit (31,464 samples, 7.40%)</title><rect x="743.4" y="337" width="87.3" height="15.0" fill="rgb(237,179,1)" rx="2" ry="2" />
<text text-anchor="" x="746.408646688165" y="347.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >vmx_handle..</text>
</g>
<g class="func_g" onmouseover="s('pit_has_pending_timer (187 samples, 0.04%)')" onmouseout="c()">
<title>pit_has_pending_timer (187 samples, 0.04%)</title><rect x="606.8" y="321" width="0.5" height="15.0" fill="rgb(253,0,31)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('perf_event_task_sched_out (153 samples, 0.04%)')" onmouseout="c()">
<title>perf_event_task_sched_out (153 samples, 0.04%)</title><rect x="612.0" y="305" width="0.4" height="15.0" fill="rgb(215,151,2)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('pit_has_pending_timer (1,155 samples, 0.27%)')" onmouseout="c()">
<title>pit_has_pending_timer (1,155 samples, 0.27%)</title><rect x="705.7" y="337" width="3.2" height="15.0" fill="rgb(253,0,31)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_ioapic_handles_vector (978 samples, 0.23%)')" onmouseout="c()">
<title>kvm_ioapic_handles_vector (978 samples, 0.23%)</title><rect x="543.5" y="337" width="2.7" height="15.0" fill="rgb(233,138,41)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('perf_pmu_enable (848 samples, 0.20%)')" onmouseout="c()">
<title>perf_pmu_enable (848 samples, 0.20%)</title><rect x="218.2" y="209" width="2.3" height="15.0" fill="rgb(238,51,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_set_shared_msr (178 samples, 0.04%)')" onmouseout="c()">
<title>kvm_set_shared_msr (178 samples, 0.04%)</title><rect x="593.7" y="337" width="0.5" height="15.0" fill="rgb(252,70,51)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('lock_hrtimer_base (880 samples, 0.21%)')" onmouseout="c()">
<title>lock_hrtimer_base (880 samples, 0.21%)</title><rect x="782.9" y="209" width="2.4" height="15.0" fill="rgb(232,201,8)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('run_timer_softirq (59 samples, 0.01%)')" onmouseout="c()">
<title>run_timer_softirq (59 samples, 0.01%)</title><rect x="1044.5" y="321" width="0.2" height="15.0" fill="rgb(219,191,6)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('sched_clock_idle_wakeup_event (80 samples, 0.02%)')" onmouseout="c()">
<title>sched_clock_idle_wakeup_event (80 samples, 0.02%)</title><rect x="1042.0" y="337" width="0.2" height="15.0" fill="rgb(230,105,29)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('do_sys_open (124 samples, 0.03%)')" onmouseout="c()">
<title>do_sys_open (124 samples, 0.03%)</title><rect x="968.2" y="417" width="0.3" height="15.0" fill="rgb(220,64,32)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('atomic_notifier_call_chain (104 samples, 0.02%)')" onmouseout="c()">
<title>atomic_notifier_call_chain (104 samples, 0.02%)</title><rect x="991.6" y="369" width="0.3" height="15.0" fill="rgb(249,80,22)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('smp_apic_timer_interrupt (115 samples, 0.03%)')" onmouseout="c()">
<title>smp_apic_timer_interrupt (115 samples, 0.03%)</title><rect x="1117.8" y="417" width="0.3" height="15.0" fill="rgb(205,22,48)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__run_hrtimer (40 samples, 0.01%)')" onmouseout="c()">
<title>__run_hrtimer (40 samples, 0.01%)</title><rect x="990.8" y="385" width="0.1" height="15.0" fill="rgb(208,100,50)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tick_program_event (543 samples, 0.13%)')" onmouseout="c()">
<title>tick_program_event (543 samples, 0.13%)</title><rect x="1166.0" y="353" width="1.5" height="15.0" fill="rgb(219,99,4)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__netif_receive_skb (42 samples, 0.01%)')" onmouseout="c()">
<title>__netif_receive_skb (42 samples, 0.01%)</title><rect x="909.4" y="161" width="0.1" height="15.0" fill="rgb(207,148,11)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('dequeue_task_fair (78 samples, 0.02%)')" onmouseout="c()">
<title>dequeue_task_fair (78 samples, 0.02%)</title><rect x="641.2" y="289" width="0.2" height="15.0" fill="rgb(205,43,0)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rb_erase (1,997 samples, 0.47%)')" onmouseout="c()">
<title>rb_erase (1,997 samples, 0.47%)</title><rect x="115.4" y="273" width="5.5" height="15.0" fill="rgb(236,5,12)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('mark_page_dirty_in_slot (49 samples, 0.01%)')" onmouseout="c()">
<title>mark_page_dirty_in_slot (49 samples, 0.01%)</title><rect x="567.3" y="321" width="0.2" height="15.0" fill="rgb(209,110,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('default_wake_function (10,499 samples, 2.47%)')" onmouseout="c()">
<title>default_wake_function (10,499 samples, 2.47%)</title><rect x="999.8" y="289" width="29.1" height="15.0" fill="rgb(208,124,20)" rx="2" ry="2" />
<text text-anchor="" x="1002.76265238488" y="299.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >de..</text>
</g>
<g class="func_g" onmouseover="s('update_cpu_load (1,240 samples, 0.29%)')" onmouseout="c()">
<title>update_cpu_load (1,240 samples, 0.29%)</title><rect x="278.9" y="241" width="3.4" height="15.0" fill="rgb(210,180,27)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vmcs_writel (120 samples, 0.03%)')" onmouseout="c()">
<title>vmcs_writel (120 samples, 0.03%)</title><rect x="838.7" y="321" width="0.4" height="15.0" fill="rgb(228,19,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rcu_irq_exit (88 samples, 0.02%)')" onmouseout="c()">
<title>rcu_irq_exit (88 samples, 0.02%)</title><rect x="1045.9" y="385" width="0.2" height="15.0" fill="rgb(253,130,37)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tick_nohz_stop_idle (563 samples, 0.13%)')" onmouseout="c()">
<title>tick_nohz_stop_idle (563 samples, 0.13%)</title><rect x="1041.9" y="353" width="1.6" height="15.0" fill="rgb(210,186,7)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rb_insert_color (237 samples, 0.06%)')" onmouseout="c()">
<title>rb_insert_color (237 samples, 0.06%)</title><rect x="1009.1" y="193" width="0.7" height="15.0" fill="rgb(222,158,21)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('perf_event_task_sched_out (40 samples, 0.01%)')" onmouseout="c()">
<title>perf_event_task_sched_out (40 samples, 0.01%)</title><rect x="605.7" y="321" width="0.1" height="15.0" fill="rgb(215,151,2)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_irq_delivery_to_apic (2,232 samples, 0.52%)')" onmouseout="c()">
<title>kvm_irq_delivery_to_apic (2,232 samples, 0.52%)</title><rect x="789.3" y="241" width="6.2" height="15.0" fill="rgb(213,217,50)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('smp_reschedule_interrupt (69 samples, 0.02%)')" onmouseout="c()">
<title>smp_reschedule_interrupt (69 samples, 0.02%)</title><rect x="1186.9" y="353" width="0.2" height="15.0" fill="rgb(236,201,24)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rb_erase (222 samples, 0.05%)')" onmouseout="c()">
<title>rb_erase (222 samples, 0.05%)</title><rect x="1131.4" y="369" width="0.6" height="15.0" fill="rgb(236,5,12)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rcu_needs_cpu (58 samples, 0.01%)')" onmouseout="c()">
<title>rcu_needs_cpu (58 samples, 0.01%)</title><rect x="1177.2" y="417" width="0.2" height="15.0" fill="rgb(243,9,17)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ktime_get_real (51 samples, 0.01%)')" onmouseout="c()">
<title>ktime_get_real (51 samples, 0.01%)</title><rect x="1186.5" y="353" width="0.1" height="15.0" fill="rgb(235,131,23)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('activate_task (7,481 samples, 1.76%)')" onmouseout="c()">
<title>activate_task (7,481 samples, 1.76%)</title><rect x="1002.5" y="257" width="20.8" height="15.0" fill="rgb(233,36,1)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_unlock_irqrestore (151 samples, 0.04%)')" onmouseout="c()">
<title>_spin_unlock_irqrestore (151 samples, 0.04%)</title><rect x="780.5" y="225" width="0.4" height="15.0" fill="rgb(250,72,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('atomic_notifier_call_chain (138 samples, 0.03%)')" onmouseout="c()">
<title>atomic_notifier_call_chain (138 samples, 0.03%)</title><rect x="1118.3" y="417" width="0.4" height="15.0" fill="rgb(249,80,22)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__wake_up (53 samples, 0.01%)')" onmouseout="c()">
<title>__wake_up (53 samples, 0.01%)</title><rect x="388.7" y="193" width="0.2" height="15.0" fill="rgb(226,70,29)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('enqueue_hrtimer (60 samples, 0.01%)')" onmouseout="c()">
<title>enqueue_hrtimer (60 samples, 0.01%)</title><rect x="292.6" y="305" width="0.1" height="15.0" fill="rgb(251,179,15)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('put_prev_task_fair (295 samples, 0.07%)')" onmouseout="c()">
<title>put_prev_task_fair (295 samples, 0.07%)</title><rect x="613.0" y="305" width="0.9" height="15.0" fill="rgb(238,46,35)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('netif_receive_skb (42 samples, 0.01%)')" onmouseout="c()">
<title>netif_receive_skb (42 samples, 0.01%)</title><rect x="909.4" y="177" width="0.1" height="15.0" fill="rgb(252,105,44)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('smp_apic_timer_interrupt (50 samples, 0.01%)')" onmouseout="c()">
<title>smp_apic_timer_interrupt (50 samples, 0.01%)</title><rect x="619.8" y="289" width="0.1" height="15.0" fill="rgb(205,22,48)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('enqueue_hrtimer (260 samples, 0.06%)')" onmouseout="c()">
<title>enqueue_hrtimer (260 samples, 0.06%)</title><rect x="816.6" y="209" width="0.7" height="15.0" fill="rgb(251,179,15)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_inject_pending_timer_irqs (4,976 samples, 1.17%)')" onmouseout="c()">
<title>kvm_inject_pending_timer_irqs (4,976 samples, 1.17%)</title><rect x="529.6" y="337" width="13.9" height="15.0" fill="rgb(216,161,22)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rb_insert_color (401 samples, 0.09%)')" onmouseout="c()">
<title>rb_insert_color (401 samples, 0.09%)</title><rect x="1170.2" y="369" width="1.1" height="15.0" fill="rgb(222,158,21)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('restore_args (278 samples, 0.07%)')" onmouseout="c()">
<title>restore_args (278 samples, 0.07%)</title><rect x="906.1" y="353" width="0.8" height="15.0" fill="rgb(243,81,46)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('hrtimer_start (75 samples, 0.02%)')" onmouseout="c()">
<title>hrtimer_start (75 samples, 0.02%)</title><rect x="1119.8" y="433" width="0.2" height="15.0" fill="rgb(252,146,16)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_unlock_irqrestore (45 samples, 0.01%)')" onmouseout="c()">
<title>_spin_unlock_irqrestore (45 samples, 0.01%)</title><rect x="1144.7" y="385" width="0.1" height="15.0" fill="rgb(250,72,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ns_to_timespec (44 samples, 0.01%)')" onmouseout="c()">
<title>ns_to_timespec (44 samples, 0.01%)</title><rect x="1111.1" y="401" width="0.1" height="15.0" fill="rgb(227,143,32)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_cr8_intercept (527 samples, 0.12%)')" onmouseout="c()">
<title>update_cr8_intercept (527 samples, 0.12%)</title><rect x="727.0" y="321" width="1.5" height="15.0" fill="rgb(242,70,44)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_lock (126 samples, 0.03%)')" onmouseout="c()">
<title>_spin_lock (126 samples, 0.03%)</title><rect x="755.9" y="257" width="0.4" height="15.0" fill="rgb(207,37,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_lapic_find_highest_irr (172 samples, 0.04%)')" onmouseout="c()">
<title>kvm_lapic_find_highest_irr (172 samples, 0.04%)</title><rect x="725.8" y="321" width="0.5" height="15.0" fill="rgb(217,222,0)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('napi_skb_finish (83 samples, 0.02%)')" onmouseout="c()">
<title>napi_skb_finish (83 samples, 0.02%)</title><rect x="909.1" y="193" width="0.3" height="15.0" fill="rgb(250,77,17)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('lock_hrtimer_base (615 samples, 0.14%)')" onmouseout="c()">
<title>lock_hrtimer_base (615 samples, 0.14%)</title><rect x="812.7" y="193" width="1.7" height="15.0" fill="rgb(232,201,8)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__rb_rotate_left (217 samples, 0.05%)')" onmouseout="c()">
<title>__rb_rotate_left (217 samples, 0.05%)</title><rect x="130.1" y="257" width="0.6" height="15.0" fill="rgb(251,185,40)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('mark_page_dirty_in_slot (128 samples, 0.03%)')" onmouseout="c()">
<title>mark_page_dirty_in_slot (128 samples, 0.03%)</title><rect x="567.0" y="305" width="0.3" height="15.0" fill="rgb(209,110,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('hrtimer_interrupt (73 samples, 0.02%)')" onmouseout="c()">
<title>hrtimer_interrupt (73 samples, 0.02%)</title><rect x="487.9" y="305" width="0.3" height="15.0" fill="rgb(252,20,51)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vcpu_load (168 samples, 0.04%)')" onmouseout="c()">
<title>vcpu_load (168 samples, 0.04%)</title><rect x="729.6" y="337" width="0.4" height="15.0" fill="rgb(220,63,17)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tick_nohz_restart_sched_tick (104 samples, 0.02%)')" onmouseout="c()">
<title>tick_nohz_restart_sched_tick (104 samples, 0.02%)</title><rect x="1179.5" y="449" width="0.3" height="15.0" fill="rgb(210,23,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_write_guest (46 samples, 0.01%)')" onmouseout="c()">
<title>kvm_write_guest (46 samples, 0.01%)</title><rect x="904.2" y="353" width="0.1" height="15.0" fill="rgb(222,39,12)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ntp_tick_length (119 samples, 0.03%)')" onmouseout="c()">
<title>ntp_tick_length (119 samples, 0.03%)</title><rect x="1041.3" y="321" width="0.4" height="15.0" fill="rgb(212,167,37)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('[unknown] (344,897 samples, 81.12%)')" onmouseout="c()">
<title>[unknown] (344,897 samples, 81.12%)</title><rect x="10.3" y="465" width="957.3" height="15.0" fill="rgb(229,75,51)" rx="2" ry="2" />
<text text-anchor="" x="13.3413797726487" y="475.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >[unknown]</text>
</g>
<g class="func_g" onmouseover="s('hrtimer_get_next_event (196 samples, 0.05%)')" onmouseout="c()">
<title>hrtimer_get_next_event (196 samples, 0.05%)</title><rect x="1162.9" y="401" width="0.5" height="15.0" fill="rgb(234,169,24)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('copy_user_generic_string (121 samples, 0.03%)')" onmouseout="c()">
<title>copy_user_generic_string (121 samples, 0.03%)</title><rect x="561.6" y="321" width="0.4" height="15.0" fill="rgb(228,65,19)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_arch_vcpu_put (1,302 samples, 0.31%)')" onmouseout="c()">
<title>kvm_arch_vcpu_put (1,302 samples, 0.31%)</title><rect x="687.2" y="305" width="3.6" height="15.0" fill="rgb(243,89,40)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('hrtimer_force_reprogram (123 samples, 0.03%)')" onmouseout="c()">
<title>hrtimer_force_reprogram (123 samples, 0.03%)</title><rect x="1144.8" y="385" width="0.3" height="15.0" fill="rgb(237,60,9)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('hrtimer_start_range_ns (142 samples, 0.03%)')" onmouseout="c()">
<title>hrtimer_start_range_ns (142 samples, 0.03%)</title><rect x="1188.6" y="369" width="0.4" height="15.0" fill="rgb(207,72,23)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ktime_get (907 samples, 0.21%)')" onmouseout="c()">
<title>ktime_get (907 samples, 0.21%)</title><rect x="818.1" y="225" width="2.5" height="15.0" fill="rgb(213,13,52)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('thread_return (52 samples, 0.01%)')" onmouseout="c()">
<title>thread_return (52 samples, 0.01%)</title><rect x="976.8" y="417" width="0.2" height="15.0" fill="rgb(241,141,48)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ext4_da_write_end (53 samples, 0.01%)')" onmouseout="c()">
<title>ext4_da_write_end (53 samples, 0.01%)</title><rect x="970.6" y="273" width="0.1" height="15.0" fill="rgb(246,221,8)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('napi_skb_finish (42 samples, 0.01%)')" onmouseout="c()">
<title>napi_skb_finish (42 samples, 0.01%)</title><rect x="909.4" y="193" width="0.1" height="15.0" fill="rgb(250,77,17)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_read_cr0 (294 samples, 0.07%)')" onmouseout="c()">
<title>native_read_cr0 (294 samples, 0.07%)</title><rect x="878.8" y="321" width="0.8" height="15.0" fill="rgb(248,172,48)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('gfn_to_memslot (180 samples, 0.04%)')" onmouseout="c()">
<title>gfn_to_memslot (180 samples, 0.04%)</title><rect x="694.1" y="289" width="0.5" height="15.0" fill="rgb(239,135,10)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('start_kernel (3,326 samples, 0.78%)')" onmouseout="c()">
<title>start_kernel (3,326 samples, 0.78%)</title><rect x="1180.8" y="433" width="9.2" height="15.0" fill="rgb(246,212,39)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_apic_accept_pic_intr (1,192 samples, 0.28%)')" onmouseout="c()">
<title>kvm_apic_accept_pic_intr (1,192 samples, 0.28%)</title><rect x="509.0" y="321" width="3.3" height="15.0" fill="rgb(249,176,35)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ktime_get (307 samples, 0.07%)')" onmouseout="c()">
<title>ktime_get (307 samples, 0.07%)</title><rect x="311.7" y="289" width="0.9" height="15.0" fill="rgb(213,13,52)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('run_timer_softirq (2,866 samples, 0.67%)')" onmouseout="c()">
<title>run_timer_softirq (2,866 samples, 0.67%)</title><rect x="381.5" y="257" width="8.0" height="15.0" fill="rgb(219,191,6)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_vcpu_ioctl (331,273 samples, 77.92%)')" onmouseout="c()">
<title>kvm_vcpu_ioctl (331,273 samples, 77.92%)</title><rect x="15.6" y="369" width="919.4" height="15.0" fill="rgb(251,164,31)" rx="2" ry="2" />
<text text-anchor="" x="18.5619923933982" y="379.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >kvm_vcpu_ioctl</text>
</g>
<g class="func_g" onmouseover="s('enqueue_task (85 samples, 0.02%)')" onmouseout="c()">
<title>enqueue_task (85 samples, 0.02%)</title><rect x="1024.2" y="257" width="0.3" height="15.0" fill="rgb(225,196,32)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('find_busiest_group (10,028 samples, 2.36%)')" onmouseout="c()">
<title>find_busiest_group (10,028 samples, 2.36%)</title><rect x="641.9" y="305" width="27.8" height="15.0" fill="rgb(221,103,29)" rx="2" ry="2" />
<text text-anchor="" x="644.899510063341" y="315.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >f..</text>
</g>
<g class="func_g" onmouseover="s('_spin_lock_irqsave (86 samples, 0.02%)')" onmouseout="c()">
<title>_spin_lock_irqsave (86 samples, 0.02%)</title><rect x="1171.8" y="369" width="0.2" height="15.0" fill="rgb(209,137,45)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('autoremove_wake_function (10,643 samples, 2.50%)')" onmouseout="c()">
<title>autoremove_wake_function (10,643 samples, 2.50%)</title><rect x="999.6" y="305" width="29.5" height="15.0" fill="rgb(226,61,12)" rx="2" ry="2" />
<text text-anchor="" x="1002.59057477591" y="315.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >au..</text>
</g>
<g class="func_g" onmouseover="s('sched_clock (100 samples, 0.02%)')" onmouseout="c()">
<title>sched_clock (100 samples, 0.02%)</title><rect x="640.9" y="241" width="0.3" height="15.0" fill="rgb(207,104,49)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_cr8_intercept (1,619 samples, 0.38%)')" onmouseout="c()">
<title>update_cr8_intercept (1,619 samples, 0.38%)</title><rect x="914.4" y="353" width="4.5" height="15.0" fill="rgb(242,70,44)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__run_hrtimer (13,080 samples, 3.08%)')" onmouseout="c()">
<title>__run_hrtimer (13,080 samples, 3.08%)</title><rect x="995.7" y="369" width="36.3" height="15.0" fill="rgb(208,100,50)" rx="2" ry="2" />
<text text-anchor="" x="998.666095113099" y="379.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >__r..</text>
</g>
<g class="func_g" onmouseover="s('copy_user_generic_string (67 samples, 0.02%)')" onmouseout="c()">
<title>copy_user_generic_string (67 samples, 0.02%)</title><rect x="970.7" y="257" width="0.2" height="15.0" fill="rgb(228,65,19)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_arch_vcpu_runnable (1,624 samples, 0.38%)')" onmouseout="c()">
<title>kvm_arch_vcpu_runnable (1,624 samples, 0.38%)</title><rect x="598.7" y="321" width="4.5" height="15.0" fill="rgb(252,200,20)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('thread_return (123 samples, 0.03%)')" onmouseout="c()">
<title>thread_return (123 samples, 0.03%)</title><rect x="1180.4" y="465" width="0.3" height="15.0" fill="rgb(241,141,48)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('do_futex (41 samples, 0.01%)')" onmouseout="c()">
<title>do_futex (41 samples, 0.01%)</title><rect x="980.0" y="417" width="0.1" height="15.0" fill="rgb(223,180,37)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('sched_clock (70 samples, 0.02%)')" onmouseout="c()">
<title>sched_clock (70 samples, 0.02%)</title><rect x="1022.8" y="193" width="0.2" height="15.0" fill="rgb(207,104,49)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('hrtimer_interrupt (831 samples, 0.20%)')" onmouseout="c()">
<title>hrtimer_interrupt (831 samples, 0.20%)</title><rect x="35.1" y="337" width="2.3" height="15.0" fill="rgb(252,20,51)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_set_msr_common (566 samples, 0.13%)')" onmouseout="c()">
<title>kvm_set_msr_common (566 samples, 0.13%)</title><rect x="758.2" y="305" width="1.5" height="15.0" fill="rgb(225,95,21)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__perf_event_task_sched_out (160 samples, 0.04%)')" onmouseout="c()">
<title>__perf_event_task_sched_out (160 samples, 0.04%)</title><rect x="1127.3" y="401" width="0.4" height="15.0" fill="rgb(233,222,44)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('hrtick_start_fair (148 samples, 0.03%)')" onmouseout="c()">
<title>hrtick_start_fair (148 samples, 0.03%)</title><rect x="1129.5" y="401" width="0.4" height="15.0" fill="rgb(209,42,50)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('do_path_lookup (72 samples, 0.02%)')" onmouseout="c()">
<title>do_path_lookup (72 samples, 0.02%)</title><rect x="968.2" y="385" width="0.2" height="15.0" fill="rgb(236,229,17)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__cond_resched (69 samples, 0.02%)')" onmouseout="c()">
<title>__cond_resched (69 samples, 0.02%)</title><rect x="593.5" y="305" width="0.2" height="15.0" fill="rgb(234,125,34)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('pick_next_task_fair (2,270 samples, 0.53%)')" onmouseout="c()">
<title>pick_next_task_fair (2,270 samples, 0.53%)</title><rect x="1127.7" y="417" width="6.3" height="15.0" fill="rgb(241,43,16)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ktime_get (247 samples, 0.06%)')" onmouseout="c()">
<title>ktime_get (247 samples, 0.06%)</title><rect x="788.6" y="241" width="0.6" height="15.0" fill="rgb(213,13,52)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vcpu_put (147 samples, 0.03%)')" onmouseout="c()">
<title>vcpu_put (147 samples, 0.03%)</title><rect x="730.0" y="337" width="0.4" height="15.0" fill="rgb(238,142,29)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('pick_next_task_stop (154 samples, 0.04%)')" onmouseout="c()">
<title>pick_next_task_stop (154 samples, 0.04%)</title><rect x="606.4" y="321" width="0.4" height="15.0" fill="rgb(220,169,2)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_cfs_load (567 samples, 0.13%)')" onmouseout="c()">
<title>update_cfs_load (567 samples, 0.13%)</title><rect x="1009.8" y="193" width="1.6" height="15.0" fill="rgb(242,121,52)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('handle_wrmsr (574 samples, 0.14%)')" onmouseout="c()">
<title>handle_wrmsr (574 samples, 0.14%)</title><rect x="490.6" y="337" width="1.6" height="15.0" fill="rgb(219,72,31)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_lapic_enabled (612 samples, 0.14%)')" onmouseout="c()">
<title>kvm_lapic_enabled (612 samples, 0.14%)</title><rect x="895.1" y="353" width="1.7" height="15.0" fill="rgb(211,200,42)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('do_munmap (58 samples, 0.01%)')" onmouseout="c()">
<title>do_munmap (58 samples, 0.01%)</title><rect x="969.8" y="417" width="0.2" height="15.0" fill="rgb(212,226,48)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('account_process_tick (291 samples, 0.07%)')" onmouseout="c()">
<title>account_process_tick (291 samples, 0.07%)</title><rect x="144.6" y="273" width="0.8" height="15.0" fill="rgb(250,142,19)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('seq_vprintf (39 samples, 0.01%)')" onmouseout="c()">
<title>seq_vprintf (39 samples, 0.01%)</title><rect x="969.0" y="321" width="0.1" height="15.0" fill="rgb(210,166,31)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rcu_irq_exit (938 samples, 0.22%)')" onmouseout="c()">
<title>rcu_irq_exit (938 samples, 0.22%)</title><rect x="401.0" y="321" width="2.6" height="15.0" fill="rgb(253,130,37)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('hrtimer_start (151 samples, 0.04%)')" onmouseout="c()">
<title>hrtimer_start (151 samples, 0.04%)</title><rect x="1189.4" y="369" width="0.4" height="15.0" fill="rgb(252,146,16)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_load_tls (174 samples, 0.04%)')" onmouseout="c()">
<title>native_load_tls (174 samples, 0.04%)</title><rect x="977.9" y="465" width="0.5" height="15.0" fill="rgb(235,161,10)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tick_nohz_stop_sched_tick (76 samples, 0.02%)')" onmouseout="c()">
<title>tick_nohz_stop_sched_tick (76 samples, 0.02%)</title><rect x="1179.8" y="449" width="0.2" height="15.0" fill="rgb(217,57,10)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rb_erase (477 samples, 0.11%)')" onmouseout="c()">
<title>rb_erase (477 samples, 0.11%)</title><rect x="1167.5" y="369" width="1.3" height="15.0" fill="rgb(236,5,12)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('thread_return (108 samples, 0.03%)')" onmouseout="c()">
<title>thread_return (108 samples, 0.03%)</title><rect x="935.0" y="433" width="0.3" height="15.0" fill="rgb(241,141,48)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('place_entity (268 samples, 0.06%)')" onmouseout="c()">
<title>place_entity (268 samples, 0.06%)</title><rect x="1008.4" y="193" width="0.7" height="15.0" fill="rgb(226,35,29)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('reschedule_interrupt (69 samples, 0.02%)')" onmouseout="c()">
<title>reschedule_interrupt (69 samples, 0.02%)</title><rect x="1186.9" y="369" width="0.2" height="15.0" fill="rgb(213,96,24)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('start_apic_timer (9,209 samples, 2.17%)')" onmouseout="c()">
<title>start_apic_timer (9,209 samples, 2.17%)</title><rect x="795.5" y="241" width="25.6" height="15.0" fill="rgb(251,224,41)" rx="2" ry="2" />
<text text-anchor="" x="798.537060427089" y="251.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >s..</text>
</g>
<g class="func_g" onmouseover="s('__switch_to (114 samples, 0.03%)')" onmouseout="c()">
<title>__switch_to (114 samples, 0.03%)</title><rect x="1180.4" y="449" width="0.3" height="15.0" fill="rgb(218,57,18)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('hrtimer_forward (65 samples, 0.02%)')" onmouseout="c()">
<title>hrtimer_forward (65 samples, 0.02%)</title><rect x="131.3" y="289" width="0.2" height="15.0" fill="rgb(212,164,2)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_curr (130 samples, 0.03%)')" onmouseout="c()">
<title>update_curr (130 samples, 0.03%)</title><rect x="639.3" y="257" width="0.4" height="15.0" fill="rgb(218,93,52)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_cpu_has_pending_timer (194 samples, 0.05%)')" onmouseout="c()">
<title>kvm_cpu_has_pending_timer (194 samples, 0.05%)</title><rect x="603.7" y="321" width="0.5" height="15.0" fill="rgb(221,78,37)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rebalance_domains (436 samples, 0.10%)')" onmouseout="c()">
<title>rebalance_domains (436 samples, 0.10%)</title><rect x="380.3" y="241" width="1.2" height="15.0" fill="rgb(207,28,22)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rcu_exit_nohz (157 samples, 0.04%)')" onmouseout="c()">
<title>rcu_exit_nohz (157 samples, 0.04%)</title><rect x="1155.7" y="417" width="0.4" height="15.0" fill="rgb(242,77,24)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__local_bh_enable (213 samples, 0.05%)')" onmouseout="c()">
<title>__local_bh_enable (213 samples, 0.05%)</title><rect x="351.3" y="257" width="0.6" height="15.0" fill="rgb(250,106,10)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('enqueue_hrtimer (3,125 samples, 0.74%)')" onmouseout="c()">
<title>enqueue_hrtimer (3,125 samples, 0.74%)</title><rect x="803.8" y="193" width="8.7" height="15.0" fill="rgb(251,179,15)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('check_preempt_curr (337 samples, 0.08%)')" onmouseout="c()">
<title>check_preempt_curr (337 samples, 0.08%)</title><rect x="1023.3" y="257" width="0.9" height="15.0" fill="rgb(211,70,34)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_arch_vcpu_load (218 samples, 0.05%)')" onmouseout="c()">
<title>kvm_arch_vcpu_load (218 samples, 0.05%)</title><rect x="597.8" y="321" width="0.6" height="15.0" fill="rgb(215,87,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_lock (65 samples, 0.02%)')" onmouseout="c()">
<title>_spin_lock (65 samples, 0.02%)</title><rect x="794.9" y="81" width="0.2" height="15.0" fill="rgb(207,37,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_cfs_shares (54 samples, 0.01%)')" onmouseout="c()">
<title>update_cfs_shares (54 samples, 0.01%)</title><rect x="639.2" y="257" width="0.1" height="15.0" fill="rgb(240,28,1)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('autoremove_wake_function (72 samples, 0.02%)')" onmouseout="c()">
<title>autoremove_wake_function (72 samples, 0.02%)</title><rect x="1029.6" y="321" width="0.2" height="15.0" fill="rgb(226,61,12)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_cfs_load (42 samples, 0.01%)')" onmouseout="c()">
<title>update_cfs_load (42 samples, 0.01%)</title><rect x="792.2" y="33" width="0.2" height="15.0" fill="rgb(242,121,52)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__run_hrtimer (118 samples, 0.03%)')" onmouseout="c()">
<title>__run_hrtimer (118 samples, 0.03%)</title><rect x="53.9" y="321" width="0.4" height="15.0" fill="rgb(208,100,50)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_rq_clock (475 samples, 0.11%)')" onmouseout="c()">
<title>update_rq_clock (475 samples, 0.11%)</title><rect x="639.9" y="273" width="1.3" height="15.0" fill="rgb(227,206,0)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_lock_irqsave (320 samples, 0.08%)')" onmouseout="c()">
<title>_spin_lock_irqsave (320 samples, 0.08%)</title><rect x="158.5" y="225" width="0.9" height="15.0" fill="rgb(209,137,45)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('exit_idle (81 samples, 0.02%)')" onmouseout="c()">
<title>exit_idle (81 samples, 0.02%)</title><rect x="34.9" y="337" width="0.2" height="15.0" fill="rgb(217,85,44)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tick_dev_program_event (74 samples, 0.02%)')" onmouseout="c()">
<title>tick_dev_program_event (74 samples, 0.02%)</title><rect x="1165.8" y="353" width="0.2" height="15.0" fill="rgb(218,118,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ret_from_intr (992 samples, 0.23%)')" onmouseout="c()">
<title>ret_from_intr (992 samples, 0.23%)</title><rect x="906.9" y="353" width="2.7" height="15.0" fill="rgb(237,83,40)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('call_softirq (490 samples, 0.12%)')" onmouseout="c()">
<title>call_softirq (490 samples, 0.12%)</title><rect x="340.6" y="305" width="1.4" height="15.0" fill="rgb(240,215,19)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_rq_clock (1,779 samples, 0.42%)')" onmouseout="c()">
<title>update_rq_clock (1,779 samples, 0.42%)</title><rect x="282.7" y="241" width="5.0" height="15.0" fill="rgb(227,206,0)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('select_nohz_load_balancer (40 samples, 0.01%)')" onmouseout="c()">
<title>select_nohz_load_balancer (40 samples, 0.01%)</title><rect x="1134.3" y="433" width="0.2" height="15.0" fill="rgb(224,12,41)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_apic_has_interrupt (529 samples, 0.12%)')" onmouseout="c()">
<title>kvm_apic_has_interrupt (529 samples, 0.12%)</title><rect x="503.6" y="305" width="1.5" height="15.0" fill="rgb(217,29,8)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('activate_task (582 samples, 0.14%)')" onmouseout="c()">
<title>activate_task (582 samples, 0.14%)</title><rect x="791.5" y="97" width="1.6" height="15.0" fill="rgb(233,36,1)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('clockevents_program_event (1,405 samples, 0.33%)')" onmouseout="c()">
<title>clockevents_program_event (1,405 samples, 0.33%)</title><rect x="317.9" y="273" width="3.9" height="15.0" fill="rgb(233,130,45)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_stats_wait_end (127 samples, 0.03%)')" onmouseout="c()">
<title>update_stats_wait_end (127 samples, 0.03%)</title><rect x="1133.6" y="401" width="0.3" height="15.0" fill="rgb(253,139,44)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('pick_next_task_rt (139 samples, 0.03%)')" onmouseout="c()">
<title>pick_next_task_rt (139 samples, 0.03%)</title><rect x="674.5" y="305" width="0.4" height="15.0" fill="rgb(244,112,2)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('try_to_wake_up (10,151 samples, 2.39%)')" onmouseout="c()">
<title>try_to_wake_up (10,151 samples, 2.39%)</title><rect x="1000.7" y="273" width="28.2" height="15.0" fill="rgb(248,130,8)" rx="2" ry="2" />
<text text-anchor="" x="1003.7285073514" y="283.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >t..</text>
</g>
<g class="func_g" onmouseover="s('vfs_read (409 samples, 0.10%)')" onmouseout="c()">
<title>vfs_read (409 samples, 0.10%)</title><rect x="968.6" y="417" width="1.1" height="15.0" fill="rgb(215,113,29)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('enqueue_task (7,337 samples, 1.73%)')" onmouseout="c()">
<title>enqueue_task (7,337 samples, 1.73%)</title><rect x="1002.6" y="241" width="20.4" height="15.0" fill="rgb(225,196,32)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_vcpu_block (35,350 samples, 8.31%)')" onmouseout="c()">
<title>kvm_vcpu_block (35,350 samples, 8.31%)</title><rect x="594.2" y="337" width="98.1" height="15.0" fill="rgb(221,155,20)" rx="2" ry="2" />
<text text-anchor="" x="597.167401689258" y="347.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >kvm_vcpu_bl..</text>
</g>
<g class="func_g" onmouseover="s('rcu_exit_nohz (71 samples, 0.02%)')" onmouseout="c()">
<title>rcu_exit_nohz (71 samples, 0.02%)</title><rect x="1122.0" y="433" width="0.2" height="15.0" fill="rgb(242,77,24)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('call_softirq (235 samples, 0.06%)')" onmouseout="c()">
<title>call_softirq (235 samples, 0.06%)</title><rect x="909.0" y="289" width="0.6" height="15.0" fill="rgb(240,215,19)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('task_rq_lock (89 samples, 0.02%)')" onmouseout="c()">
<title>task_rq_lock (89 samples, 0.02%)</title><rect x="1000.2" y="273" width="0.2" height="15.0" fill="rgb(231,149,47)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__rb_rotate_right (221 samples, 0.05%)')" onmouseout="c()">
<title>__rb_rotate_right (221 samples, 0.05%)</title><rect x="130.7" y="257" width="0.6" height="15.0" fill="rgb(206,117,53)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('sched_clock_tick (348 samples, 0.08%)')" onmouseout="c()">
<title>sched_clock_tick (348 samples, 0.08%)</title><rect x="185.5" y="257" width="1.0" height="15.0" fill="rgb(221,107,31)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('dequeue_task (79 samples, 0.02%)')" onmouseout="c()">
<title>dequeue_task (79 samples, 0.02%)</title><rect x="641.7" y="305" width="0.2" height="15.0" fill="rgb(236,71,32)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('set_next_entity (98 samples, 0.02%)')" onmouseout="c()">
<title>set_next_entity (98 samples, 0.02%)</title><rect x="1134.1" y="417" width="0.2" height="15.0" fill="rgb(232,112,33)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tick_program_event (225 samples, 0.05%)')" onmouseout="c()">
<title>tick_program_event (225 samples, 0.05%)</title><rect x="1153.2" y="385" width="0.6" height="15.0" fill="rgb(219,99,4)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_lapic_sync_to_vapic (6,367 samples, 1.50%)')" onmouseout="c()">
<title>kvm_lapic_sync_to_vapic (6,367 samples, 1.50%)</title><rect x="567.5" y="337" width="17.6" height="15.0" fill="rgb(227,46,35)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__rb_rotate_right (225 samples, 0.05%)')" onmouseout="c()">
<title>__rb_rotate_right (225 samples, 0.05%)</title><rect x="807.4" y="177" width="0.6" height="15.0" fill="rgb(206,117,53)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('proc_single_show (295 samples, 0.07%)')" onmouseout="c()">
<title>proc_single_show (295 samples, 0.07%)</title><rect x="968.9" y="385" width="0.8" height="15.0" fill="rgb(210,183,31)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_load_tls (138 samples, 0.03%)')" onmouseout="c()">
<title>native_load_tls (138 samples, 0.03%)</title><rect x="13.1" y="433" width="0.4" height="15.0" fill="rgb(235,161,10)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__remove_hrtimer (133 samples, 0.03%)')" onmouseout="c()">
<title>__remove_hrtimer (133 samples, 0.03%)</title><rect x="108.7" y="305" width="0.3" height="15.0" fill="rgb(244,9,30)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ns_to_timeval (82 samples, 0.02%)')" onmouseout="c()">
<title>ns_to_timeval (82 samples, 0.02%)</title><rect x="1112.5" y="417" width="0.2" height="15.0" fill="rgb(249,153,53)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_cfs_load (130 samples, 0.03%)')" onmouseout="c()">
<title>update_cfs_load (130 samples, 0.03%)</title><rect x="1020.7" y="209" width="0.4" height="15.0" fill="rgb(242,121,52)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_ts_time_stats (95 samples, 0.02%)')" onmouseout="c()">
<title>update_ts_time_stats (95 samples, 0.02%)</title><rect x="1043.5" y="353" width="0.2" height="15.0" fill="rgb(229,229,29)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('enqueue_hrtimer (579 samples, 0.14%)')" onmouseout="c()">
<title>enqueue_hrtimer (579 samples, 0.14%)</title><rect x="1169.7" y="385" width="1.6" height="15.0" fill="rgb(251,179,15)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('select_task_rq_fair (519 samples, 0.12%)')" onmouseout="c()">
<title>select_task_rq_fair (519 samples, 0.12%)</title><rect x="793.4" y="97" width="1.4" height="15.0" fill="rgb(210,171,2)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('task_waking_fair (116 samples, 0.03%)')" onmouseout="c()">
<title>task_waking_fair (116 samples, 0.03%)</title><rect x="1000.4" y="273" width="0.3" height="15.0" fill="rgb(252,81,50)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('perf_event_task_tick (7,499 samples, 1.76%)')" onmouseout="c()">
<title>perf_event_task_tick (7,499 samples, 1.76%)</title><rect x="202.5" y="241" width="20.8" height="15.0" fill="rgb(224,223,5)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__perf_event_task_sched_out (109 samples, 0.03%)')" onmouseout="c()">
<title>__perf_event_task_sched_out (109 samples, 0.03%)</title><rect x="612.1" y="289" width="0.3" height="15.0" fill="rgb(233,222,44)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_lock (118 samples, 0.03%)')" onmouseout="c()">
<title>_spin_lock (118 samples, 0.03%)</title><rect x="1162.4" y="401" width="0.3" height="15.0" fill="rgb(207,37,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tick_program_event (7,651 samples, 1.80%)')" onmouseout="c()">
<title>tick_program_event (7,651 samples, 1.80%)</title><rect x="305.6" y="305" width="21.2" height="15.0" fill="rgb(219,99,4)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('preempt_notifier_register (56 samples, 0.01%)')" onmouseout="c()">
<title>preempt_notifier_register (56 samples, 0.01%)</title><rect x="607.3" y="321" width="0.2" height="15.0" fill="rgb(231,114,6)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('irq_work_run (196 samples, 0.05%)')" onmouseout="c()">
<title>irq_work_run (196 samples, 0.05%)</title><rect x="146.0" y="273" width="0.5" height="15.0" fill="rgb(252,101,10)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('apic_update_ppr (86 samples, 0.02%)')" onmouseout="c()">
<title>apic_update_ppr (86 samples, 0.02%)</title><rect x="504.8" y="289" width="0.3" height="15.0" fill="rgb(244,88,21)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tick_check_idle (2,178 samples, 0.51%)')" onmouseout="c()">
<title>tick_check_idle (2,178 samples, 0.51%)</title><rect x="1037.7" y="369" width="6.0" height="15.0" fill="rgb(235,88,28)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('drain_array (165 samples, 0.04%)')" onmouseout="c()">
<title>drain_array (165 samples, 0.04%)</title><rect x="976.2" y="401" width="0.5" height="15.0" fill="rgb(218,31,51)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('perf_guest_get_msrs (762 samples, 0.18%)')" onmouseout="c()">
<title>perf_guest_get_msrs (762 samples, 0.18%)</title><rect x="703.6" y="337" width="2.1" height="15.0" fill="rgb(219,77,23)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_sched_clock (126 samples, 0.03%)')" onmouseout="c()">
<title>native_sched_clock (126 samples, 0.03%)</title><rect x="1022.4" y="193" width="0.4" height="15.0" fill="rgb(233,205,14)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('zap_page_range (37 samples, 0.01%)')" onmouseout="c()">
<title>zap_page_range (37 samples, 0.01%)</title><rect x="977.2" y="417" width="0.1" height="15.0" fill="rgb(220,173,50)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('try_to_wake_up (355 samples, 0.08%)')" onmouseout="c()">
<title>try_to_wake_up (355 samples, 0.08%)</title><rect x="1181.6" y="225" width="1.0" height="15.0" fill="rgb(248,130,8)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('x86_pmu_disable (591 samples, 0.14%)')" onmouseout="c()">
<title>x86_pmu_disable (591 samples, 0.14%)</title><rect x="220.5" y="209" width="1.7" height="15.0" fill="rgb(223,177,32)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__apic_accept_irq (2,818 samples, 0.66%)')" onmouseout="c()">
<title>__apic_accept_irq (2,818 samples, 0.66%)</title><rect x="533.7" y="289" width="7.8" height="15.0" fill="rgb(223,215,48)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tick_do_update_jiffies64 (93 samples, 0.02%)')" onmouseout="c()">
<title>tick_do_update_jiffies64 (93 samples, 0.02%)</title><rect x="142.0" y="289" width="0.2" height="15.0" fill="rgb(236,208,31)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('hrtimer_interrupt (127 samples, 0.03%)')" onmouseout="c()">
<title>hrtimer_interrupt (127 samples, 0.03%)</title><rect x="1136.2" y="385" width="0.3" height="15.0" fill="rgb(252,20,51)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('pick_next_task_fair (97 samples, 0.02%)')" onmouseout="c()">
<title>pick_next_task_fair (97 samples, 0.02%)</title><rect x="605.8" y="321" width="0.3" height="15.0" fill="rgb(241,43,16)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ktime_get (132 samples, 0.03%)')" onmouseout="c()">
<title>ktime_get (132 samples, 0.03%)</title><rect x="1035.6" y="337" width="0.3" height="15.0" fill="rgb(213,13,52)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_write_msr_safe (94 samples, 0.02%)')" onmouseout="c()">
<title>native_write_msr_safe (94 samples, 0.02%)</title><rect x="1046.5" y="241" width="0.2" height="15.0" fill="rgb(215,163,22)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('gfn_to_memslot (105 samples, 0.02%)')" onmouseout="c()">
<title>gfn_to_memslot (105 samples, 0.02%)</title><rect x="693.7" y="305" width="0.3" height="15.0" fill="rgb(239,135,10)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__remove_hrtimer (3,219 samples, 0.76%)')" onmouseout="c()">
<title>__remove_hrtimer (3,219 samples, 0.76%)</title><rect x="114.1" y="289" width="8.9" height="15.0" fill="rgb(244,9,30)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('run_local_timers (40 samples, 0.01%)')" onmouseout="c()">
<title>run_local_timers (40 samples, 0.01%)</title><rect x="153.9" y="273" width="0.1" height="15.0" fill="rgb(216,154,32)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('sched_clock (230 samples, 0.05%)')" onmouseout="c()">
<title>sched_clock (230 samples, 0.05%)</title><rect x="682.3" y="257" width="0.6" height="15.0" fill="rgb(207,104,49)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_curr (143 samples, 0.03%)')" onmouseout="c()">
<title>update_curr (143 samples, 0.03%)</title><rect x="792.6" y="33" width="0.4" height="15.0" fill="rgb(218,93,52)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('proc_pid_status (116 samples, 0.03%)')" onmouseout="c()">
<title>proc_pid_status (116 samples, 0.03%)</title><rect x="968.9" y="369" width="0.3" height="15.0" fill="rgb(216,44,16)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('br_handle_frame_finish (41 samples, 0.01%)')" onmouseout="c()">
<title>br_handle_frame_finish (41 samples, 0.01%)</title><rect x="909.2" y="129" width="0.1" height="15.0" fill="rgb(213,109,0)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('remote_function (102 samples, 0.02%)')" onmouseout="c()">
<title>remote_function (102 samples, 0.02%)</title><rect x="1046.4" y="369" width="0.3" height="15.0" fill="rgb(252,215,48)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('system_call_fastpath (45 samples, 0.01%)')" onmouseout="c()">
<title>system_call_fastpath (45 samples, 0.01%)</title><rect x="968.0" y="449" width="0.1" height="15.0" fill="rgb(224,162,39)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tick_program_event (82 samples, 0.02%)')" onmouseout="c()">
<title>tick_program_event (82 samples, 0.02%)</title><rect x="1168.9" y="369" width="0.3" height="15.0" fill="rgb(219,99,4)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('do_softirq (18,415 samples, 4.33%)')" onmouseout="c()">
<title>do_softirq (18,415 samples, 4.33%)</title><rect x="342.0" y="305" width="51.1" height="15.0" fill="rgb(243,77,3)" rx="2" ry="2" />
<text text-anchor="" x="344.965462170445" y="315.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >do_so..</text>
</g>
<g class="func_g" onmouseover="s('vmx_vcpu_load (357 samples, 0.08%)')" onmouseout="c()">
<title>vmx_vcpu_load (357 samples, 0.08%)</title><rect x="686.1" y="305" width="1.0" height="15.0" fill="rgb(243,164,51)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_get_apic_interrupt (830 samples, 0.20%)')" onmouseout="c()">
<title>kvm_get_apic_interrupt (830 samples, 0.20%)</title><rect x="526.1" y="337" width="2.3" height="15.0" fill="rgb(252,74,32)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__do_softirq (103 samples, 0.02%)')" onmouseout="c()">
<title>__do_softirq (103 samples, 0.02%)</title><rect x="1044.4" y="337" width="0.3" height="15.0" fill="rgb(208,218,33)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('pick_next_task_fair (68 samples, 0.02%)')" onmouseout="c()">
<title>pick_next_task_fair (68 samples, 0.02%)</title><rect x="1121.5" y="433" width="0.1" height="15.0" fill="rgb(241,43,16)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('setup_vmcs_config (683 samples, 0.16%)')" onmouseout="c()">
<title>setup_vmcs_config (683 samples, 0.16%)</title><rect x="824.9" y="321" width="1.9" height="15.0" fill="rgb(236,211,23)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_cpu_get_interrupt (3,483 samples, 0.82%)')" onmouseout="c()">
<title>kvm_cpu_get_interrupt (3,483 samples, 0.82%)</title><rect x="495.4" y="337" width="9.7" height="15.0" fill="rgb(215,172,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('check_for_new_grace_period (66 samples, 0.02%)')" onmouseout="c()">
<title>check_for_new_grace_period (66 samples, 0.02%)</title><rect x="378.0" y="241" width="0.2" height="15.0" fill="rgb(217,224,28)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_set_shared_msr (256 samples, 0.06%)')" onmouseout="c()">
<title>kvm_set_shared_msr (256 samples, 0.06%)</title><rect x="836.2" y="321" width="0.7" height="15.0" fill="rgb(252,70,51)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('smp_call_function_single_interrupt (120 samples, 0.03%)')" onmouseout="c()">
<title>smp_call_function_single_interrupt (120 samples, 0.03%)</title><rect x="1046.4" y="401" width="0.3" height="15.0" fill="rgb(216,30,48)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_cr8_intercept (3,028 samples, 0.71%)')" onmouseout="c()">
<title>update_cr8_intercept (3,028 samples, 0.71%)</title><rect x="721.1" y="337" width="8.4" height="15.0" fill="rgb(242,70,44)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_x2apic_msr_write (17,182 samples, 4.04%)')" onmouseout="c()">
<title>kvm_x2apic_msr_write (17,182 samples, 4.04%)</title><rect x="775.2" y="273" width="47.7" height="15.0" fill="rgb(227,74,43)" rx="2" ry="2" />
<text text-anchor="" x="778.220800786533" y="283.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >kvm_..</text>
</g>
<g class="func_g" onmouseover="s('hrtimer_forward (94 samples, 0.02%)')" onmouseout="c()">
<title>hrtimer_forward (94 samples, 0.02%)</title><rect x="1119.6" y="433" width="0.2" height="15.0" fill="rgb(212,164,2)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('mutex_unlock (126 samples, 0.03%)')" onmouseout="c()">
<title>mutex_unlock (126 samples, 0.03%)</title><rect x="605.3" y="321" width="0.4" height="15.0" fill="rgb(235,145,26)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('apic_timer_interrupt (52 samples, 0.01%)')" onmouseout="c()">
<title>apic_timer_interrupt (52 samples, 0.01%)</title><rect x="619.8" y="305" width="0.1" height="15.0" fill="rgb(239,200,41)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('mark_page_dirty (58 samples, 0.01%)')" onmouseout="c()">
<title>mark_page_dirty (58 samples, 0.01%)</title><rect x="695.4" y="321" width="0.2" height="15.0" fill="rgb(223,37,41)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_read_guest_page (116 samples, 0.03%)')" onmouseout="c()">
<title>kvm_read_guest_page (116 samples, 0.03%)</title><rect x="593.2" y="337" width="0.3" height="15.0" fill="rgb(221,162,42)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('menu_reflect (122 samples, 0.03%)')" onmouseout="c()">
<title>menu_reflect (122 samples, 0.03%)</title><rect x="1120.8" y="433" width="0.3" height="15.0" fill="rgb(231,223,36)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('pm_qos_requirement (164 samples, 0.04%)')" onmouseout="c()">
<title>pm_qos_requirement (164 samples, 0.04%)</title><rect x="1111.2" y="401" width="0.4" height="15.0" fill="rgb(235,16,7)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tick_do_update_jiffies64 (1,783 samples, 0.42%)')" onmouseout="c()">
<title>tick_do_update_jiffies64 (1,783 samples, 0.42%)</title><rect x="155.3" y="273" width="5.0" height="15.0" fill="rgb(236,208,31)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('madvise (37 samples, 0.01%)')" onmouseout="c()">
<title>madvise (37 samples, 0.01%)</title><rect x="977.2" y="465" width="0.1" height="15.0" fill="rgb(209,95,10)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('system_call_fastpath (42 samples, 0.01%)')" onmouseout="c()">
<title>system_call_fastpath (42 samples, 0.01%)</title><rect x="980.0" y="449" width="0.1" height="15.0" fill="rgb(224,162,39)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('generic_smp_call_function_single_interrupt (111 samples, 0.03%)')" onmouseout="c()">
<title>generic_smp_call_function_single_interrupt (111 samples, 0.03%)</title><rect x="1046.4" y="385" width="0.3" height="15.0" fill="rgb(210,50,39)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__rcu_process_callbacks (222 samples, 0.05%)')" onmouseout="c()">
<title>__rcu_process_callbacks (222 samples, 0.05%)</title><rect x="351.9" y="257" width="0.6" height="15.0" fill="rgb(211,185,49)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('call_softirq (112 samples, 0.03%)')" onmouseout="c()">
<title>call_softirq (112 samples, 0.03%)</title><rect x="1136.5" y="353" width="0.3" height="15.0" fill="rgb(240,215,19)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_lock_irqsave (294 samples, 0.07%)')" onmouseout="c()">
<title>_spin_lock_irqsave (294 samples, 0.07%)</title><rect x="376.9" y="241" width="0.8" height="15.0" fill="rgb(209,137,45)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rcu_bh_qs (46 samples, 0.01%)')" onmouseout="c()">
<title>rcu_bh_qs (46 samples, 0.01%)</title><rect x="390.1" y="273" width="0.1" height="15.0" fill="rgb(238,81,33)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__remove_hrtimer (45 samples, 0.01%)')" onmouseout="c()">
<title>__remove_hrtimer (45 samples, 0.01%)</title><rect x="1175.7" y="401" width="0.1" height="15.0" fill="rgb(244,9,30)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vmx_interrupt_allowed (674 samples, 0.16%)')" onmouseout="c()">
<title>vmx_interrupt_allowed (674 samples, 0.16%)</title><rect x="931.0" y="353" width="1.8" height="15.0" fill="rgb(206,40,41)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__timer_stats_hrtimer_set_start_info (200 samples, 0.05%)')" onmouseout="c()">
<title>__timer_stats_hrtimer_set_start_info (200 samples, 0.05%)</title><rect x="815.4" y="209" width="0.5" height="15.0" fill="rgb(211,194,18)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('do_softirq (112 samples, 0.03%)')" onmouseout="c()">
<title>do_softirq (112 samples, 0.03%)</title><rect x="1136.5" y="369" width="0.3" height="15.0" fill="rgb(243,77,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('scheduler_ipi (69 samples, 0.02%)')" onmouseout="c()">
<title>scheduler_ipi (69 samples, 0.02%)</title><rect x="1186.9" y="337" width="0.2" height="15.0" fill="rgb(221,38,15)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rh_timer_func (56 samples, 0.01%)')" onmouseout="c()">
<title>rh_timer_func (56 samples, 0.01%)</title><rect x="389.3" y="241" width="0.2" height="15.0" fill="rgb(206,78,34)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('x86_64_start_reservations (3,326 samples, 0.78%)')" onmouseout="c()">
<title>x86_64_start_reservations (3,326 samples, 0.78%)</title><rect x="1180.8" y="449" width="9.2" height="15.0" fill="rgb(215,162,23)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vmx_vcpu_run (481 samples, 0.11%)')" onmouseout="c()">
<title>vmx_vcpu_run (481 samples, 0.11%)</title><rect x="933.7" y="353" width="1.3" height="15.0" fill="rgb(235,49,53)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('skip_emulated_instruction (458 samples, 0.11%)')" onmouseout="c()">
<title>skip_emulated_instruction (458 samples, 0.11%)</title><rect x="826.8" y="321" width="1.3" height="15.0" fill="rgb(220,204,48)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('hrtimer_try_to_cancel (1,571 samples, 0.37%)')" onmouseout="c()">
<title>hrtimer_try_to_cancel (1,571 samples, 0.37%)</title><rect x="780.9" y="225" width="4.4" height="15.0" fill="rgb(221,57,30)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_apic_mem_write (94 samples, 0.02%)')" onmouseout="c()">
<title>native_apic_mem_write (94 samples, 0.02%)</title><rect x="1166.9" y="305" width="0.3" height="15.0" fill="rgb(209,177,4)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rb_next (95 samples, 0.02%)')" onmouseout="c()">
<title>rb_next (95 samples, 0.02%)</title><rect x="1031.6" y="353" width="0.3" height="15.0" fill="rgb(251,1,22)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_lock_irqsave (608 samples, 0.14%)')" onmouseout="c()">
<title>_spin_lock_irqsave (608 samples, 0.14%)</title><rect x="361.0" y="225" width="1.7" height="15.0" fill="rgb(209,137,45)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('account_entity_enqueue (71 samples, 0.02%)')" onmouseout="c()">
<title>account_entity_enqueue (71 samples, 0.02%)</title><rect x="625.0" y="241" width="0.2" height="15.0" fill="rgb(224,51,23)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('uhci_hub_status_data (49 samples, 0.01%)')" onmouseout="c()">
<title>uhci_hub_status_data (49 samples, 0.01%)</title><rect x="389.3" y="209" width="0.2" height="15.0" fill="rgb(245,1,53)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vmx_set_interrupt_shadow (94 samples, 0.02%)')" onmouseout="c()">
<title>vmx_set_interrupt_shadow (94 samples, 0.02%)</title><rect x="754.8" y="289" width="0.2" height="15.0" fill="rgb(218,142,39)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('apic_timer_interrupt (872 samples, 0.21%)')" onmouseout="c()">
<title>apic_timer_interrupt (872 samples, 0.21%)</title><rect x="1181.0" y="369" width="2.4" height="15.0" fill="rgb(239,200,41)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('memcpy (220 samples, 0.05%)')" onmouseout="c()">
<title>memcpy (220 samples, 0.05%)</title><rect x="604.2" y="321" width="0.6" height="15.0" fill="rgb(218,208,31)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('smp_apic_timer_interrupt (258 samples, 0.06%)')" onmouseout="c()">
<title>smp_apic_timer_interrupt (258 samples, 0.06%)</title><rect x="1136.2" y="401" width="0.7" height="15.0" fill="rgb(205,22,48)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('generic_file_buffered_write (214 samples, 0.05%)')" onmouseout="c()">
<title>generic_file_buffered_write (214 samples, 0.05%)</title><rect x="970.4" y="289" width="0.6" height="15.0" fill="rgb(206,181,37)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__do_softirq (1,282 samples, 0.30%)')" onmouseout="c()">
<title>__do_softirq (1,282 samples, 0.30%)</title><rect x="1113.4" y="321" width="3.5" height="15.0" fill="rgb(208,218,33)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tick_dev_program_event (5,126 samples, 1.21%)')" onmouseout="c()">
<title>tick_dev_program_event (5,126 samples, 1.21%)</title><rect x="312.6" y="289" width="14.2" height="15.0" fill="rgb(218,118,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_lock_irqsave (199 samples, 0.05%)')" onmouseout="c()">
<title>_spin_lock_irqsave (199 samples, 0.05%)</title><rect x="608.6" y="305" width="0.5" height="15.0" fill="rgb(209,137,45)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__local_bh_enable (201 samples, 0.05%)')" onmouseout="c()">
<title>__local_bh_enable (201 samples, 0.05%)</title><rect x="389.5" y="273" width="0.5" height="15.0" fill="rgb(250,106,10)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_curr (1,525 samples, 0.36%)')" onmouseout="c()">
<title>update_curr (1,525 samples, 0.36%)</title><rect x="634.6" y="241" width="4.2" height="15.0" fill="rgb(218,93,52)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rb_insert_color (260 samples, 0.06%)')" onmouseout="c()">
<title>rb_insert_color (260 samples, 0.06%)</title><rect x="814.4" y="193" width="0.8" height="15.0" fill="rgb(222,158,21)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_cfs_load (762 samples, 0.18%)')" onmouseout="c()">
<title>update_cfs_load (762 samples, 0.18%)</title><rect x="678.5" y="289" width="2.1" height="15.0" fill="rgb(242,121,52)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('run_timer_softirq (449 samples, 0.11%)')" onmouseout="c()">
<title>run_timer_softirq (449 samples, 0.11%)</title><rect x="391.8" y="273" width="1.3" height="15.0" fill="rgb(219,191,6)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tick_dev_program_event (415 samples, 0.10%)')" onmouseout="c()">
<title>tick_dev_program_event (415 samples, 0.10%)</title><rect x="1166.3" y="337" width="1.2" height="15.0" fill="rgb(218,118,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('scheduler_tick (342 samples, 0.08%)')" onmouseout="c()">
<title>scheduler_tick (342 samples, 0.08%)</title><rect x="154.4" y="273" width="0.9" height="15.0" fill="rgb(240,30,11)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_rq_clock (64 samples, 0.02%)')" onmouseout="c()">
<title>update_rq_clock (64 samples, 0.02%)</title><rect x="1023.1" y="241" width="0.2" height="15.0" fill="rgb(227,206,0)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ktime_get (41 samples, 0.01%)')" onmouseout="c()">
<title>ktime_get (41 samples, 0.01%)</title><rect x="1037.2" y="369" width="0.1" height="15.0" fill="rgb(213,13,52)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('do_futex (53 samples, 0.01%)')" onmouseout="c()">
<title>do_futex (53 samples, 0.01%)</title><rect x="980.1" y="417" width="0.2" height="15.0" fill="rgb(223,180,37)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_timer_fn (1,199 samples, 0.28%)')" onmouseout="c()">
<title>kvm_timer_fn (1,199 samples, 0.28%)</title><rect x="132.4" y="289" width="3.3" height="15.0" fill="rgb(249,104,46)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('finish_task_switch (400 samples, 0.09%)')" onmouseout="c()">
<title>finish_task_switch (400 samples, 0.09%)</title><rect x="673.0" y="305" width="1.1" height="15.0" fill="rgb(221,162,9)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_lock (386 samples, 0.09%)')" onmouseout="c()">
<title>_spin_lock (386 samples, 0.09%)</title><rect x="1027.2" y="241" width="1.1" height="15.0" fill="rgb(207,37,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_cfs_shares (56 samples, 0.01%)')" onmouseout="c()">
<title>update_cfs_shares (56 samples, 0.01%)</title><rect x="1021.1" y="209" width="0.1" height="15.0" fill="rgb(240,28,1)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_sched_clock (107 samples, 0.03%)')" onmouseout="c()">
<title>native_sched_clock (107 samples, 0.03%)</title><rect x="640.6" y="241" width="0.3" height="15.0" fill="rgb(233,205,14)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_apic_has_interrupt (2,670 samples, 0.63%)')" onmouseout="c()">
<title>kvm_apic_has_interrupt (2,670 samples, 0.63%)</title><rect x="512.3" y="321" width="7.4" height="15.0" fill="rgb(217,29,8)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('pick_next_task_fair (110 samples, 0.03%)')" onmouseout="c()">
<title>pick_next_task_fair (110 samples, 0.03%)</title><rect x="1187.8" y="369" width="0.4" height="15.0" fill="rgb(241,43,16)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('hrtimer_cancel (2,262 samples, 0.53%)')" onmouseout="c()">
<title>hrtimer_cancel (2,262 samples, 0.53%)</title><rect x="780.1" y="241" width="6.3" height="15.0" fill="rgb(211,94,12)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('futex_wake (38 samples, 0.01%)')" onmouseout="c()">
<title>futex_wake (38 samples, 0.01%)</title><rect x="980.0" y="401" width="0.1" height="15.0" fill="rgb(243,120,7)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('[unknown] (123 samples, 0.03%)')" onmouseout="c()">
<title>[unknown] (123 samples, 0.03%)</title><rect x="1180.0" y="449" width="0.3" height="15.0" fill="rgb(229,75,51)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('copy_to_user (666 samples, 0.16%)')" onmouseout="c()">
<title>copy_to_user (666 samples, 0.16%)</title><rect x="573.1" y="321" width="1.9" height="15.0" fill="rgb(206,70,41)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('perf_guest_get_msrs (1,978 samples, 0.47%)')" onmouseout="c()">
<title>perf_guest_get_msrs (1,978 samples, 0.47%)</title><rect x="879.6" y="321" width="5.5" height="15.0" fill="rgb(219,77,23)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('find_next_bit (40 samples, 0.01%)')" onmouseout="c()">
<title>find_next_bit (40 samples, 0.01%)</title><rect x="202.0" y="193" width="0.2" height="15.0" fill="rgb(231,134,31)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('intel_pmu_enable_all (582 samples, 0.14%)')" onmouseout="c()">
<title>intel_pmu_enable_all (582 samples, 0.14%)</title><rect x="218.8" y="161" width="1.6" height="15.0" fill="rgb(252,128,48)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('printk_needs_cpu (81 samples, 0.02%)')" onmouseout="c()">
<title>printk_needs_cpu (81 samples, 0.02%)</title><rect x="1176.6" y="417" width="0.2" height="15.0" fill="rgb(225,165,39)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('check_preempt_curr (76 samples, 0.02%)')" onmouseout="c()">
<title>check_preempt_curr (76 samples, 0.02%)</title><rect x="793.1" y="97" width="0.3" height="15.0" fill="rgb(211,70,34)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('hrtimer_forward (41 samples, 0.01%)')" onmouseout="c()">
<title>hrtimer_forward (41 samples, 0.01%)</title><rect x="1146.1" y="417" width="0.1" height="15.0" fill="rgb(212,164,2)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__wake_up (1,524 samples, 0.36%)')" onmouseout="c()">
<title>__wake_up (1,524 samples, 0.36%)</title><rect x="791.1" y="177" width="4.2" height="15.0" fill="rgb(226,70,29)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rb_insert_color (131 samples, 0.03%)')" onmouseout="c()">
<title>rb_insert_color (131 samples, 0.03%)</title><rect x="1020.1" y="209" width="0.4" height="15.0" fill="rgb(222,158,21)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vmx_save_host_state (300 samples, 0.07%)')" onmouseout="c()">
<title>vmx_save_host_state (300 samples, 0.07%)</title><rect x="932.8" y="353" width="0.9" height="15.0" fill="rgb(229,161,33)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('run_rebalance_domains (449 samples, 0.11%)')" onmouseout="c()">
<title>run_rebalance_domains (449 samples, 0.11%)</title><rect x="380.3" y="257" width="1.2" height="15.0" fill="rgb(234,111,10)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('menu_select (95 samples, 0.02%)')" onmouseout="c()">
<title>menu_select (95 samples, 0.02%)</title><rect x="1121.1" y="433" width="0.3" height="15.0" fill="rgb(249,45,6)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('gfn_to_hva (327 samples, 0.08%)')" onmouseout="c()">
<title>gfn_to_hva (327 samples, 0.08%)</title><rect x="589.5" y="305" width="1.0" height="15.0" fill="rgb(234,174,13)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('x86_64_start_kernel (3,326 samples, 0.78%)')" onmouseout="c()">
<title>x86_64_start_kernel (3,326 samples, 0.78%)</title><rect x="1180.8" y="465" width="9.2" height="15.0" fill="rgb(230,171,2)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__GI___libc_open (131 samples, 0.03%)')" onmouseout="c()">
<title>__GI___libc_open (131 samples, 0.03%)</title><rect x="968.2" y="465" width="0.3" height="15.0" fill="rgb(247,135,45)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_apic_has_interrupt (581 samples, 0.14%)')" onmouseout="c()">
<title>kvm_apic_has_interrupt (581 samples, 0.14%)</title><rect x="600.8" y="289" width="1.6" height="15.0" fill="rgb(217,29,8)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_lock (68 samples, 0.02%)')" onmouseout="c()">
<title>_spin_lock (68 samples, 0.02%)</title><rect x="595.5" y="321" width="0.2" height="15.0" fill="rgb(207,37,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__rb_rotate_left (90 samples, 0.02%)')" onmouseout="c()">
<title>__rb_rotate_left (90 samples, 0.02%)</title><rect x="1170.9" y="353" width="0.2" height="15.0" fill="rgb(251,185,40)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_cpu_has_pending_timer (1,177 samples, 0.28%)')" onmouseout="c()">
<title>kvm_cpu_has_pending_timer (1,177 samples, 0.28%)</title><rect x="890.5" y="353" width="3.2" height="15.0" fill="rgb(221,78,37)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_unlock_irqrestore (134 samples, 0.03%)')" onmouseout="c()">
<title>_spin_unlock_irqrestore (134 samples, 0.03%)</title><rect x="609.1" y="305" width="0.4" height="15.0" fill="rgb(250,72,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_lapic_sync_from_vapic (691 samples, 0.16%)')" onmouseout="c()">
<title>kvm_lapic_sync_from_vapic (691 samples, 0.16%)</title><rect x="896.8" y="353" width="2.0" height="15.0" fill="rgb(239,223,14)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('lock_hrtimer_base (85 samples, 0.02%)')" onmouseout="c()">
<title>lock_hrtimer_base (85 samples, 0.02%)</title><rect x="1145.1" y="385" width="0.3" height="15.0" fill="rgb(232,201,8)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ktime_get (73 samples, 0.02%)')" onmouseout="c()">
<title>ktime_get (73 samples, 0.02%)</title><rect x="1143.0" y="321" width="0.2" height="15.0" fill="rgb(213,13,52)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_smp_send_reschedule (42 samples, 0.01%)')" onmouseout="c()">
<title>native_smp_send_reschedule (42 samples, 0.01%)</title><rect x="795.4" y="177" width="0.1" height="15.0" fill="rgb(218,74,11)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_apic_set_irq (1,760 samples, 0.41%)')" onmouseout="c()">
<title>kvm_apic_set_irq (1,760 samples, 0.41%)</title><rect x="790.7" y="225" width="4.8" height="15.0" fill="rgb(231,19,41)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('mcount (42 samples, 0.01%)')" onmouseout="c()">
<title>mcount (42 samples, 0.01%)</title><rect x="1118.7" y="417" width="0.1" height="15.0" fill="rgb(206,108,44)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('irq_work_run (497 samples, 0.12%)')" onmouseout="c()">
<title>irq_work_run (497 samples, 0.12%)</title><rect x="170.5" y="257" width="1.4" height="15.0" fill="rgb(252,101,10)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('sys_open (124 samples, 0.03%)')" onmouseout="c()">
<title>sys_open (124 samples, 0.03%)</title><rect x="968.2" y="433" width="0.3" height="15.0" fill="rgb(246,217,12)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ps_read_process (42 samples, 0.01%)')" onmouseout="c()">
<title>ps_read_process (42 samples, 0.01%)</title><rect x="1180.2" y="417" width="0.1" height="15.0" fill="rgb(224,223,21)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_lock (52 samples, 0.01%)')" onmouseout="c()">
<title>_spin_lock (52 samples, 0.01%)</title><rect x="997.9" y="353" width="0.2" height="15.0" fill="rgb(207,37,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ktime_get (89 samples, 0.02%)')" onmouseout="c()">
<title>ktime_get (89 samples, 0.02%)</title><rect x="1167.2" y="321" width="0.2" height="15.0" fill="rgb(213,13,52)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('set_next_entity (1,340 samples, 0.32%)')" onmouseout="c()">
<title>set_next_entity (1,340 samples, 0.32%)</title><rect x="1129.9" y="401" width="3.7" height="15.0" fill="rgb(232,112,33)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_cfs_load (684 samples, 0.16%)')" onmouseout="c()">
<title>update_cfs_load (684 samples, 0.16%)</title><rect x="233.0" y="225" width="1.9" height="15.0" fill="rgb(242,121,52)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rcu_irq_enter (329 samples, 0.08%)')" onmouseout="c()">
<title>rcu_irq_enter (329 samples, 0.08%)</title><rect x="400.1" y="321" width="0.9" height="15.0" fill="rgb(233,147,36)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('hrtimer_run_queues (341 samples, 0.08%)')" onmouseout="c()">
<title>hrtimer_run_queues (341 samples, 0.08%)</title><rect x="180.4" y="241" width="0.9" height="15.0" fill="rgb(250,176,31)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('call_softirq (103 samples, 0.02%)')" onmouseout="c()">
<title>call_softirq (103 samples, 0.02%)</title><rect x="1044.4" y="353" width="0.3" height="15.0" fill="rgb(240,215,19)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ktime_get (77 samples, 0.02%)')" onmouseout="c()">
<title>ktime_get (77 samples, 0.02%)</title><rect x="1153.6" y="353" width="0.2" height="15.0" fill="rgb(213,13,52)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('nr_iowait_cpu (169 samples, 0.04%)')" onmouseout="c()">
<title>nr_iowait_cpu (169 samples, 0.04%)</title><rect x="1043.0" y="321" width="0.5" height="15.0" fill="rgb(208,117,1)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_cfs_load (47 samples, 0.01%)')" onmouseout="c()">
<title>update_cfs_load (47 samples, 0.01%)</title><rect x="381.2" y="209" width="0.1" height="15.0" fill="rgb(242,121,52)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('sys_futex (54 samples, 0.01%)')" onmouseout="c()">
<title>sys_futex (54 samples, 0.01%)</title><rect x="980.1" y="433" width="0.2" height="15.0" fill="rgb(236,194,21)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_set_msr_common (19,717 samples, 4.64%)')" onmouseout="c()">
<title>kvm_set_msr_common (19,717 samples, 4.64%)</title><rect x="768.2" y="289" width="54.7" height="15.0" fill="rgb(225,95,21)" rx="2" ry="2" />
<text text-anchor="" x="771.187822380909" y="299.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >kvm_s..</text>
</g>
<g class="func_g" onmouseover="s('handle_halt (274 samples, 0.06%)')" onmouseout="c()">
<title>handle_halt (274 samples, 0.06%)</title><rect x="754.4" y="321" width="0.7" height="15.0" fill="rgb(226,182,42)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__run_hrtimer (486 samples, 0.11%)')" onmouseout="c()">
<title>__run_hrtimer (486 samples, 0.11%)</title><rect x="1181.4" y="321" width="1.3" height="15.0" fill="rgb(208,100,50)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('system_call_fastpath (37 samples, 0.01%)')" onmouseout="c()">
<title>system_call_fastpath (37 samples, 0.01%)</title><rect x="977.2" y="449" width="0.1" height="15.0" fill="rgb(224,162,39)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rcu_irq_enter (134 samples, 0.03%)')" onmouseout="c()">
<title>rcu_irq_enter (134 samples, 0.03%)</title><rect x="1037.3" y="369" width="0.4" height="15.0" fill="rgb(233,147,36)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('cpumask_next_and (45 samples, 0.01%)')" onmouseout="c()">
<title>cpumask_next_and (45 samples, 0.01%)</title><rect x="1114.9" y="257" width="0.1" height="15.0" fill="rgb(231,93,24)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('lock_hrtimer_base (165 samples, 0.04%)')" onmouseout="c()">
<title>lock_hrtimer_base (165 samples, 0.04%)</title><rect x="1171.5" y="385" width="0.5" height="15.0" fill="rgb(232,201,8)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__hrtimer_start_range_ns (113 samples, 0.03%)')" onmouseout="c()">
<title>__hrtimer_start_range_ns (113 samples, 0.03%)</title><rect x="1188.6" y="353" width="0.3" height="15.0" fill="rgb(250,176,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_write_msr_safe (1,198 samples, 0.28%)')" onmouseout="c()">
<title>native_write_msr_safe (1,198 samples, 0.28%)</title><rect x="213.7" y="161" width="3.3" height="15.0" fill="rgb(215,163,22)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('lapic_is_periodic (156 samples, 0.04%)')" onmouseout="c()">
<title>lapic_is_periodic (156 samples, 0.04%)</title><rect x="1030.2" y="337" width="0.4" height="15.0" fill="rgb(205,14,15)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_lock_irq (62 samples, 0.01%)')" onmouseout="c()">
<title>_spin_lock_irq (62 samples, 0.01%)</title><rect x="1126.0" y="417" width="0.2" height="15.0" fill="rgb(221,92,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('mutex_lock (111 samples, 0.03%)')" onmouseout="c()">
<title>mutex_lock (111 samples, 0.03%)</title><rect x="605.0" y="321" width="0.3" height="15.0" fill="rgb(238,5,22)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__timer_stats_hrtimer_set_start_info (42 samples, 0.01%)')" onmouseout="c()">
<title>__timer_stats_hrtimer_set_start_info (42 samples, 0.01%)</title><rect x="1175.8" y="401" width="0.1" height="15.0" fill="rgb(211,194,18)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_read_guest_cached (797 samples, 0.19%)')" onmouseout="c()">
<title>kvm_read_guest_cached (797 samples, 0.19%)</title><rect x="590.9" y="337" width="2.3" height="15.0" fill="rgb(224,155,18)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('account_cfs_rq_runtime (43 samples, 0.01%)')" onmouseout="c()">
<title>account_cfs_rq_runtime (43 samples, 0.01%)</title><rect x="634.0" y="209" width="0.1" height="15.0" fill="rgb(230,19,12)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('apic_update_ppr (259 samples, 0.06%)')" onmouseout="c()">
<title>apic_update_ppr (259 samples, 0.06%)</title><rect x="502.9" y="305" width="0.7" height="15.0" fill="rgb(244,88,21)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rcu_sched_qs (563 samples, 0.13%)')" onmouseout="c()">
<title>rcu_sched_qs (563 samples, 0.13%)</title><rect x="904.3" y="353" width="1.6" height="15.0" fill="rgb(239,116,8)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_read_tsc (210 samples, 0.05%)')" onmouseout="c()">
<title>native_read_tsc (210 samples, 0.05%)</title><rect x="325.4" y="241" width="0.6" height="15.0" fill="rgb(249,99,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tick_sched_timer (40 samples, 0.01%)')" onmouseout="c()">
<title>tick_sched_timer (40 samples, 0.01%)</title><rect x="488.0" y="273" width="0.1" height="15.0" fill="rgb(213,15,28)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rb_erase (1,186 samples, 0.28%)')" onmouseout="c()">
<title>rb_erase (1,186 samples, 0.28%)</title><rect x="1172.0" y="385" width="3.3" height="15.0" fill="rgb(236,5,12)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('irq_exit (406 samples, 0.10%)')" onmouseout="c()">
<title>irq_exit (406 samples, 0.10%)</title><rect x="987.2" y="401" width="1.2" height="15.0" fill="rgb(216,86,39)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__switch_to (678 samples, 0.16%)')" onmouseout="c()">
<title>__switch_to (678 samples, 0.16%)</title><rect x="971.2" y="465" width="1.9" height="15.0" fill="rgb(218,57,18)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('path_walk (68 samples, 0.02%)')" onmouseout="c()">
<title>path_walk (68 samples, 0.02%)</title><rect x="968.2" y="369" width="0.2" height="15.0" fill="rgb(244,163,14)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('sched_clock (489 samples, 0.12%)')" onmouseout="c()">
<title>sched_clock (489 samples, 0.12%)</title><rect x="286.3" y="209" width="1.4" height="15.0" fill="rgb(207,104,49)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('irq_enter (132 samples, 0.03%)')" onmouseout="c()">
<title>irq_enter (132 samples, 0.03%)</title><rect x="37.4" y="337" width="0.4" height="15.0" fill="rgb(247,79,15)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('yield_to (270 samples, 0.06%)')" onmouseout="c()">
<title>yield_to (270 samples, 0.06%)</title><rect x="755.8" y="289" width="0.7" height="15.0" fill="rgb(209,32,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('schedule (76 samples, 0.02%)')" onmouseout="c()">
<title>schedule (76 samples, 0.02%)</title><rect x="756.3" y="273" width="0.2" height="15.0" fill="rgb(222,218,51)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('do_task_stat (161 samples, 0.04%)')" onmouseout="c()">
<title>do_task_stat (161 samples, 0.04%)</title><rect x="969.2" y="353" width="0.5" height="15.0" fill="rgb(221,62,45)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rb_insert_color (78 samples, 0.02%)')" onmouseout="c()">
<title>rb_insert_color (78 samples, 0.02%)</title><rect x="1175.3" y="385" width="0.2" height="15.0" fill="rgb(222,158,21)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('skip_emulated_instruction (178 samples, 0.04%)')" onmouseout="c()">
<title>skip_emulated_instruction (178 samples, 0.04%)</title><rect x="754.5" y="305" width="0.5" height="15.0" fill="rgb(220,204,48)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_load_tr_desc (38 samples, 0.01%)')" onmouseout="c()">
<title>native_load_tr_desc (38 samples, 0.01%)</title><rect x="690.5" y="273" width="0.1" height="15.0" fill="rgb(229,52,27)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_read_cr0 (619 samples, 0.15%)')" onmouseout="c()">
<title>native_read_cr0 (619 samples, 0.15%)</title><rect x="698.9" y="337" width="1.7" height="15.0" fill="rgb(248,172,48)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('clockevents_program_event (85 samples, 0.02%)')" onmouseout="c()">
<title>clockevents_program_event (85 samples, 0.02%)</title><rect x="1033.8" y="353" width="0.2" height="15.0" fill="rgb(233,130,45)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rcu_irq_enter (574 samples, 0.14%)')" onmouseout="c()">
<title>rcu_irq_enter (574 samples, 0.14%)</title><rect x="332.1" y="305" width="1.6" height="15.0" fill="rgb(233,147,36)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__remove_hrtimer (63 samples, 0.01%)')" onmouseout="c()">
<title>__remove_hrtimer (63 samples, 0.01%)</title><rect x="1189.4" y="337" width="0.2" height="15.0" fill="rgb(244,9,30)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('try_to_wake_up (82 samples, 0.02%)')" onmouseout="c()">
<title>try_to_wake_up (82 samples, 0.02%)</title><rect x="1028.9" y="289" width="0.2" height="15.0" fill="rgb(248,130,8)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_write_guest_cached (873 samples, 0.21%)')" onmouseout="c()">
<title>kvm_write_guest_cached (873 samples, 0.21%)</title><rect x="582.5" y="321" width="2.4" height="15.0" fill="rgb(227,3,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tick_program_event (37 samples, 0.01%)')" onmouseout="c()">
<title>tick_program_event (37 samples, 0.01%)</title><rect x="1182.8" y="321" width="0.1" height="15.0" fill="rgb(219,99,4)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('put_prev_task_fair (48 samples, 0.01%)')" onmouseout="c()">
<title>put_prev_task_fair (48 samples, 0.01%)</title><rect x="609.5" y="321" width="0.1" height="15.0" fill="rgb(238,46,35)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('sys_madvise (37 samples, 0.01%)')" onmouseout="c()">
<title>sys_madvise (37 samples, 0.01%)</title><rect x="977.2" y="433" width="0.1" height="15.0" fill="rgb(245,84,0)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_read_tsc (43 samples, 0.01%)')" onmouseout="c()">
<title>native_read_tsc (43 samples, 0.01%)</title><rect x="688.1" y="273" width="0.1" height="15.0" fill="rgb(249,99,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('read_tsc (938 samples, 0.22%)')" onmouseout="c()">
<title>read_tsc (938 samples, 0.22%)</title><rect x="301.1" y="305" width="2.6" height="15.0" fill="rgb(233,66,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vmx_vcpu_put (924 samples, 0.22%)')" onmouseout="c()">
<title>vmx_vcpu_put (924 samples, 0.22%)</title><rect x="688.2" y="289" width="2.6" height="15.0" fill="rgb(225,213,42)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('reschedule_interrupt (1,370 samples, 0.32%)')" onmouseout="c()">
<title>reschedule_interrupt (1,370 samples, 0.32%)</title><rect x="1113.2" y="417" width="3.8" height="15.0" fill="rgb(213,96,24)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_lapic_get_cr8 (253 samples, 0.06%)')" onmouseout="c()">
<title>kvm_lapic_get_cr8 (253 samples, 0.06%)</title><rect x="726.3" y="321" width="0.7" height="15.0" fill="rgb(234,219,18)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('profile_tick (103 samples, 0.02%)')" onmouseout="c()">
<title>profile_tick (103 samples, 0.02%)</title><rect x="138.2" y="289" width="0.3" height="15.0" fill="rgb(220,196,40)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('select_idle_sibling (140 samples, 0.03%)')" onmouseout="c()">
<title>select_idle_sibling (140 samples, 0.03%)</title><rect x="1026.2" y="241" width="0.4" height="15.0" fill="rgb(233,205,23)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('irq_exit (118 samples, 0.03%)')" onmouseout="c()">
<title>irq_exit (118 samples, 0.03%)</title><rect x="1136.5" y="385" width="0.4" height="15.0" fill="rgb(216,86,39)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_lock (491 samples, 0.12%)')" onmouseout="c()">
<title>_spin_lock (491 samples, 0.12%)</title><rect x="209.3" y="209" width="1.4" height="15.0" fill="rgb(207,37,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('copy_user_generic_string (346 samples, 0.08%)')" onmouseout="c()">
<title>copy_user_generic_string (346 samples, 0.08%)</title><rect x="565.3" y="305" width="1.0" height="15.0" fill="rgb(228,65,19)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_sched_clock (50 samples, 0.01%)')" onmouseout="c()">
<title>native_sched_clock (50 samples, 0.01%)</title><rect x="1177.6" y="385" width="0.2" height="15.0" fill="rgb(233,205,14)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('idle_cpu (524 samples, 0.12%)')" onmouseout="c()">
<title>idle_cpu (524 samples, 0.12%)</title><rect x="327.2" y="321" width="1.5" height="15.0" fill="rgb(231,163,39)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__rb_rotate_right (60 samples, 0.01%)')" onmouseout="c()">
<title>__rb_rotate_right (60 samples, 0.01%)</title><rect x="1171.1" y="353" width="0.2" height="15.0" fill="rgb(206,117,53)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_apic_local_deliver (514 samples, 0.12%)')" onmouseout="c()">
<title>kvm_apic_local_deliver (514 samples, 0.12%)</title><rect x="529.7" y="321" width="1.4" height="15.0" fill="rgb(221,119,6)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vmx_set_interrupt_shadow (369 samples, 0.09%)')" onmouseout="c()">
<title>vmx_set_interrupt_shadow (369 samples, 0.09%)</title><rect x="761.4" y="289" width="1.0" height="15.0" fill="rgb(218,142,39)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('notifier_call_chain (102 samples, 0.02%)')" onmouseout="c()">
<title>notifier_call_chain (102 samples, 0.02%)</title><rect x="1118.4" y="401" width="0.3" height="15.0" fill="rgb(240,117,28)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('bnx2_poll (124 samples, 0.03%)')" onmouseout="c()">
<title>bnx2_poll (124 samples, 0.03%)</title><rect x="909.0" y="241" width="0.4" height="15.0" fill="rgb(238,178,49)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('enqueue_task_fair (527 samples, 0.12%)')" onmouseout="c()">
<title>enqueue_task_fair (527 samples, 0.12%)</title><rect x="791.6" y="65" width="1.4" height="15.0" fill="rgb(251,134,19)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('intel_guest_get_msrs (1,334 samples, 0.31%)')" onmouseout="c()">
<title>intel_guest_get_msrs (1,334 samples, 0.31%)</title><rect x="881.4" y="305" width="3.7" height="15.0" fill="rgb(223,196,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_arch_vcpu_load (711 samples, 0.17%)')" onmouseout="c()">
<title>kvm_arch_vcpu_load (711 samples, 0.17%)</title><rect x="683.4" y="305" width="2.0" height="15.0" fill="rgb(215,87,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_lapic_sync_from_vapic (6,083 samples, 1.43%)')" onmouseout="c()">
<title>kvm_lapic_sync_from_vapic (6,083 samples, 1.43%)</title><rect x="550.6" y="337" width="16.9" height="15.0" fill="rgb(239,223,14)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('hrtimer_interrupt (81 samples, 0.02%)')" onmouseout="c()">
<title>hrtimer_interrupt (81 samples, 0.02%)</title><rect x="986.8" y="401" width="0.3" height="15.0" fill="rgb(252,20,51)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('x86_pmu_commit_txn (99 samples, 0.02%)')" onmouseout="c()">
<title>x86_pmu_commit_txn (99 samples, 0.02%)</title><rect x="1046.4" y="321" width="0.3" height="15.0" fill="rgb(234,33,2)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_apic_accept_pic_intr (163 samples, 0.04%)')" onmouseout="c()">
<title>kvm_apic_accept_pic_intr (163 samples, 0.04%)</title><rect x="600.4" y="289" width="0.4" height="15.0" fill="rgb(249,176,35)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_lock (43 samples, 0.01%)')" onmouseout="c()">
<title>_spin_lock (43 samples, 0.01%)</title><rect x="1002.3" y="257" width="0.1" height="15.0" fill="rgb(207,37,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rcu_check_callbacks (2,029 samples, 0.48%)')" onmouseout="c()">
<title>rcu_check_callbacks (2,029 samples, 0.48%)</title><rect x="174.6" y="257" width="5.6" height="15.0" fill="rgb(244,188,26)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('filp_close (43 samples, 0.01%)')" onmouseout="c()">
<title>filp_close (43 samples, 0.01%)</title><rect x="968.0" y="417" width="0.1" height="15.0" fill="rgb(225,162,37)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('select_nohz_load_balancer (774 samples, 0.18%)')" onmouseout="c()">
<title>select_nohz_load_balancer (774 samples, 0.18%)</title><rect x="1156.2" y="417" width="2.1" height="15.0" fill="rgb(224,12,41)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__hrtimer_start_range_ns (109 samples, 0.03%)')" onmouseout="c()">
<title>__hrtimer_start_range_ns (109 samples, 0.03%)</title><rect x="1138.0" y="417" width="0.3" height="15.0" fill="rgb(250,176,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('effective_load (417 samples, 0.10%)')" onmouseout="c()">
<title>effective_load (417 samples, 0.10%)</title><rect x="793.6" y="81" width="1.2" height="15.0" fill="rgb(239,60,39)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('clockevents_program_event (41 samples, 0.01%)')" onmouseout="c()">
<title>clockevents_program_event (41 samples, 0.01%)</title><rect x="1153.5" y="353" width="0.1" height="15.0" fill="rgb(233,130,45)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('reschedule_interrupt (75 samples, 0.02%)')" onmouseout="c()">
<title>reschedule_interrupt (75 samples, 0.02%)</title><rect x="905.9" y="353" width="0.2" height="15.0" fill="rgb(213,96,24)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_ioapic_handles_vector (377 samples, 0.09%)')" onmouseout="c()">
<title>kvm_ioapic_handles_vector (377 samples, 0.09%)</title><rect x="562.0" y="321" width="1.0" height="15.0" fill="rgb(233,138,41)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('sched_clock_tick (435 samples, 0.10%)')" onmouseout="c()">
<title>sched_clock_tick (435 samples, 0.10%)</title><rect x="223.4" y="241" width="1.3" height="15.0" fill="rgb(221,107,31)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('perf_pmu_disable (392 samples, 0.09%)')" onmouseout="c()">
<title>perf_pmu_disable (392 samples, 0.09%)</title><rect x="222.2" y="225" width="1.1" height="15.0" fill="rgb(218,159,27)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('apic_timer_interrupt (22,349 samples, 5.26%)')" onmouseout="c()">
<title>apic_timer_interrupt (22,349 samples, 5.26%)</title><rect x="984.4" y="417" width="62.0" height="15.0" fill="rgb(239,200,41)" rx="2" ry="2" />
<text text-anchor="" x="987.364481826713" y="427.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >apic_t..</text>
</g>
<g class="func_g" onmouseover="s('futex_wait_queue_me (43 samples, 0.01%)')" onmouseout="c()">
<title>futex_wait_queue_me (43 samples, 0.01%)</title><rect x="980.1" y="385" width="0.1" height="15.0" fill="rgb(210,34,49)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('sched_clock_cpu (374 samples, 0.09%)')" onmouseout="c()">
<title>sched_clock_cpu (374 samples, 0.09%)</title><rect x="681.9" y="273" width="1.0" height="15.0" fill="rgb(232,83,53)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('sem_wait (71 samples, 0.02%)')" onmouseout="c()">
<title>sem_wait (71 samples, 0.02%)</title><rect x="980.1" y="465" width="0.2" height="15.0" fill="rgb(233,162,35)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('idle_cpu (56 samples, 0.01%)')" onmouseout="c()">
<title>idle_cpu (56 samples, 0.01%)</title><rect x="178.7" y="241" width="0.2" height="15.0" fill="rgb(231,163,39)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('preempt_notifier_unregister (104 samples, 0.02%)')" onmouseout="c()">
<title>preempt_notifier_unregister (104 samples, 0.02%)</title><rect x="607.5" y="321" width="0.3" height="15.0" fill="rgb(249,30,46)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tick_nohz_stop_sched_tick (7,134 samples, 1.68%)')" onmouseout="c()">
<title>tick_nohz_stop_sched_tick (7,134 samples, 1.68%)</title><rect x="1158.8" y="433" width="19.8" height="15.0" fill="rgb(217,57,10)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__list_add (108 samples, 0.03%)')" onmouseout="c()">
<title>__list_add (108 samples, 0.03%)</title><rect x="595.2" y="321" width="0.3" height="15.0" fill="rgb(223,130,16)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ns_to_timeval (77 samples, 0.02%)')" onmouseout="c()">
<title>ns_to_timeval (77 samples, 0.02%)</title><rect x="1106.0" y="401" width="0.2" height="15.0" fill="rgb(249,153,53)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rb_next (73 samples, 0.02%)')" onmouseout="c()">
<title>rb_next (73 samples, 0.02%)</title><rect x="1144.3" y="369" width="0.2" height="15.0" fill="rgb(251,1,22)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('find_next_bit (199 samples, 0.05%)')" onmouseout="c()">
<title>find_next_bit (199 samples, 0.05%)</title><rect x="669.0" y="289" width="0.6" height="15.0" fill="rgb(231,134,31)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_read_msr_safe (398 samples, 0.09%)')" onmouseout="c()">
<title>native_read_msr_safe (398 samples, 0.09%)</title><rect x="836.9" y="321" width="1.1" height="15.0" fill="rgb(238,178,54)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('skip_emulated_instruction (953 samples, 0.22%)')" onmouseout="c()">
<title>skip_emulated_instruction (953 samples, 0.22%)</title><rect x="759.7" y="305" width="2.7" height="15.0" fill="rgb(220,204,48)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_apic_has_interrupt (76 samples, 0.02%)')" onmouseout="c()">
<title>kvm_apic_has_interrupt (76 samples, 0.02%)</title><rect x="495.9" y="321" width="0.2" height="15.0" fill="rgb(217,29,8)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__apic_accept_irq (1,741 samples, 0.41%)')" onmouseout="c()">
<title>__apic_accept_irq (1,741 samples, 0.41%)</title><rect x="790.7" y="209" width="4.8" height="15.0" fill="rgb(223,215,48)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('activate_task (260 samples, 0.06%)')" onmouseout="c()">
<title>activate_task (260 samples, 0.06%)</title><rect x="1181.7" y="209" width="0.7" height="15.0" fill="rgb(233,36,1)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('bnx2_poll_work (117 samples, 0.03%)')" onmouseout="c()">
<title>bnx2_poll_work (117 samples, 0.03%)</title><rect x="909.0" y="225" width="0.4" height="15.0" fill="rgb(253,180,45)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('getnstimeofday (37 samples, 0.01%)')" onmouseout="c()">
<title>getnstimeofday (37 samples, 0.01%)</title><rect x="1186.5" y="337" width="0.1" height="15.0" fill="rgb(214,199,10)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_cfs_load (207 samples, 0.05%)')" onmouseout="c()">
<title>update_cfs_load (207 samples, 0.05%)</title><rect x="1116.0" y="257" width="0.6" height="15.0" fill="rgb(242,121,52)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tick_program_event (56 samples, 0.01%)')" onmouseout="c()">
<title>tick_program_event (56 samples, 0.01%)</title><rect x="1155.2" y="401" width="0.1" height="15.0" fill="rgb(219,99,4)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__select (40 samples, 0.01%)')" onmouseout="c()">
<title>__select (40 samples, 0.01%)</title><rect x="967.2" y="449" width="0.1" height="15.0" fill="rgb(244,9,2)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vsnprintf (92 samples, 0.02%)')" onmouseout="c()">
<title>vsnprintf (92 samples, 0.02%)</title><rect x="969.4" y="305" width="0.3" height="15.0" fill="rgb(215,213,10)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('find_next_bit (62 samples, 0.01%)')" onmouseout="c()">
<title>find_next_bit (62 samples, 0.01%)</title><rect x="672.5" y="289" width="0.2" height="15.0" fill="rgb(231,134,31)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_lock (152 samples, 0.04%)')" onmouseout="c()">
<title>_spin_lock (152 samples, 0.04%)</title><rect x="155.8" y="257" width="0.4" height="15.0" fill="rgb(207,37,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_read_tsc (58 samples, 0.01%)')" onmouseout="c()">
<title>native_read_tsc (58 samples, 0.01%)</title><rect x="325.1" y="257" width="0.2" height="15.0" fill="rgb(249,99,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('lapic_next_event (87 samples, 0.02%)')" onmouseout="c()">
<title>lapic_next_event (87 samples, 0.02%)</title><rect x="319.9" y="257" width="0.2" height="15.0" fill="rgb(229,17,16)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('default_wake_function (375 samples, 0.09%)')" onmouseout="c()">
<title>default_wake_function (375 samples, 0.09%)</title><rect x="1181.6" y="241" width="1.0" height="15.0" fill="rgb(208,124,20)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_sched_clock (591 samples, 0.14%)')" onmouseout="c()">
<title>native_sched_clock (591 samples, 0.14%)</title><rect x="284.7" y="209" width="1.6" height="15.0" fill="rgb(233,205,14)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_ioapic_handles_vector (786 samples, 0.18%)')" onmouseout="c()">
<title>kvm_ioapic_handles_vector (786 samples, 0.18%)</title><rect x="580.3" y="321" width="2.2" height="15.0" fill="rgb(233,138,41)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rcu_irq_exit (206 samples, 0.05%)')" onmouseout="c()">
<title>rcu_irq_exit (206 samples, 0.05%)</title><rect x="1044.7" y="369" width="0.6" height="15.0" fill="rgb(253,130,37)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('irq_enter (120 samples, 0.03%)')" onmouseout="c()">
<title>irq_enter (120 samples, 0.03%)</title><rect x="1182.9" y="337" width="0.3" height="15.0" fill="rgb(247,79,15)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_arch_vcpu_runnable (245 samples, 0.06%)')" onmouseout="c()">
<title>kvm_arch_vcpu_runnable (245 samples, 0.06%)</title><rect x="494.7" y="337" width="0.7" height="15.0" fill="rgb(252,200,20)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('put_prev_task_fair (41 samples, 0.01%)')" onmouseout="c()">
<title>put_prev_task_fair (41 samples, 0.01%)</title><rect x="756.4" y="257" width="0.1" height="15.0" fill="rgb(238,46,35)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('lock_hrtimer_base (56 samples, 0.01%)')" onmouseout="c()">
<title>lock_hrtimer_base (56 samples, 0.01%)</title><rect x="1145.9" y="401" width="0.2" height="15.0" fill="rgb(232,201,8)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vmx_get_msr (94 samples, 0.02%)')" onmouseout="c()">
<title>vmx_get_msr (94 samples, 0.02%)</title><rect x="687.9" y="289" width="0.3" height="15.0" fill="rgb(217,85,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rb_insert_color (42 samples, 0.01%)')" onmouseout="c()">
<title>rb_insert_color (42 samples, 0.01%)</title><rect x="1153.1" y="385" width="0.1" height="15.0" fill="rgb(222,158,21)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vmcs_writel (373 samples, 0.09%)')" onmouseout="c()">
<title>vmcs_writel (373 samples, 0.09%)</title><rect x="727.4" y="305" width="1.1" height="15.0" fill="rgb(228,19,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__timer_stats_hrtimer_set_start_info (132 samples, 0.03%)')" onmouseout="c()">
<title>__timer_stats_hrtimer_set_start_info (132 samples, 0.03%)</title><rect x="1151.6" y="385" width="0.4" height="15.0" fill="rgb(211,194,18)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_lock (116 samples, 0.03%)')" onmouseout="c()">
<title>_spin_lock (116 samples, 0.03%)</title><rect x="161.9" y="257" width="0.3" height="15.0" fill="rgb(207,37,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('hrtimer_interrupt (604 samples, 0.14%)')" onmouseout="c()">
<title>hrtimer_interrupt (604 samples, 0.14%)</title><rect x="1181.2" y="337" width="1.7" height="15.0" fill="rgb(252,20,51)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('do_sync_write (233 samples, 0.05%)')" onmouseout="c()">
<title>do_sync_write (233 samples, 0.05%)</title><rect x="970.3" y="353" width="0.7" height="15.0" fill="rgb(226,116,34)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('hrtimer_start_range_ns (3,292 samples, 0.77%)')" onmouseout="c()">
<title>hrtimer_start_range_ns (3,292 samples, 0.77%)</title><rect x="1146.2" y="417" width="9.1" height="15.0" fill="rgb(207,72,23)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vmx_set_msr (22,104 samples, 5.20%)')" onmouseout="c()">
<title>vmx_set_msr (22,104 samples, 5.20%)</title><rect x="763.3" y="305" width="61.4" height="15.0" fill="rgb(249,17,2)" rx="2" ry="2" />
<text text-anchor="" x="766.347445767093" y="315.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >vmx_se..</text>
</g>
<g class="func_g" onmouseover="s('dequeue_entity (92 samples, 0.02%)')" onmouseout="c()">
<title>dequeue_entity (92 samples, 0.02%)</title><rect x="621.4" y="273" width="0.2" height="15.0" fill="rgb(208,108,34)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rb_erase (37 samples, 0.01%)')" onmouseout="c()">
<title>rb_erase (37 samples, 0.01%)</title><rect x="1188.4" y="321" width="0.1" height="15.0" fill="rgb(236,5,12)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('do_softirq (683 samples, 0.16%)')" onmouseout="c()">
<title>do_softirq (683 samples, 0.16%)</title><rect x="55.3" y="321" width="1.9" height="15.0" fill="rgb(243,77,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_write_msr_safe (190 samples, 0.04%)')" onmouseout="c()">
<title>native_write_msr_safe (190 samples, 0.04%)</title><rect x="690.0" y="257" width="0.5" height="15.0" fill="rgb(215,163,22)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('pit_has_pending_timer (614 samples, 0.14%)')" onmouseout="c()">
<title>pit_has_pending_timer (614 samples, 0.14%)</title><rect x="524.4" y="321" width="1.7" height="15.0" fill="rgb(253,0,31)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unmap_vmas (37 samples, 0.01%)')" onmouseout="c()">
<title>unmap_vmas (37 samples, 0.01%)</title><rect x="977.2" y="401" width="0.1" height="15.0" fill="rgb(217,28,45)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('timekeeping_update (191 samples, 0.04%)')" onmouseout="c()">
<title>timekeeping_update (191 samples, 0.04%)</title><rect x="159.5" y="241" width="0.5" height="15.0" fill="rgb(253,156,9)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ret_from_intr (90 samples, 0.02%)')" onmouseout="c()">
<title>ret_from_intr (90 samples, 0.02%)</title><rect x="1117.1" y="417" width="0.3" height="15.0" fill="rgb(237,83,40)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__run_hrtimer (55 samples, 0.01%)')" onmouseout="c()">
<title>__run_hrtimer (55 samples, 0.01%)</title><rect x="488.0" y="289" width="0.1" height="15.0" fill="rgb(208,100,50)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vmcs_writel (390 samples, 0.09%)')" onmouseout="c()">
<title>vmcs_writel (390 samples, 0.09%)</title><rect x="728.5" y="321" width="1.0" height="15.0" fill="rgb(228,19,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('task_tick_fair (19,123 samples, 4.50%)')" onmouseout="c()">
<title>task_tick_fair (19,123 samples, 4.50%)</title><rect x="224.7" y="241" width="53.0" height="15.0" fill="rgb(231,38,9)" rx="2" ry="2" />
<text text-anchor="" x="227.652939972763" y="251.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >task_..</text>
</g>
<g class="func_g" onmouseover="s('calc_delta_mine (426 samples, 0.10%)')" onmouseout="c()">
<title>calc_delta_mine (426 samples, 0.10%)</title><rect x="276.5" y="209" width="1.2" height="15.0" fill="rgb(235,150,13)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('task_of (63 samples, 0.01%)')" onmouseout="c()">
<title>task_of (63 samples, 0.01%)</title><rect x="625.2" y="241" width="0.2" height="15.0" fill="rgb(226,191,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rb_insert_color (250 samples, 0.06%)')" onmouseout="c()">
<title>rb_insert_color (250 samples, 0.06%)</title><rect x="139.1" y="289" width="0.7" height="15.0" fill="rgb(222,158,21)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_cfs_shares (55 samples, 0.01%)')" onmouseout="c()">
<title>update_cfs_shares (55 samples, 0.01%)</title><rect x="381.3" y="209" width="0.2" height="15.0" fill="rgb(240,28,1)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rcu_check_callbacks (259 samples, 0.06%)')" onmouseout="c()">
<title>rcu_check_callbacks (259 samples, 0.06%)</title><rect x="153.0" y="273" width="0.8" height="15.0" fill="rgb(244,188,26)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('sched_clock_idle_sleep_event (75 samples, 0.02%)')" onmouseout="c()">
<title>sched_clock_idle_sleep_event (75 samples, 0.02%)</title><rect x="1122.6" y="433" width="0.2" height="15.0" fill="rgb(247,203,54)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tick_sched_timer (53,098 samples, 12.49%)')" onmouseout="c()">
<title>tick_sched_timer (53,098 samples, 12.49%)</title><rect x="142.2" y="289" width="147.4" height="15.0" fill="rgb(213,15,28)" rx="2" ry="2" />
<text text-anchor="" x="145.216663491369" y="299.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >tick_sched_timer</text>
</g>
<g class="func_g" onmouseover="s('check_preempt_curr (39 samples, 0.01%)')" onmouseout="c()">
<title>check_preempt_curr (39 samples, 0.01%)</title><rect x="1000.0" y="273" width="0.1" height="15.0" fill="rgb(211,70,34)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('read_tsc (55 samples, 0.01%)')" onmouseout="c()">
<title>read_tsc (55 samples, 0.01%)</title><rect x="1177.4" y="417" width="0.1" height="15.0" fill="rgb(233,66,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('intel_pmu_disable_all (1,253 samples, 0.29%)')" onmouseout="c()">
<title>intel_pmu_disable_all (1,253 samples, 0.29%)</title><rect x="213.5" y="177" width="3.5" height="15.0" fill="rgb(251,19,31)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('schedule (177 samples, 0.04%)')" onmouseout="c()">
<title>schedule (177 samples, 0.04%)</title><rect x="1187.7" y="385" width="0.5" height="15.0" fill="rgb(222,218,51)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('intel_pmu_enable_all (94 samples, 0.02%)')" onmouseout="c()">
<title>intel_pmu_enable_all (94 samples, 0.02%)</title><rect x="1046.5" y="257" width="0.2" height="15.0" fill="rgb(252,128,48)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__GI___libc_close (54 samples, 0.01%)')" onmouseout="c()">
<title>__GI___libc_close (54 samples, 0.01%)</title><rect x="968.0" y="465" width="0.1" height="15.0" fill="rgb(236,37,15)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('irq_exit (65 samples, 0.02%)')" onmouseout="c()">
<title>irq_exit (65 samples, 0.02%)</title><rect x="1186.9" y="321" width="0.2" height="15.0" fill="rgb(216,86,39)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('finish_task_switch (164 samples, 0.04%)')" onmouseout="c()">
<title>finish_task_switch (164 samples, 0.04%)</title><rect x="1136.9" y="417" width="0.4" height="15.0" fill="rgb(221,162,9)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('account_cfs_rq_runtime (96 samples, 0.02%)')" onmouseout="c()">
<title>account_cfs_rq_runtime (96 samples, 0.02%)</title><rect x="231.4" y="225" width="0.3" height="15.0" fill="rgb(230,19,12)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vhost_worker (78 samples, 0.02%)')" onmouseout="c()">
<title>vhost_worker (78 samples, 0.02%)</title><rect x="973.4" y="417" width="0.2" height="15.0" fill="rgb(253,191,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('smp_apic_timer_interrupt (131,540 samples, 30.94%)')" onmouseout="c()">
<title>smp_apic_timer_interrupt (131,540 samples, 30.94%)</title><rect x="49.2" y="337" width="365.0" height="15.0" fill="rgb(205,22,48)" rx="2" ry="2" />
<text text-anchor="" x="52.1559823782744" y="347.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >smp_apic_timer_interrupt</text>
</g>
<g class="func_g" onmouseover="s('update_process_times (46,596 samples, 10.96%)')" onmouseout="c()">
<title>update_process_times (46,596 samples, 10.96%)</title><rect x="160.3" y="273" width="129.3" height="15.0" fill="rgb(209,13,10)" rx="2" ry="2" />
<text text-anchor="" x="163.262608871546" y="283.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >update_process_t..</text>
</g>
<g class="func_g" onmouseover="s('__apic_accept_irq (468 samples, 0.11%)')" onmouseout="c()">
<title>__apic_accept_irq (468 samples, 0.11%)</title><rect x="532.0" y="305" width="1.3" height="15.0" fill="rgb(223,215,48)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_arch_vcpu_ioctl_run (170,459 samples, 40.09%)')" onmouseout="c()">
<title>kvm_arch_vcpu_ioctl_run (170,459 samples, 40.09%)</title><rect x="414.4" y="353" width="473.1" height="15.0" fill="rgb(233,14,15)" rx="2" ry="2" />
<text text-anchor="" x="417.399033768702" y="363.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >kvm_arch_vcpu_ioctl_run</text>
</g>
<g class="func_g" onmouseover="s('sched_clock (78 samples, 0.02%)')" onmouseout="c()">
<title>sched_clock (78 samples, 0.02%)</title><rect x="1177.8" y="385" width="0.2" height="15.0" fill="rgb(207,104,49)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_lock_irqsave (300 samples, 0.07%)')" onmouseout="c()">
<title>_spin_lock_irqsave (300 samples, 0.07%)</title><rect x="781.5" y="209" width="0.8" height="15.0" fill="rgb(209,137,45)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('finish_task_switch (109 samples, 0.03%)')" onmouseout="c()">
<title>finish_task_switch (109 samples, 0.03%)</title><rect x="596.7" y="321" width="0.3" height="15.0" fill="rgb(221,162,9)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_curr (110 samples, 0.03%)')" onmouseout="c()">
<title>update_curr (110 samples, 0.03%)</title><rect x="1021.2" y="209" width="0.3" height="15.0" fill="rgb(218,93,52)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('pick_next_task_idle (81 samples, 0.02%)')" onmouseout="c()">
<title>pick_next_task_idle (81 samples, 0.02%)</title><rect x="674.3" y="305" width="0.2" height="15.0" fill="rgb(240,58,24)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('idle_cpu (369 samples, 0.09%)')" onmouseout="c()">
<title>idle_cpu (369 samples, 0.09%)</title><rect x="393.1" y="305" width="1.0" height="15.0" fill="rgb(231,163,39)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_lapic_get_cr8 (563 samples, 0.13%)')" onmouseout="c()">
<title>kvm_lapic_get_cr8 (563 samples, 0.13%)</title><rect x="549.0" y="337" width="1.6" height="15.0" fill="rgb(234,219,18)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rb_next (45 samples, 0.01%)')" onmouseout="c()">
<title>rb_next (45 samples, 0.01%)</title><rect x="1168.8" y="369" width="0.1" height="15.0" fill="rgb(251,1,22)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('deactivate_task (89 samples, 0.02%)')" onmouseout="c()">
<title>deactivate_task (89 samples, 0.02%)</title><rect x="596.2" y="321" width="0.3" height="15.0" fill="rgb(245,205,36)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__GI___libc_read (433 samples, 0.10%)')" onmouseout="c()">
<title>__GI___libc_read (433 samples, 0.10%)</title><rect x="968.5" y="465" width="1.2" height="15.0" fill="rgb(248,1,9)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ntp_tick_length (46 samples, 0.01%)')" onmouseout="c()">
<title>ntp_tick_length (46 samples, 0.01%)</title><rect x="160.1" y="257" width="0.1" height="15.0" fill="rgb(212,167,37)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('default_send_IPI_mask_sequence_phys (110 samples, 0.03%)')" onmouseout="c()">
<title>default_send_IPI_mask_sequence_phys (110 samples, 0.03%)</title><rect x="201.8" y="209" width="0.4" height="15.0" fill="rgb(237,15,8)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vmx_cache_reg (1,818 samples, 0.43%)')" onmouseout="c()">
<title>vmx_cache_reg (1,818 samples, 0.43%)</title><rect x="732.2" y="337" width="5.0" height="15.0" fill="rgb(209,28,34)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('get_pid_task (77 samples, 0.02%)')" onmouseout="c()">
<title>get_pid_task (77 samples, 0.02%)</title><rect x="755.6" y="289" width="0.2" height="15.0" fill="rgb(227,156,42)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('hrtimer_try_to_cancel (96 samples, 0.02%)')" onmouseout="c()">
<title>hrtimer_try_to_cancel (96 samples, 0.02%)</title><rect x="1188.3" y="353" width="0.3" height="15.0" fill="rgb(221,57,30)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('task_tick_fair (426 samples, 0.10%)')" onmouseout="c()">
<title>task_tick_fair (426 samples, 0.10%)</title><rect x="287.7" y="257" width="1.2" height="15.0" fill="rgb(231,38,9)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('hrtimer_run_pending (139 samples, 0.03%)')" onmouseout="c()">
<title>hrtimer_run_pending (139 samples, 0.03%)</title><rect x="388.9" y="241" width="0.4" height="15.0" fill="rgb(230,45,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('delayed_work_timer_fn (60 samples, 0.01%)')" onmouseout="c()">
<title>delayed_work_timer_fn (60 samples, 0.01%)</title><rect x="388.7" y="241" width="0.2" height="15.0" fill="rgb(222,94,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__rcu_process_callbacks (7,755 samples, 1.82%)')" onmouseout="c()">
<title>__rcu_process_callbacks (7,755 samples, 1.82%)</title><rect x="355.3" y="241" width="21.6" height="15.0" fill="rgb(211,185,49)" rx="2" ry="2" />
<text text-anchor="" x="358.348659436396" y="251.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >_..</text>
</g>
<g class="func_g" onmouseover="s('cpu_idle (71,488 samples, 16.81%)')" onmouseout="c()">
<title>cpu_idle (71,488 samples, 16.81%)</title><rect x="980.3" y="449" width="198.4" height="15.0" fill="rgb(233,201,15)" rx="2" ry="2" />
<text text-anchor="" x="983.328984351663" y="459.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >cpu_idle</text>
</g>
<g class="func_g" onmouseover="s('hrtimer_force_reprogram (91 samples, 0.02%)')" onmouseout="c()">
<title>hrtimer_force_reprogram (91 samples, 0.02%)</title><rect x="1171.3" y="385" width="0.2" height="15.0" fill="rgb(237,60,9)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('system_call_fastpath (54 samples, 0.01%)')" onmouseout="c()">
<title>system_call_fastpath (54 samples, 0.01%)</title><rect x="980.1" y="449" width="0.2" height="15.0" fill="rgb(224,162,39)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_lock_irqsave (45 samples, 0.01%)')" onmouseout="c()">
<title>_spin_lock_irqsave (45 samples, 0.01%)</title><rect x="1030.0" y="337" width="0.1" height="15.0" fill="rgb(209,137,45)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('insert_work (54 samples, 0.01%)')" onmouseout="c()">
<title>insert_work (54 samples, 0.01%)</title><rect x="388.7" y="209" width="0.2" height="15.0" fill="rgb(248,180,6)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rest_init (3,326 samples, 0.78%)')" onmouseout="c()">
<title>rest_init (3,326 samples, 0.78%)</title><rect x="1180.8" y="417" width="9.2" height="15.0" fill="rgb(232,7,36)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('napi_gro_receive (42 samples, 0.01%)')" onmouseout="c()">
<title>napi_gro_receive (42 samples, 0.01%)</title><rect x="909.4" y="209" width="0.1" height="15.0" fill="rgb(219,67,15)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('generic_file_aio_write (233 samples, 0.05%)')" onmouseout="c()">
<title>generic_file_aio_write (233 samples, 0.05%)</title><rect x="970.3" y="321" width="0.7" height="15.0" fill="rgb(233,76,46)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('fput (41 samples, 0.01%)')" onmouseout="c()">
<title>fput (41 samples, 0.01%)</title><rect x="968.0" y="401" width="0.1" height="15.0" fill="rgb(236,4,48)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('find_first_bit (124 samples, 0.03%)')" onmouseout="c()">
<title>find_first_bit (124 samples, 0.03%)</title><rect x="668.7" y="289" width="0.3" height="15.0" fill="rgb(218,191,53)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_process_times (52 samples, 0.01%)')" onmouseout="c()">
<title>update_process_times (52 samples, 0.01%)</title><rect x="1136.3" y="337" width="0.2" height="15.0" fill="rgb(209,13,10)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rb_next (113 samples, 0.03%)')" onmouseout="c()">
<title>rb_next (113 samples, 0.03%)</title><rect x="997.6" y="337" width="0.3" height="15.0" fill="rgb(251,1,22)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('notifier_call_chain (96 samples, 0.02%)')" onmouseout="c()">
<title>notifier_call_chain (96 samples, 0.02%)</title><rect x="1118.8" y="417" width="0.3" height="15.0" fill="rgb(240,117,28)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('account_cfs_rq_runtime (472 samples, 0.11%)')" onmouseout="c()">
<title>account_cfs_rq_runtime (472 samples, 0.11%)</title><rect x="275.2" y="209" width="1.3" height="15.0" fill="rgb(230,19,12)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_unlock_irqrestore (300 samples, 0.07%)')" onmouseout="c()">
<title>_spin_unlock_irqrestore (300 samples, 0.07%)</title><rect x="362.7" y="225" width="0.9" height="15.0" fill="rgb(250,72,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_unlock_irqrestore (37 samples, 0.01%)')" onmouseout="c()">
<title>_spin_unlock_irqrestore (37 samples, 0.01%)</title><rect x="999.8" y="273" width="0.1" height="15.0" fill="rgb(250,72,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('cpumask_next_and (1,598 samples, 0.38%)')" onmouseout="c()">
<title>cpumask_next_and (1,598 samples, 0.38%)</title><rect x="664.3" y="289" width="4.4" height="15.0" fill="rgb(231,93,24)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('exit_intr (42 samples, 0.01%)')" onmouseout="c()">
<title>exit_intr (42 samples, 0.01%)</title><rect x="414.3" y="353" width="0.1" height="15.0" fill="rgb(229,76,28)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('smp_apic_timer_interrupt (115 samples, 0.03%)')" onmouseout="c()">
<title>smp_apic_timer_interrupt (115 samples, 0.03%)</title><rect x="487.9" y="321" width="0.3" height="15.0" fill="rgb(205,22,48)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('dequeue_task (7,331 samples, 1.72%)')" onmouseout="c()">
<title>dequeue_task (7,331 samples, 1.72%)</title><rect x="620.9" y="289" width="20.3" height="15.0" fill="rgb(236,71,32)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_rq_clock (492 samples, 0.12%)')" onmouseout="c()">
<title>update_rq_clock (492 samples, 0.12%)</title><rect x="1021.6" y="225" width="1.4" height="15.0" fill="rgb(227,206,0)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_lock (297 samples, 0.07%)')" onmouseout="c()">
<title>_spin_lock (297 samples, 0.07%)</title><rect x="157.3" y="241" width="0.9" height="15.0" fill="rgb(207,37,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_vcpu_block (91 samples, 0.02%)')" onmouseout="c()">
<title>kvm_vcpu_block (91 samples, 0.02%)</title><rect x="903.9" y="353" width="0.3" height="15.0" fill="rgb(221,155,20)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vmx_handle_exit (1,425 samples, 0.34%)')" onmouseout="c()">
<title>vmx_handle_exit (1,425 samples, 0.34%)</title><rect x="924.7" y="353" width="3.9" height="15.0" fill="rgb(237,179,1)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('apic_update_ppr (62 samples, 0.01%)')" onmouseout="c()">
<title>apic_update_ppr (62 samples, 0.01%)</title><rect x="600.2" y="289" width="0.2" height="15.0" fill="rgb(244,88,21)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('default_wake_function (47 samples, 0.01%)')" onmouseout="c()">
<title>default_wake_function (47 samples, 0.01%)</title><rect x="388.7" y="145" width="0.2" height="15.0" fill="rgb(208,124,20)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('enqueue_hrtimer (179 samples, 0.04%)')" onmouseout="c()">
<title>enqueue_hrtimer (179 samples, 0.04%)</title><rect x="1152.3" y="385" width="0.5" height="15.0" fill="rgb(251,179,15)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('do_softirq (83 samples, 0.02%)')" onmouseout="c()">
<title>do_softirq (83 samples, 0.02%)</title><rect x="1187.2" y="321" width="0.2" height="15.0" fill="rgb(243,77,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_get_apic_interrupt (3,216 samples, 0.76%)')" onmouseout="c()">
<title>kvm_get_apic_interrupt (3,216 samples, 0.76%)</title><rect x="496.1" y="321" width="9.0" height="15.0" fill="rgb(252,74,32)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tick_program_event (867 samples, 0.20%)')" onmouseout="c()">
<title>tick_program_event (867 samples, 0.20%)</title><rect x="1033.7" y="369" width="2.4" height="15.0" fill="rgb(219,99,4)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('lapic_next_event (64 samples, 0.02%)')" onmouseout="c()">
<title>lapic_next_event (64 samples, 0.02%)</title><rect x="1166.7" y="305" width="0.2" height="15.0" fill="rgb(229,17,16)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('idle_cpu (224 samples, 0.05%)')" onmouseout="c()">
<title>idle_cpu (224 samples, 0.05%)</title><rect x="817.3" y="209" width="0.6" height="15.0" fill="rgb(231,163,39)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__run_hrtimer (65,166 samples, 15.33%)')" onmouseout="c()">
<title>__run_hrtimer (65,166 samples, 15.33%)</title><rect x="109.0" y="305" width="180.9" height="15.0" fill="rgb(208,100,50)" rx="2" ry="2" />
<text text-anchor="" x="112.047316638324" y="315.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >__run_hrtimer</text>
</g>
<g class="func_g" onmouseover="s('__switch_to (74 samples, 0.02%)')" onmouseout="c()">
<title>__switch_to (74 samples, 0.02%)</title><rect x="935.1" y="417" width="0.2" height="15.0" fill="rgb(218,57,18)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_shares (594 samples, 0.14%)')" onmouseout="c()">
<title>update_shares (594 samples, 0.14%)</title><rect x="1115.2" y="273" width="1.6" height="15.0" fill="rgb(229,50,20)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unalias_gfn (57 samples, 0.01%)')" onmouseout="c()">
<title>unalias_gfn (57 samples, 0.01%)</title><rect x="694.6" y="289" width="0.2" height="15.0" fill="rgb(221,189,37)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('schedule (91 samples, 0.02%)')" onmouseout="c()">
<title>schedule (91 samples, 0.02%)</title><rect x="15.3" y="433" width="0.3" height="15.0" fill="rgb(222,218,51)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('intel_pmu_nhm_enable_all (708 samples, 0.17%)')" onmouseout="c()">
<title>intel_pmu_nhm_enable_all (708 samples, 0.17%)</title><rect x="218.5" y="177" width="2.0" height="15.0" fill="rgb(208,11,28)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vmx_get_msr (2,215 samples, 0.52%)')" onmouseout="c()">
<title>vmx_get_msr (2,215 samples, 0.52%)</title><rect x="737.3" y="337" width="6.1" height="15.0" fill="rgb(217,85,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('apic_has_pending_timer (596 samples, 0.14%)')" onmouseout="c()">
<title>apic_has_pending_timer (596 samples, 0.14%)</title><rect x="484.5" y="337" width="1.7" height="15.0" fill="rgb(248,212,39)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rb_next (775 samples, 0.18%)')" onmouseout="c()">
<title>rb_next (775 samples, 0.18%)</title><rect x="139.8" y="289" width="2.2" height="15.0" fill="rgb(251,1,22)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('schedule (68 samples, 0.02%)')" onmouseout="c()">
<title>schedule (68 samples, 0.02%)</title><rect x="973.8" y="417" width="0.2" height="15.0" fill="rgb(222,218,51)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_apic_mem_write (596 samples, 0.14%)')" onmouseout="c()">
<title>native_apic_mem_write (596 samples, 0.14%)</title><rect x="320.1" y="257" width="1.7" height="15.0" fill="rgb(209,177,4)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ipmi_thread (695 samples, 0.16%)')" onmouseout="c()">
<title>ipmi_thread (695 samples, 0.16%)</title><rect x="973.8" y="433" width="1.9" height="15.0" fill="rgb(254,34,24)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_stats_wait_end (430 samples, 0.10%)')" onmouseout="c()">
<title>update_stats_wait_end (430 samples, 0.10%)</title><rect x="1132.4" y="385" width="1.2" height="15.0" fill="rgb(253,139,44)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_apic_present (263 samples, 0.06%)')" onmouseout="c()">
<title>kvm_apic_present (263 samples, 0.06%)</title><rect x="789.9" y="225" width="0.8" height="15.0" fill="rgb(244,129,41)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('select_idle_sibling (47 samples, 0.01%)')" onmouseout="c()">
<title>select_idle_sibling (47 samples, 0.01%)</title><rect x="1024.6" y="257" width="0.1" height="15.0" fill="rgb(233,205,23)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_apic_mem_write (694 samples, 0.16%)')" onmouseout="c()">
<title>native_apic_mem_write (694 samples, 0.16%)</title><rect x="398.1" y="321" width="2.0" height="15.0" fill="rgb(209,177,4)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tick_nohz_get_sleep_length (56 samples, 0.01%)')" onmouseout="c()">
<title>tick_nohz_get_sleep_length (56 samples, 0.01%)</title><rect x="1118.2" y="417" width="0.1" height="15.0" fill="rgb(227,96,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('task_rq_lock (632 samples, 0.15%)')" onmouseout="c()">
<title>task_rq_lock (632 samples, 0.15%)</title><rect x="1026.6" y="257" width="1.7" height="15.0" fill="rgb(231,149,47)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('mark_page_dirty (283 samples, 0.07%)')" onmouseout="c()">
<title>mark_page_dirty (283 samples, 0.07%)</title><rect x="694.0" y="305" width="0.8" height="15.0" fill="rgb(223,37,41)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rcu_process_callbacks (582 samples, 0.14%)')" onmouseout="c()">
<title>rcu_process_callbacks (582 samples, 0.14%)</title><rect x="390.2" y="273" width="1.6" height="15.0" fill="rgb(209,24,23)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('mark_page_dirty_in_slot (81 samples, 0.02%)')" onmouseout="c()">
<title>mark_page_dirty_in_slot (81 samples, 0.02%)</title><rect x="584.9" y="321" width="0.2" height="15.0" fill="rgb(209,110,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('hrtimer_cancel (2,752 samples, 0.65%)')" onmouseout="c()">
<title>hrtimer_cancel (2,752 samples, 0.65%)</title><rect x="1138.4" y="417" width="7.7" height="15.0" fill="rgb(211,94,12)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_lock_irqsave (542 samples, 0.13%)')" onmouseout="c()">
<title>_spin_lock_irqsave (542 samples, 0.13%)</title><rect x="812.9" y="177" width="1.5" height="15.0" fill="rgb(209,137,45)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('irq_exit (22,931 samples, 5.39%)')" onmouseout="c()">
<title>irq_exit (22,931 samples, 5.39%)</title><rect x="333.7" y="321" width="63.6" height="15.0" fill="rgb(216,86,39)" rx="2" ry="2" />
<text text-anchor="" x="336.650228033409" y="331.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >irq_exit</text>
</g>
<g class="func_g" onmouseover="s('vmx_interrupt_allowed (283 samples, 0.07%)')" onmouseout="c()">
<title>vmx_interrupt_allowed (283 samples, 0.07%)</title><rect x="832.8" y="337" width="0.8" height="15.0" fill="rgb(206,40,41)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('net_rx_action (54 samples, 0.01%)')" onmouseout="c()">
<title>net_rx_action (54 samples, 0.01%)</title><rect x="1187.3" y="273" width="0.1" height="15.0" fill="rgb(234,84,46)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_lock_irqsave (41 samples, 0.01%)')" onmouseout="c()">
<title>_spin_lock_irqsave (41 samples, 0.01%)</title><rect x="1163.2" y="385" width="0.1" height="15.0" fill="rgb(209,137,45)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__rb_rotate_right (249 samples, 0.06%)')" onmouseout="c()">
<title>__rb_rotate_right (249 samples, 0.06%)</title><rect x="811.8" y="161" width="0.7" height="15.0" fill="rgb(206,117,53)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rcu_sched_qs (97 samples, 0.02%)')" onmouseout="c()">
<title>rcu_sched_qs (97 samples, 0.02%)</title><rect x="1122.4" y="433" width="0.2" height="15.0" fill="rgb(239,116,8)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('intel_guest_get_msrs (339 samples, 0.08%)')" onmouseout="c()">
<title>intel_guest_get_msrs (339 samples, 0.08%)</title><rect x="877.9" y="321" width="0.9" height="15.0" fill="rgb(223,196,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rb_next (765 samples, 0.18%)')" onmouseout="c()">
<title>rb_next (765 samples, 0.18%)</title><rect x="120.9" y="273" width="2.1" height="15.0" fill="rgb(251,1,22)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('call_softirq (82 samples, 0.02%)')" onmouseout="c()">
<title>call_softirq (82 samples, 0.02%)</title><rect x="1187.2" y="305" width="0.2" height="15.0" fill="rgb(240,215,19)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_apic_has_interrupt (65 samples, 0.02%)')" onmouseout="c()">
<title>kvm_apic_has_interrupt (65 samples, 0.02%)</title><rect x="599.3" y="305" width="0.2" height="15.0" fill="rgb(217,29,8)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('sys_getdents (62 samples, 0.01%)')" onmouseout="c()">
<title>sys_getdents (62 samples, 0.01%)</title><rect x="970.1" y="433" width="0.1" height="15.0" fill="rgb(233,30,53)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_read_msr_safe (120 samples, 0.03%)')" onmouseout="c()">
<title>native_read_msr_safe (120 samples, 0.03%)</title><rect x="689.7" y="257" width="0.3" height="15.0" fill="rgb(238,178,54)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('sys_close (45 samples, 0.01%)')" onmouseout="c()">
<title>sys_close (45 samples, 0.01%)</title><rect x="968.0" y="433" width="0.1" height="15.0" fill="rgb(216,132,0)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('call_softirq (1,286 samples, 0.30%)')" onmouseout="c()">
<title>call_softirq (1,286 samples, 0.30%)</title><rect x="1113.4" y="337" width="3.5" height="15.0" fill="rgb(240,215,19)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__timer_stats_hrtimer_set_start_info (122 samples, 0.03%)')" onmouseout="c()">
<title>__timer_stats_hrtimer_set_start_info (122 samples, 0.03%)</title><rect x="1169.2" y="385" width="0.3" height="15.0" fill="rgb(211,194,18)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_lock (322 samples, 0.08%)')" onmouseout="c()">
<title>_spin_lock (322 samples, 0.08%)</title><rect x="618.7" y="305" width="0.8" height="15.0" fill="rgb(207,37,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('hrtimer_try_to_cancel (461 samples, 0.11%)')" onmouseout="c()">
<title>hrtimer_try_to_cancel (461 samples, 0.11%)</title><rect x="787.3" y="241" width="1.3" height="15.0" fill="rgb(221,57,30)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_timer_fn (207 samples, 0.05%)')" onmouseout="c()">
<title>kvm_timer_fn (207 samples, 0.05%)</title><rect x="1032.8" y="369" width="0.6" height="15.0" fill="rgb(249,104,46)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('enqueue_entity (466 samples, 0.11%)')" onmouseout="c()">
<title>enqueue_entity (466 samples, 0.11%)</title><rect x="791.7" y="49" width="1.3" height="15.0" fill="rgb(214,151,21)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('free_block (43 samples, 0.01%)')" onmouseout="c()">
<title>free_block (43 samples, 0.01%)</title><rect x="976.6" y="385" width="0.1" height="15.0" fill="rgb(207,157,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__hrtimer_start_range_ns (2,748 samples, 0.65%)')" onmouseout="c()">
<title>__hrtimer_start_range_ns (2,748 samples, 0.65%)</title><rect x="1146.2" y="401" width="7.6" height="15.0" fill="rgb(250,176,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('child_rip (78 samples, 0.02%)')" onmouseout="c()">
<title>child_rip (78 samples, 0.02%)</title><rect x="973.4" y="449" width="0.2" height="15.0" fill="rgb(211,13,52)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_unlock_irqrestore (187 samples, 0.04%)')" onmouseout="c()">
<title>_spin_unlock_irqrestore (187 samples, 0.04%)</title><rect x="782.3" y="209" width="0.5" height="15.0" fill="rgb(250,72,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vmx_set_msr (332 samples, 0.08%)')" onmouseout="c()">
<title>vmx_set_msr (332 samples, 0.08%)</title><rect x="829.8" y="321" width="0.9" height="15.0" fill="rgb(249,17,2)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_arch_vcpu_put (105 samples, 0.02%)')" onmouseout="c()">
<title>kvm_arch_vcpu_put (105 samples, 0.02%)</title><rect x="598.4" y="321" width="0.3" height="15.0" fill="rgb(243,89,40)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_ts_time_stats (297 samples, 0.07%)')" onmouseout="c()">
<title>update_ts_time_stats (297 samples, 0.07%)</title><rect x="1042.6" y="337" width="0.9" height="15.0" fill="rgb(229,229,29)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rb_insert_color (1,597 samples, 0.38%)')" onmouseout="c()">
<title>rb_insert_color (1,597 samples, 0.38%)</title><rect x="808.0" y="177" width="4.5" height="15.0" fill="rgb(222,158,21)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_unlock_irqrestore (65 samples, 0.02%)')" onmouseout="c()">
<title>_spin_unlock_irqrestore (65 samples, 0.02%)</title><rect x="596.0" y="321" width="0.1" height="15.0" fill="rgb(250,72,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_rq_clock (90 samples, 0.02%)')" onmouseout="c()">
<title>update_rq_clock (90 samples, 0.02%)</title><rect x="641.4" y="289" width="0.3" height="15.0" fill="rgb(227,206,0)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_lock (358 samples, 0.08%)')" onmouseout="c()">
<title>_spin_lock (358 samples, 0.08%)</title><rect x="123.0" y="289" width="1.0" height="15.0" fill="rgb(207,37,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_cfs_shares (87 samples, 0.02%)')" onmouseout="c()">
<title>update_cfs_shares (87 samples, 0.02%)</title><rect x="1182.0" y="145" width="0.2" height="15.0" fill="rgb(240,28,1)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('scheduler_ipi (1,354 samples, 0.32%)')" onmouseout="c()">
<title>scheduler_ipi (1,354 samples, 0.32%)</title><rect x="1113.3" y="385" width="3.7" height="15.0" fill="rgb(221,38,15)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('apic_reg_write (16,106 samples, 3.79%)')" onmouseout="c()">
<title>apic_reg_write (16,106 samples, 3.79%)</title><rect x="776.4" y="257" width="44.7" height="15.0" fill="rgb(252,104,44)" rx="2" ry="2" />
<text text-anchor="" x="779.394814151008" y="267.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >apic..</text>
</g>
<g class="func_g" onmouseover="s('__local_bh_enable (54 samples, 0.01%)')" onmouseout="c()">
<title>__local_bh_enable (54 samples, 0.01%)</title><rect x="1036.4" y="369" width="0.2" height="15.0" fill="rgb(250,106,10)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('apic_update_ppr (115 samples, 0.03%)')" onmouseout="c()">
<title>apic_update_ppr (115 samples, 0.03%)</title><rect x="559.3" y="321" width="0.3" height="15.0" fill="rgb(244,88,21)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('notifier_call_chain (89 samples, 0.02%)')" onmouseout="c()">
<title>notifier_call_chain (89 samples, 0.02%)</title><rect x="991.7" y="353" width="0.2" height="15.0" fill="rgb(240,117,28)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('read_tsc (47 samples, 0.01%)')" onmouseout="c()">
<title>read_tsc (47 samples, 0.01%)</title><rect x="153.8" y="273" width="0.1" height="15.0" fill="rgb(233,66,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('save_args (119 samples, 0.03%)')" onmouseout="c()">
<title>save_args (119 samples, 0.03%)</title><rect x="1117.5" y="417" width="0.3" height="15.0" fill="rgb(249,138,42)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('srcu_read_lock (3,362 samples, 0.79%)')" onmouseout="c()">
<title>srcu_read_lock (3,362 samples, 0.79%)</title><rect x="710.3" y="337" width="9.4" height="15.0" fill="rgb(236,159,30)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('do_softirq (1,290 samples, 0.30%)')" onmouseout="c()">
<title>do_softirq (1,290 samples, 0.30%)</title><rect x="1113.4" y="353" width="3.5" height="15.0" fill="rgb(243,77,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_apic_match_dest (142 samples, 0.03%)')" onmouseout="c()">
<title>kvm_apic_match_dest (142 samples, 0.03%)</title><rect x="789.5" y="225" width="0.4" height="15.0" fill="rgb(236,199,44)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__list_add (61 samples, 0.01%)')" onmouseout="c()">
<title>__list_add (61 samples, 0.01%)</title><rect x="608.4" y="305" width="0.2" height="15.0" fill="rgb(223,130,16)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('apic_timer_interrupt (129 samples, 0.03%)')" onmouseout="c()">
<title>apic_timer_interrupt (129 samples, 0.03%)</title><rect x="487.9" y="337" width="0.3" height="15.0" fill="rgb(239,200,41)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('x86_pmu_disable (2,142 samples, 0.50%)')" onmouseout="c()">
<title>x86_pmu_disable (2,142 samples, 0.50%)</title><rect x="212.2" y="193" width="6.0" height="15.0" fill="rgb(223,177,32)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_read_guest_page (554 samples, 0.13%)')" onmouseout="c()">
<title>kvm_read_guest_page (554 samples, 0.13%)</title><rect x="589.4" y="321" width="1.5" height="15.0" fill="rgb(221,162,42)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tick_nohz_stop_idle (56 samples, 0.01%)')" onmouseout="c()">
<title>tick_nohz_stop_idle (56 samples, 0.01%)</title><rect x="1158.6" y="417" width="0.1" height="15.0" fill="rgb(210,186,7)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('thread_return (1,039 samples, 0.24%)')" onmouseout="c()">
<title>thread_return (1,039 samples, 0.24%)</title><rect x="1134.5" y="433" width="2.8" height="15.0" fill="rgb(241,141,48)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ret_from_intr (103 samples, 0.02%)')" onmouseout="c()">
<title>ret_from_intr (103 samples, 0.02%)</title><rect x="1187.1" y="369" width="0.3" height="15.0" fill="rgb(237,83,40)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_apic_mem_write (133 samples, 0.03%)')" onmouseout="c()">
<title>native_apic_mem_write (133 samples, 0.03%)</title><rect x="1045.4" y="385" width="0.4" height="15.0" fill="rgb(209,177,4)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_unlock_irqrestore (119 samples, 0.03%)')" onmouseout="c()">
<title>_spin_unlock_irqrestore (119 samples, 0.03%)</title><rect x="678.1" y="289" width="0.3" height="15.0" fill="rgb(250,72,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('srcu_read_lock (77 samples, 0.02%)')" onmouseout="c()">
<title>srcu_read_lock (77 samples, 0.02%)</title><rect x="913.5" y="353" width="0.2" height="15.0" fill="rgb(236,159,30)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('grab_cache_page_write_begin (39 samples, 0.01%)')" onmouseout="c()">
<title>grab_cache_page_write_begin (39 samples, 0.01%)</title><rect x="970.4" y="257" width="0.2" height="15.0" fill="rgb(213,2,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('do_IRQ (265 samples, 0.06%)')" onmouseout="c()">
<title>do_IRQ (265 samples, 0.06%)</title><rect x="908.9" y="337" width="0.7" height="15.0" fill="rgb(219,133,48)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_curr (861 samples, 0.20%)')" onmouseout="c()">
<title>update_curr (861 samples, 0.20%)</title><rect x="1017.4" y="193" width="2.4" height="15.0" fill="rgb(218,93,52)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('copy_user_generic_string (510 samples, 0.12%)')" onmouseout="c()">
<title>copy_user_generic_string (510 samples, 0.12%)</title><rect x="587.7" y="321" width="1.4" height="15.0" fill="rgb(228,65,19)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('fix_small_imbalance (53 samples, 0.01%)')" onmouseout="c()">
<title>fix_small_imbalance (53 samples, 0.01%)</title><rect x="669.6" y="289" width="0.1" height="15.0" fill="rgb(206,210,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('default_wake_function (1,408 samples, 0.33%)')" onmouseout="c()">
<title>default_wake_function (1,408 samples, 0.33%)</title><rect x="791.2" y="129" width="4.0" height="15.0" fill="rgb(208,124,20)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('handle_wrmsr (24,550 samples, 5.77%)')" onmouseout="c()">
<title>handle_wrmsr (24,550 samples, 5.77%)</title><rect x="756.6" y="321" width="68.1" height="15.0" fill="rgb(219,72,31)" rx="2" ry="2" />
<text text-anchor="" x="759.558706548405" y="331.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >handle_..</text>
</g>
<g class="func_g" onmouseover="s('_cond_resched (311 samples, 0.07%)')" onmouseout="c()">
<title>_cond_resched (311 samples, 0.07%)</title><rect x="553.5" y="321" width="0.9" height="15.0" fill="rgb(243,183,12)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('smp_reschedule_interrupt (1,362 samples, 0.32%)')" onmouseout="c()">
<title>smp_reschedule_interrupt (1,362 samples, 0.32%)</title><rect x="1113.2" y="401" width="3.8" height="15.0" fill="rgb(236,201,24)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('hrtimer_start (7,417 samples, 1.74%)')" onmouseout="c()">
<title>hrtimer_start (7,417 samples, 1.74%)</title><rect x="797.5" y="225" width="20.6" height="15.0" fill="rgb(252,146,16)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__generic_file_aio_write (231 samples, 0.05%)')" onmouseout="c()">
<title>__generic_file_aio_write (231 samples, 0.05%)</title><rect x="970.3" y="305" width="0.7" height="15.0" fill="rgb(240,68,50)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('autoremove_wake_function (47 samples, 0.01%)')" onmouseout="c()">
<title>autoremove_wake_function (47 samples, 0.01%)</title><rect x="388.7" y="161" width="0.2" height="15.0" fill="rgb(226,61,12)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_write_msr_safe (468 samples, 0.11%)')" onmouseout="c()">
<title>native_write_msr_safe (468 samples, 0.11%)</title><rect x="219.1" y="145" width="1.3" height="15.0" fill="rgb(215,163,22)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('lapic_next_event (188 samples, 0.04%)')" onmouseout="c()">
<title>lapic_next_event (188 samples, 0.04%)</title><rect x="326.0" y="273" width="0.5" height="15.0" fill="rgb(229,17,16)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_unlock_irqrestore (120 samples, 0.03%)')" onmouseout="c()">
<title>_spin_unlock_irqrestore (120 samples, 0.03%)</title><rect x="377.7" y="241" width="0.3" height="15.0" fill="rgb(250,72,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_lock (52 samples, 0.01%)')" onmouseout="c()">
<title>_spin_lock (52 samples, 0.01%)</title><rect x="1040.5" y="337" width="0.1" height="15.0" fill="rgb(207,37,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('system_call_fastpath (412 samples, 0.10%)')" onmouseout="c()">
<title>system_call_fastpath (412 samples, 0.10%)</title><rect x="968.6" y="449" width="1.1" height="15.0" fill="rgb(224,162,39)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('this_cpu_load (90 samples, 0.02%)')" onmouseout="c()">
<title>this_cpu_load (90 samples, 0.02%)</title><rect x="1111.6" y="401" width="0.3" height="15.0" fill="rgb(228,193,47)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('system_call_fastpath (124 samples, 0.03%)')" onmouseout="c()">
<title>system_call_fastpath (124 samples, 0.03%)</title><rect x="968.2" y="449" width="0.3" height="15.0" fill="rgb(224,162,39)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_cfs_load (433 samples, 0.10%)')" onmouseout="c()">
<title>update_cfs_load (433 samples, 0.10%)</title><rect x="625.4" y="241" width="1.2" height="15.0" fill="rgb(242,121,52)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('irq_exit (424 samples, 0.10%)')" onmouseout="c()">
<title>irq_exit (424 samples, 0.10%)</title><rect x="1044.1" y="385" width="1.2" height="15.0" fill="rgb(216,86,39)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vmx_save_host_state (1,955 samples, 0.46%)')" onmouseout="c()">
<title>vmx_save_host_state (1,955 samples, 0.46%)</title><rect x="833.6" y="337" width="5.5" height="15.0" fill="rgb(229,161,33)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('dequeue_task_fair (6,497 samples, 1.53%)')" onmouseout="c()">
<title>dequeue_task_fair (6,497 samples, 1.53%)</title><rect x="621.6" y="273" width="18.1" height="15.0" fill="rgb(205,43,0)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vmx_inject_irq (840 samples, 0.20%)')" onmouseout="c()">
<title>vmx_inject_irq (840 samples, 0.20%)</title><rect x="928.6" y="353" width="2.4" height="15.0" fill="rgb(206,62,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('handle_pause (479 samples, 0.11%)')" onmouseout="c()">
<title>handle_pause (479 samples, 0.11%)</title><rect x="755.2" y="321" width="1.4" height="15.0" fill="rgb(223,162,13)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tick_check_idle (85 samples, 0.02%)')" onmouseout="c()">
<title>tick_check_idle (85 samples, 0.02%)</title><rect x="1183.0" y="321" width="0.2" height="15.0" fill="rgb(235,88,28)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kthread (78 samples, 0.02%)')" onmouseout="c()">
<title>kthread (78 samples, 0.02%)</title><rect x="973.4" y="433" width="0.2" height="15.0" fill="rgb(254,163,7)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_load_guest_fpu (706 samples, 0.17%)')" onmouseout="c()">
<title>kvm_load_guest_fpu (706 samples, 0.17%)</title><rect x="585.1" y="337" width="2.0" height="15.0" fill="rgb(222,228,20)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_load_sp0 (145 samples, 0.03%)')" onmouseout="c()">
<title>native_load_sp0 (145 samples, 0.03%)</title><rect x="977.5" y="465" width="0.4" height="15.0" fill="rgb(243,63,32)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('intel_pmu_disable_all (475 samples, 0.11%)')" onmouseout="c()">
<title>intel_pmu_disable_all (475 samples, 0.11%)</title><rect x="210.9" y="193" width="1.3" height="15.0" fill="rgb(251,19,31)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('call_softirq (62 samples, 0.01%)')" onmouseout="c()">
<title>call_softirq (62 samples, 0.01%)</title><rect x="1186.9" y="289" width="0.2" height="15.0" fill="rgb(240,215,19)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rb_next (63 samples, 0.01%)')" onmouseout="c()">
<title>rb_next (63 samples, 0.01%)</title><rect x="1175.5" y="385" width="0.2" height="15.0" fill="rgb(251,1,22)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('smp_apic_timer_interrupt (810 samples, 0.19%)')" onmouseout="c()">
<title>smp_apic_timer_interrupt (810 samples, 0.19%)</title><rect x="1181.1" y="353" width="2.3" height="15.0" fill="rgb(205,22,48)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_read_tsc (959 samples, 0.23%)')" onmouseout="c()">
<title>native_read_tsc (959 samples, 0.23%)</title><rect x="700.9" y="337" width="2.7" height="15.0" fill="rgb(249,99,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('restore_args (43 samples, 0.01%)')" onmouseout="c()">
<title>restore_args (43 samples, 0.01%)</title><rect x="1117.0" y="417" width="0.1" height="15.0" fill="rgb(243,81,46)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rcu_enter_nohz (127 samples, 0.03%)')" onmouseout="c()">
<title>rcu_enter_nohz (127 samples, 0.03%)</title><rect x="1176.8" y="417" width="0.4" height="15.0" fill="rgb(229,176,51)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__hrtimer_start_range_ns (6,394 samples, 1.50%)')" onmouseout="c()">
<title>__hrtimer_start_range_ns (6,394 samples, 1.50%)</title><rect x="797.6" y="209" width="17.8" height="15.0" fill="rgb(250,176,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('gfn_to_hva (112 samples, 0.03%)')" onmouseout="c()">
<title>gfn_to_hva (112 samples, 0.03%)</title><rect x="589.1" y="321" width="0.3" height="15.0" fill="rgb(234,174,13)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('prepare_to_wait (107 samples, 0.03%)')" onmouseout="c()">
<title>prepare_to_wait (107 samples, 0.03%)</title><rect x="708.9" y="337" width="0.3" height="15.0" fill="rgb(227,79,27)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('read_tsc (245 samples, 0.06%)')" onmouseout="c()">
<title>read_tsc (245 samples, 0.06%)</title><rect x="325.3" y="257" width="0.7" height="15.0" fill="rgb(233,66,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('copy_user_generic_string (90 samples, 0.02%)')" onmouseout="c()">
<title>copy_user_generic_string (90 samples, 0.02%)</title><rect x="693.0" y="321" width="0.2" height="15.0" fill="rgb(228,65,19)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('raise_softirq (485 samples, 0.11%)')" onmouseout="c()">
<title>raise_softirq (485 samples, 0.11%)</title><rect x="178.9" y="241" width="1.3" height="15.0" fill="rgb(249,163,14)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__rcu_pending (537 samples, 0.13%)')" onmouseout="c()">
<title>__rcu_pending (537 samples, 0.13%)</title><rect x="177.3" y="241" width="1.4" height="15.0" fill="rgb(252,143,42)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_curr (12,697 samples, 2.99%)')" onmouseout="c()">
<title>update_curr (12,697 samples, 2.99%)</title><rect x="242.5" y="225" width="35.2" height="15.0" fill="rgb(218,93,52)" rx="2" ry="2" />
<text text-anchor="" x="245.487951509678" y="235.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >up..</text>
</g>
<g class="func_g" onmouseover="s('smp_apic_timer_interrupt (20,741 samples, 4.88%)')" onmouseout="c()">
<title>smp_apic_timer_interrupt (20,741 samples, 4.88%)</title><rect x="988.8" y="401" width="57.6" height="15.0" fill="rgb(205,22,48)" rx="2" ry="2" />
<text text-anchor="" x="991.827397878901" y="411.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >smp_ap..</text>
</g>
<g class="func_g" onmouseover="s('hrtimer_cancel (455 samples, 0.11%)')" onmouseout="c()">
<title>hrtimer_cancel (455 samples, 0.11%)</title><rect x="821.1" y="257" width="1.3" height="15.0" fill="rgb(211,94,12)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('call_softirq (17,577 samples, 4.13%)')" onmouseout="c()">
<title>call_softirq (17,577 samples, 4.13%)</title><rect x="344.3" y="289" width="48.8" height="15.0" fill="rgb(240,215,19)" rx="2" ry="2" />
<text text-anchor="" x="347.291285336946" y="299.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >call..</text>
</g>
<g class="func_g" onmouseover="s('hrtimer_get_next_event (117 samples, 0.03%)')" onmouseout="c()">
<title>hrtimer_get_next_event (117 samples, 0.03%)</title><rect x="1163.4" y="417" width="0.4" height="15.0" fill="rgb(234,169,24)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__hrtimer_start_range_ns (259 samples, 0.06%)')" onmouseout="c()">
<title>__hrtimer_start_range_ns (259 samples, 0.06%)</title><rect x="796.8" y="225" width="0.7" height="15.0" fill="rgb(250,176,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__dequeue_entity (400 samples, 0.09%)')" onmouseout="c()">
<title>__dequeue_entity (400 samples, 0.09%)</title><rect x="1131.1" y="385" width="1.1" height="15.0" fill="rgb(254,73,10)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vfs_ioctl (331,273 samples, 77.92%)')" onmouseout="c()">
<title>vfs_ioctl (331,273 samples, 77.92%)</title><rect x="15.6" y="385" width="919.4" height="15.0" fill="rgb(237,75,25)" rx="2" ry="2" />
<text text-anchor="" x="18.5619923933982" y="395.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >vfs_ioctl</text>
</g>
<g class="func_g" onmouseover="s('do_timer (450 samples, 0.11%)')" onmouseout="c()">
<title>do_timer (450 samples, 0.11%)</title><rect x="1040.6" y="337" width="1.3" height="15.0" fill="rgb(223,101,40)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('do_vfs_ioctl (331,273 samples, 77.92%)')" onmouseout="c()">
<title>do_vfs_ioctl (331,273 samples, 77.92%)</title><rect x="15.6" y="401" width="919.4" height="15.0" fill="rgb(240,120,33)" rx="2" ry="2" />
<text text-anchor="" x="18.5619923933982" y="411.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >do_vfs_ioctl</text>
</g>
<g class="func_g" onmouseover="s('_spin_lock_irq (64 samples, 0.02%)')" onmouseout="c()">
<title>_spin_lock_irq (64 samples, 0.02%)</title><rect x="352.5" y="257" width="0.2" height="15.0" fill="rgb(221,92,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_cfs_load (65 samples, 0.02%)')" onmouseout="c()">
<title>update_cfs_load (65 samples, 0.02%)</title><rect x="639.0" y="257" width="0.2" height="15.0" fill="rgb(242,121,52)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rb_insert_color (1,255 samples, 0.30%)')" onmouseout="c()">
<title>rb_insert_color (1,255 samples, 0.30%)</title><rect x="127.8" y="273" width="3.5" height="15.0" fill="rgb(222,158,21)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('cpuidle_idle_call (202 samples, 0.05%)')" onmouseout="c()">
<title>cpuidle_idle_call (202 samples, 0.05%)</title><rect x="1178.7" y="449" width="0.6" height="15.0" fill="rgb(214,193,0)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_cfs_shares (276 samples, 0.06%)')" onmouseout="c()">
<title>update_cfs_shares (276 samples, 0.06%)</title><rect x="278.1" y="241" width="0.8" height="15.0" fill="rgb(240,28,1)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('account_process_tick (2,398 samples, 0.56%)')" onmouseout="c()">
<title>account_process_tick (2,398 samples, 0.56%)</title><rect x="162.2" y="257" width="6.7" height="15.0" fill="rgb(250,142,19)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('main (293 samples, 0.07%)')" onmouseout="c()">
<title>main (293 samples, 0.07%)</title><rect x="970.2" y="449" width="0.8" height="15.0" fill="rgb(210,111,50)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_unlock_irqrestore (231 samples, 0.05%)')" onmouseout="c()">
<title>_spin_unlock_irqrestore (231 samples, 0.05%)</title><rect x="815.9" y="209" width="0.7" height="15.0" fill="rgb(250,72,43)" rx="2" ry="2" />
</g>
</svg>

# C++ 崩溃测试套件

本目录包含5种典型的C++崩溃测试用例和多种分析工具。

## 📋 测试文件

| 文件名 | 崩溃类型 | 故障类别 |
|--------|----------|----------|
| `normal_execution.cpp` | 正常执行 | 对照组 |
| `null_pointer.cpp` | 空指针解引用 | 内存访问错误 |
| `deadlock.cpp` | 并发死锁 | **并发故障** |
| `memory_leak.cpp` | 内存泄漏 | **内存故障** |
| `out_of_memory.cpp` | 内存耗尽 | **OOM故障** |
| `buffer_overflow.cpp` | 缓冲区溢出 | 内存访问错误 |

## 🛠️ 分析工具

### ASan (AddressSanitizer)
- 位置: `ASan/ASan.sh`
- 功能: 内存错误检测、缓冲区溢出、使用后释放等
- 用法: `cd ASan && ./ASan.sh ../test_file.cpp`

### Valgrind (计划中)
- 位置: `Valgrind/valgrind.sh`
- 功能: 内存泄漏检测、内存错误分析

### GDB (计划中)
- 位置: `GDB/gdb.sh`
- 功能: 调试器分析、核心转储分析

## 🚀 使用方法

### 1. 工具选择器 (推荐)
```bash
# 交互式选择
./test_all_crashes.sh

# 命令行参数
./test_all_crashes.sh asan 1        # 使用ASan测试null_pointer.cpp
./test_all_crashes.sh all 0         # 使用所有工具测试所有文件
```

### 2. 直接使用工具
```bash
# 使用ASan
cd ASan
./ASan.sh ../null_pointer.cpp

# 使用其他工具 (计划中)
cd Valgrind
./valgrind.sh ../memory_leak.cpp
```

## 📊 输出格式

调用链格式：`文件名;函数1;函数2;...;错误类型`

示例：
```
null_pointer;main;func_level_1;func_level_2;func_level_3;segmentation-fault
buffer_overflow;main;func_level_1;func_level_2;func_level_3;strcpy;stack-buffer-overflow
deadlock;main;func_level_1;func_level_2;func_level_3;create_deadlock_threads;deadlock
```

## 🎯 故障覆盖

✅ **并发死锁**: `deadlock.cpp` - 多线程互相等待锁资源  
✅ **内存泄漏**: `memory_leak.cpp` - 分配内存但不释放  
✅ **OOM故障**: `out_of_memory.cpp` - 耗尽系统内存资源

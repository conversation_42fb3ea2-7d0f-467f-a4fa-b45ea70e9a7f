#include <iostream>
#include <thread>
#include <mutex>
#include <chrono>

std::mutex mutex1;
std::mutex mutex2;

void thread1_func() {
    std::lock_guard<std::mutex> lock1(mutex1);

    std::this_thread::sleep_for(std::chrono::seconds(2));


    std::lock_guard<std::mutex> lock2(mutex2);  // 死锁点
}

void thread2_func() {
    std::lock_guard<std::mutex> lock2(mutex2);

    std::this_thread::sleep_for(std::chrono::seconds(2));

    std::lock_guard<std::mutex> lock1(mutex1);  // 死锁点
}

void create_deadlock_threads() {

    std::thread t1(thread1_func);
    std::thread t2(thread2_func);

    t1.join();  // 崩溃点：死锁导致程序挂起
    t2.join();

}

void func_level_3() {
    create_deadlock_threads();
}

void func_level_2() {
    func_level_3();
}

void func_level_1() {
    func_level_2();
}

int main() {
    std::cout << "程序开始运行..." << std::endl;
    func_level_1();
    std::cout << "程序结束（不会执行到这里）" << std::endl;
    return 0;
}

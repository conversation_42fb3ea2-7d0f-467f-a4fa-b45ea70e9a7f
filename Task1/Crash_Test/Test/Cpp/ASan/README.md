# C/C++ 崩溃测试用例

本目录包含5种典型的C/C++崩溃情况测试代码，覆盖并发死锁、内存泄漏、OOM等关键故障场景。

## 📋 5种典型崩溃场景

| 文件名 | 崩溃类型 | 故障类别 | 检测工具 | 错误标识 |
|--------|----------|----------|----------|----------|
| `null_pointer.cpp` | 空指针解引用 | 内存访问错误 | AddressSanitizer | `segmentation-fault` |
| `deadlock.cpp` | 并发死锁 | **并发故障** | 系统检测/超时 | `deadlock` |
| `memory_leak.cpp` | 内存泄漏 | **内存故障** | AddressSanitizer | `memory-leak` |
| `out_of_memory.cpp` | 内存耗尽 | **OOM故障** | 系统异常 | `out-of-memory` |
| `buffer_overflow.cpp` | 缓冲区溢出 | 内存访问错误 | AddressSanitizer | `buffer-overflow` |

## 🎯 故障覆盖要求

✅ **并发死锁**: `deadlock.cpp` - 多线程互相等待锁资源
✅ **内存泄漏**: `memory_leak.cpp` - 分配内存但不释放
✅ **OOM故障**: `out_of_memory.cpp` - 耗尽系统内存资源

## 🛠️ 使用方法

### 单个测试
```bash
./ASan.sh <test_file.cpp>
```

### 批量测试
```bash
./test_all_crashes.sh
```

## 🔍 ASan.sh 检测策略

### 1. AddressSanitizer (ASan) 检测
- **堆缓冲区溢出**: `heap-buffer-overflow`
- **栈缓冲区溢出**: `stack-buffer-overflow`
- **使用已释放内存**: `heap-use-after-free`
- **双重释放**: `double-free`
- **空指针解引用**: 通过SIGSEGV信号检测

### 2. UndefinedBehaviorSanitizer (UBSan) 检测
- **整数溢出**: `signed integer overflow`
- **除零错误**: `division by zero`
- **数组越界**: `index out of bounds`
- **类型转换错误**: `type confusion`

### 3. 系统信号检测
- **SIGSEGV (139)**: 段错误
- **SIGFPE (136)**: 浮点异常
- **SIGABRT (134)**: 程序中止
- **SIGBUS (138)**: 总线错误

### 4. 退出码检测
当sanitizer无法检测时，根据程序退出码推断错误类型。

## 📊 输出格式

调用链格式：`文件名;函数1;函数2;...;错误类型`

示例：
```
SEGV;main;func_level_1;func_level_2;func_level_3;func_level_4;func_level_5;segmentation-fault
```

## 🧪 编译选项

ASan.sh 使用以下编译选项：
```bash
-g -O0 -fsanitize=address -fsanitize=undefined -fno-omit-frame-pointer -fno-optimize-sibling-calls
```

- `-g`: 包含调试信息
- `-O0`: 禁用优化
- `-fsanitize=address`: 启用AddressSanitizer
- `-fsanitize=undefined`: 启用UndefinedBehaviorSanitizer
- `-fno-omit-frame-pointer`: 保留帧指针
- `-fno-optimize-sibling-calls`: 禁用尾调用优化

## 📁 输出文件

每次运行会在 `./文件名/` 目录下生成：
- `文件名_crash_log.txt`: 完整的崩溃日志
- `文件名_call_chain.txt`: 格式化的调用链
- `文件名_debug`: 编译后的可执行文件

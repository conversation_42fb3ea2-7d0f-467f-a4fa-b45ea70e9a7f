#include <iostream>
#include <vector>
#include <string>
#include <cstring>

void string_operations() {
    std::cout << "进入 string_operations()，执行字符串操作..." << std::endl;
    
    // 安全的字符串操作
    char buffer[100];
    const char* safe_string = "这是一个安全的字符串";
    
    std::cout << "缓冲区大小: " << sizeof(buffer) << " 字节" << std::endl;
    std::cout << "字符串长度: " << strlen(safe_string) << " 字节" << std::endl;
    
    // 使用安全的字符串复制
    strncpy(buffer, safe_string, sizeof(buffer) - 1);
    buffer[sizeof(buffer) - 1] = '\0';  // 确保字符串结束
    
    std::cout << "安全复制完成: " << buffer << std::endl;
}

void memory_operations() {
    std::cout << "进入 memory_operations()，执行内存操作..." << std::endl;
    
    // 正确的内存管理
    int* ptr = new int(42);
    std::cout << "分配内存并设置值: " << *ptr << std::endl;
    
    // 使用内存
    *ptr = 100;
    std::cout << "修改后的值: " << *ptr << std::endl;
    
    // 正确释放内存
    delete ptr;
    ptr = nullptr;  // 避免悬空指针
    std::cout << "内存已正确释放" << std::endl;
}

void array_operations() {
    std::cout << "进入 array_operations()，执行数组操作..." << std::endl;
    
    // 安全的数组访问
    std::vector<int> arr = {1, 2, 3, 4, 5};
    std::cout << "数组大小: " << arr.size() << std::endl;
    
    // 安全的索引访问
    for (size_t i = 0; i < arr.size(); i++) {
        std::cout << "arr[" << i << "] = " << arr[i] << std::endl;
    }
    
    // 使用at()进行边界检查
    try {
        std::cout << "安全访问 arr.at(2): " << arr.at(2) << std::endl;
    } catch (const std::out_of_range& e) {
        std::cout << "索引越界: " << e.what() << std::endl;
    }
}

void func_level_3() {
    std::cout << "进入 func_level_3()" << std::endl;
    string_operations();
    memory_operations();
    array_operations();
    std::cout << "func_level_3() 执行完成" << std::endl;
}

void func_level_2() {
    std::cout << "进入 func_level_2()" << std::endl;
    func_level_3();
    std::cout << "func_level_2() 执行完成" << std::endl;
}

void func_level_1() {
    std::cout << "进入 func_level_1()" << std::endl;
    func_level_2();
    std::cout << "func_level_1() 执行完成" << std::endl;
}

int main() {
    std::cout << "程序开始运行..." << std::endl;
    
    try {
        func_level_1();
        std::cout << "所有操作成功完成！" << std::endl;
    } catch (const std::exception& e) {
        std::cout << "捕获异常: " << e.what() << std::endl;
        return 1;
    }
    
    std::cout << "程序正常结束" << std::endl;
    return 0;
}

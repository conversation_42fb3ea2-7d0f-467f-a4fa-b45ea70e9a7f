#include <iostream>

void func_level_3() {
    std::cout << "进入 func_level_3()，准备访问空指针..." << std::endl;
    int* ptr = nullptr;
    std::cout << "空指针地址: " << ptr << std::endl;
    int value = *ptr;  // 崩溃点：空指针解引用
    std::cout << "不会执行到这行，value: " << value << std::endl;
}

void func_level_2() {
    std::cout << "进入 func_level_2()" << std::endl;
    func_level_3();
}

void func_level_1() {
    std::cout << "进入 func_level_1()" << std::endl;
    func_level_2();
}

int main() {
    std::cout << "程序开始运行..." << std::endl;
    func_level_1();
    std::cout << "程序结束（不会执行到这里）" << std::endl;
    return 0;
}

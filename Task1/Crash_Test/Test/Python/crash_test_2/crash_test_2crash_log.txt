Traceback (most recent call last):
  File "/home/<USER>/Ryan_/TENmini/Crash_Test/Test/Python/_wrapped_crash_runner.py", line 8, in <module>
    runpy.run_path("/home/<USER>/Ryan_/TEN<PERSON>/Crash_Test/Test/Python/crash_test_2.py", run_name="__main__")
  File "<frozen runpy>", line 291, in run_path
  File "<frozen runpy>", line 98, in _run_module_code
  File "<frozen runpy>", line 88, in _run_code
  File "/home/<USER>/Ryan_/TENmini/Crash_Test/Test/Python/crash_test_2.py", line 6, in <module>
    recurse_forever(0)
  File "/home/<USER>/Ryan_/TENmini/Crash_Test/Test/Python/crash_test_2.py", line 2, in recurse_forever
    return recurse_forever(n + 1)
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Ryan_/TENmini/Crash_Test/Test/Python/crash_test_2.py", line 2, in recurse_forever
    return recurse_forever(n + 1)
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Ryan_/TENmini/Crash_Test/Test/Python/crash_test_2.py", line 2, in recurse_forever
    return recurse_forever(n + 1)
           ^^^^^^^^^^^^^^^^^^^^^^
  [Previous line repeated 991 more times]
RecursionError: maximum recursion depth exceeded

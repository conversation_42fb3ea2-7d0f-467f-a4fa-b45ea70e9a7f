<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" width="1200" height="1266" onload="init(evt)" viewBox="0 0 1200 1266" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<!-- Flame graph stack visualization. See https://github.com/brendangregg/FlameGraph for latest version, and http://www.brendangregg.com/flamegraphs.html for examples. -->
<defs >
	<linearGradient id="background" y1="0" y2="1" x1="0" x2="0" >
		<stop stop-color="#eeeeee" offset="5%" />
		<stop stop-color="#eeeeb0" offset="95%" />
	</linearGradient>
</defs>
<style type="text/css">
	.func_g:hover { stroke:black; stroke-width:0.5; cursor:pointer; }
</style>
<script type="text/ecmascript">
<![CDATA[
	var details, searchbtn, matchedtxt, svg;
	function init(evt) { 
		details = document.getElementById("details").firstChild; 
		searchbtn = document.getElementById("search");
		matchedtxt = document.getElementById("matched");
		svg = document.getElementsByTagName("svg")[0];
		searching = 0;
	}

	// mouse-over for info
	function s(node) {		// show
		info = g_to_text(node);
		details.nodeValue = "Function: " + info;
	}
	function c() {			// clear
		details.nodeValue = ' ';
	}

	// ctrl-F for search
	window.addEventListener("keydown",function (e) {
		if (e.keyCode === 114 || (e.ctrlKey && e.keyCode === 70)) {
			e.preventDefault();
			search_prompt();
		}
	})

	// functions
	function find_child(parent, name, attr) {
		var children = parent.childNodes;
		for (var i=0; i<children.length;i++) {
			if (children[i].tagName == name)
				return (attr != undefined) ? children[i].attributes[attr].value : children[i];
		}
		return;
	}
	function orig_save(e, attr, val) {
		if (e.attributes["_orig_"+attr] != undefined) return;
		if (e.attributes[attr] == undefined) return;
		if (val == undefined) val = e.attributes[attr].value;
		e.setAttribute("_orig_"+attr, val);
	}
	function orig_load(e, attr) {
		if (e.attributes["_orig_"+attr] == undefined) return;
		e.attributes[attr].value = e.attributes["_orig_"+attr].value;
		e.removeAttribute("_orig_"+attr);
	}
	function g_to_text(e) {
		var text = find_child(e, "title").firstChild.nodeValue;
		return (text)
	}
	function g_to_func(e) {
		var func = g_to_text(e);
		if (func != null)
			func = func.replace(/ .*/, "");
		return (func);
	}
	function update_text(e) {
		var r = find_child(e, "rect");
		var t = find_child(e, "text");
		var w = parseFloat(r.attributes["width"].value) -3;
		var txt = find_child(e, "title").textContent.replace(/\([^(]*\)/,"");
		t.attributes["x"].value = parseFloat(r.attributes["x"].value) +3;
		
		// Smaller than this size won't fit anything
		if (w < 2*12*0.59) {
			t.textContent = "";
			return;
		}
		
		t.textContent = txt;
		// Fit in full text width
		if (/^ *$/.test(txt) || t.getSubStringLength(0, txt.length) < w)
			return;
		
		for (var x=txt.length-2; x>0; x--) {
			if (t.getSubStringLength(0, x+2) <= w) { 
				t.textContent = txt.substring(0,x) + "..";
				return;
			}
		}
		t.textContent = "";
	}

	// zoom
	function zoom_reset(e) {
		if (e.attributes != undefined) {
			orig_load(e, "x");
			orig_load(e, "width");
		}
		if (e.childNodes == undefined) return;
		for(var i=0, c=e.childNodes; i<c.length; i++) {
			zoom_reset(c[i]);
		}
	}
	function zoom_child(e, x, ratio) {
		if (e.attributes != undefined) {
			if (e.attributes["x"] != undefined) {
				orig_save(e, "x");
				e.attributes["x"].value = (parseFloat(e.attributes["x"].value) - x - 10) * ratio + 10;
				if(e.tagName == "text") e.attributes["x"].value = find_child(e.parentNode, "rect", "x") + 3;
			}
			if (e.attributes["width"] != undefined) {
				orig_save(e, "width");
				e.attributes["width"].value = parseFloat(e.attributes["width"].value) * ratio;
			}
		}
		
		if (e.childNodes == undefined) return;
		for(var i=0, c=e.childNodes; i<c.length; i++) {
			zoom_child(c[i], x-10, ratio);
		}
	}
	function zoom_parent(e) {
		if (e.attributes) {
			if (e.attributes["x"] != undefined) {
				orig_save(e, "x");
				e.attributes["x"].value = 10;
			}
			if (e.attributes["width"] != undefined) {
				orig_save(e, "width");
				e.attributes["width"].value = parseInt(svg.width.baseVal.value) - (10*2);
			}
		}
		if (e.childNodes == undefined) return;
		for(var i=0, c=e.childNodes; i<c.length; i++) {
			zoom_parent(c[i]);
		}
	}
	function zoom(node) { 
		var attr = find_child(node, "rect").attributes;
		var width = parseFloat(attr["width"].value);
		var xmin = parseFloat(attr["x"].value);
		var xmax = parseFloat(xmin + width);
		var ymin = parseFloat(attr["y"].value);
		var ratio = (svg.width.baseVal.value - 2*10) / width;
		
		// XXX: Workaround for JavaScript float issues (fix me)
		var fudge = 0.0001;
		
		var unzoombtn = document.getElementById("unzoom");
		unzoombtn.style["opacity"] = "1.0";
		
		var el = document.getElementsByTagName("g");
		for(var i=0;i<el.length;i++){
			var e = el[i];
			var a = find_child(e, "rect").attributes;
			var ex = parseFloat(a["x"].value);
			var ew = parseFloat(a["width"].value);
			// Is it an ancestor
			if (0 == 0) {
				var upstack = parseFloat(a["y"].value) > ymin;
			} else {
				var upstack = parseFloat(a["y"].value) < ymin;
			}
			if (upstack) {
				// Direct ancestor
				if (ex <= xmin && (ex+ew+fudge) >= xmax) {
					e.style["opacity"] = "0.5";
					zoom_parent(e);
					e.onclick = function(e){unzoom(); zoom(this);};
					update_text(e);
				}
				// not in current path
				else
					e.style["display"] = "none";
			}
			// Children maybe
			else {
				// no common path
				if (ex < xmin || ex + fudge >= xmax) {
					e.style["display"] = "none";
				}
				else {
					zoom_child(e, xmin, ratio);
					e.onclick = function(e){zoom(this);};
					update_text(e);
				}
			}
		}
	}
	function unzoom() {
		var unzoombtn = document.getElementById("unzoom");
		unzoombtn.style["opacity"] = "0.0";
		
		var el = document.getElementsByTagName("g");
		for(i=0;i<el.length;i++) {
			el[i].style["display"] = "block";
			el[i].style["opacity"] = "1";
			zoom_reset(el[i]);
			update_text(el[i]);
		}
	}	

	// search
	function reset_search() {
		var el = document.getElementsByTagName("rect");
		for (var i=0; i < el.length; i++) {
			orig_load(el[i], "fill")
		}
	}
	function search_prompt() {
		if (!searching) {
			var term = prompt("Enter a search term (regexp " +
			    "allowed, eg: ^ext4_)", "");
			if (term != null) {
				search(term)
			}
		} else {
			reset_search();
			searching = 0;
			searchbtn.style["opacity"] = "0.1";
			searchbtn.firstChild.nodeValue = "Search"
			matchedtxt.style["opacity"] = "0.0";
			matchedtxt.firstChild.nodeValue = ""
		}
	}
	function search(term) {
		var re = new RegExp(term);
		var el = document.getElementsByTagName("g");
		var matches = new Object();
		var maxwidth = 0;
		for (var i = 0; i < el.length; i++) {
			var e = el[i];
			if (e.attributes["class"].value != "func_g")
				continue;
			var func = g_to_func(e);
			var rect = find_child(e, "rect");
			if (rect == null) {
				// the rect might be wrapped in an anchor
				// if nameattr href is being used
				if (rect = find_child(e, "a")) {
				    rect = find_child(r, "rect");
				}
			}
			if (func == null || rect == null)
				continue;

			// Save max width. Only works as we have a root frame
			var w = parseFloat(rect.attributes["width"].value);
			if (w > maxwidth)
				maxwidth = w;

			if (func.match(re)) {
				// highlight
				var x = parseFloat(rect.attributes["x"].value);
				orig_save(rect, "fill");
				rect.attributes["fill"].value =
				    "rgb(230,0,230)";

				// remember matches
				if (matches[x] == undefined) {
					matches[x] = w;
				} else {
					if (w > matches[x]) {
						// overwrite with parent
						matches[x] = w;
					}
				}
				searching = 1;
			}
		}
		if (!searching)
			return;

		searchbtn.style["opacity"] = "1.0";
		searchbtn.firstChild.nodeValue = "Reset Search"

		// calculate percent matched, excluding vertical overlap
		var count = 0;
		var lastx = -1;
		var lastw = 0;
		var keys = Array();
		for (k in matches) {
			if (matches.hasOwnProperty(k))
				keys.push(k);
		}
		// sort the matched frames by their x location
		// ascending, then width descending
		keys.sort(function(a, b){
				return a - b;
			if (a < b || a > b)
				return a - b;
			return matches[b] - matches[a];
		});
		// Step through frames saving only the biggest bottom-up frames
		// thanks to the sort order. This relies on the tree property
		// where children are always smaller than their parents.
		for (var k in keys) {
			var x = parseFloat(keys[k]);
			var w = matches[keys[k]];
			if (x >= lastx + lastw) {
				count += w;
				lastx = x;
				lastw = w;
			}
		}
		// display matched percent
		matchedtxt.style["opacity"] = "1.0";
		pct = 100 * count / maxwidth;
		if (pct == 100)
			pct = "100"
		else
			pct = pct.toFixed(1)
		matchedtxt.firstChild.nodeValue = "Matched: " + pct + "%";
	}
	function searchover(e) {
		searchbtn.style["opacity"] = "1.0";
	}
	function searchout(e) {
		if (searching) {
			searchbtn.style["opacity"] = "1.0";
		} else {
			searchbtn.style["opacity"] = "0.1";
		}
	}
]]>
</script>
<rect x="0.0" y="0" width="1200.0" height="1266.0" fill="url(#background)"  />
<text text-anchor="middle" x="600.00" y="24" font-size="17" font-family="Verdana" fill="rgb(0,0,0)"  >Flame Graph</text>
<text text-anchor="" x="10.00" y="1249" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" id="details" > </text>
<text text-anchor="" x="10.00" y="24" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" id="unzoom" onclick="unzoom()" style="opacity:0.0;cursor:pointer" >Reset Zoom</text>
<text text-anchor="" x="1090.00" y="24" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" id="search" onmouseover="searchover()" onmouseout="searchout()" onclick="search_prompt()" style="opacity:0.1;cursor:pointer" >Search</text>
<text text-anchor="" x="1090.00" y="1249" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" id="matched" > </text>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>rw_verify_area (9 samples, 0.68%)</title><rect x="1058.1" y="1041" width="8.1" height="15.0" fill="rgb(223,123,0)" rx="2" ry="2" />
<text text-anchor="" x="1061.09" y="1051.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>_raw_spin_lock_irqsave (2 samples, 0.15%)</title><rect x="990.8" y="1057" width="1.8" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="993.79" y="1067.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>sun/nio/ch/FileDispatcherImpl:.read0 (31 samples, 2.36%)</title><rect x="71.9" y="897" width="27.8" height="15.0" fill="rgb(88,235,88)" rx="2" ry="2" />
<text text-anchor="" x="74.92" y="907.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >s..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>do_sync_read (22 samples, 1.67%)</title><rect x="76.4" y="817" width="19.7" height="15.0" fill="rgb(243,143,0)" rx="2" ry="2" />
<text text-anchor="" x="79.40" y="827.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>sun/nio/ch/SocketChannelImpl:.write (209 samples, 15.89%)</title><rect x="692.9" y="769" width="187.5" height="15.0" fill="rgb(88,235,88)" rx="2" ry="2" />
<text text-anchor="" x="695.87" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >sun/nio/ch/SocketChannel..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>timerqueue_del (1 samples, 0.08%)</title><rect x="966.6" y="1009" width="0.9" height="15.0" fill="rgb(231,131,0)" rx="2" ry="2" />
<text text-anchor="" x="969.56" y="1019.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/channel/AdaptiveRecvByteBufAllocator$HandleImpl:.record (2 samples, 0.15%)</title><rect x="52.2" y="961" width="1.8" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="55.17" y="971.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptRuntime:.setObjectProp (86 samples, 6.54%)</title><rect x="514.3" y="785" width="77.2" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="517.30" y="795.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >org/mozi..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>read_tsc (1 samples, 0.08%)</title><rect x="956.7" y="1041" width="0.9" height="15.0" fill="rgb(242,142,0)" rx="2" ry="2" />
<text text-anchor="" x="959.69" y="1051.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/gen/file__root_vert_x_2_1_5_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_2 (14 samples, 1.06%)</title><rect x="598.7" y="769" width="12.5" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="601.65" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptableObject:.putImpl (45 samples, 3.42%)</title><rect x="550.2" y="753" width="40.4" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="553.20" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >org..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>netdev_pick_tx (1 samples, 0.08%)</title><rect x="754.8" y="481" width="0.9" height="15.0" fill="rgb(244,144,0)" rx="2" ry="2" />
<text text-anchor="" x="757.79" y="491.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/channel/AbstractChannelHandlerContext:.write (33 samples, 2.51%)</title><rect x="205.6" y="705" width="29.6" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="208.62" y="715.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >io..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>java/lang/String:.equals (1 samples, 0.08%)</title><rect x="631.9" y="865" width="0.9" height="15.0" fill="rgb(77,224,77)" rx="2" ry="2" />
<text text-anchor="" x="634.86" y="875.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>system_call_fastpath (7 samples, 0.53%)</title><rect x="1179.2" y="1089" width="6.3" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="1182.23" y="1099.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>GCTaskManager::get_task (1 samples, 0.08%)</title><rect x="19.9" y="1137" width="0.9" height="15.0" fill="rgb(226,226,68)" rx="2" ry="2" />
<text text-anchor="" x="22.87" y="1147.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>security_file_free (1 samples, 0.08%)</title><rect x="904.6" y="977" width="0.9" height="15.0" fill="rgb(238,138,0)" rx="2" ry="2" />
<text text-anchor="" x="907.65" y="987.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>apparmor_socket_recvmsg (5 samples, 0.38%)</title><rect x="76.4" y="769" width="4.5" height="15.0" fill="rgb(232,132,0)" rx="2" ry="2" />
<text text-anchor="" x="79.40" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>itable stub (1 samples, 0.08%)</title><rect x="168.8" y="801" width="0.9" height="15.0" fill="rgb(237,104,104)" rx="2" ry="2" />
<text text-anchor="" x="171.83" y="811.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>skb_release_data (3 samples, 0.23%)</title><rect x="803.2" y="193" width="2.7" height="15.0" fill="rgb(230,130,0)" rx="2" ry="2" />
<text text-anchor="" x="806.25" y="203.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>hrtimer_try_to_cancel (3 samples, 0.23%)</title><rect x="1008.7" y="1041" width="2.7" height="15.0" fill="rgb(232,132,0)" rx="2" ry="2" />
<text text-anchor="" x="1011.74" y="1051.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>default_wake_function (25 samples, 1.90%)</title><rect x="819.4" y="145" width="22.4" height="15.0" fill="rgb(246,146,0)" rx="2" ry="2" />
<text text-anchor="" x="822.40" y="155.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >d..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__remove_hrtimer (3 samples, 0.23%)</title><rect x="947.7" y="1089" width="2.7" height="15.0" fill="rgb(228,128,0)" rx="2" ry="2" />
<text text-anchor="" x="950.72" y="1099.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>epoll_ctl (1 samples, 0.08%)</title><rect x="976.4" y="1153" width="0.9" height="15.0" fill="rgb(248,120,120)" rx="2" ry="2" />
<text text-anchor="" x="979.43" y="1163.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>fsnotify (1 samples, 0.08%)</title><rect x="1065.3" y="1009" width="0.9" height="15.0" fill="rgb(241,141,0)" rx="2" ry="2" />
<text text-anchor="" x="1068.27" y="1019.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptRuntime:.nameOrFunction (4 samples, 0.30%)</title><rect x="272.0" y="721" width="3.6" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="275.02" y="731.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>tcp_clean_rtx_queue (1 samples, 0.08%)</title><rect x="811.3" y="257" width="0.9" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="814.32" y="267.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>tcp_send_delayed_ack (5 samples, 0.38%)</title><rect x="783.5" y="241" width="4.5" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="786.51" y="251.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptRuntime:.nameOrFunction (1 samples, 0.08%)</title><rect x="237.9" y="785" width="0.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="240.92" y="795.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>java/lang/String:.hashCode (1 samples, 0.08%)</title><rect x="666.0" y="897" width="0.9" height="15.0" fill="rgb(77,224,77)" rx="2" ry="2" />
<text text-anchor="" x="668.95" y="907.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>tcp_v4_rcv (87 samples, 6.62%)</title><rect x="770.0" y="305" width="78.1" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="773.05" y="315.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >tcp_v4_rcv</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>aeProcessEvents (1 samples, 0.08%)</title><rect x="1189.1" y="1153" width="0.9" height="15.0" fill="rgb(233,98,98)" rx="2" ry="2" />
<text text-anchor="" x="1192.10" y="1163.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/NativeJavaObject:.initMembers (1 samples, 0.08%)</title><rect x="151.8" y="833" width="0.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="154.78" y="843.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>schedule_preempt_disabled (2 samples, 0.15%)</title><rect x="963.0" y="1105" width="1.8" height="15.0" fill="rgb(236,136,0)" rx="2" ry="2" />
<text text-anchor="" x="965.97" y="1115.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>kfree (1 samples, 0.08%)</title><rect x="805.0" y="161" width="0.9" height="15.0" fill="rgb(229,129,0)" rx="2" ry="2" />
<text text-anchor="" x="808.04" y="171.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>sun/nio/ch/SocketChannelImpl:.write (1 samples, 0.08%)</title><rect x="882.2" y="785" width="0.9" height="15.0" fill="rgb(88,235,88)" rx="2" ry="2" />
<text text-anchor="" x="885.21" y="795.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>sk_reset_timer (2 samples, 0.15%)</title><rect x="735.9" y="561" width="1.8" height="15.0" fill="rgb(222,122,0)" rx="2" ry="2" />
<text text-anchor="" x="738.95" y="571.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptableObject:.putImpl (6 samples, 0.46%)</title><rect x="401.2" y="737" width="5.4" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="404.24" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>remote_function (4 samples, 0.30%)</title><rect x="914.5" y="1073" width="3.6" height="15.0" fill="rgb(242,142,0)" rx="2" ry="2" />
<text text-anchor="" x="917.52" y="1083.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/handler/codec/http/HttpObjectDecoder:.skipControlCharacters (1 samples, 0.08%)</title><rect x="670.4" y="913" width="0.9" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="673.44" y="923.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>intel_pmu_enable_all (4 samples, 0.30%)</title><rect x="906.4" y="881" width="3.6" height="15.0" fill="rgb(236,136,0)" rx="2" ry="2" />
<text text-anchor="" x="909.44" y="891.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>mod_timer (5 samples, 0.38%)</title><rect x="783.5" y="209" width="4.5" height="15.0" fill="rgb(229,129,0)" rx="2" ry="2" />
<text text-anchor="" x="786.51" y="219.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/handler/codec/MessageToMessageEncoder:.write (31 samples, 2.36%)</title><rect x="205.6" y="689" width="27.8" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="208.62" y="699.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >i..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/buffer/UnpooledHeapByteBuf:.init (1 samples, 0.08%)</title><rect x="186.8" y="737" width="0.9" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="189.78" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>intel_pmu_enable_all (4 samples, 0.30%)</title><rect x="1128.1" y="705" width="3.6" height="15.0" fill="rgb(236,136,0)" rx="2" ry="2" />
<text text-anchor="" x="1131.08" y="715.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/channel/AbstractChannelHandlerContext:.write (2 samples, 0.15%)</title><rect x="190.4" y="753" width="1.8" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="193.37" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>enqueue_hrtimer (1 samples, 0.08%)</title><rect x="1007.8" y="1041" width="0.9" height="15.0" fill="rgb(248,148,0)" rx="2" ry="2" />
<text text-anchor="" x="1010.84" y="1051.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>itable stub (1 samples, 0.08%)</title><rect x="492.8" y="769" width="0.9" height="15.0" fill="rgb(237,104,104)" rx="2" ry="2" />
<text text-anchor="" x="495.77" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptableObject$Slot:.setAttributes (12 samples, 0.91%)</title><rect x="467.6" y="753" width="10.8" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="470.64" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>cpuidle_idle_call (6 samples, 0.46%)</title><rect x="954.0" y="1089" width="5.4" height="15.0" fill="rgb(223,123,0)" rx="2" ry="2" />
<text text-anchor="" x="957.00" y="1099.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>system_call (1 samples, 0.08%)</title><rect x="975.5" y="1105" width="0.9" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="978.54" y="1115.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/IdScriptableObject:.get (2 samples, 0.15%)</title><rect x="160.8" y="785" width="1.7" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="163.75" y="795.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>[unknown] (6 samples, 0.46%)</title><rect x="904.6" y="1137" width="5.4" height="15.0" fill="rgb(243,112,112)" rx="2" ry="2" />
<text text-anchor="" x="907.65" y="1147.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>Monitor::IWait (1 samples, 0.08%)</title><rect x="19.9" y="1105" width="0.9" height="15.0" fill="rgb(213,213,64)" rx="2" ry="2" />
<text text-anchor="" x="22.87" y="1115.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptRuntime:.bind (1 samples, 0.08%)</title><rect x="326.8" y="737" width="0.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="329.76" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptableObject:.getSlot (40 samples, 3.04%)</title><rect x="554.7" y="737" width="35.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="557.68" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >org..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__wake_up_sync_key (3 samples, 0.23%)</title><rect x="1158.6" y="593" width="2.7" height="15.0" fill="rgb(223,123,0)" rx="2" ry="2" />
<text text-anchor="" x="1161.59" y="603.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>system_call_fastpath (1 samples, 0.08%)</title><rect x="902.9" y="1089" width="0.8" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="905.85" y="1099.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>vfs_write (85 samples, 6.46%)</title><rect x="1103.0" y="1057" width="76.2" height="15.0" fill="rgb(226,126,0)" rx="2" ry="2" />
<text text-anchor="" x="1105.96" y="1067.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >vfs_write</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>mod_timer (2 samples, 0.15%)</title><rect x="1154.1" y="577" width="1.8" height="15.0" fill="rgb(229,129,0)" rx="2" ry="2" />
<text text-anchor="" x="1157.11" y="587.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>rcu_sysidle_enter (1 samples, 0.08%)</title><rect x="936.1" y="1137" width="0.9" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="939.05" y="1147.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>oopDesc* PSPromotionManager::copy_to_survivor_spacefalse (1 samples, 0.08%)</title><rect x="43.2" y="1057" width="0.9" height="15.0" fill="rgb(223,223,67)" rx="2" ry="2" />
<text text-anchor="" x="46.20" y="1067.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__wake_up_common (2 samples, 0.15%)</title><rect x="1158.6" y="577" width="1.8" height="15.0" fill="rgb(223,123,0)" rx="2" ry="2" />
<text text-anchor="" x="1161.59" y="587.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead (637 samples, 48.44%)</title><rect x="101.5" y="945" width="571.6" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="104.53" y="955.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>_raw_spin_lock_irqsave (2 samples, 0.15%)</title><rect x="823.0" y="113" width="1.8" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="825.99" y="123.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>ScavengeRootsTask::do_it (1 samples, 0.08%)</title><rect x="38.7" y="1137" width="0.9" height="15.0" fill="rgb(223,223,67)" rx="2" ry="2" />
<text text-anchor="" x="41.71" y="1147.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>tcp_urg (1 samples, 0.08%)</title><rect x="847.2" y="257" width="0.9" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="850.22" y="267.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>aa_file_perm (1 samples, 0.08%)</title><rect x="1063.5" y="977" width="0.9" height="15.0" fill="rgb(216,116,0)" rx="2" ry="2" />
<text text-anchor="" x="1066.48" y="987.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptRuntime:.setObjectProp (21 samples, 1.60%)</title><rect x="413.8" y="769" width="18.8" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="416.80" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__remove_hrtimer (1 samples, 0.08%)</title><rect x="1008.7" y="1025" width="0.9" height="15.0" fill="rgb(228,128,0)" rx="2" ry="2" />
<text text-anchor="" x="1011.74" y="1035.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>put_filp (1 samples, 0.08%)</title><rect x="904.6" y="993" width="0.9" height="15.0" fill="rgb(233,133,0)" rx="2" ry="2" />
<text text-anchor="" x="907.65" y="1003.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>skb_free_head (1 samples, 0.08%)</title><rect x="805.0" y="177" width="0.9" height="15.0" fill="rgb(230,130,0)" rx="2" ry="2" />
<text text-anchor="" x="808.04" y="187.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>apparmor_file_permission (1 samples, 0.08%)</title><rect x="875.9" y="641" width="0.9" height="15.0" fill="rgb(232,132,0)" rx="2" ry="2" />
<text text-anchor="" x="878.93" y="651.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>ktime_get (1 samples, 0.08%)</title><rect x="928.9" y="1121" width="0.9" height="15.0" fill="rgb(222,122,0)" rx="2" ry="2" />
<text text-anchor="" x="931.87" y="1131.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>JavaCalls::call_virtual (956 samples, 72.70%)</title><rect x="45.0" y="1105" width="857.9" height="15.0" fill="rgb(203,203,60)" rx="2" ry="2" />
<text text-anchor="" x="48.00" y="1115.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >JavaCalls::call_virtual</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__copy_skb_header (1 samples, 0.08%)</title><rect x="854.4" y="529" width="0.9" height="15.0" fill="rgb(230,130,0)" rx="2" ry="2" />
<text text-anchor="" x="857.40" y="539.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__slab_alloc (1 samples, 0.08%)</title><rect x="864.3" y="577" width="0.9" height="15.0" fill="rgb(227,127,0)" rx="2" ry="2" />
<text text-anchor="" x="867.27" y="587.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>cpuidle_idle_call (1 samples, 0.08%)</title><rect x="935.2" y="1153" width="0.9" height="15.0" fill="rgb(223,123,0)" rx="2" ry="2" />
<text text-anchor="" x="938.16" y="1163.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/IdScriptableObject:.has (30 samples, 2.28%)</title><rect x="521.5" y="769" width="26.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="524.48" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >o..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>ip_queue_xmit (51 samples, 3.88%)</title><rect x="1120.0" y="929" width="45.8" height="15.0" fill="rgb(230,130,0)" rx="2" ry="2" />
<text text-anchor="" x="1123.01" y="939.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >ip_q..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/NativeCall:.init (15 samples, 1.14%)</title><rect x="257.7" y="721" width="13.4" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="260.67" y="731.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>tcp_ack (9 samples, 0.68%)</title><rect x="1147.8" y="625" width="8.1" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="1150.83" y="635.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>sys_ioctl (5 samples, 0.38%)</title><rect x="905.5" y="1089" width="4.5" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="908.54" y="1099.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>fsnotify (2 samples, 0.15%)</title><rect x="1177.4" y="1041" width="1.8" height="15.0" fill="rgb(241,141,0)" rx="2" ry="2" />
<text text-anchor="" x="1180.44" y="1051.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>sk_reset_timer (2 samples, 0.15%)</title><rect x="1154.1" y="593" width="1.8" height="15.0" fill="rgb(222,122,0)" rx="2" ry="2" />
<text text-anchor="" x="1157.11" y="603.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptableObject:.addKnownAbsentSlot (1 samples, 0.08%)</title><rect x="453.3" y="721" width="0.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="456.29" y="731.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>lapic_next_deadline (2 samples, 0.15%)</title><rect x="950.4" y="1057" width="1.8" height="15.0" fill="rgb(220,120,0)" rx="2" ry="2" />
<text text-anchor="" x="953.41" y="1067.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/gen/file__root_vert_x_2_1_5_Server2_js_1:.call (79 samples, 6.01%)</title><rect x="167.9" y="817" width="70.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="170.93" y="827.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >org/mozi..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>sys_execve (1 samples, 0.08%)</title><rect x="904.6" y="1073" width="0.9" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="907.65" y="1083.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>perf_event_enable (5 samples, 0.38%)</title><rect x="905.5" y="1025" width="4.5" height="15.0" fill="rgb(242,142,0)" rx="2" ry="2" />
<text text-anchor="" x="908.54" y="1035.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>sys_futex (1 samples, 0.08%)</title><rect x="19.9" y="1073" width="0.9" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="22.87" y="1083.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>java/lang/String:.init (1 samples, 0.08%)</title><rect x="663.3" y="881" width="0.9" height="15.0" fill="rgb(77,224,77)" rx="2" ry="2" />
<text text-anchor="" x="666.26" y="891.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>inet_recvmsg (7 samples, 0.53%)</title><rect x="1051.8" y="993" width="6.3" height="15.0" fill="rgb(239,139,0)" rx="2" ry="2" />
<text text-anchor="" x="1054.81" y="1003.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/gen/file__root_vert_x_2_1_5_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_2 (2 samples, 0.15%)</title><rect x="17.2" y="1153" width="1.8" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="20.18" y="1163.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/util/internal/AppendableCharSequence:.substring (4 samples, 0.30%)</title><rect x="654.3" y="865" width="3.6" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="657.29" y="875.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>_raw_spin_lock_irqsave (1 samples, 0.08%)</title><rect x="1160.4" y="577" width="0.9" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="1163.39" y="587.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>x86_pmu_enable (4 samples, 0.30%)</title><rect x="906.4" y="897" width="3.6" height="15.0" fill="rgb(248,148,0)" rx="2" ry="2" />
<text text-anchor="" x="909.44" y="907.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__libc_read (1 samples, 0.08%)</title><rect x="1066.2" y="1105" width="0.9" height="15.0" fill="rgb(236,103,103)" rx="2" ry="2" />
<text text-anchor="" x="1069.17" y="1115.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>tcp_sendmsg (77 samples, 5.86%)</title><rect x="1106.5" y="993" width="69.1" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="1109.55" y="1003.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >tcp_sen..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>cpuidle_enter_state (12 samples, 0.91%)</title><rect x="918.1" y="1121" width="10.8" height="15.0" fill="rgb(223,123,0)" rx="2" ry="2" />
<text text-anchor="" x="921.11" y="1131.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>flush_tlb_mm_range (1 samples, 0.08%)</title><rect x="902.9" y="1009" width="0.8" height="15.0" fill="rgb(229,129,0)" rx="2" ry="2" />
<text text-anchor="" x="905.85" y="1019.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>ksize (1 samples, 0.08%)</title><rect x="867.0" y="577" width="0.9" height="15.0" fill="rgb(223,123,0)" rx="2" ry="2" />
<text text-anchor="" x="869.96" y="587.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>cpu_startup_entry (44 samples, 3.35%)</title><rect x="913.6" y="1169" width="39.5" height="15.0" fill="rgb(223,123,0)" rx="2" ry="2" />
<text text-anchor="" x="916.62" y="1179.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >cpu..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>pthread_self (1 samples, 0.08%)</title><rect x="696.5" y="753" width="0.9" height="15.0" fill="rgb(237,104,104)" rx="2" ry="2" />
<text text-anchor="" x="699.46" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptableObject:.getSlot (1 samples, 0.08%)</title><rect x="399.4" y="737" width="0.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="402.44" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>_raw_spin_lock_bh (1 samples, 0.08%)</title><rect x="858.9" y="593" width="0.9" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="861.88" y="603.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/channel/DefaultChannelPipeline$HeadContext:.flush (2 samples, 0.15%)</title><rect x="884.0" y="817" width="1.8" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="887.01" y="827.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>tcp_rcv_established (23 samples, 1.75%)</title><rect x="1143.3" y="641" width="20.7" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="1146.34" y="651.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/BaseFunction:.execIdCall (48 samples, 3.65%)</title><rect x="390.5" y="785" width="43.0" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="393.47" y="795.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >org/..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>lapic_next_deadline (1 samples, 0.08%)</title><rect x="945.0" y="1009" width="0.9" height="15.0" fill="rgb(220,120,0)" rx="2" ry="2" />
<text text-anchor="" x="948.03" y="1019.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>[unknown] (197 samples, 14.98%)</title><rect x="700.1" y="737" width="176.7" height="15.0" fill="rgb(243,112,112)" rx="2" ry="2" />
<text text-anchor="" x="703.05" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >[unknown]</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete (242 samples, 18.40%)</title><rect x="673.1" y="945" width="217.2" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="676.13" y="955.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >io/netty/channel/AbstractCha..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>bictcp_cong_avoid (1 samples, 0.08%)</title><rect x="1151.4" y="609" width="0.9" height="15.0" fill="rgb(225,125,0)" rx="2" ry="2" />
<text text-anchor="" x="1154.41" y="619.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptRuntime:.nameOrFunction (5 samples, 0.38%)</title><rect x="505.3" y="785" width="4.5" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="508.33" y="795.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>JavaCalls::call_virtual (956 samples, 72.70%)</title><rect x="45.0" y="1089" width="857.9" height="15.0" fill="rgb(203,203,60)" rx="2" ry="2" />
<text text-anchor="" x="48.00" y="1099.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >JavaCalls::call_virtual</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>resched_task (2 samples, 0.15%)</title><rect x="837.3" y="81" width="1.8" height="15.0" fill="rgb(236,136,0)" rx="2" ry="2" />
<text text-anchor="" x="840.35" y="91.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>sock_wfree (1 samples, 0.08%)</title><rect x="753.0" y="417" width="0.9" height="15.0" fill="rgb(239,139,0)" rx="2" ry="2" />
<text text-anchor="" x="756.00" y="427.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptableObject:.getSlot (4 samples, 0.30%)</title><rect x="346.5" y="705" width="3.6" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="349.50" y="715.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/buffer/AbstractByteBuf:.getByte (1 samples, 0.08%)</title><rect x="658.8" y="881" width="0.9" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="661.78" y="891.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>check_preempt_curr (2 samples, 0.15%)</title><rect x="835.6" y="81" width="1.7" height="15.0" fill="rgb(222,122,0)" rx="2" ry="2" />
<text text-anchor="" x="838.55" y="91.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/channel/ChannelOutboundBuffer:.progress (1 samples, 0.08%)</title><rect x="687.5" y="769" width="0.9" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="690.49" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>tcp_current_mss (1 samples, 0.08%)</title><rect x="1174.7" y="961" width="0.9" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="1177.75" y="971.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__execve (1 samples, 0.08%)</title><rect x="904.6" y="1105" width="0.9" height="15.0" fill="rgb(233,98,98)" rx="2" ry="2" />
<text text-anchor="" x="907.65" y="1115.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>hrtimer_force_reprogram (1 samples, 0.08%)</title><rect x="945.9" y="1073" width="0.9" height="15.0" fill="rgb(232,132,0)" rx="2" ry="2" />
<text text-anchor="" x="948.92" y="1083.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__GI___mprotect (1 samples, 0.08%)</title><rect x="902.9" y="1105" width="0.8" height="15.0" fill="rgb(228,91,91)" rx="2" ry="2" />
<text text-anchor="" x="905.85" y="1115.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>ep_send_events_proc (9 samples, 0.68%)</title><rect x="994.4" y="1057" width="8.1" height="15.0" fill="rgb(239,139,0)" rx="2" ry="2" />
<text text-anchor="" x="997.38" y="1067.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>schedule (11 samples, 0.84%)</title><rect x="1013.2" y="1041" width="9.9" height="15.0" fill="rgb(236,136,0)" rx="2" ry="2" />
<text text-anchor="" x="1016.22" y="1051.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/IdScriptableObject:.put (3 samples, 0.23%)</title><rect x="509.8" y="769" width="2.7" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="512.82" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/IdScriptableObject:.get (1 samples, 0.08%)</title><rect x="399.4" y="753" width="0.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="402.44" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/gen/file__root_vert_x_2_1_5_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_2 (409 samples, 31.10%)</title><rect x="245.1" y="801" width="367.0" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="248.10" y="811.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >org/mozilla/javascript/gen/file__root_vert_x_2_1_..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/channel/ChannelOutboundBuffer:.decrementPendingOutboundBytes (2 samples, 0.15%)</title><rect x="676.7" y="785" width="1.8" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="679.72" y="795.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>ktime_get_real (1 samples, 0.08%)</title><rect x="807.7" y="225" width="0.9" height="15.0" fill="rgb(222,122,0)" rx="2" ry="2" />
<text text-anchor="" x="810.73" y="235.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>aa_revalidate_sk (2 samples, 0.15%)</title><rect x="1175.6" y="993" width="1.8" height="15.0" fill="rgb(216,116,0)" rx="2" ry="2" />
<text text-anchor="" x="1178.64" y="1003.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>stats_record (1 samples, 0.08%)</title><rect x="1093.1" y="1089" width="0.9" height="15.0" fill="rgb(237,104,104)" rx="2" ry="2" />
<text text-anchor="" x="1096.09" y="1099.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptableObject:.getSlot (1 samples, 0.08%)</title><rect x="159.0" y="801" width="0.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="161.96" y="811.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__perf_event_enable (4 samples, 0.30%)</title><rect x="906.4" y="961" width="3.6" height="15.0" fill="rgb(230,130,0)" rx="2" ry="2" />
<text text-anchor="" x="909.44" y="971.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__alloc_skb (9 samples, 0.68%)</title><rect x="859.8" y="593" width="8.1" height="15.0" fill="rgb(232,132,0)" rx="2" ry="2" />
<text text-anchor="" x="862.78" y="603.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/gen/file__root_vert_x_2_1_5_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_2 (17 samples, 1.29%)</title><rect x="374.3" y="737" width="15.3" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="377.32" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>socket_readable (2 samples, 0.15%)</title><rect x="1186.4" y="1137" width="1.8" height="15.0" fill="rgb(241,110,110)" rx="2" ry="2" />
<text text-anchor="" x="1189.41" y="1147.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>ns_to_timeval (1 samples, 0.08%)</title><rect x="1152.3" y="593" width="0.9" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="1155.31" y="603.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>ip_rcv (33 samples, 2.51%)</title><rect x="1134.4" y="737" width="29.6" height="15.0" fill="rgb(230,130,0)" rx="2" ry="2" />
<text text-anchor="" x="1137.37" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >ip..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>SafepointSynchronize::begin (1 samples, 0.08%)</title><rect x="902.9" y="1121" width="0.8" height="15.0" fill="rgb(208,208,62)" rx="2" ry="2" />
<text text-anchor="" x="905.85" y="1131.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>java/nio/DirectByteBuffer:.duplicate (1 samples, 0.08%)</title><rect x="63.8" y="929" width="0.9" height="15.0" fill="rgb(77,224,77)" rx="2" ry="2" />
<text text-anchor="" x="66.84" y="939.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptableObject:.getSlot (2 samples, 0.15%)</title><rect x="445.2" y="753" width="1.8" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="448.21" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/IdScriptableObject:.get (1 samples, 0.08%)</title><rect x="498.2" y="769" width="0.8" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="501.15" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/gen/file__root_vert_x_2_1_5_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_2 (20 samples, 1.52%)</title><rect x="593.3" y="785" width="17.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="596.27" y="795.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read (939 samples, 71.41%)</title><rect x="54.0" y="961" width="842.6" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="56.97" y="971.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>raw_local_deliver (1 samples, 0.08%)</title><rect x="1137.1" y="673" width="0.9" height="15.0" fill="rgb(223,123,0)" rx="2" ry="2" />
<text text-anchor="" x="1140.06" y="683.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__dev_queue_xmit (4 samples, 0.30%)</title><rect x="1124.5" y="849" width="3.6" height="15.0" fill="rgb(230,130,0)" rx="2" ry="2" />
<text text-anchor="" x="1127.49" y="859.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>skb_copy_datagram_iovec (3 samples, 0.23%)</title><rect x="91.7" y="737" width="2.6" height="15.0" fill="rgb(230,130,0)" rx="2" ry="2" />
<text text-anchor="" x="94.66" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>apic_timer_interrupt (1 samples, 0.08%)</title><rect x="1038.3" y="1121" width="0.9" height="15.0" fill="rgb(228,128,0)" rx="2" ry="2" />
<text text-anchor="" x="1041.35" y="1131.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>do_vfs_ioctl (5 samples, 0.38%)</title><rect x="905.5" y="1073" width="4.5" height="15.0" fill="rgb(243,143,0)" rx="2" ry="2" />
<text text-anchor="" x="908.54" y="1083.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>do_sync_read (8 samples, 0.61%)</title><rect x="1050.9" y="1041" width="7.2" height="15.0" fill="rgb(243,143,0)" rx="2" ry="2" />
<text text-anchor="" x="1053.91" y="1051.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>system_call_after_swapgs (1 samples, 0.08%)</title><rect x="1045.5" y="1089" width="0.9" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="1048.53" y="1099.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>_raw_spin_lock_irqsave (1 samples, 0.08%)</title><rect x="963.9" y="1089" width="0.9" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="966.87" y="1099.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>call_function_single_interrupt (4 samples, 0.30%)</title><rect x="914.5" y="1121" width="3.6" height="15.0" fill="rgb(221,121,0)" rx="2" ry="2" />
<text text-anchor="" x="917.52" y="1131.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/handler/codec/http/HttpHeaders:.hash (4 samples, 0.30%)</title><rect x="627.4" y="897" width="3.6" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="630.37" y="907.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/handler/codec/http/DefaultHttpMessage:.init (2 samples, 0.15%)</title><rect x="625.6" y="897" width="1.8" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="628.57" y="907.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>rcu_sysidle_enter (1 samples, 0.08%)</title><rect x="960.3" y="1089" width="0.9" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="963.28" y="1099.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>java/nio/channels/spi/AbstractInterruptibleChannel:.end (3 samples, 0.23%)</title><rect x="68.3" y="913" width="2.7" height="15.0" fill="rgb(77,224,77)" rx="2" ry="2" />
<text text-anchor="" x="71.33" y="923.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>clockevents_program_event (2 samples, 0.15%)</title><rect x="950.4" y="1073" width="1.8" height="15.0" fill="rgb(219,119,0)" rx="2" ry="2" />
<text text-anchor="" x="953.41" y="1083.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/gen/file__root_vert_x_2_1_5_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_2 (9 samples, 0.68%)</title><rect x="381.5" y="721" width="8.1" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="384.50" y="731.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>tcp_try_rmem_schedule (2 samples, 0.15%)</title><rect x="845.4" y="241" width="1.8" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="848.42" y="251.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__schedule (1 samples, 0.08%)</title><rect x="963.0" y="1089" width="0.9" height="15.0" fill="rgb(227,127,0)" rx="2" ry="2" />
<text text-anchor="" x="965.97" y="1099.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptableObject:.putImpl (10 samples, 0.76%)</title><rect x="422.8" y="737" width="8.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="425.78" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>tcp_v4_md5_lookup (1 samples, 0.08%)</title><rect x="873.2" y="577" width="0.9" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="876.24" y="587.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>CardTableExtension::scavenge_contents_parallel (20 samples, 1.52%)</title><rect x="20.8" y="1121" width="17.9" height="15.0" fill="rgb(201,201,59)" rx="2" ry="2" />
<text text-anchor="" x="23.77" y="1131.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>aa_revalidate_sk (1 samples, 0.08%)</title><rect x="874.1" y="625" width="0.9" height="15.0" fill="rgb(216,116,0)" rx="2" ry="2" />
<text text-anchor="" x="877.14" y="635.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__fsnotify_parent (1 samples, 0.08%)</title><rect x="1062.6" y="1009" width="0.9" height="15.0" fill="rgb(228,128,0)" rx="2" ry="2" />
<text text-anchor="" x="1065.58" y="1019.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/IdScriptableObject:.get (7 samples, 0.53%)</title><rect x="329.5" y="721" width="6.2" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="332.45" y="731.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>sk_reset_timer (5 samples, 0.38%)</title><rect x="729.7" y="545" width="4.5" height="15.0" fill="rgb(222,122,0)" rx="2" ry="2" />
<text text-anchor="" x="732.67" y="555.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__schedule (2 samples, 0.15%)</title><rect x="978.2" y="1121" width="1.8" height="15.0" fill="rgb(227,127,0)" rx="2" ry="2" />
<text text-anchor="" x="981.23" y="1131.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/handler/codec/http/DefaultHttpHeaders:.init (1 samples, 0.08%)</title><rect x="137.4" y="849" width="0.9" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="140.42" y="859.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptableObject:.getSlot (1 samples, 0.08%)</title><rect x="478.4" y="769" width="0.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="481.41" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/channel/nio/AbstractNioByteChannel:.doWrite (225 samples, 17.11%)</title><rect x="678.5" y="785" width="201.9" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="681.52" y="795.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >io/netty/channel/nio/Abstr..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>timerqueue_add (1 samples, 0.08%)</title><rect x="1006.0" y="1009" width="0.9" height="15.0" fill="rgb(231,131,0)" rx="2" ry="2" />
<text text-anchor="" x="1009.05" y="1019.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>_raw_spin_unlock_irqrestore (2 samples, 0.15%)</title><rect x="992.6" y="1057" width="1.8" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="995.59" y="1067.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>try_to_wake_up (24 samples, 1.83%)</title><rect x="819.4" y="129" width="21.5" height="15.0" fill="rgb(234,134,0)" rx="2" ry="2" />
<text text-anchor="" x="822.40" y="139.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >t..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/IdScriptableObject:.setAttributes (4 samples, 0.30%)</title><rect x="406.6" y="753" width="3.6" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="409.62" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/handler/codec/http/HttpResponseEncoder:.acceptOutboundMessage (1 samples, 0.08%)</title><rect x="229.8" y="673" width="0.9" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="232.85" y="683.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>rw_verify_area (2 samples, 0.15%)</title><rect x="96.1" y="817" width="1.8" height="15.0" fill="rgb(223,123,0)" rx="2" ry="2" />
<text text-anchor="" x="99.14" y="827.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>x86_pmu_commit_txn (4 samples, 0.30%)</title><rect x="1128.1" y="753" width="3.6" height="15.0" fill="rgb(248,148,0)" rx="2" ry="2" />
<text text-anchor="" x="1131.08" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>alloc_pages_current (1 samples, 0.08%)</title><rect x="1173.0" y="897" width="0.8" height="15.0" fill="rgb(226,126,0)" rx="2" ry="2" />
<text text-anchor="" x="1175.95" y="907.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/gen/file__root_vert_x_2_1_5_sys_mods_io_vertx_lang_js_1_1_0_vertx_streams_j (1 samples, 0.08%)</title><rect x="432.6" y="769" width="0.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="435.65" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/IdScriptableObject:.setAttributes (7 samples, 0.53%)</title><rect x="319.6" y="721" width="6.3" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="322.58" y="731.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/IdScriptableObject:.get (1 samples, 0.08%)</title><rect x="259.5" y="705" width="0.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="262.46" y="715.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>tcp_transmit_skb (1 samples, 0.08%)</title><rect x="1111.9" y="961" width="0.9" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="1114.93" y="971.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>sock_aio_read.part.8 (7 samples, 0.53%)</title><rect x="1051.8" y="1009" width="6.3" height="15.0" fill="rgb(239,139,0)" rx="2" ry="2" />
<text text-anchor="" x="1054.81" y="1019.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>sys_read (28 samples, 2.13%)</title><rect x="73.7" y="849" width="25.1" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="76.71" y="859.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >s..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptRuntime:.setObjectProp (28 samples, 2.13%)</title><rect x="275.6" y="721" width="25.1" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="278.61" y="731.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >o..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>JavaCalls::call_helper (956 samples, 72.70%)</title><rect x="45.0" y="1073" width="857.9" height="15.0" fill="rgb(203,203,60)" rx="2" ry="2" />
<text text-anchor="" x="48.00" y="1083.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >JavaCalls::call_helper</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>ttwu_do_wakeup (1 samples, 0.08%)</title><rect x="839.1" y="113" width="0.9" height="15.0" fill="rgb(222,122,0)" rx="2" ry="2" />
<text text-anchor="" x="842.14" y="123.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>generic_smp_call_function_single_interrupt (4 samples, 0.30%)</title><rect x="711.7" y="609" width="3.6" height="15.0" fill="rgb(243,143,0)" rx="2" ry="2" />
<text text-anchor="" x="714.72" y="619.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>mutex_unlock (1 samples, 0.08%)</title><rect x="1085.0" y="1041" width="0.9" height="15.0" fill="rgb(217,117,0)" rx="2" ry="2" />
<text text-anchor="" x="1088.01" y="1051.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/handler/codec/http/HttpHeaders:.hash (2 samples, 0.15%)</title><rect x="648.0" y="881" width="1.8" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="651.01" y="891.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>http_parser_execute (1 samples, 0.08%)</title><rect x="1040.1" y="1121" width="0.9" height="15.0" fill="rgb(233,99,99)" rx="2" ry="2" />
<text text-anchor="" x="1043.14" y="1131.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>mod_timer (5 samples, 0.38%)</title><rect x="729.7" y="529" width="4.5" height="15.0" fill="rgb(229,129,0)" rx="2" ry="2" />
<text text-anchor="" x="732.67" y="539.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>system_call_fastpath (1 samples, 0.08%)</title><rect x="10.0" y="1153" width="0.9" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="13.00" y="1163.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>tcp_recvmsg (13 samples, 0.99%)</title><rect x="84.5" y="753" width="11.6" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="87.48" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__slab_alloc (1 samples, 0.08%)</title><rect x="866.1" y="561" width="0.9" height="15.0" fill="rgb(227,127,0)" rx="2" ry="2" />
<text text-anchor="" x="869.06" y="571.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__alloc_skb (7 samples, 0.53%)</title><rect x="1168.5" y="961" width="6.2" height="15.0" fill="rgb(232,132,0)" rx="2" ry="2" />
<text text-anchor="" x="1171.46" y="971.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>clockevents_program_event (1 samples, 0.08%)</title><rect x="945.0" y="1025" width="0.9" height="15.0" fill="rgb(219,119,0)" rx="2" ry="2" />
<text text-anchor="" x="948.03" y="1035.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>vfs_read (18 samples, 1.37%)</title><rect x="1050.0" y="1057" width="16.2" height="15.0" fill="rgb(226,126,0)" rx="2" ry="2" />
<text text-anchor="" x="1053.02" y="1067.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__internal_add_timer (1 samples, 0.08%)</title><rect x="1146.0" y="545" width="0.9" height="15.0" fill="rgb(225,125,0)" rx="2" ry="2" />
<text text-anchor="" x="1149.03" y="555.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>epoll_wait (1 samples, 0.08%)</title><rect x="1185.5" y="1137" width="0.9" height="15.0" fill="rgb(248,120,120)" rx="2" ry="2" />
<text text-anchor="" x="1188.51" y="1147.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>lock_sock_nested (1 samples, 0.08%)</title><rect x="89.9" y="737" width="0.9" height="15.0" fill="rgb(232,132,0)" rx="2" ry="2" />
<text text-anchor="" x="92.86" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptableObject:.getSlot (1 samples, 0.08%)</title><rect x="447.9" y="753" width="0.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="450.90" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>native_write_msr_safe (4 samples, 0.30%)</title><rect x="1128.1" y="689" width="3.6" height="15.0" fill="rgb(234,134,0)" rx="2" ry="2" />
<text text-anchor="" x="1131.08" y="699.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>Interpreter (956 samples, 72.70%)</title><rect x="45.0" y="1041" width="857.9" height="15.0" fill="rgb(243,112,112)" rx="2" ry="2" />
<text text-anchor="" x="48.00" y="1051.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >Interpreter</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptableObject:.getBase (4 samples, 0.30%)</title><rect x="493.7" y="769" width="3.6" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="496.67" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>dev_hard_start_xmit (9 samples, 0.68%)</title><rect x="746.7" y="465" width="8.1" height="15.0" fill="rgb(243,143,0)" rx="2" ry="2" />
<text text-anchor="" x="749.71" y="475.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>java/lang/String:.hashCode (1 samples, 0.08%)</title><rect x="151.8" y="817" width="0.9" height="15.0" fill="rgb(77,224,77)" rx="2" ry="2" />
<text text-anchor="" x="154.78" y="827.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>ip_output (46 samples, 3.50%)</title><rect x="1124.5" y="897" width="41.3" height="15.0" fill="rgb(230,130,0)" rx="2" ry="2" />
<text text-anchor="" x="1127.49" y="907.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >ip_..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>account_entity_enqueue (1 samples, 0.08%)</title><rect x="830.2" y="33" width="0.9" height="15.0" fill="rgb(236,136,0)" rx="2" ry="2" />
<text text-anchor="" x="833.17" y="43.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>itable stub (1 samples, 0.08%)</title><rect x="148.2" y="817" width="0.9" height="15.0" fill="rgb(237,104,104)" rx="2" ry="2" />
<text text-anchor="" x="151.19" y="827.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>ip_rcv (1 samples, 0.08%)</title><rect x="1164.0" y="753" width="0.9" height="15.0" fill="rgb(230,130,0)" rx="2" ry="2" />
<text text-anchor="" x="1166.98" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/buffer/AbstractByteBuf:.writeBytes (5 samples, 0.38%)</title><rect x="180.5" y="753" width="4.5" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="183.49" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>tcp_clean_rtx_queue (14 samples, 1.06%)</title><rect x="796.1" y="241" width="12.5" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="799.07" y="251.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/channel/AbstractChannelHandlerContext:.flush (1 samples, 0.08%)</title><rect x="673.1" y="897" width="0.9" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="676.13" y="907.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>sys_read (21 samples, 1.60%)</title><rect x="1047.3" y="1073" width="18.9" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="1050.32" y="1083.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>[unknown] (10 samples, 0.76%)</title><rect x="10.9" y="1169" width="9.0" height="15.0" fill="rgb(243,112,112)" rx="2" ry="2" />
<text text-anchor="" x="13.90" y="1179.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>java/util/concurrent/ConcurrentHashMap:.get (1 samples, 0.08%)</title><rect x="888.5" y="881" width="0.9" height="15.0" fill="rgb(77,224,77)" rx="2" ry="2" />
<text text-anchor="" x="891.49" y="891.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/channel/AbstractChannel:.hashCode (4 samples, 0.30%)</title><rect x="123.1" y="881" width="3.6" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="126.06" y="891.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>rcu_idle_enter (1 samples, 0.08%)</title><rect x="960.3" y="1105" width="0.9" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="963.28" y="1115.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>gettimeofday@plt (1 samples, 0.08%)</title><rect x="1039.2" y="1121" width="0.9" height="15.0" fill="rgb(240,109,109)" rx="2" ry="2" />
<text text-anchor="" x="1042.25" y="1131.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__do_softirq (103 samples, 7.83%)</title><rect x="756.6" y="449" width="92.4" height="15.0" fill="rgb(230,130,0)" rx="2" ry="2" />
<text text-anchor="" x="759.59" y="459.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >__do_softirq</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptRuntime:.nameOrFunction (8 samples, 0.61%)</title><rect x="159.9" y="801" width="7.1" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="162.86" y="811.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/buffer/AbstractByteBuf:.writeBytes (3 samples, 0.23%)</title><rect x="216.4" y="657" width="2.7" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="219.39" y="667.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>inotify_add_watch (1 samples, 0.08%)</title><rect x="10.0" y="1169" width="0.9" height="15.0" fill="rgb(243,112,112)" rx="2" ry="2" />
<text text-anchor="" x="13.00" y="1179.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>fdval (1 samples, 0.08%)</title><rect x="699.2" y="721" width="0.9" height="15.0" fill="rgb(242,111,111)" rx="2" ry="2" />
<text text-anchor="" x="702.16" y="731.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/handler/codec/http/HttpHeaders:.encode (7 samples, 0.53%)</title><rect x="220.9" y="657" width="6.3" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="223.87" y="667.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>unsafe_arraycopy (1 samples, 0.08%)</title><rect x="229.0" y="657" width="0.8" height="15.0" fill="rgb(236,102,102)" rx="2" ry="2" />
<text text-anchor="" x="231.95" y="667.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>sk_stream_alloc_skb (10 samples, 0.76%)</title><rect x="859.8" y="609" width="9.0" height="15.0" fill="rgb(222,122,0)" rx="2" ry="2" />
<text text-anchor="" x="862.78" y="619.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>lock_timer_base.isra.35 (1 samples, 0.08%)</title><rect x="736.8" y="529" width="0.9" height="15.0" fill="rgb(232,132,0)" rx="2" ry="2" />
<text text-anchor="" x="739.84" y="539.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>ip_local_out (121 samples, 9.20%)</title><rect x="740.4" y="545" width="108.6" height="15.0" fill="rgb(230,130,0)" rx="2" ry="2" />
<text text-anchor="" x="743.43" y="555.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >ip_local_out</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>java/lang/String:.hashCode (1 samples, 0.08%)</title><rect x="494.6" y="737" width="0.9" height="15.0" fill="rgb(77,224,77)" rx="2" ry="2" />
<text text-anchor="" x="497.56" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/util/internal/AppendableCharSequence:.substring (2 samples, 0.15%)</title><rect x="661.5" y="881" width="1.8" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="664.47" y="891.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptRuntime:.bind (1 samples, 0.08%)</title><rect x="240.6" y="801" width="0.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="243.62" y="811.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>ep_poll (53 samples, 4.03%)</title><rect x="981.8" y="1089" width="47.6" height="15.0" fill="rgb(239,139,0)" rx="2" ry="2" />
<text text-anchor="" x="984.82" y="1099.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >ep_p..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>lock_hrtimer_base.isra.19 (1 samples, 0.08%)</title><rect x="1006.9" y="1025" width="0.9" height="15.0" fill="rgb(232,132,0)" rx="2" ry="2" />
<text text-anchor="" x="1009.94" y="1035.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>InstanceKlass::oop_push_contents (1 samples, 0.08%)</title><rect x="43.2" y="1041" width="0.9" height="15.0" fill="rgb(218,218,65)" rx="2" ry="2" />
<text text-anchor="" x="46.20" y="1051.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>cpuacct_charge (1 samples, 0.08%)</title><rect x="1018.6" y="929" width="0.9" height="15.0" fill="rgb(223,123,0)" rx="2" ry="2" />
<text text-anchor="" x="1021.61" y="939.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>harmonize_features.isra.92.part.93 (1 samples, 0.08%)</title><rect x="1126.3" y="817" width="0.9" height="15.0" fill="rgb(227,127,0)" rx="2" ry="2" />
<text text-anchor="" x="1129.29" y="827.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>update_rq_clock.part.63 (1 samples, 0.08%)</title><rect x="1020.4" y="977" width="0.9" height="15.0" fill="rgb(232,132,0)" rx="2" ry="2" />
<text text-anchor="" x="1023.40" y="987.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>native_write_msr_safe (1 samples, 0.08%)</title><rect x="945.0" y="993" width="0.9" height="15.0" fill="rgb(234,134,0)" rx="2" ry="2" />
<text text-anchor="" x="948.03" y="1003.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/handler/codec/http/HttpObjectDecoder:.findNonWhitespace (1 samples, 0.08%)</title><rect x="653.4" y="865" width="0.9" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="656.39" y="875.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/gen/file__root_vert_x_2_1_5_sys_mods_io_vertx_lang_js_1_1_0_vertx_streams_j (1 samples, 0.08%)</title><rect x="611.2" y="785" width="0.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="614.22" y="795.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/BaseFunction:.construct (156 samples, 11.86%)</title><rect x="250.5" y="785" width="140.0" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="253.49" y="795.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >org/mozilla/javas..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>_raw_spin_lock (2 samples, 0.15%)</title><rect x="774.5" y="289" width="1.8" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="777.53" y="299.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>cpu_function_call (5 samples, 0.38%)</title><rect x="905.5" y="1009" width="4.5" height="15.0" fill="rgb(223,123,0)" rx="2" ry="2" />
<text text-anchor="" x="908.54" y="1019.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>fget_light (2 samples, 0.15%)</title><rect x="1048.2" y="1057" width="1.8" height="15.0" fill="rgb(240,140,0)" rx="2" ry="2" />
<text text-anchor="" x="1051.22" y="1067.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>start_kernel (24 samples, 1.83%)</title><rect x="953.1" y="1153" width="21.5" height="15.0" fill="rgb(234,134,0)" rx="2" ry="2" />
<text text-anchor="" x="956.10" y="1163.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >s..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>native_write_msr_safe (2 samples, 0.15%)</title><rect x="950.4" y="1041" width="1.8" height="15.0" fill="rgb(234,134,0)" rx="2" ry="2" />
<text text-anchor="" x="953.41" y="1051.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/gen/file__root_vert_x_2_1_5_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_2 (511 samples, 38.86%)</title><rect x="154.5" y="833" width="458.5" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="157.47" y="843.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >org/mozilla/javascript/gen/file__root_vert_x_2_1_5_sys_mods_io..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>ipv4_mtu (1 samples, 0.08%)</title><rect x="868.8" y="593" width="0.9" height="15.0" fill="rgb(231,131,0)" rx="2" ry="2" />
<text text-anchor="" x="871.75" y="603.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__schedule (11 samples, 0.84%)</title><rect x="1013.2" y="1025" width="9.9" height="15.0" fill="rgb(227,127,0)" rx="2" ry="2" />
<text text-anchor="" x="1016.22" y="1035.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>system_call_fastpath (88 samples, 6.69%)</title><rect x="1100.3" y="1089" width="78.9" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="1103.27" y="1099.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >system_ca..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/channel/nio/NioEventLoop:.select (7 samples, 0.53%)</title><rect x="896.6" y="993" width="6.3" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="899.57" y="1003.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/IdScriptableObject:.get (3 samples, 0.23%)</title><rect x="272.9" y="705" width="2.7" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="275.92" y="715.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/TopLevel:.getBuiltinPrototype (7 samples, 0.53%)</title><rect x="601.3" y="737" width="6.3" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="604.35" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>sun/nio/ch/IOUtil:.readIntoNativeBuffer (31 samples, 2.36%)</title><rect x="71.9" y="913" width="27.8" height="15.0" fill="rgb(88,235,88)" rx="2" ry="2" />
<text text-anchor="" x="74.92" y="923.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >s..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>itable stub (1 samples, 0.08%)</title><rect x="889.4" y="913" width="0.9" height="15.0" fill="rgb(237,104,104)" rx="2" ry="2" />
<text text-anchor="" x="892.39" y="923.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/channel/AbstractChannelHandlerContext:.write (6 samples, 0.46%)</title><rect x="207.4" y="673" width="5.4" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="210.41" y="683.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>timerqueue_del (1 samples, 0.08%)</title><rect x="1008.7" y="1009" width="0.9" height="15.0" fill="rgb(231,131,0)" rx="2" ry="2" />
<text text-anchor="" x="1011.74" y="1019.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__tcp_v4_send_check (1 samples, 0.08%)</title><rect x="855.3" y="545" width="0.9" height="15.0" fill="rgb(226,126,0)" rx="2" ry="2" />
<text text-anchor="" x="858.29" y="555.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptRuntime:.nameOrFunction (3 samples, 0.23%)</title><rect x="335.7" y="737" width="2.7" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="338.73" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/buffer/AbstractByteBuf:.writeBytes (4 samples, 0.30%)</title><rect x="221.8" y="641" width="3.6" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="224.77" y="651.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/WrapFactory:.wrapAsJavaObject (1 samples, 0.08%)</title><rect x="151.8" y="849" width="0.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="154.78" y="859.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/handler/codec/http/DefaultHttpMessage:.init (2 samples, 0.15%)</title><rect x="136.5" y="865" width="1.8" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="139.52" y="875.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>_raw_spin_lock_irqsave (2 samples, 0.15%)</title><rect x="985.4" y="1073" width="1.8" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="988.41" y="1083.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/IdScriptableObject:.put (7 samples, 0.53%)</title><rect x="260.4" y="705" width="6.2" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="263.36" y="715.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/handler/codec/http/DefaultHttpHeaders:.add0 (1 samples, 0.08%)</title><rect x="194.0" y="737" width="0.9" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="196.95" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptableObject:.getSlot (8 samples, 0.61%)</title><rect x="541.2" y="753" width="7.2" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="544.22" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>java/util/ArrayList:.ensureCapacityInternal (1 samples, 0.08%)</title><rect x="667.7" y="897" width="0.9" height="15.0" fill="rgb(77,224,77)" rx="2" ry="2" />
<text text-anchor="" x="670.75" y="907.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__wake_up_locked (25 samples, 1.90%)</title><rect x="819.4" y="177" width="22.4" height="15.0" fill="rgb(223,123,0)" rx="2" ry="2" />
<text text-anchor="" x="822.40" y="187.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >_..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>java/util/HashMap:.getNode (1 samples, 0.08%)</title><rect x="149.1" y="817" width="0.9" height="15.0" fill="rgb(77,224,77)" rx="2" ry="2" />
<text text-anchor="" x="152.09" y="827.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptRuntime:.toObjectOrNull (1 samples, 0.08%)</title><rect x="167.0" y="817" width="0.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="170.03" y="827.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__tcp_push_pending_frames (1 samples, 0.08%)</title><rect x="1105.7" y="993" width="0.8" height="15.0" fill="rgb(226,126,0)" rx="2" ry="2" />
<text text-anchor="" x="1108.65" y="1003.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>[unknown] (61 samples, 4.64%)</title><rect x="977.3" y="1137" width="54.8" height="15.0" fill="rgb(243,112,112)" rx="2" ry="2" />
<text text-anchor="" x="980.33" y="1147.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >[unkn..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__slab_free (1 samples, 0.08%)</title><rect x="805.0" y="145" width="0.9" height="15.0" fill="rgb(227,127,0)" rx="2" ry="2" />
<text text-anchor="" x="808.04" y="155.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/IdScriptableObject:.put (11 samples, 0.84%)</title><rect x="421.9" y="753" width="9.8" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="424.88" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>sock_def_readable (5 samples, 0.38%)</title><rect x="1156.8" y="609" width="4.5" height="15.0" fill="rgb(239,139,0)" rx="2" ry="2" />
<text text-anchor="" x="1159.80" y="619.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>gmain (1 samples, 0.08%)</title><rect x="10.0" y="1201" width="0.9" height="15.0" fill="rgb(236,102,102)" rx="2" ry="2" />
<text text-anchor="" x="13.00" y="1211.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>_raw_spin_lock_irqsave (1 samples, 0.08%)</title><rect x="1010.5" y="1009" width="0.9" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="1013.53" y="1019.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__kmalloc_reserve.isra.26 (3 samples, 0.23%)</title><rect x="861.6" y="577" width="2.7" height="15.0" fill="rgb(223,123,0)" rx="2" ry="2" />
<text text-anchor="" x="864.57" y="587.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptableObject:.putImpl (5 samples, 0.38%)</title><rect x="262.2" y="689" width="4.4" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="265.15" y="699.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/NativeCall:.init (20 samples, 1.52%)</title><rect x="308.8" y="737" width="18.0" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="311.81" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/WrapFactory:.wrap (1 samples, 0.08%)</title><rect x="141.0" y="865" width="0.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="144.01" y="875.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>_raw_spin_lock_bh (1 samples, 0.08%)</title><rect x="857.1" y="593" width="0.9" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="860.09" y="603.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>aeProcessEvents (3 samples, 0.23%)</title><rect x="974.6" y="1185" width="2.7" height="15.0" fill="rgb(233,98,98)" rx="2" ry="2" />
<text text-anchor="" x="977.64" y="1195.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>java/lang/String:.hashCode (1 samples, 0.08%)</title><rect x="423.7" y="721" width="0.9" height="15.0" fill="rgb(77,224,77)" rx="2" ry="2" />
<text text-anchor="" x="426.67" y="731.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/channel/AbstractChannelHandlerContext:.executor (1 samples, 0.08%)</title><rect x="202.0" y="721" width="0.9" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="205.03" y="731.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>fget_light (2 samples, 0.15%)</title><rect x="701.8" y="689" width="1.8" height="15.0" fill="rgb(240,140,0)" rx="2" ry="2" />
<text text-anchor="" x="704.85" y="699.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/buffer/PooledByteBufAllocator:.newDirectBuffer (2 samples, 0.15%)</title><rect x="61.1" y="945" width="1.8" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="64.15" y="955.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>menu_select (1 samples, 0.08%)</title><rect x="959.4" y="1089" width="0.9" height="15.0" fill="rgb(229,129,0)" rx="2" ry="2" />
<text text-anchor="" x="962.38" y="1099.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>generic_smp_call_function_single_interrupt (4 samples, 0.30%)</title><rect x="914.5" y="1089" width="3.6" height="15.0" fill="rgb(243,143,0)" rx="2" ry="2" />
<text text-anchor="" x="917.52" y="1099.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/TopLevel:.getBuiltinPrototype (1 samples, 0.08%)</title><rect x="479.3" y="769" width="0.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="482.31" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/IdScriptableObject:.setAttributes (1 samples, 0.08%)</title><rect x="436.2" y="785" width="0.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="439.24" y="795.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>schedule_hrtimeout_range_clock (20 samples, 1.52%)</title><rect x="1005.1" y="1057" width="18.0" height="15.0" fill="rgb(236,136,0)" rx="2" ry="2" />
<text text-anchor="" x="1008.15" y="1067.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/buffer/UnreleasableByteBuf:.duplicate (1 samples, 0.08%)</title><rect x="188.6" y="753" width="0.9" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="191.57" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>tick_program_event (2 samples, 0.15%)</title><rect x="950.4" y="1089" width="1.8" height="15.0" fill="rgb(230,130,0)" rx="2" ry="2" />
<text text-anchor="" x="953.41" y="1099.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__netif_receive_skb_core (33 samples, 2.51%)</title><rect x="1134.4" y="753" width="29.6" height="15.0" fill="rgb(231,131,0)" rx="2" ry="2" />
<text text-anchor="" x="1137.37" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >__..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>java/util/HashMap:.getNode (1 samples, 0.08%)</title><rect x="503.5" y="737" width="0.9" height="15.0" fill="rgb(77,224,77)" rx="2" ry="2" />
<text text-anchor="" x="506.54" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/buffer/AbstractByteBuf:.forEachByteAsc0 (2 samples, 0.15%)</title><rect x="620.2" y="897" width="1.8" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="623.19" y="907.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>get_next_timer_interrupt (2 samples, 0.15%)</title><rect x="964.8" y="1057" width="1.8" height="15.0" fill="rgb(238,138,0)" rx="2" ry="2" />
<text text-anchor="" x="967.77" y="1067.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>vtable stub (1 samples, 0.08%)</title><rect x="299.8" y="705" width="0.9" height="15.0" fill="rgb(231,96,96)" rx="2" ry="2" />
<text text-anchor="" x="302.84" y="715.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>start_secondary (44 samples, 3.35%)</title><rect x="913.6" y="1185" width="39.5" height="15.0" fill="rgb(234,134,0)" rx="2" ry="2" />
<text text-anchor="" x="916.62" y="1195.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >sta..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>skb_release_all (3 samples, 0.23%)</title><rect x="803.2" y="209" width="2.7" height="15.0" fill="rgb(230,130,0)" rx="2" ry="2" />
<text text-anchor="" x="806.25" y="219.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>update_cfs_rq_blocked_load (1 samples, 0.08%)</title><rect x="1017.7" y="945" width="0.9" height="15.0" fill="rgb(232,132,0)" rx="2" ry="2" />
<text text-anchor="" x="1020.71" y="955.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/IdScriptableObject:.get (1 samples, 0.08%)</title><rect x="159.0" y="817" width="0.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="161.96" y="827.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>call_function_single_interrupt (4 samples, 0.30%)</title><rect x="1128.1" y="849" width="3.6" height="15.0" fill="rgb(221,121,0)" rx="2" ry="2" />
<text text-anchor="" x="1131.08" y="859.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>sun/nio/ch/SocketChannelImpl:.read (40 samples, 3.04%)</title><rect x="64.7" y="929" width="35.9" height="15.0" fill="rgb(88,235,88)" rx="2" ry="2" />
<text text-anchor="" x="67.74" y="939.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >sun..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>sys_epoll_wait (1 samples, 0.08%)</title><rect x="974.6" y="1105" width="0.9" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="977.64" y="1115.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>tcp_check_space (1 samples, 0.08%)</title><rect x="781.7" y="273" width="0.9" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="784.71" y="283.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__wake_up_common (25 samples, 1.90%)</title><rect x="819.4" y="161" width="22.4" height="15.0" fill="rgb(223,123,0)" rx="2" ry="2" />
<text text-anchor="" x="822.40" y="171.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >_..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>native_sched_clock (1 samples, 0.08%)</title><rect x="1020.4" y="929" width="0.9" height="15.0" fill="rgb(234,134,0)" rx="2" ry="2" />
<text text-anchor="" x="1023.40" y="939.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>fget_light (3 samples, 0.23%)</title><rect x="73.7" y="833" width="2.7" height="15.0" fill="rgb(240,140,0)" rx="2" ry="2" />
<text text-anchor="" x="76.71" y="843.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>sys_inotify_add_watch (1 samples, 0.08%)</title><rect x="10.0" y="1137" width="0.9" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="13.00" y="1147.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptableObject$Slot:.getValue (1 samples, 0.08%)</title><rect x="162.5" y="785" width="0.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="165.55" y="795.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>_raw_spin_lock (1 samples, 0.08%)</title><rect x="732.4" y="513" width="0.9" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="735.36" y="523.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>smp_call_function_single_interrupt (4 samples, 0.30%)</title><rect x="914.5" y="1105" width="3.6" height="15.0" fill="rgb(225,125,0)" rx="2" ry="2" />
<text text-anchor="" x="917.52" y="1115.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__kmalloc_node_track_caller (1 samples, 0.08%)</title><rect x="1170.3" y="945" width="0.9" height="15.0" fill="rgb(223,123,0)" rx="2" ry="2" />
<text text-anchor="" x="1173.26" y="955.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptableObject$RelinkedSlot:.getValue (1 samples, 0.08%)</title><rect x="336.6" y="721" width="0.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="339.63" y="731.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>tcp_send_mss (6 samples, 0.46%)</title><rect x="868.8" y="609" width="5.3" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="871.75" y="619.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>sched_clock (1 samples, 0.08%)</title><rect x="1020.4" y="945" width="0.9" height="15.0" fill="rgb(236,136,0)" rx="2" ry="2" />
<text text-anchor="" x="1023.40" y="955.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>kmem_cache_alloc_node (4 samples, 0.30%)</title><rect x="1171.2" y="945" width="3.5" height="15.0" fill="rgb(220,120,0)" rx="2" ry="2" />
<text text-anchor="" x="1174.16" y="955.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>_raw_spin_lock_irqsave (1 samples, 0.08%)</title><rect x="841.8" y="177" width="0.9" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="844.83" y="187.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>tick_sched_handle.isra.17 (1 samples, 0.08%)</title><rect x="1038.3" y="1025" width="0.9" height="15.0" fill="rgb(230,130,0)" rx="2" ry="2" />
<text text-anchor="" x="1041.35" y="1035.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptableObject:.getBase (1 samples, 0.08%)</title><rect x="326.8" y="721" width="0.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="329.76" y="731.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>[unknown] (6 samples, 0.46%)</title><rect x="904.6" y="1153" width="5.4" height="15.0" fill="rgb(243,112,112)" rx="2" ry="2" />
<text text-anchor="" x="907.65" y="1163.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/util/internal/AppendableCharSequence:.substring (2 samples, 0.15%)</title><rect x="664.2" y="897" width="1.8" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="667.16" y="907.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__inet_lookup_established (2 samples, 0.15%)</title><rect x="1141.5" y="657" width="1.8" height="15.0" fill="rgb(225,125,0)" rx="2" ry="2" />
<text text-anchor="" x="1144.54" y="667.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>apparmor_file_permission (1 samples, 0.08%)</title><rect x="96.1" y="785" width="0.9" height="15.0" fill="rgb(232,132,0)" rx="2" ry="2" />
<text text-anchor="" x="99.14" y="795.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>dst_release (1 samples, 0.08%)</title><rect x="788.0" y="257" width="0.9" height="15.0" fill="rgb(240,140,0)" rx="2" ry="2" />
<text text-anchor="" x="790.99" y="267.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/handler/codec/http/HttpObjectEncoder:.encode (1 samples, 0.08%)</title><rect x="233.4" y="689" width="0.9" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="236.44" y="699.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>open_exec (1 samples, 0.08%)</title><rect x="904.6" y="1041" width="0.9" height="15.0" fill="rgb(245,145,0)" rx="2" ry="2" />
<text text-anchor="" x="907.65" y="1051.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>tcp_transmit_skb (132 samples, 10.04%)</title><rect x="737.7" y="577" width="118.5" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="740.74" y="587.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >tcp_transmit_skb</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>ttwu_do_wakeup (5 samples, 0.38%)</title><rect x="834.7" y="97" width="4.4" height="15.0" fill="rgb(222,122,0)" rx="2" ry="2" />
<text text-anchor="" x="837.65" y="107.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>idle_cpu (1 samples, 0.08%)</title><rect x="1011.4" y="1041" width="0.9" height="15.0" fill="rgb(242,142,0)" rx="2" ry="2" />
<text text-anchor="" x="1014.43" y="1051.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__lll_unlock_wake (1 samples, 0.08%)</title><rect x="19.9" y="1089" width="0.9" height="15.0" fill="rgb(236,103,103)" rx="2" ry="2" />
<text text-anchor="" x="22.87" y="1099.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>[unknown] (7 samples, 0.53%)</title><rect x="10.9" y="1153" width="6.3" height="15.0" fill="rgb(243,112,112)" rx="2" ry="2" />
<text text-anchor="" x="13.90" y="1163.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>security_file_permission (2 samples, 0.15%)</title><rect x="96.1" y="801" width="1.8" height="15.0" fill="rgb(238,138,0)" rx="2" ry="2" />
<text text-anchor="" x="99.14" y="811.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>[unknown] (1 samples, 0.08%)</title><rect x="904.6" y="1121" width="0.9" height="15.0" fill="rgb(243,112,112)" rx="2" ry="2" />
<text text-anchor="" x="907.65" y="1131.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__switch_to (1 samples, 0.08%)</title><rect x="903.7" y="1185" width="0.9" height="15.0" fill="rgb(227,127,0)" rx="2" ry="2" />
<text text-anchor="" x="906.75" y="1195.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/channel/DefaultChannelPromise:.trySuccess (3 samples, 0.23%)</title><rect x="688.4" y="769" width="2.7" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="691.39" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/IdScriptableObject:.has (7 samples, 0.53%)</title><rect x="343.8" y="721" width="6.3" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="346.81" y="731.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>native_write_msr_safe (3 samples, 0.23%)</title><rect x="910.9" y="1185" width="2.7" height="15.0" fill="rgb(234,134,0)" rx="2" ry="2" />
<text text-anchor="" x="913.93" y="1195.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/handler/codec/http/DefaultHttpHeaders:.add0 (2 samples, 0.15%)</title><rect x="645.3" y="881" width="1.8" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="648.32" y="891.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>do_softirq (103 samples, 7.83%)</title><rect x="756.6" y="481" width="92.4" height="15.0" fill="rgb(243,143,0)" rx="2" ry="2" />
<text text-anchor="" x="759.59" y="491.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >do_softirq</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>rw_verify_area (1 samples, 0.08%)</title><rect x="875.9" y="673" width="0.9" height="15.0" fill="rgb(223,123,0)" rx="2" ry="2" />
<text text-anchor="" x="878.93" y="683.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>tcp_poll (1 samples, 0.08%)</title><rect x="1001.6" y="1025" width="0.9" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="1004.56" y="1035.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>tcp_rearm_rto (5 samples, 0.38%)</title><rect x="729.7" y="561" width="4.5" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="732.67" y="571.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/channel/AbstractChannelHandlerContext:.newPromise (1 samples, 0.08%)</title><rect x="202.0" y="737" width="0.9" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="205.03" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>tick_nohz_idle_exit (5 samples, 0.38%)</title><rect x="969.3" y="1105" width="4.4" height="15.0" fill="rgb(230,130,0)" rx="2" ry="2" />
<text text-anchor="" x="972.25" y="1115.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/BaseFunction:.execIdCall (60 samples, 4.56%)</title><rect x="254.1" y="737" width="53.8" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="257.08" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >org/m..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptableObject:.putImpl (1 samples, 0.08%)</title><rect x="339.3" y="705" width="0.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="342.32" y="715.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>_copy_from_user (1 samples, 0.08%)</title><rect x="1179.2" y="1073" width="0.9" height="15.0" fill="rgb(240,140,0)" rx="2" ry="2" />
<text text-anchor="" x="1182.23" y="1083.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__netif_receive_skb (34 samples, 2.59%)</title><rect x="1134.4" y="769" width="30.5" height="15.0" fill="rgb(231,131,0)" rx="2" ry="2" />
<text text-anchor="" x="1137.37" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >__..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>java/util/concurrent/ConcurrentHashMap:.get (3 samples, 0.23%)</title><rect x="127.6" y="881" width="2.6" height="15.0" fill="rgb(77,224,77)" rx="2" ry="2" />
<text text-anchor="" x="130.55" y="891.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>fput (1 samples, 0.08%)</title><rect x="701.0" y="705" width="0.8" height="15.0" fill="rgb(239,139,0)" rx="2" ry="2" />
<text text-anchor="" x="703.95" y="715.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>JavaThread::thread_main_inner (956 samples, 72.70%)</title><rect x="45.0" y="1137" width="857.9" height="15.0" fill="rgb(203,203,60)" rx="2" ry="2" />
<text text-anchor="" x="48.00" y="1147.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >JavaThread::thread_main_inner</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>java/lang/String:.hashCode (1 samples, 0.08%)</title><rect x="421.0" y="721" width="0.9" height="15.0" fill="rgb(77,224,77)" rx="2" ry="2" />
<text text-anchor="" x="423.98" y="731.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/util/Recycler:.get (2 samples, 0.15%)</title><rect x="61.1" y="929" width="1.8" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="64.15" y="939.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>[unknown] (6 samples, 0.46%)</title><rect x="904.6" y="1169" width="5.4" height="15.0" fill="rgb(243,112,112)" rx="2" ry="2" />
<text text-anchor="" x="907.65" y="1179.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__dev_queue_xmit (1 samples, 0.08%)</title><rect x="744.9" y="497" width="0.9" height="15.0" fill="rgb(230,130,0)" rx="2" ry="2" />
<text text-anchor="" x="747.92" y="507.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>common_file_perm (1 samples, 0.08%)</title><rect x="96.1" y="769" width="0.9" height="15.0" fill="rgb(232,132,0)" rx="2" ry="2" />
<text text-anchor="" x="99.14" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/JavaMembers:.get (1 samples, 0.08%)</title><rect x="503.5" y="753" width="0.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="506.54" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptRuntime:.getPropFunctionAndThisHelper (1 samples, 0.08%)</title><rect x="412.0" y="769" width="0.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="415.01" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/IdScriptableObject:.findInstanceIdInfo (1 samples, 0.08%)</title><rect x="159.9" y="785" width="0.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="162.86" y="795.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptableObject:.getSlot (6 samples, 0.46%)</title><rect x="314.2" y="689" width="5.4" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="317.20" y="699.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>jiffies_to_timeval (1 samples, 0.08%)</title><rect x="1038.3" y="929" width="0.9" height="15.0" fill="rgb(227,127,0)" rx="2" ry="2" />
<text text-anchor="" x="1041.35" y="939.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptRuntime:.setName (2 samples, 0.15%)</title><rect x="338.4" y="737" width="1.8" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="341.43" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>PSRootsClosurefalse::do_oop (1 samples, 0.08%)</title><rect x="43.2" y="1073" width="0.9" height="15.0" fill="rgb(215,215,64)" rx="2" ry="2" />
<text text-anchor="" x="46.20" y="1083.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptableObject:.getSlot (1 samples, 0.08%)</title><rect x="495.5" y="737" width="0.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="498.46" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>vtable stub (1 samples, 0.08%)</title><rect x="504.4" y="769" width="0.9" height="15.0" fill="rgb(231,96,96)" rx="2" ry="2" />
<text text-anchor="" x="507.43" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>skb_clone (4 samples, 0.30%)</title><rect x="851.7" y="561" width="3.6" height="15.0" fill="rgb(230,130,0)" rx="2" ry="2" />
<text text-anchor="" x="854.70" y="571.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>OldToYoungRootsTask::do_it (20 samples, 1.52%)</title><rect x="20.8" y="1137" width="17.9" height="15.0" fill="rgb(198,198,58)" rx="2" ry="2" />
<text text-anchor="" x="23.77" y="1147.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/channel/ChannelDuplexHandler:.flush (237 samples, 18.02%)</title><rect x="674.9" y="865" width="212.7" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="677.93" y="875.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >io/netty/channel/ChannelDupl..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptableObject:.putImpl (1 samples, 0.08%)</title><rect x="298.9" y="705" width="0.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="301.94" y="715.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>mutex_unlock (1 samples, 0.08%)</title><rect x="1180.1" y="1073" width="0.9" height="15.0" fill="rgb(217,117,0)" rx="2" ry="2" />
<text text-anchor="" x="1183.13" y="1083.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>hrtimer_force_reprogram (1 samples, 0.08%)</title><rect x="945.0" y="1057" width="0.9" height="15.0" fill="rgb(232,132,0)" rx="2" ry="2" />
<text text-anchor="" x="948.03" y="1067.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>stub_execve (1 samples, 0.08%)</title><rect x="904.6" y="1089" width="0.9" height="15.0" fill="rgb(226,126,0)" rx="2" ry="2" />
<text text-anchor="" x="907.65" y="1099.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>sock_poll (3 samples, 0.23%)</title><rect x="999.8" y="1041" width="2.7" height="15.0" fill="rgb(239,139,0)" rx="2" ry="2" />
<text text-anchor="" x="1002.76" y="1051.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/IdScriptableObject:.setAttributes (5 samples, 0.38%)</title><rect x="266.6" y="705" width="4.5" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="269.64" y="715.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>_raw_spin_lock_bh (1 samples, 0.08%)</title><rect x="1055.4" y="961" width="0.9" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="1058.40" y="971.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>native_write_msr_safe (3 samples, 0.23%)</title><rect x="947.7" y="1009" width="2.7" height="15.0" fill="rgb(234,134,0)" rx="2" ry="2" />
<text text-anchor="" x="950.72" y="1019.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>sched_clock_cpu (1 samples, 0.08%)</title><rect x="833.8" y="49" width="0.9" height="15.0" fill="rgb(236,136,0)" rx="2" ry="2" />
<text text-anchor="" x="836.76" y="59.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/channel/DefaultChannelPipeline$HeadContext:.flush (232 samples, 17.64%)</title><rect x="674.9" y="801" width="208.2" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="677.93" y="811.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >io/netty/channel/DefaultCha..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/IdScriptableObject:.get (4 samples, 0.30%)</title><rect x="506.2" y="769" width="3.6" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="509.23" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>rcu_idle_enter (1 samples, 0.08%)</title><rect x="936.1" y="1153" width="0.9" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="939.05" y="1163.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>java (995 samples, 75.67%)</title><rect x="10.9" y="1201" width="892.8" height="15.0" fill="rgb(224,86,86)" rx="2" ry="2" />
<text text-anchor="" x="13.90" y="1211.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >java</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>tcp_cleanup_rbuf (2 samples, 0.15%)</title><rect x="94.3" y="737" width="1.8" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="97.35" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/NativeJavaObject:.initMembers (4 samples, 0.30%)</title><rect x="148.2" y="833" width="3.6" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="151.19" y="843.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/NativeCall:.init (16 samples, 1.22%)</title><rect x="396.8" y="769" width="14.3" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="399.75" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>http_parser_execute (2 samples, 0.15%)</title><rect x="974.6" y="1153" width="1.8" height="15.0" fill="rgb(233,99,99)" rx="2" ry="2" />
<text text-anchor="" x="977.64" y="1163.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>_raw_spin_unlock_irqrestore (1 samples, 0.08%)</title><rect x="1009.6" y="1025" width="0.9" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="1012.63" y="1035.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>ThreadRootsTask::do_it (3 samples, 0.23%)</title><rect x="42.3" y="1137" width="2.7" height="15.0" fill="rgb(211,211,63)" rx="2" ry="2" />
<text text-anchor="" x="45.30" y="1147.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>mutex_lock (3 samples, 0.23%)</title><rect x="1002.5" y="1057" width="2.6" height="15.0" fill="rgb(217,117,0)" rx="2" ry="2" />
<text text-anchor="" x="1005.46" y="1067.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>cpu_startup_entry (23 samples, 1.75%)</title><rect x="953.1" y="1121" width="20.6" height="15.0" fill="rgb(223,123,0)" rx="2" ry="2" />
<text text-anchor="" x="956.10" y="1131.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>itable stub (1 samples, 0.08%)</title><rect x="886.7" y="849" width="0.9" height="15.0" fill="rgb(237,104,104)" rx="2" ry="2" />
<text text-anchor="" x="889.70" y="859.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__srcu_read_lock (2 samples, 0.15%)</title><rect x="707.2" y="673" width="1.8" height="15.0" fill="rgb(227,127,0)" rx="2" ry="2" />
<text text-anchor="" x="710.23" y="683.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptableObject:.getSlot (5 samples, 0.38%)</title><rect x="262.2" y="673" width="4.4" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="265.15" y="683.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/vertx/java/core/impl/DefaultVertx:.setContext (1 samples, 0.08%)</title><rect x="613.9" y="865" width="0.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="616.91" y="875.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>ip_rcv_finish (89 samples, 6.77%)</title><rect x="768.3" y="353" width="79.8" height="15.0" fill="rgb(230,130,0)" rx="2" ry="2" />
<text text-anchor="" x="771.25" y="363.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >ip_rcv_fi..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>response_complete (13 samples, 0.99%)</title><rect x="1081.4" y="1089" width="11.7" height="15.0" fill="rgb(239,107,107)" rx="2" ry="2" />
<text text-anchor="" x="1084.42" y="1099.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/handler/codec/http/HttpObjectDecoder:.skipControlCharacters (2 samples, 0.15%)</title><rect x="657.9" y="897" width="1.8" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="660.88" y="907.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>tcp_v4_rcv (27 samples, 2.05%)</title><rect x="1139.7" y="673" width="24.3" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="1142.75" y="683.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >t..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>ktime_get_ts (2 samples, 0.15%)</title><rect x="1025.8" y="1057" width="1.8" height="15.0" fill="rgb(222,122,0)" rx="2" ry="2" />
<text text-anchor="" x="1028.79" y="1067.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>tick_nohz_restart (4 samples, 0.30%)</title><rect x="969.3" y="1089" width="3.5" height="15.0" fill="rgb(230,130,0)" rx="2" ry="2" />
<text text-anchor="" x="972.25" y="1099.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/channel/ChannelOutboundHandlerAdapter:.flush (1 samples, 0.08%)</title><rect x="885.8" y="849" width="0.9" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="888.80" y="859.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>sun/nio/ch/FileDispatcherImpl:.write0 (2 samples, 0.15%)</title><rect x="691.1" y="769" width="1.8" height="15.0" fill="rgb(88,235,88)" rx="2" ry="2" />
<text text-anchor="" x="694.08" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>GCTaskThread::run (28 samples, 2.13%)</title><rect x="19.9" y="1153" width="25.1" height="15.0" fill="rgb(226,226,68)" rx="2" ry="2" />
<text text-anchor="" x="22.87" y="1163.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >G..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/handler/codec/http/HttpHeaders:.encode (1 samples, 0.08%)</title><rect x="213.7" y="673" width="0.9" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="216.70" y="683.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__kmalloc_node_track_caller (1 samples, 0.08%)</title><rect x="860.7" y="577" width="0.9" height="15.0" fill="rgb(223,123,0)" rx="2" ry="2" />
<text text-anchor="" x="863.68" y="587.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/IdScriptableObject:.put (25 samples, 1.90%)</title><rect x="350.1" y="721" width="22.4" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="353.09" y="731.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >o..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/IdScriptableObject:.has (2 samples, 0.15%)</title><rect x="512.5" y="753" width="1.8" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="515.51" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>atomic_notifier_call_chain (1 samples, 0.08%)</title><rect x="934.3" y="1153" width="0.9" height="15.0" fill="rgb(227,127,0)" rx="2" ry="2" />
<text text-anchor="" x="937.26" y="1163.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>remote_function (4 samples, 0.30%)</title><rect x="1128.1" y="801" width="3.6" height="15.0" fill="rgb(242,142,0)" rx="2" ry="2" />
<text text-anchor="" x="1131.08" y="811.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptableObject:.getSlot (1 samples, 0.08%)</title><rect x="325.0" y="705" width="0.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="327.97" y="715.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>common_file_perm (1 samples, 0.08%)</title><rect x="1064.4" y="1009" width="0.9" height="15.0" fill="rgb(232,132,0)" rx="2" ry="2" />
<text text-anchor="" x="1067.37" y="1019.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>sun/nio/ch/SocketChannelImpl:.isConnected (2 samples, 0.15%)</title><rect x="880.4" y="785" width="1.8" height="15.0" fill="rgb(88,235,88)" rx="2" ry="2" />
<text text-anchor="" x="883.42" y="795.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/IdScriptableObject:.has (9 samples, 0.68%)</title><rect x="413.8" y="753" width="8.1" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="416.80" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>tcp_init_tso_segs (1 samples, 0.08%)</title><rect x="734.2" y="577" width="0.8" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="737.15" y="587.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/BaseFunction:.findInstanceIdInfo (1 samples, 0.08%)</title><rect x="328.6" y="721" width="0.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="331.56" y="731.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>tcp_v4_do_rcv (77 samples, 5.86%)</title><rect x="779.0" y="289" width="69.1" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="782.02" y="299.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >tcp_v4_..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__tcp_push_pending_frames (61 samples, 4.64%)</title><rect x="1111.9" y="977" width="54.8" height="15.0" fill="rgb(226,126,0)" rx="2" ry="2" />
<text text-anchor="" x="1114.93" y="987.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >__tcp..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/IdScriptableObject:.findInstanceIdInfo (1 samples, 0.08%)</title><rect x="350.1" y="705" width="0.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="353.09" y="715.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>native_read_tsc (1 samples, 0.08%)</title><rect x="956.7" y="1025" width="0.9" height="15.0" fill="rgb(234,134,0)" rx="2" ry="2" />
<text text-anchor="" x="959.69" y="1035.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>tcp_md5_do_lookup (1 samples, 0.08%)</title><rect x="1174.7" y="929" width="0.9" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="1177.75" y="939.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>do_sync_write (186 samples, 14.14%)</title><rect x="709.0" y="673" width="166.9" height="15.0" fill="rgb(243,143,0)" rx="2" ry="2" />
<text text-anchor="" x="712.03" y="683.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >do_sync_write</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>cpuidle_enter_state (4 samples, 0.30%)</title><rect x="954.0" y="1073" width="3.6" height="15.0" fill="rgb(223,123,0)" rx="2" ry="2" />
<text text-anchor="" x="957.00" y="1083.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>ep_poll_callback (1 samples, 0.08%)</title><rect x="1159.5" y="561" width="0.9" height="15.0" fill="rgb(239,139,0)" rx="2" ry="2" />
<text text-anchor="" x="1162.49" y="571.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>x86_pmu_enable (4 samples, 0.30%)</title><rect x="1128.1" y="721" width="3.6" height="15.0" fill="rgb(248,148,0)" rx="2" ry="2" />
<text text-anchor="" x="1131.08" y="731.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>copy_user_generic_string (3 samples, 0.23%)</title><rect x="91.7" y="721" width="2.6" height="15.0" fill="rgb(229,129,0)" rx="2" ry="2" />
<text text-anchor="" x="94.66" y="731.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>perf_pmu_enable (4 samples, 0.30%)</title><rect x="711.7" y="529" width="3.6" height="15.0" fill="rgb(242,142,0)" rx="2" ry="2" />
<text text-anchor="" x="714.72" y="539.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>vfs_read (25 samples, 1.90%)</title><rect x="76.4" y="833" width="22.4" height="15.0" fill="rgb(226,126,0)" rx="2" ry="2" />
<text text-anchor="" x="79.40" y="843.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >v..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>x86_64_start_reservations (24 samples, 1.83%)</title><rect x="953.1" y="1169" width="21.5" height="15.0" fill="rgb(248,148,0)" rx="2" ry="2" />
<text text-anchor="" x="956.10" y="1179.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >x..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>security_file_permission (1 samples, 0.08%)</title><rect x="97.9" y="817" width="0.9" height="15.0" fill="rgb(238,138,0)" rx="2" ry="2" />
<text text-anchor="" x="100.94" y="827.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptableObject:.addKnownAbsentSlot (1 samples, 0.08%)</title><rect x="358.2" y="673" width="0.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="361.17" y="683.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>nr_iowait_cpu (1 samples, 0.08%)</title><rect x="972.8" y="1073" width="0.9" height="15.0" fill="rgb(239,139,0)" rx="2" ry="2" />
<text text-anchor="" x="975.84" y="1083.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__hrtimer_start_range_ns (2 samples, 0.15%)</title><rect x="950.4" y="1105" width="1.8" height="15.0" fill="rgb(226,126,0)" rx="2" ry="2" />
<text text-anchor="" x="953.41" y="1115.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>system_call_after_swapgs (1 samples, 0.08%)</title><rect x="1099.4" y="1089" width="0.9" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="1102.37" y="1099.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>release_sock (2 samples, 0.15%)</title><rect x="858.0" y="609" width="1.8" height="15.0" fill="rgb(243,143,0)" rx="2" ry="2" />
<text text-anchor="" x="860.98" y="619.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptableObject:.getSlot (11 samples, 0.84%)</title><rect x="289.1" y="673" width="9.8" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="292.07" y="683.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>_raw_spin_unlock_irqrestore (1 samples, 0.08%)</title><rect x="785.3" y="193" width="0.9" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="788.30" y="203.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>itable stub (1 samples, 0.08%)</title><rect x="394.1" y="769" width="0.9" height="15.0" fill="rgb(237,104,104)" rx="2" ry="2" />
<text text-anchor="" x="397.06" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>call_stub (956 samples, 72.70%)</title><rect x="45.0" y="1057" width="857.9" height="15.0" fill="rgb(226,89,89)" rx="2" ry="2" />
<text text-anchor="" x="48.00" y="1067.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >call_stub</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>dev_hard_start_xmit (3 samples, 0.23%)</title><rect x="1125.4" y="833" width="2.7" height="15.0" fill="rgb(243,143,0)" rx="2" ry="2" />
<text text-anchor="" x="1128.39" y="843.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>dev_queue_xmit (11 samples, 0.84%)</title><rect x="745.8" y="497" width="9.9" height="15.0" fill="rgb(243,143,0)" rx="2" ry="2" />
<text text-anchor="" x="748.82" y="507.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>task_nice (2 samples, 0.15%)</title><rect x="1027.6" y="1073" width="1.8" height="15.0" fill="rgb(222,122,0)" rx="2" ry="2" />
<text text-anchor="" x="1030.58" y="1083.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>ip_finish_output (119 samples, 9.05%)</title><rect x="742.2" y="513" width="106.8" height="15.0" fill="rgb(230,130,0)" rx="2" ry="2" />
<text text-anchor="" x="745.23" y="523.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >ip_finish_out..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__remove_hrtimer (1 samples, 0.08%)</title><rect x="945.0" y="1073" width="0.9" height="15.0" fill="rgb(228,128,0)" rx="2" ry="2" />
<text text-anchor="" x="948.03" y="1083.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>sys_epoll_wait (4 samples, 0.30%)</title><rect x="898.4" y="897" width="3.6" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="901.37" y="907.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>rcu_cpu_has_callbacks (1 samples, 0.08%)</title><rect x="967.5" y="1057" width="0.9" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="970.46" y="1067.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>java/lang/ThreadLocal:.get (1 samples, 0.08%)</title><rect x="230.7" y="657" width="0.9" height="15.0" fill="rgb(77,224,77)" rx="2" ry="2" />
<text text-anchor="" x="233.75" y="667.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>rcu_idle_exit (1 samples, 0.08%)</title><rect x="937.0" y="1153" width="0.8" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="939.95" y="1163.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>net_rx_action (97 samples, 7.38%)</title><rect x="762.0" y="433" width="87.0" height="15.0" fill="rgb(244,144,0)" rx="2" ry="2" />
<text text-anchor="" x="764.97" y="443.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >net_rx_act..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>lock_sock_nested (1 samples, 0.08%)</title><rect x="1166.7" y="977" width="0.9" height="15.0" fill="rgb(232,132,0)" rx="2" ry="2" />
<text text-anchor="" x="1169.67" y="987.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>mod_timer (2 samples, 0.15%)</title><rect x="735.9" y="545" width="1.8" height="15.0" fill="rgb(229,129,0)" rx="2" ry="2" />
<text text-anchor="" x="738.95" y="555.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>apparmor_file_free_security (1 samples, 0.08%)</title><rect x="904.6" y="961" width="0.9" height="15.0" fill="rgb(232,132,0)" rx="2" ry="2" />
<text text-anchor="" x="907.65" y="971.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__remove_hrtimer (1 samples, 0.08%)</title><rect x="966.6" y="1025" width="0.9" height="15.0" fill="rgb(228,128,0)" rx="2" ry="2" />
<text text-anchor="" x="969.56" y="1035.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>tcp_established_options (4 samples, 0.30%)</title><rect x="869.7" y="577" width="3.5" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="872.65" y="587.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>sk_reset_timer (5 samples, 0.38%)</title><rect x="783.5" y="225" width="4.5" height="15.0" fill="rgb(222,122,0)" rx="2" ry="2" />
<text text-anchor="" x="786.51" y="235.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/channel/ChannelOutboundHandlerAdapter:.flush (235 samples, 17.87%)</title><rect x="674.9" y="833" width="210.9" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="677.93" y="843.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >io/netty/channel/ChannelOut..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/NativeFunction:.initScriptFunction (1 samples, 0.08%)</title><rect x="411.1" y="769" width="0.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="414.11" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>menu_reflect (1 samples, 0.08%)</title><rect x="933.4" y="1137" width="0.9" height="15.0" fill="rgb(229,129,0)" rx="2" ry="2" />
<text text-anchor="" x="936.36" y="1147.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__slab_alloc (3 samples, 0.23%)</title><rect x="1171.2" y="929" width="2.6" height="15.0" fill="rgb(227,127,0)" rx="2" ry="2" />
<text text-anchor="" x="1174.16" y="939.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>PSScavengeKlassClosure::do_klass (1 samples, 0.08%)</title><rect x="38.7" y="1105" width="0.9" height="15.0" fill="rgb(214,214,64)" rx="2" ry="2" />
<text text-anchor="" x="41.71" y="1115.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__ip_local_out (1 samples, 0.08%)</title><rect x="1123.6" y="913" width="0.9" height="15.0" fill="rgb(225,125,0)" rx="2" ry="2" />
<text text-anchor="" x="1126.60" y="923.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/TopLevel:.getBuiltinPrototype (5 samples, 0.38%)</title><rect x="382.4" y="689" width="4.5" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="385.40" y="699.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>tcp_send_delayed_ack (3 samples, 0.23%)</title><rect x="1144.2" y="609" width="2.7" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="1147.24" y="619.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>arch_local_irq_save (1 samples, 0.08%)</title><rect x="1173.8" y="929" width="0.9" height="15.0" fill="rgb(230,130,0)" rx="2" ry="2" />
<text text-anchor="" x="1176.85" y="939.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__kmalloc_node_track_caller (1 samples, 0.08%)</title><rect x="861.6" y="561" width="0.9" height="15.0" fill="rgb(223,123,0)" rx="2" ry="2" />
<text text-anchor="" x="864.57" y="571.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>java/nio/channels/spi/AbstractInterruptibleChannel:.end (1 samples, 0.08%)</title><rect x="695.6" y="753" width="0.9" height="15.0" fill="rgb(77,224,77)" rx="2" ry="2" />
<text text-anchor="" x="698.57" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptableObject:.createSlot (15 samples, 1.14%)</title><rect x="359.1" y="673" width="13.4" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="362.06" y="683.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>apparmor_file_permission (2 samples, 0.15%)</title><rect x="1059.9" y="1025" width="1.8" height="15.0" fill="rgb(232,132,0)" rx="2" ry="2" />
<text text-anchor="" x="1062.89" y="1035.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>mutex_lock (2 samples, 0.15%)</title><rect x="1183.7" y="1057" width="1.8" height="15.0" fill="rgb(217,117,0)" rx="2" ry="2" />
<text text-anchor="" x="1186.72" y="1067.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>sys_epoll_ctl (5 samples, 0.38%)</title><rect x="1085.9" y="1041" width="4.5" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="1088.91" y="1051.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>hrtimer_interrupt (1 samples, 0.08%)</title><rect x="1038.3" y="1073" width="0.9" height="15.0" fill="rgb(232,132,0)" rx="2" ry="2" />
<text text-anchor="" x="1041.35" y="1083.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>ParallelTaskTerminator::offer_termination (2 samples, 0.15%)</title><rect x="39.6" y="1121" width="1.8" height="15.0" fill="rgb(214,214,64)" rx="2" ry="2" />
<text text-anchor="" x="42.61" y="1131.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>dequeue_entity (4 samples, 0.30%)</title><rect x="1015.9" y="961" width="3.6" height="15.0" fill="rgb(247,147,0)" rx="2" ry="2" />
<text text-anchor="" x="1018.92" y="971.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/buffer/PooledByteBuf:.deallocate (5 samples, 0.38%)</title><rect x="682.1" y="753" width="4.5" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="685.11" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/IdScriptableObject:.findInstanceIdInfo (1 samples, 0.08%)</title><rect x="326.8" y="689" width="0.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="329.76" y="699.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>wrk (240 samples, 18.25%)</title><rect x="974.6" y="1201" width="215.4" height="15.0" fill="rgb(224,85,85)" rx="2" ry="2" />
<text text-anchor="" x="977.64" y="1211.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >wrk</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>perf_pmu_enable (4 samples, 0.30%)</title><rect x="906.4" y="913" width="3.6" height="15.0" fill="rgb(242,142,0)" rx="2" ry="2" />
<text text-anchor="" x="909.44" y="923.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/IdScriptableObject:.get (2 samples, 0.15%)</title><rect x="395.0" y="769" width="1.8" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="397.96" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>remote_function (4 samples, 0.30%)</title><rect x="906.4" y="977" width="3.6" height="15.0" fill="rgb(242,142,0)" rx="2" ry="2" />
<text text-anchor="" x="909.44" y="987.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__GI___ioctl (5 samples, 0.38%)</title><rect x="905.5" y="1121" width="4.5" height="15.0" fill="rgb(228,91,91)" rx="2" ry="2" />
<text text-anchor="" x="908.54" y="1131.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>socket_readable (2 samples, 0.15%)</title><rect x="974.6" y="1169" width="1.8" height="15.0" fill="rgb(241,110,110)" rx="2" ry="2" />
<text text-anchor="" x="977.64" y="1179.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>epoll_ctl (1 samples, 0.08%)</title><rect x="975.5" y="1121" width="0.9" height="15.0" fill="rgb(248,120,120)" rx="2" ry="2" />
<text text-anchor="" x="978.54" y="1131.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptableObject:.putImpl (21 samples, 1.60%)</title><rect x="448.8" y="753" width="18.8" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="451.80" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>tick_nohz_stop_sched_tick (4 samples, 0.30%)</title><rect x="964.8" y="1073" width="3.6" height="15.0" fill="rgb(230,130,0)" rx="2" ry="2" />
<text text-anchor="" x="967.77" y="1083.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/channel/DefaultChannelPipeline$HeadContext:.write (6 samples, 0.46%)</title><rect x="207.4" y="657" width="5.4" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="210.41" y="667.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>tcp_write_xmit (147 samples, 11.18%)</title><rect x="724.3" y="593" width="131.9" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="727.28" y="603.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >tcp_write_xmit</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptRuntime:.getPropFunctionAndThisHelper (5 samples, 0.38%)</title><rect x="500.8" y="785" width="4.5" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="503.84" y="795.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>lock_hrtimer_base.isra.19 (1 samples, 0.08%)</title><rect x="1010.5" y="1025" width="0.9" height="15.0" fill="rgb(232,132,0)" rx="2" ry="2" />
<text text-anchor="" x="1013.53" y="1035.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>inet_recvmsg (17 samples, 1.29%)</title><rect x="80.9" y="769" width="15.2" height="15.0" fill="rgb(239,139,0)" rx="2" ry="2" />
<text text-anchor="" x="83.89" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>native_write_msr_safe (4 samples, 0.30%)</title><rect x="914.5" y="961" width="3.6" height="15.0" fill="rgb(234,134,0)" rx="2" ry="2" />
<text text-anchor="" x="917.52" y="971.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>ip_local_out (46 samples, 3.50%)</title><rect x="1124.5" y="913" width="41.3" height="15.0" fill="rgb(230,130,0)" rx="2" ry="2" />
<text text-anchor="" x="1127.49" y="923.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >ip_..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>java/lang/String:.hashCode (1 samples, 0.08%)</title><rect x="237.9" y="753" width="0.9" height="15.0" fill="rgb(77,224,77)" rx="2" ry="2" />
<text text-anchor="" x="240.92" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/IdScriptableObject:.has (1 samples, 0.08%)</title><rect x="326.8" y="705" width="0.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="329.76" y="715.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>local_bh_enable (42 samples, 3.19%)</title><rect x="1128.1" y="865" width="37.7" height="15.0" fill="rgb(232,132,0)" rx="2" ry="2" />
<text text-anchor="" x="1131.08" y="875.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >loc..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>hrtimer_start_range_ns (3 samples, 0.23%)</title><rect x="970.2" y="1073" width="2.6" height="15.0" fill="rgb(232,132,0)" rx="2" ry="2" />
<text text-anchor="" x="973.15" y="1083.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>jlong_disjoint_arraycopy (1 samples, 0.08%)</title><rect x="196.6" y="689" width="0.9" height="15.0" fill="rgb(230,94,94)" rx="2" ry="2" />
<text text-anchor="" x="199.65" y="699.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>ep_send_events_proc (4 samples, 0.30%)</title><rect x="898.4" y="849" width="3.6" height="15.0" fill="rgb(239,139,0)" rx="2" ry="2" />
<text text-anchor="" x="901.37" y="859.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptableObject:.getParentScope (3 samples, 0.23%)</title><rect x="386.9" y="705" width="2.7" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="389.88" y="715.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>itable stub (1 samples, 0.08%)</title><rect x="672.2" y="913" width="0.9" height="15.0" fill="rgb(237,104,104)" rx="2" ry="2" />
<text text-anchor="" x="675.24" y="923.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>path_openat (1 samples, 0.08%)</title><rect x="904.6" y="1009" width="0.9" height="15.0" fill="rgb(230,130,0)" rx="2" ry="2" />
<text text-anchor="" x="907.65" y="1019.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>activate_task (7 samples, 0.53%)</title><rect x="828.4" y="97" width="6.3" height="15.0" fill="rgb(231,131,0)" rx="2" ry="2" />
<text text-anchor="" x="831.37" y="107.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>pick_next_task_fair (1 samples, 0.08%)</title><rect x="1021.3" y="1009" width="0.9" height="15.0" fill="rgb(239,139,0)" rx="2" ry="2" />
<text text-anchor="" x="1024.30" y="1019.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>security_file_permission (5 samples, 0.38%)</title><rect x="1061.7" y="1025" width="4.5" height="15.0" fill="rgb(238,138,0)" rx="2" ry="2" />
<text text-anchor="" x="1064.68" y="1035.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/channel/ChannelOutboundBuffer:.decrementPendingOutboundBytes (1 samples, 0.08%)</title><rect x="686.6" y="769" width="0.9" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="689.59" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>system_call_fastpath (56 samples, 4.26%)</title><rect x="981.8" y="1121" width="50.3" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="984.82" y="1131.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >syste..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/NativeFunction:.initScriptFunction (6 samples, 0.46%)</title><rect x="381.5" y="705" width="5.4" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="384.50" y="715.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>ip_local_deliver_finish (30 samples, 2.28%)</title><rect x="1137.1" y="689" width="26.9" height="15.0" fill="rgb(230,130,0)" rx="2" ry="2" />
<text text-anchor="" x="1140.06" y="699.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >i..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>sock_read (2 samples, 0.15%)</title><rect x="1041.0" y="1121" width="1.8" height="15.0" fill="rgb(241,110,110)" rx="2" ry="2" />
<text text-anchor="" x="1044.04" y="1131.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>deactivate_task (7 samples, 0.53%)</title><rect x="1015.0" y="1009" width="6.3" height="15.0" fill="rgb(251,151,0)" rx="2" ry="2" />
<text text-anchor="" x="1018.02" y="1019.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>lock_sock_nested (1 samples, 0.08%)</title><rect x="81.8" y="753" width="0.9" height="15.0" fill="rgb(232,132,0)" rx="2" ry="2" />
<text text-anchor="" x="84.79" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>sock_put (1 samples, 0.08%)</title><rect x="1138.0" y="673" width="0.9" height="15.0" fill="rgb(239,139,0)" rx="2" ry="2" />
<text text-anchor="" x="1140.95" y="683.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>mod_timer (3 samples, 0.23%)</title><rect x="1144.2" y="577" width="2.7" height="15.0" fill="rgb(229,129,0)" rx="2" ry="2" />
<text text-anchor="" x="1147.24" y="587.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>aeProcessEvents (171 samples, 13.00%)</title><rect x="1032.1" y="1137" width="153.4" height="15.0" fill="rgb(233,98,98)" rx="2" ry="2" />
<text text-anchor="" x="1035.07" y="1147.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >aeProcessEvents</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/buffer/AbstractByteBuf:.ensureWritable (2 samples, 0.15%)</title><rect x="223.6" y="625" width="1.8" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="226.57" y="635.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>tick_nohz_stop_sched_tick (5 samples, 0.38%)</title><rect x="942.3" y="1121" width="4.5" height="15.0" fill="rgb(230,130,0)" rx="2" ry="2" />
<text text-anchor="" x="945.33" y="1131.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/NativeJavaMethod:.findCachedFunction (2 samples, 0.15%)</title><rect x="489.2" y="785" width="1.8" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="492.18" y="795.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptableObject:.getSlot (1 samples, 0.08%)</title><rect x="498.2" y="753" width="0.8" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="501.15" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/handler/codec/http/HttpMethod:.valueOf (2 samples, 0.15%)</title><rect x="631.0" y="897" width="1.8" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="633.96" y="907.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>hrtimer_try_to_cancel (1 samples, 0.08%)</title><rect x="969.3" y="1057" width="0.9" height="15.0" fill="rgb(232,132,0)" rx="2" ry="2" />
<text text-anchor="" x="972.25" y="1067.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>system_call (1 samples, 0.08%)</title><rect x="10.9" y="1137" width="0.9" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="13.90" y="1147.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>hrtimer_cancel (1 samples, 0.08%)</title><rect x="969.3" y="1073" width="0.9" height="15.0" fill="rgb(232,132,0)" rx="2" ry="2" />
<text text-anchor="" x="972.25" y="1083.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>system_call_fastpath (196 samples, 14.90%)</title><rect x="701.0" y="721" width="175.8" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="703.95" y="731.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >system_call_fastpath</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/channel/AbstractChannelHandlerContext:.read (2 samples, 0.15%)</title><rect x="892.1" y="881" width="1.8" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="895.08" y="891.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>[unknown] (10 samples, 0.76%)</title><rect x="10.9" y="1185" width="9.0" height="15.0" fill="rgb(243,112,112)" rx="2" ry="2" />
<text text-anchor="" x="13.90" y="1195.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/handler/codec/MessageToMessageEncoder:.write (1 samples, 0.08%)</title><rect x="235.2" y="705" width="0.9" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="238.23" y="715.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/IdScriptableObject:.put (47 samples, 3.57%)</title><rect x="548.4" y="769" width="42.2" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="551.40" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >org..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>jlong_disjoint_arraycopy (1 samples, 0.08%)</title><rect x="197.5" y="705" width="0.9" height="15.0" fill="rgb(230,94,94)" rx="2" ry="2" />
<text text-anchor="" x="200.54" y="715.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>[unknown] (1 samples, 0.08%)</title><rect x="974.6" y="1121" width="0.9" height="15.0" fill="rgb(243,112,112)" rx="2" ry="2" />
<text text-anchor="" x="977.64" y="1131.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>native_read_tsc (1 samples, 0.08%)</title><rect x="807.7" y="161" width="0.9" height="15.0" fill="rgb(234,134,0)" rx="2" ry="2" />
<text text-anchor="" x="810.73" y="171.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/handler/codec/http/HttpObjectDecoder:.splitHeader (8 samples, 0.61%)</title><rect x="650.7" y="881" width="7.2" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="653.70" y="891.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>intel_pmu_enable_all (4 samples, 0.30%)</title><rect x="914.5" y="977" width="3.6" height="15.0" fill="rgb(236,136,0)" rx="2" ry="2" />
<text text-anchor="" x="917.52" y="987.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/handler/codec/http/DefaultHttpHeaders:.set (3 samples, 0.23%)</title><rect x="193.1" y="753" width="2.6" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="196.06" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>read_tsc (1 samples, 0.08%)</title><rect x="807.7" y="177" width="0.9" height="15.0" fill="rgb(242,142,0)" rx="2" ry="2" />
<text text-anchor="" x="810.73" y="187.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>_raw_spin_lock (1 samples, 0.08%)</title><rect x="750.3" y="417" width="0.9" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="753.30" y="427.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptRuntime:.toObjectOrNull (1 samples, 0.08%)</title><rect x="373.4" y="737" width="0.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="376.42" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>_raw_spin_lock_irqsave (1 samples, 0.08%)</title><rect x="1006.9" y="1009" width="0.9" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="1009.94" y="1019.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>dequeue_task_fair (6 samples, 0.46%)</title><rect x="1015.0" y="977" width="5.4" height="15.0" fill="rgb(247,147,0)" rx="2" ry="2" />
<text text-anchor="" x="1018.02" y="987.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptRuntime:.getPropFunctionAndThisHelper (1 samples, 0.08%)</title><rect x="237.0" y="785" width="0.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="240.03" y="795.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>do_softirq (38 samples, 2.89%)</title><rect x="1131.7" y="849" width="34.1" height="15.0" fill="rgb(243,143,0)" rx="2" ry="2" />
<text text-anchor="" x="1134.67" y="859.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >do..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>response_complete (2 samples, 0.15%)</title><rect x="974.6" y="1137" width="1.8" height="15.0" fill="rgb(239,107,107)" rx="2" ry="2" />
<text text-anchor="" x="977.64" y="1147.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>get_next_timer_interrupt (3 samples, 0.23%)</title><rect x="942.3" y="1105" width="2.7" height="15.0" fill="rgb(238,138,0)" rx="2" ry="2" />
<text text-anchor="" x="945.33" y="1115.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__perf_event_enable (4 samples, 0.30%)</title><rect x="914.5" y="1057" width="3.6" height="15.0" fill="rgb(230,130,0)" rx="2" ry="2" />
<text text-anchor="" x="917.52" y="1067.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>_raw_spin_lock_irqsave (1 samples, 0.08%)</title><rect x="1155.0" y="545" width="0.9" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="1158.00" y="555.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>lapic_next_deadline (3 samples, 0.23%)</title><rect x="970.2" y="1009" width="2.6" height="15.0" fill="rgb(220,120,0)" rx="2" ry="2" />
<text text-anchor="" x="973.15" y="1019.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptableObject:.createSlot (4 samples, 0.30%)</title><rect x="263.0" y="657" width="3.6" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="266.05" y="667.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>fput (1 samples, 0.08%)</title><rect x="703.6" y="689" width="0.9" height="15.0" fill="rgb(239,139,0)" rx="2" ry="2" />
<text text-anchor="" x="706.64" y="699.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>tcp_rearm_rto (3 samples, 0.23%)</title><rect x="1153.2" y="609" width="2.7" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="1156.21" y="619.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptableObject:.getSlot (2 samples, 0.15%)</title><rect x="510.7" y="737" width="1.8" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="513.71" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>group_sched_in (4 samples, 0.30%)</title><rect x="906.4" y="945" width="3.6" height="15.0" fill="rgb(239,139,0)" rx="2" ry="2" />
<text text-anchor="" x="909.44" y="955.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__getnstimeofday (1 samples, 0.08%)</title><rect x="807.7" y="193" width="0.9" height="15.0" fill="rgb(227,127,0)" rx="2" ry="2" />
<text text-anchor="" x="810.73" y="203.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>java/util/Arrays:.copyOf (1 samples, 0.08%)</title><rect x="198.4" y="721" width="0.9" height="15.0" fill="rgb(77,224,77)" rx="2" ry="2" />
<text text-anchor="" x="201.44" y="731.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>local_bh_enable (104 samples, 7.91%)</title><rect x="755.7" y="497" width="93.3" height="15.0" fill="rgb(232,132,0)" rx="2" ry="2" />
<text text-anchor="" x="758.69" y="507.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >local_bh_en..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>tcp_event_new_data_sent (3 samples, 0.23%)</title><rect x="1114.6" y="945" width="2.7" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="1117.62" y="955.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>read_tsc (2 samples, 0.15%)</title><rect x="849.9" y="529" width="1.8" height="15.0" fill="rgb(242,142,0)" rx="2" ry="2" />
<text text-anchor="" x="852.91" y="539.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>system_call_fastpath (6 samples, 0.46%)</title><rect x="1085.0" y="1057" width="5.4" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="1088.01" y="1067.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>tcp_prequeue (1 samples, 0.08%)</title><rect x="1138.9" y="673" width="0.8" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="1141.85" y="683.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>call_function_single_interrupt (4 samples, 0.30%)</title><rect x="711.7" y="641" width="3.6" height="15.0" fill="rgb(221,121,0)" rx="2" ry="2" />
<text text-anchor="" x="714.72" y="651.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>get_page_from_freelist (1 samples, 0.08%)</title><rect x="1173.0" y="865" width="0.8" height="15.0" fill="rgb(238,138,0)" rx="2" ry="2" />
<text text-anchor="" x="1175.95" y="875.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptableObject$Slot:.setAttributes (5 samples, 0.38%)</title><rect x="266.6" y="689" width="4.5" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="269.64" y="699.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/channel/AbstractChannelHandlerContext:.read (4 samples, 0.30%)</title><rect x="890.3" y="945" width="3.6" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="893.29" y="955.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/vertx/java/core/http/impl/VertxHttpHandler:.write (34 samples, 2.59%)</title><rect x="205.6" y="721" width="30.5" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="208.62" y="731.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >or..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>_raw_spin_lock_irqsave (1 samples, 0.08%)</title><rect x="842.7" y="225" width="0.9" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="845.73" y="235.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/handler/codec/http/HttpObjectDecoder:.splitInitialLine (5 samples, 0.38%)</title><rect x="659.7" y="897" width="4.5" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="662.67" y="907.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptableObject:.getParentScope (4 samples, 0.30%)</title><rect x="607.6" y="753" width="3.6" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="610.63" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/IdScriptableObject:.get (3 samples, 0.23%)</title><rect x="440.7" y="769" width="2.7" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="443.72" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptRuntime:.getObjectProp (1 samples, 0.08%)</title><rect x="241.5" y="801" width="0.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="244.51" y="811.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>swapper (72 samples, 5.48%)</title><rect x="910.0" y="1201" width="64.6" height="15.0" fill="rgb(233,99,99)" rx="2" ry="2" />
<text text-anchor="" x="913.03" y="1211.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >swapper</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>ktime_get (1 samples, 0.08%)</title><rect x="968.4" y="1089" width="0.9" height="15.0" fill="rgb(222,122,0)" rx="2" ry="2" />
<text text-anchor="" x="971.36" y="1099.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>_raw_spin_unlock_irqrestore (1 samples, 0.08%)</title><rect x="733.3" y="513" width="0.9" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="736.25" y="523.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>java/lang/ThreadLocal:.get (1 samples, 0.08%)</title><rect x="140.1" y="865" width="0.9" height="15.0" fill="rgb(77,224,77)" rx="2" ry="2" />
<text text-anchor="" x="143.11" y="875.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/gen/file__root_vert_x_2_1_5_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_2 (416 samples, 31.63%)</title><rect x="238.8" y="817" width="373.3" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="241.82" y="827.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >org/mozilla/javascript/gen/file__root_vert_x_2_1_5..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>java/lang/String:.hashCode (1 samples, 0.08%)</title><rect x="506.2" y="753" width="0.9" height="15.0" fill="rgb(77,224,77)" rx="2" ry="2" />
<text text-anchor="" x="509.23" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__schedule (4 samples, 0.30%)</title><rect x="937.8" y="1137" width="3.6" height="15.0" fill="rgb(227,127,0)" rx="2" ry="2" />
<text text-anchor="" x="940.85" y="1147.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>bictcp_cong_avoid (3 samples, 0.23%)</title><rect x="793.4" y="241" width="2.7" height="15.0" fill="rgb(225,125,0)" rx="2" ry="2" />
<text text-anchor="" x="796.38" y="251.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>tcp_rcv_space_adjust (2 samples, 0.15%)</title><rect x="82.7" y="753" width="1.8" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="85.68" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>JavaThread::run (956 samples, 72.70%)</title><rect x="45.0" y="1153" width="857.9" height="15.0" fill="rgb(203,203,60)" rx="2" ry="2" />
<text text-anchor="" x="48.00" y="1163.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >JavaThread::run</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>apparmor_socket_sendmsg (1 samples, 0.08%)</title><rect x="710.8" y="641" width="0.9" height="15.0" fill="rgb(232,132,0)" rx="2" ry="2" />
<text text-anchor="" x="713.82" y="651.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>InstanceKlass::oop_push_contents (8 samples, 0.61%)</title><rect x="27.9" y="1105" width="7.2" height="15.0" fill="rgb(218,218,65)" rx="2" ry="2" />
<text text-anchor="" x="30.95" y="1115.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>sun/nio/ch/SocketChannelImpl:.isConnected (1 samples, 0.08%)</title><rect x="883.1" y="801" width="0.9" height="15.0" fill="rgb(88,235,88)" rx="2" ry="2" />
<text text-anchor="" x="886.11" y="811.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__libc_start_main (6 samples, 0.46%)</title><rect x="904.6" y="1185" width="5.4" height="15.0" fill="rgb(236,103,103)" rx="2" ry="2" />
<text text-anchor="" x="907.65" y="1195.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>tcp_is_cwnd_limited (2 samples, 0.15%)</title><rect x="794.3" y="225" width="1.8" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="797.27" y="235.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>sun/nio/ch/FileDispatcherImpl:.write0 (203 samples, 15.44%)</title><rect x="697.4" y="753" width="182.1" height="15.0" fill="rgb(88,235,88)" rx="2" ry="2" />
<text text-anchor="" x="700.36" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >sun/nio/ch/FileDispatch..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>internal_add_timer (2 samples, 0.15%)</title><rect x="1145.1" y="561" width="1.8" height="15.0" fill="rgb(236,136,0)" rx="2" ry="2" />
<text text-anchor="" x="1148.13" y="571.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptableObject$Slot:.setAttributes (2 samples, 0.15%)</title><rect x="407.5" y="737" width="1.8" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="410.52" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>[unknown] (30 samples, 2.28%)</title><rect x="71.9" y="881" width="26.9" height="15.0" fill="rgb(243,112,112)" rx="2" ry="2" />
<text text-anchor="" x="74.92" y="891.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >[..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/buffer/AbstractByteBufAllocator:.heapBuffer (3 samples, 0.23%)</title><rect x="185.0" y="753" width="2.7" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="187.98" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__tcp_push_pending_frames (149 samples, 11.33%)</title><rect x="722.5" y="609" width="133.7" height="15.0" fill="rgb(226,126,0)" rx="2" ry="2" />
<text text-anchor="" x="725.49" y="619.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >__tcp_push_pendi..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>ClassLoaderDataGraph::oops_do (1 samples, 0.08%)</title><rect x="38.7" y="1121" width="0.9" height="15.0" fill="rgb(204,204,60)" rx="2" ry="2" />
<text text-anchor="" x="41.71" y="1131.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>tick_nohz_stop_idle (1 samples, 0.08%)</title><rect x="972.8" y="1089" width="0.9" height="15.0" fill="rgb(230,130,0)" rx="2" ry="2" />
<text text-anchor="" x="975.84" y="1099.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/channel/AbstractChannelHandlerContext:.executor (1 samples, 0.08%)</title><rect x="100.6" y="945" width="0.9" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="103.63" y="955.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__skb_clone (1 samples, 0.08%)</title><rect x="1165.8" y="913" width="0.9" height="15.0" fill="rgb(227,127,0)" rx="2" ry="2" />
<text text-anchor="" x="1168.77" y="923.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>tcp_ack (20 samples, 1.52%)</title><rect x="790.7" y="257" width="17.9" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="793.68" y="267.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__inet_lookup_established (4 samples, 0.30%)</title><rect x="770.9" y="289" width="3.6" height="15.0" fill="rgb(225,125,0)" rx="2" ry="2" />
<text text-anchor="" x="773.94" y="299.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/NativeJavaMethod:.findFunction (2 samples, 0.15%)</title><rect x="482.0" y="769" width="1.8" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="485.00" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptableObject:.createSlot (4 samples, 0.30%)</title><rect x="403.0" y="705" width="3.6" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="406.03" y="715.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>enqueue_task (7 samples, 0.53%)</title><rect x="828.4" y="81" width="6.3" height="15.0" fill="rgb(248,148,0)" rx="2" ry="2" />
<text text-anchor="" x="831.37" y="91.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>sock_def_readable (2 samples, 0.15%)</title><rect x="788.9" y="257" width="1.8" height="15.0" fill="rgb(239,139,0)" rx="2" ry="2" />
<text text-anchor="" x="791.89" y="267.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptableObject:.addKnownAbsentSlot (1 samples, 0.08%)</title><rect x="371.6" y="657" width="0.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="374.63" y="667.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>tcp_data_queue (39 samples, 2.97%)</title><rect x="812.2" y="257" width="35.0" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="815.22" y="267.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >tc..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>security_file_permission (1 samples, 0.08%)</title><rect x="875.9" y="657" width="0.9" height="15.0" fill="rgb(238,138,0)" rx="2" ry="2" />
<text text-anchor="" x="878.93" y="667.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/channel/AbstractChannelHandlerContext:.executor (1 samples, 0.08%)</title><rect x="116.8" y="897" width="0.9" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="119.78" y="907.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>sun/reflect/DelegatingMethodAccessorImpl:.invoke (66 samples, 5.02%)</title><rect x="176.9" y="769" width="59.2" height="15.0" fill="rgb(88,235,88)" rx="2" ry="2" />
<text text-anchor="" x="179.90" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >sun/re..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__skb_clone (1 samples, 0.08%)</title><rect x="1119.1" y="929" width="0.9" height="15.0" fill="rgb(227,127,0)" rx="2" ry="2" />
<text text-anchor="" x="1122.11" y="939.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/vertx/java/platform/impl/RhinoContextFactory:.onContextCreated (1 samples, 0.08%)</title><rect x="613.0" y="849" width="0.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="616.01" y="859.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>tcp_poll (1 samples, 0.08%)</title><rect x="901.1" y="817" width="0.9" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="904.06" y="827.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>netif_skb_dev_features (1 samples, 0.08%)</title><rect x="753.9" y="449" width="0.9" height="15.0" fill="rgb(244,144,0)" rx="2" ry="2" />
<text text-anchor="" x="756.89" y="459.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>ep_scan_ready_list.isra.9 (4 samples, 0.30%)</title><rect x="898.4" y="865" width="3.6" height="15.0" fill="rgb(239,139,0)" rx="2" ry="2" />
<text text-anchor="" x="901.37" y="875.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>native_write_msr_safe (4 samples, 0.30%)</title><rect x="711.7" y="481" width="3.6" height="15.0" fill="rgb(234,134,0)" rx="2" ry="2" />
<text text-anchor="" x="714.72" y="491.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>copy_user_generic_string (1 samples, 0.08%)</title><rect x="1056.3" y="945" width="0.9" height="15.0" fill="rgb(229,129,0)" rx="2" ry="2" />
<text text-anchor="" x="1059.30" y="955.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>intel_pmu_enable_all (4 samples, 0.30%)</title><rect x="711.7" y="497" width="3.6" height="15.0" fill="rgb(236,136,0)" rx="2" ry="2" />
<text text-anchor="" x="714.72" y="507.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__switch_to (1 samples, 0.08%)</title><rect x="980.0" y="1121" width="0.9" height="15.0" fill="rgb(227,127,0)" rx="2" ry="2" />
<text text-anchor="" x="983.02" y="1131.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/IdScriptableObject:.has (4 samples, 0.30%)</title><rect x="443.4" y="769" width="3.6" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="446.41" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__hrtimer_start_range_ns (3 samples, 0.23%)</title><rect x="1005.1" y="1041" width="2.7" height="15.0" fill="rgb(226,126,0)" rx="2" ry="2" />
<text text-anchor="" x="1008.15" y="1051.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__srcu_read_lock (2 samples, 0.15%)</title><rect x="1177.4" y="1025" width="1.8" height="15.0" fill="rgb(227,127,0)" rx="2" ry="2" />
<text text-anchor="" x="1180.44" y="1035.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/channel/AbstractChannelHandlerContext:.validatePromise (2 samples, 0.15%)</title><rect x="202.9" y="737" width="1.8" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="205.93" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptableObject:.createSlot (11 samples, 0.84%)</title><rect x="289.1" y="657" width="9.8" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="292.07" y="667.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptableObject:.getSlot (8 samples, 0.61%)</title><rect x="424.6" y="721" width="7.1" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="427.57" y="731.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/NativeJavaMethod:.findCachedFunction (2 samples, 0.15%)</title><rect x="173.3" y="769" width="1.8" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="176.32" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>sock_poll (1 samples, 0.08%)</title><rect x="1089.5" y="1025" width="0.9" height="15.0" fill="rgb(239,139,0)" rx="2" ry="2" />
<text text-anchor="" x="1092.50" y="1035.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>tick_program_event (3 samples, 0.23%)</title><rect x="970.2" y="1041" width="2.6" height="15.0" fill="rgb(230,130,0)" rx="2" ry="2" />
<text text-anchor="" x="973.15" y="1051.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>tcp_transmit_skb (55 samples, 4.18%)</title><rect x="1117.3" y="945" width="49.4" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="1120.32" y="955.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >tcp_..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/NativeFunction:.initScriptFunction (1 samples, 0.08%)</title><rect x="432.6" y="753" width="0.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="435.65" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/WrapFactory:.wrap (5 samples, 0.38%)</title><rect x="147.3" y="849" width="4.5" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="150.29" y="859.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>java/lang/String:.getBytes (3 samples, 0.23%)</title><rect x="196.6" y="753" width="2.7" height="15.0" fill="rgb(77,224,77)" rx="2" ry="2" />
<text text-anchor="" x="199.65" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/NativeJavaObject:.initMembers (4 samples, 0.30%)</title><rect x="485.6" y="753" width="3.6" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="488.59" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>bictcp_cong_avoid (1 samples, 0.08%)</title><rect x="1146.9" y="625" width="0.9" height="15.0" fill="rgb(225,125,0)" rx="2" ry="2" />
<text text-anchor="" x="1149.93" y="635.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>ktime_get_real (3 samples, 0.23%)</title><rect x="849.0" y="561" width="2.7" height="15.0" fill="rgb(222,122,0)" rx="2" ry="2" />
<text text-anchor="" x="852.01" y="571.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>java/lang/ThreadLocal:.get (1 samples, 0.08%)</title><rect x="613.0" y="817" width="0.9" height="15.0" fill="rgb(77,224,77)" rx="2" ry="2" />
<text text-anchor="" x="616.01" y="827.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptableObject:.getSlot (21 samples, 1.60%)</title><rect x="353.7" y="689" width="18.8" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="356.68" y="699.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>rcu_sysidle_force_exit (2 samples, 0.15%)</title><rect x="961.2" y="1089" width="1.8" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="964.18" y="1099.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptableObject$Slot:.getValue (2 samples, 0.15%)</title><rect x="499.0" y="769" width="1.8" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="502.05" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>aa_file_perm (1 samples, 0.08%)</title><rect x="96.1" y="753" width="0.9" height="15.0" fill="rgb(216,116,0)" rx="2" ry="2" />
<text text-anchor="" x="99.14" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>tick_sched_timer (1 samples, 0.08%)</title><rect x="1038.3" y="1041" width="0.9" height="15.0" fill="rgb(230,130,0)" rx="2" ry="2" />
<text text-anchor="" x="1041.35" y="1051.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>sk_reset_timer (3 samples, 0.23%)</title><rect x="1114.6" y="913" width="2.7" height="15.0" fill="rgb(222,122,0)" rx="2" ry="2" />
<text text-anchor="" x="1117.62" y="923.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/IdScriptableObject:.findInstanceIdInfo (1 samples, 0.08%)</title><rect x="261.3" y="689" width="0.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="264.25" y="699.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>msecs_to_jiffies (1 samples, 0.08%)</title><rect x="761.1" y="433" width="0.9" height="15.0" fill="rgb(222,122,0)" rx="2" ry="2" />
<text text-anchor="" x="764.07" y="443.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>ipv4_dst_check (1 samples, 0.08%)</title><rect x="777.2" y="289" width="0.9" height="15.0" fill="rgb(231,131,0)" rx="2" ry="2" />
<text text-anchor="" x="780.22" y="299.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>tcp_write_xmit (60 samples, 4.56%)</title><rect x="1112.8" y="961" width="53.9" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="1115.83" y="971.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >tcp_w..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/util/Recycler:.recycle (1 samples, 0.08%)</title><rect x="231.6" y="673" width="0.9" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="234.64" y="683.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>group_sched_in (4 samples, 0.30%)</title><rect x="914.5" y="1041" width="3.6" height="15.0" fill="rgb(239,139,0)" rx="2" ry="2" />
<text text-anchor="" x="917.52" y="1051.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>generic_exec_single (1 samples, 0.08%)</title><rect x="905.5" y="977" width="0.9" height="15.0" fill="rgb(243,143,0)" rx="2" ry="2" />
<text text-anchor="" x="908.54" y="987.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>menu_select (2 samples, 0.15%)</title><rect x="957.6" y="1073" width="1.8" height="15.0" fill="rgb(229,129,0)" rx="2" ry="2" />
<text text-anchor="" x="960.59" y="1083.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/BaseFunction:.construct (1 samples, 0.08%)</title><rect x="239.7" y="801" width="0.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="242.72" y="811.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>process_backlog (97 samples, 7.38%)</title><rect x="762.0" y="417" width="87.0" height="15.0" fill="rgb(242,142,0)" rx="2" ry="2" />
<text text-anchor="" x="764.97" y="427.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >process_ba..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__pthread_disable_asynccancel (1 samples, 0.08%)</title><rect x="877.7" y="737" width="0.9" height="15.0" fill="rgb(233,99,99)" rx="2" ry="2" />
<text text-anchor="" x="880.73" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/handler/codec/http/HttpObjectDecoder:.decode (57 samples, 4.33%)</title><rect x="617.5" y="913" width="51.1" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="620.50" y="923.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >io/ne..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>schedule_preempt_disabled (4 samples, 0.30%)</title><rect x="937.8" y="1153" width="3.6" height="15.0" fill="rgb(236,136,0)" rx="2" ry="2" />
<text text-anchor="" x="940.85" y="1163.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>rcu_idle_exit (2 samples, 0.15%)</title><rect x="961.2" y="1105" width="1.8" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="964.18" y="1115.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>tcp_send_mss (1 samples, 0.08%)</title><rect x="1174.7" y="977" width="0.9" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="1177.75" y="987.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>[unknown] (26 samples, 1.98%)</title><rect x="1042.8" y="1105" width="23.4" height="15.0" fill="rgb(243,112,112)" rx="2" ry="2" />
<text text-anchor="" x="1045.84" y="1115.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >[..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptRuntime:.nameOrFunction (1 samples, 0.08%)</title><rect x="412.9" y="769" width="0.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="415.90" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>inet_sendmsg (78 samples, 5.93%)</title><rect x="1105.7" y="1009" width="69.9" height="15.0" fill="rgb(239,139,0)" rx="2" ry="2" />
<text text-anchor="" x="1108.65" y="1019.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >inet_se..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__getnstimeofday (1 samples, 0.08%)</title><rect x="849.0" y="529" width="0.9" height="15.0" fill="rgb(227,127,0)" rx="2" ry="2" />
<text text-anchor="" x="852.01" y="539.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>kfree_skbmem (1 samples, 0.08%)</title><rect x="806.8" y="225" width="0.9" height="15.0" fill="rgb(229,129,0)" rx="2" ry="2" />
<text text-anchor="" x="809.84" y="235.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>smp_apic_timer_interrupt (1 samples, 0.08%)</title><rect x="1038.3" y="1105" width="0.9" height="15.0" fill="rgb(225,125,0)" rx="2" ry="2" />
<text text-anchor="" x="1041.35" y="1115.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>kmalloc_slab (2 samples, 0.15%)</title><rect x="862.5" y="561" width="1.8" height="15.0" fill="rgb(223,123,0)" rx="2" ry="2" />
<text text-anchor="" x="865.47" y="571.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>[unknown] (1 samples, 0.08%)</title><rect x="1084.1" y="1073" width="0.9" height="15.0" fill="rgb(243,112,112)" rx="2" ry="2" />
<text text-anchor="" x="1087.11" y="1083.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/handler/codec/http/HttpObjectDecoder:.readHeaders (2 samples, 0.15%)</title><rect x="668.6" y="913" width="1.8" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="671.65" y="923.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>ip_rcv_finish (32 samples, 2.43%)</title><rect x="1135.3" y="721" width="28.7" height="15.0" fill="rgb(230,130,0)" rx="2" ry="2" />
<text text-anchor="" x="1138.26" y="731.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >ip..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/channel/DefaultChannelPipeline$HeadContext:.read (1 samples, 0.08%)</title><rect x="892.1" y="865" width="0.9" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="895.08" y="875.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptableObject:.putImpl (8 samples, 0.61%)</title><rect x="312.4" y="705" width="7.2" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="315.40" y="715.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>inet_ehashfn (1 samples, 0.08%)</title><rect x="1142.4" y="641" width="0.9" height="15.0" fill="rgb(239,139,0)" rx="2" ry="2" />
<text text-anchor="" x="1145.44" y="651.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptableObject:.getSlot (1 samples, 0.08%)</title><rect x="271.1" y="705" width="0.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="274.13" y="715.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptableObject:.createSlot (33 samples, 2.51%)</title><rect x="561.0" y="721" width="29.6" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="563.97" y="731.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >or..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>frame::oops_do_internal (1 samples, 0.08%)</title><rect x="43.2" y="1105" width="0.9" height="15.0" fill="rgb(220,220,66)" rx="2" ry="2" />
<text text-anchor="" x="46.20" y="1115.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>thread_entry (956 samples, 72.70%)</title><rect x="45.0" y="1121" width="857.9" height="15.0" fill="rgb(232,97,97)" rx="2" ry="2" />
<text text-anchor="" x="48.00" y="1131.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >thread_entry</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>sun/nio/ch/SelectorImpl:.select (7 samples, 0.53%)</title><rect x="896.6" y="977" width="6.3" height="15.0" fill="rgb(88,235,88)" rx="2" ry="2" />
<text text-anchor="" x="899.57" y="987.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>_raw_spin_lock_irq (1 samples, 0.08%)</title><rect x="1088.6" y="1025" width="0.9" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="1091.60" y="1035.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>ttwu_do_activate.constprop.74 (12 samples, 0.91%)</title><rect x="828.4" y="113" width="10.7" height="15.0" fill="rgb(222,122,0)" rx="2" ry="2" />
<text text-anchor="" x="831.37" y="123.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>skb_copy_datagram_iovec (1 samples, 0.08%)</title><rect x="1056.3" y="961" width="0.9" height="15.0" fill="rgb(230,130,0)" rx="2" ry="2" />
<text text-anchor="" x="1059.30" y="971.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>Interpreter (956 samples, 72.70%)</title><rect x="45.0" y="1025" width="857.9" height="15.0" fill="rgb(243,112,112)" rx="2" ry="2" />
<text text-anchor="" x="48.00" y="1035.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >Interpreter</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/handler/codec/http/HttpObjectDecoder:.readHeaders (22 samples, 1.67%)</title><rect x="638.1" y="897" width="19.8" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="641.14" y="907.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived (540 samples, 41.06%)</title><rect x="130.2" y="881" width="484.6" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="133.24" y="891.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doM..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>_raw_spin_unlock_irqrestore (1 samples, 0.08%)</title><rect x="1116.4" y="881" width="0.9" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="1119.42" y="891.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>fget_light (1 samples, 0.08%)</title><rect x="1029.4" y="1089" width="0.9" height="15.0" fill="rgb(240,140,0)" rx="2" ry="2" />
<text text-anchor="" x="1032.38" y="1099.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/handler/codec/http/DefaultHttpHeaders:.contains (1 samples, 0.08%)</title><rect x="647.1" y="881" width="0.9" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="650.11" y="891.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>kfree_skbmem (1 samples, 0.08%)</title><rect x="1054.5" y="945" width="0.9" height="15.0" fill="rgb(229,129,0)" rx="2" ry="2" />
<text text-anchor="" x="1057.50" y="955.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__alloc_pages_nodemask (1 samples, 0.08%)</title><rect x="1173.0" y="881" width="0.8" height="15.0" fill="rgb(232,132,0)" rx="2" ry="2" />
<text text-anchor="" x="1175.95" y="891.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptableObject:.addKnownAbsentSlot (1 samples, 0.08%)</title><rect x="560.1" y="721" width="0.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="563.07" y="731.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>enqueue_task_fair (5 samples, 0.38%)</title><rect x="829.3" y="65" width="4.5" height="15.0" fill="rgb(248,148,0)" rx="2" ry="2" />
<text text-anchor="" x="832.27" y="75.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>perf_pmu_enable (4 samples, 0.30%)</title><rect x="914.5" y="1009" width="3.6" height="15.0" fill="rgb(242,142,0)" rx="2" ry="2" />
<text text-anchor="" x="917.52" y="1019.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>tcp_rcv_established (73 samples, 5.55%)</title><rect x="782.6" y="273" width="65.5" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="785.61" y="283.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >tcp_rcv..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/NativeJavaObject:.initMembers (1 samples, 0.08%)</title><rect x="145.5" y="849" width="0.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="148.50" y="859.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>vfs_write (192 samples, 14.60%)</title><rect x="704.5" y="689" width="172.3" height="15.0" fill="rgb(226,126,0)" rx="2" ry="2" />
<text text-anchor="" x="707.54" y="699.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >vfs_write</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>fdval (1 samples, 0.08%)</title><rect x="98.8" y="881" width="0.9" height="15.0" fill="rgb(242,111,111)" rx="2" ry="2" />
<text text-anchor="" x="101.84" y="891.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>ip_queue_xmit (122 samples, 9.28%)</title><rect x="739.5" y="561" width="109.5" height="15.0" fill="rgb(230,130,0)" rx="2" ry="2" />
<text text-anchor="" x="742.54" y="571.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >ip_queue_xmit</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>sock_aio_write (82 samples, 6.24%)</title><rect x="1103.9" y="1025" width="73.5" height="15.0" fill="rgb(239,139,0)" rx="2" ry="2" />
<text text-anchor="" x="1106.86" y="1035.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >sock_aio..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/gen/file__root_vert_x_2_1_5_sys_mods_io_vertx_lang_js_1_1_0_vertx_streams_j (1 samples, 0.08%)</title><rect x="389.6" y="737" width="0.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="392.57" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>aeMain (236 samples, 17.95%)</title><rect x="977.3" y="1153" width="211.8" height="15.0" fill="rgb(235,101,101)" rx="2" ry="2" />
<text text-anchor="" x="980.33" y="1163.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >aeMain</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/channel/ChannelDuplexHandler:.read (3 samples, 0.23%)</title><rect x="891.2" y="929" width="2.7" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="894.19" y="939.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/vertx/java/core/http/impl/AssembledFullHttpResponse:.toLastContent (2 samples, 0.15%)</title><rect x="175.1" y="769" width="1.8" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="178.11" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>internal_add_timer (1 samples, 0.08%)</title><rect x="787.1" y="193" width="0.9" height="15.0" fill="rgb(236,136,0)" rx="2" ry="2" />
<text text-anchor="" x="790.10" y="203.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptableObject:.getSlot (1 samples, 0.08%)</title><rect x="442.5" y="753" width="0.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="445.52" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>vtable stub (2 samples, 0.15%)</title><rect x="165.2" y="785" width="1.8" height="15.0" fill="rgb(231,96,96)" rx="2" ry="2" />
<text text-anchor="" x="168.24" y="795.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptableObject:.getParentScope (1 samples, 0.08%)</title><rect x="307.0" y="705" width="0.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="310.02" y="715.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/buffer/PooledByteBufAllocator:.newDirectBuffer (2 samples, 0.15%)</title><rect x="219.1" y="657" width="1.8" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="222.08" y="667.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>SpinPause (1 samples, 0.08%)</title><rect x="41.4" y="1121" width="0.9" height="15.0" fill="rgb(239,108,108)" rx="2" ry="2" />
<text text-anchor="" x="44.41" y="1131.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/IdScriptableObject:.has (3 samples, 0.23%)</title><rect x="433.5" y="785" width="2.7" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="436.54" y="795.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>java/nio/charset/CharsetEncoder:.replaceWith (2 samples, 0.15%)</title><rect x="196.6" y="721" width="1.8" height="15.0" fill="rgb(77,224,77)" rx="2" ry="2" />
<text text-anchor="" x="199.65" y="731.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>tcp_queue_rcv (2 samples, 0.15%)</title><rect x="1162.2" y="609" width="1.8" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="1165.18" y="619.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>stats_record (3 samples, 0.23%)</title><rect x="1090.4" y="1073" width="2.7" height="15.0" fill="rgb(237,104,104)" rx="2" ry="2" />
<text text-anchor="" x="1093.40" y="1083.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/WrapFactory:.wrap (5 samples, 0.38%)</title><rect x="484.7" y="769" width="4.5" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="487.69" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__wake_up_sync_key (27 samples, 2.05%)</title><rect x="818.5" y="225" width="24.2" height="15.0" fill="rgb(223,123,0)" rx="2" ry="2" />
<text text-anchor="" x="821.50" y="235.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >_..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__acct_update_integrals (1 samples, 0.08%)</title><rect x="1038.3" y="945" width="0.9" height="15.0" fill="rgb(232,132,0)" rx="2" ry="2" />
<text text-anchor="" x="1041.35" y="955.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>fget_light (1 samples, 0.08%)</title><rect x="1182.8" y="1057" width="0.9" height="15.0" fill="rgb(240,140,0)" rx="2" ry="2" />
<text text-anchor="" x="1185.82" y="1067.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>local_bh_enable (1 samples, 0.08%)</title><rect x="1166.7" y="961" width="0.9" height="15.0" fill="rgb(232,132,0)" rx="2" ry="2" />
<text text-anchor="" x="1169.67" y="971.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>eth_type_trans (1 samples, 0.08%)</title><rect x="749.4" y="433" width="0.9" height="15.0" fill="rgb(236,136,0)" rx="2" ry="2" />
<text text-anchor="" x="752.41" y="443.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/vertx/java/core/net/impl/VertxHandler:.channelRead (555 samples, 42.21%)</title><rect x="117.7" y="897" width="498.0" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="120.68" y="907.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >org/vertx/java/core/net/impl/VertxHandler:.channelRead</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptableObject:.getSlot (1 samples, 0.08%)</title><rect x="412.9" y="753" width="0.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="415.90" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>sock_put (1 samples, 0.08%)</title><rect x="769.1" y="305" width="0.9" height="15.0" fill="rgb(239,139,0)" rx="2" ry="2" />
<text text-anchor="" x="772.15" y="315.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__kfree_skb (3 samples, 0.23%)</title><rect x="803.2" y="225" width="2.7" height="15.0" fill="rgb(223,123,0)" rx="2" ry="2" />
<text text-anchor="" x="806.25" y="235.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>dequeue_task (7 samples, 0.53%)</title><rect x="1015.0" y="993" width="6.3" height="15.0" fill="rgb(247,147,0)" rx="2" ry="2" />
<text text-anchor="" x="1018.02" y="1003.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/channel/AbstractChannelHandlerContext:.executor (1 samples, 0.08%)</title><rect x="110.5" y="913" width="0.9" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="113.50" y="923.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>ip_rcv_finish (1 samples, 0.08%)</title><rect x="848.1" y="369" width="0.9" height="15.0" fill="rgb(230,130,0)" rx="2" ry="2" />
<text text-anchor="" x="851.11" y="379.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptRuntime:.getObjectProp (4 samples, 0.30%)</title><rect x="497.3" y="785" width="3.5" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="500.25" y="795.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__tick_nohz_idle_enter (4 samples, 0.30%)</title><rect x="964.8" y="1089" width="3.6" height="15.0" fill="rgb(226,126,0)" rx="2" ry="2" />
<text text-anchor="" x="967.77" y="1099.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__tcp_ack_snd_check (3 samples, 0.23%)</title><rect x="1144.2" y="625" width="2.7" height="15.0" fill="rgb(226,126,0)" rx="2" ry="2" />
<text text-anchor="" x="1147.24" y="635.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>[unknown] (4 samples, 0.30%)</title><rect x="898.4" y="929" width="3.6" height="15.0" fill="rgb(243,112,112)" rx="2" ry="2" />
<text text-anchor="" x="901.37" y="939.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/IdScriptableObject:.get (1 samples, 0.08%)</title><rect x="335.7" y="721" width="0.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="338.73" y="731.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/NativeFunction:.initScriptFunction (2 samples, 0.15%)</title><rect x="305.2" y="705" width="1.8" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="308.22" y="715.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>int_sqrt (1 samples, 0.08%)</title><rect x="958.5" y="1057" width="0.9" height="15.0" fill="rgb(236,136,0)" rx="2" ry="2" />
<text text-anchor="" x="961.49" y="1067.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>nmethod::fix_oop_relocations (1 samples, 0.08%)</title><rect x="44.1" y="1105" width="0.9" height="15.0" fill="rgb(214,214,64)" rx="2" ry="2" />
<text text-anchor="" x="47.10" y="1115.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>tcp_sendmsg (1 samples, 0.08%)</title><rect x="875.0" y="641" width="0.9" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="878.03" y="651.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>java/lang/ThreadLocal:.get (1 samples, 0.08%)</title><rect x="220.0" y="641" width="0.9" height="15.0" fill="rgb(77,224,77)" rx="2" ry="2" />
<text text-anchor="" x="222.98" y="651.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>java/lang/String:.hashCode (1 samples, 0.08%)</title><rect x="498.2" y="737" width="0.8" height="15.0" fill="rgb(77,224,77)" rx="2" ry="2" />
<text text-anchor="" x="501.15" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/BaseFunction:.findPrototypeId (1 samples, 0.08%)</title><rect x="333.9" y="705" width="0.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="336.94" y="715.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>native_write_msr_safe (3 samples, 0.23%)</title><rect x="970.2" y="993" width="2.6" height="15.0" fill="rgb(234,134,0)" rx="2" ry="2" />
<text text-anchor="" x="973.15" y="1003.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>perf_pmu_enable (4 samples, 0.30%)</title><rect x="1128.1" y="737" width="3.6" height="15.0" fill="rgb(242,142,0)" rx="2" ry="2" />
<text text-anchor="" x="1131.08" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/gen/file__root_vert_x_2_1_5_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_2 (77 samples, 5.86%)</title><rect x="169.7" y="801" width="69.1" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="172.73" y="811.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >org/moz..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__netif_receive_skb_core (94 samples, 7.15%)</title><rect x="764.7" y="385" width="84.3" height="15.0" fill="rgb(231,131,0)" rx="2" ry="2" />
<text text-anchor="" x="767.66" y="395.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >__netif_r..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>java/lang/String:.hashCode (1 samples, 0.08%)</title><rect x="409.3" y="721" width="0.9" height="15.0" fill="rgb(77,224,77)" rx="2" ry="2" />
<text text-anchor="" x="412.32" y="731.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>hrtimer_start (2 samples, 0.15%)</title><rect x="945.0" y="1105" width="1.8" height="15.0" fill="rgb(232,132,0)" rx="2" ry="2" />
<text text-anchor="" x="948.03" y="1115.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptRuntime:.setName (5 samples, 0.38%)</title><rect x="509.8" y="785" width="4.5" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="512.82" y="795.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__netif_receive_skb (94 samples, 7.15%)</title><rect x="764.7" y="401" width="84.3" height="15.0" fill="rgb(231,131,0)" rx="2" ry="2" />
<text text-anchor="" x="767.66" y="411.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >__netif_r..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>change_protection (1 samples, 0.08%)</title><rect x="902.9" y="1041" width="0.8" height="15.0" fill="rgb(226,126,0)" rx="2" ry="2" />
<text text-anchor="" x="905.85" y="1051.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/channel/ChannelOutboundBuffer:.current (1 samples, 0.08%)</title><rect x="675.8" y="785" width="0.9" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="678.83" y="795.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/channel/AbstractChannelHandlerContext:.flush (233 samples, 17.72%)</title><rect x="674.9" y="817" width="209.1" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="677.93" y="827.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >io/netty/channel/AbstractCh..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>do_softirq_own_stack (103 samples, 7.83%)</title><rect x="756.6" y="465" width="92.4" height="15.0" fill="rgb(243,143,0)" rx="2" ry="2" />
<text text-anchor="" x="759.59" y="475.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >do_softirq_..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>do_filp_open (1 samples, 0.08%)</title><rect x="904.6" y="1025" width="0.9" height="15.0" fill="rgb(243,143,0)" rx="2" ry="2" />
<text text-anchor="" x="907.65" y="1035.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>x86_pmu_commit_txn (4 samples, 0.30%)</title><rect x="906.4" y="929" width="3.6" height="15.0" fill="rgb(248,148,0)" rx="2" ry="2" />
<text text-anchor="" x="909.44" y="939.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptableObject:.getSlot (1 samples, 0.08%)</title><rect x="590.6" y="769" width="0.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="593.58" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/util/Recycler:.get (1 samples, 0.08%)</title><rect x="234.3" y="689" width="0.9" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="237.33" y="699.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>_raw_spin_lock (1 samples, 0.08%)</title><rect x="944.1" y="1089" width="0.9" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="947.13" y="1099.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>sun/nio/ch/SocketChannelImpl:.isConnected (1 samples, 0.08%)</title><rect x="893.0" y="865" width="0.9" height="15.0" fill="rgb(88,235,88)" rx="2" ry="2" />
<text text-anchor="" x="895.98" y="875.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>change_protection_range (1 samples, 0.08%)</title><rect x="902.9" y="1025" width="0.8" height="15.0" fill="rgb(226,126,0)" rx="2" ry="2" />
<text text-anchor="" x="905.85" y="1035.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptableObject:.putImpl (12 samples, 0.91%)</title><rect x="288.2" y="689" width="10.7" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="291.17" y="699.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__libc_write (1 samples, 0.08%)</title><rect x="876.8" y="737" width="0.9" height="15.0" fill="rgb(236,103,103)" rx="2" ry="2" />
<text text-anchor="" x="879.83" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/gen/file__root_vert_x_2_1_5_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_2 (513 samples, 39.01%)</title><rect x="152.7" y="849" width="460.3" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="155.68" y="859.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >org/mozilla/javascript/gen/file__root_vert_x_2_1_5_sys_mods_io_..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>raw_local_deliver (1 samples, 0.08%)</title><rect x="768.3" y="305" width="0.8" height="15.0" fill="rgb(223,123,0)" rx="2" ry="2" />
<text text-anchor="" x="771.25" y="315.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>apparmor_file_permission (1 samples, 0.08%)</title><rect x="1063.5" y="1009" width="0.9" height="15.0" fill="rgb(232,132,0)" rx="2" ry="2" />
<text text-anchor="" x="1066.48" y="1019.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>VMThread::loop (1 samples, 0.08%)</title><rect x="902.9" y="1137" width="0.8" height="15.0" fill="rgb(218,218,65)" rx="2" ry="2" />
<text text-anchor="" x="905.85" y="1147.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>_raw_spin_unlock_irqrestore (1 samples, 0.08%)</title><rect x="969.3" y="1041" width="0.9" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="972.25" y="1051.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/channel/AbstractChannelHandlerContext:.newPromise (1 samples, 0.08%)</title><rect x="189.5" y="753" width="0.9" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="192.47" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>epoll_ctl (7 samples, 0.53%)</title><rect x="1179.2" y="1105" width="6.3" height="15.0" fill="rgb(248,120,120)" rx="2" ry="2" />
<text text-anchor="" x="1182.23" y="1115.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/handler/codec/http/HttpVersion:.compareTo (1 samples, 0.08%)</title><rect x="126.7" y="881" width="0.9" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="129.65" y="891.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/channel/nio/NioEventLoop:.processSelectedKeys (949 samples, 72.17%)</title><rect x="45.0" y="993" width="851.6" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="48.00" y="1003.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >io/netty/channel/nio/NioEventLoop:.processSelectedKeys</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/handler/codec/http/DefaultHttpHeaders:.init (1 samples, 0.08%)</title><rect x="624.7" y="897" width="0.9" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="627.68" y="907.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>java/lang/String:.hashCode (2 samples, 0.15%)</title><rect x="552.9" y="737" width="1.8" height="15.0" fill="rgb(77,224,77)" rx="2" ry="2" />
<text text-anchor="" x="555.89" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/IdScriptableObject:.findInstanceIdInfo (1 samples, 0.08%)</title><rect x="285.5" y="689" width="0.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="288.48" y="699.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>effective_load.isra.35 (2 samples, 0.15%)</title><rect x="824.8" y="97" width="1.8" height="15.0" fill="rgb(243,143,0)" rx="2" ry="2" />
<text text-anchor="" x="827.78" y="107.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptableObject:.getSlot (2 samples, 0.15%)</title><rect x="286.4" y="689" width="1.8" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="289.38" y="699.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>rcu_sysidle_exit (1 samples, 0.08%)</title><rect x="937.0" y="1137" width="0.8" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="939.95" y="1147.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>native_load_tls (1 samples, 0.08%)</title><rect x="910.0" y="1185" width="0.9" height="15.0" fill="rgb(234,134,0)" rx="2" ry="2" />
<text text-anchor="" x="913.03" y="1195.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/IdScriptableObject:.get (1 samples, 0.08%)</title><rect x="310.6" y="721" width="0.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="313.61" y="731.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/BaseFunction:.findPrototypeId (1 samples, 0.08%)</title><rect x="502.6" y="753" width="0.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="505.64" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/gen/file__root_vert_x_2_1_5_sys_mods_io_vertx_lang_js_1_1_0_vertx_streams_j (6 samples, 0.46%)</title><rect x="302.5" y="721" width="5.4" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="305.53" y="731.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>system_call_fastpath (22 samples, 1.67%)</title><rect x="1046.4" y="1089" width="19.8" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="1049.43" y="1099.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/TopLevel:.getBuiltinPrototype (1 samples, 0.08%)</title><rect x="410.2" y="753" width="0.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="413.21" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>tcp_current_mss (5 samples, 0.38%)</title><rect x="869.7" y="593" width="4.4" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="872.65" y="603.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/channel/ChannelOutboundBuffer:.incrementPendingOutboundBytes (1 samples, 0.08%)</title><rect x="211.9" y="641" width="0.9" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="214.90" y="651.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>oopDesc* PSPromotionManager::copy_to_survivor_spacefalse (2 samples, 0.15%)</title><rect x="36.9" y="1089" width="1.8" height="15.0" fill="rgb(223,223,67)" rx="2" ry="2" />
<text text-anchor="" x="39.92" y="1099.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/gen/file__root_vert_x_2_1_5_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_2 (156 samples, 11.86%)</title><rect x="250.5" y="769" width="140.0" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="253.49" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >org/mozilla/javas..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>StealTask::do_it (3 samples, 0.23%)</title><rect x="39.6" y="1137" width="2.7" height="15.0" fill="rgb(213,213,63)" rx="2" ry="2" />
<text text-anchor="" x="42.61" y="1147.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>Interpreter (956 samples, 72.70%)</title><rect x="45.0" y="1009" width="857.9" height="15.0" fill="rgb(243,112,112)" rx="2" ry="2" />
<text text-anchor="" x="48.00" y="1019.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >Interpreter</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>PSPromotionManager::drain_stacks_depth (2 samples, 0.15%)</title><rect x="36.9" y="1105" width="1.8" height="15.0" fill="rgb(216,216,65)" rx="2" ry="2" />
<text text-anchor="" x="39.92" y="1115.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>sock_def_readable (32 samples, 2.43%)</title><rect x="814.9" y="241" width="28.7" height="15.0" fill="rgb(239,139,0)" rx="2" ry="2" />
<text text-anchor="" x="817.91" y="251.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >so..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/IdScriptableObject:.findInstanceIdInfo (1 samples, 0.08%)</title><rect x="421.9" y="737" width="0.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="424.88" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>perf (6 samples, 0.46%)</title><rect x="904.6" y="1201" width="5.4" height="15.0" fill="rgb(244,114,114)" rx="2" ry="2" />
<text text-anchor="" x="907.65" y="1211.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__wake_up_common (27 samples, 2.05%)</title><rect x="818.5" y="209" width="24.2" height="15.0" fill="rgb(223,123,0)" rx="2" ry="2" />
<text text-anchor="" x="821.50" y="219.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >_..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/IdScriptableObject:.has (1 samples, 0.08%)</title><rect x="338.4" y="721" width="0.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="341.43" y="731.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/handler/codec/http/HttpObjectDecoder$HeaderParser:.process (1 samples, 0.08%)</title><rect x="649.8" y="881" width="0.9" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="652.80" y="891.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>common_file_perm (1 samples, 0.08%)</title><rect x="1063.5" y="993" width="0.9" height="15.0" fill="rgb(232,132,0)" rx="2" ry="2" />
<text text-anchor="" x="1066.48" y="1003.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/buffer/AbstractReferenceCountedByteBuf:.release (5 samples, 0.38%)</title><rect x="682.1" y="769" width="4.5" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="685.11" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>hrtimer_force_reprogram (3 samples, 0.23%)</title><rect x="947.7" y="1073" width="2.7" height="15.0" fill="rgb(232,132,0)" rx="2" ry="2" />
<text text-anchor="" x="950.72" y="1083.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/vertx/java/core/impl/DefaultVertx:.setContext (1 samples, 0.08%)</title><rect x="614.8" y="881" width="0.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="617.81" y="891.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>msecs_to_jiffies (1 samples, 0.08%)</title><rect x="727.0" y="577" width="0.9" height="15.0" fill="rgb(222,122,0)" rx="2" ry="2" />
<text text-anchor="" x="729.97" y="587.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>arch_cpu_idle (7 samples, 0.53%)</title><rect x="954.0" y="1105" width="6.3" height="15.0" fill="rgb(230,130,0)" rx="2" ry="2" />
<text text-anchor="" x="957.00" y="1115.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>[unknown] (91 samples, 6.92%)</title><rect x="1097.6" y="1105" width="81.6" height="15.0" fill="rgb(243,112,112)" rx="2" ry="2" />
<text text-anchor="" x="1100.57" y="1115.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >[unknown]</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>tcp_cleanup_rbuf (1 samples, 0.08%)</title><rect x="1057.2" y="961" width="0.9" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="1060.19" y="971.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>release_sock (1 samples, 0.08%)</title><rect x="90.8" y="737" width="0.9" height="15.0" fill="rgb(243,143,0)" rx="2" ry="2" />
<text text-anchor="" x="93.76" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/channel/AbstractChannelHandlerContext:.flush (235 samples, 17.87%)</title><rect x="674.9" y="849" width="210.9" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="677.93" y="859.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >io/netty/channel/AbstractCh..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete (1 samples, 0.08%)</title><rect x="51.3" y="961" width="0.9" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="54.28" y="971.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>fput (2 samples, 0.15%)</title><rect x="1030.3" y="1089" width="1.8" height="15.0" fill="rgb(239,139,0)" rx="2" ry="2" />
<text text-anchor="" x="1033.27" y="1099.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>bictcp_acked (1 samples, 0.08%)</title><rect x="805.9" y="225" width="0.9" height="15.0" fill="rgb(225,125,0)" rx="2" ry="2" />
<text text-anchor="" x="808.94" y="235.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>java/nio/DirectByteBuffer:.duplicate (1 samples, 0.08%)</title><rect x="895.7" y="945" width="0.9" height="15.0" fill="rgb(77,224,77)" rx="2" ry="2" />
<text text-anchor="" x="898.67" y="955.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/buffer/AbstractByteBuf:.checkIndex (3 samples, 0.23%)</title><rect x="182.3" y="721" width="2.7" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="185.29" y="731.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>java/util/HashMap:.getNode (2 samples, 0.15%)</title><rect x="487.4" y="737" width="1.8" height="15.0" fill="rgb(77,224,77)" rx="2" ry="2" />
<text text-anchor="" x="490.38" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>ttwu_stat (1 samples, 0.08%)</title><rect x="840.0" y="113" width="0.9" height="15.0" fill="rgb(222,122,0)" rx="2" ry="2" />
<text text-anchor="" x="843.04" y="123.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/handler/codec/http/HttpHeaders:.hash (1 samples, 0.08%)</title><rect x="194.9" y="737" width="0.8" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="197.85" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/vertx/java/core/net/impl/ConnectionBase:.write (38 samples, 2.89%)</title><rect x="202.0" y="753" width="34.1" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="205.03" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >or..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>local_apic_timer_interrupt (1 samples, 0.08%)</title><rect x="1038.3" y="1089" width="0.9" height="15.0" fill="rgb(232,132,0)" rx="2" ry="2" />
<text text-anchor="" x="1041.35" y="1099.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>inet_ehashfn (1 samples, 0.08%)</title><rect x="776.3" y="289" width="0.9" height="15.0" fill="rgb(239,139,0)" rx="2" ry="2" />
<text text-anchor="" x="779.33" y="299.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__srcu_read_unlock (1 samples, 0.08%)</title><rect x="1103.0" y="1041" width="0.9" height="15.0" fill="rgb(227,127,0)" rx="2" ry="2" />
<text text-anchor="" x="1105.96" y="1051.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>java/nio/channels/spi/AbstractInterruptibleChannel:.begin (1 samples, 0.08%)</title><rect x="67.4" y="913" width="0.9" height="15.0" fill="rgb(77,224,77)" rx="2" ry="2" />
<text text-anchor="" x="70.43" y="923.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>skb_clone (1 samples, 0.08%)</title><rect x="1165.8" y="929" width="0.9" height="15.0" fill="rgb(230,130,0)" rx="2" ry="2" />
<text text-anchor="" x="1168.77" y="939.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead (562 samples, 42.74%)</title><rect x="111.4" y="913" width="504.3" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="114.40" y="923.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>ip_local_deliver (89 samples, 6.77%)</title><rect x="768.3" y="337" width="79.8" height="15.0" fill="rgb(230,130,0)" rx="2" ry="2" />
<text text-anchor="" x="771.25" y="347.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >ip_local_..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptableObject:.createSlot (8 samples, 0.61%)</title><rect x="424.6" y="705" width="7.1" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="427.57" y="715.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>itable stub (1 samples, 0.08%)</title><rect x="249.6" y="785" width="0.9" height="15.0" fill="rgb(237,104,104)" rx="2" ry="2" />
<text text-anchor="" x="252.59" y="795.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptableObject:.getSlot (1 samples, 0.08%)</title><rect x="259.5" y="689" width="0.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="262.46" y="699.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__do_softirq (36 samples, 2.74%)</title><rect x="1132.6" y="817" width="32.3" height="15.0" fill="rgb(230,130,0)" rx="2" ry="2" />
<text text-anchor="" x="1135.57" y="827.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >__..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/handler/codec/http/HttpMethod:.valueOf (2 samples, 0.15%)</title><rect x="615.7" y="913" width="1.8" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="618.70" y="923.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>clockevents_program_event (3 samples, 0.23%)</title><rect x="970.2" y="1025" width="2.6" height="15.0" fill="rgb(219,119,0)" rx="2" ry="2" />
<text text-anchor="" x="973.15" y="1035.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>tcp_set_skb_tso_segs (1 samples, 0.08%)</title><rect x="734.2" y="561" width="0.8" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="737.15" y="571.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/buffer/AbstractByteBuf:.checkSrcIndex (1 samples, 0.08%)</title><rect x="179.6" y="753" width="0.9" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="182.60" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/handler/codec/http/HttpVersion:.compareTo (2 samples, 0.15%)</title><rect x="138.3" y="865" width="1.8" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="141.32" y="875.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>ttwu_do_activate.constprop.74 (1 samples, 0.08%)</title><rect x="840.9" y="129" width="0.9" height="15.0" fill="rgb(222,122,0)" rx="2" ry="2" />
<text text-anchor="" x="843.94" y="139.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptableObject$Slot:.setAttributes (6 samples, 0.46%)</title><rect x="319.6" y="705" width="5.4" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="322.58" y="715.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>cpuidle_idle_call (21 samples, 1.60%)</title><rect x="914.5" y="1137" width="18.9" height="15.0" fill="rgb(223,123,0)" rx="2" ry="2" />
<text text-anchor="" x="917.52" y="1147.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__hrtimer_start_range_ns (2 samples, 0.15%)</title><rect x="945.0" y="1089" width="1.8" height="15.0" fill="rgb(226,126,0)" rx="2" ry="2" />
<text text-anchor="" x="948.03" y="1099.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/channel/ChannelOutboundHandlerAdapter:.read (2 samples, 0.15%)</title><rect x="892.1" y="897" width="1.8" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="895.08" y="907.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptRuntime:.bind (7 samples, 0.53%)</title><rect x="491.0" y="785" width="6.3" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="493.97" y="795.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>x86_pmu_enable (4 samples, 0.30%)</title><rect x="711.7" y="513" width="3.6" height="15.0" fill="rgb(248,148,0)" rx="2" ry="2" />
<text text-anchor="" x="714.72" y="523.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptableObject:.getSlot (1 samples, 0.08%)</title><rect x="496.4" y="753" width="0.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="499.36" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>sun/nio/ch/EPollArrayWrapper:.poll (5 samples, 0.38%)</title><rect x="897.5" y="961" width="4.5" height="15.0" fill="rgb(88,235,88)" rx="2" ry="2" />
<text text-anchor="" x="900.47" y="971.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/IdScriptableObject:.setAttributes (12 samples, 0.91%)</title><rect x="467.6" y="769" width="10.8" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="470.64" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>sock_read (3 samples, 0.23%)</title><rect x="1094.0" y="1105" width="2.7" height="15.0" fill="rgb(241,110,110)" rx="2" ry="2" />
<text text-anchor="" x="1096.98" y="1115.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>HandleArea::oops_do (1 samples, 0.08%)</title><rect x="42.3" y="1105" width="0.9" height="15.0" fill="rgb(213,213,64)" rx="2" ry="2" />
<text text-anchor="" x="45.30" y="1115.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>tcp_v4_send_check (1 samples, 0.08%)</title><rect x="855.3" y="561" width="0.9" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="858.29" y="571.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>tcp_wfree (1 samples, 0.08%)</title><rect x="1127.2" y="817" width="0.9" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="1130.19" y="827.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptableObject$Slot:.getValue (1 samples, 0.08%)</title><rect x="337.5" y="721" width="0.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="340.53" y="731.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>sun/nio/ch/FileDispatcherImpl:.read0 (1 samples, 0.08%)</title><rect x="71.0" y="913" width="0.9" height="15.0" fill="rgb(88,235,88)" rx="2" ry="2" />
<text text-anchor="" x="74.02" y="923.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete (240 samples, 18.25%)</title><rect x="674.0" y="897" width="215.4" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="677.03" y="907.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >org/vertx/java/core/net/impl..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>rcu_bh_qs (1 samples, 0.08%)</title><rect x="1164.9" y="817" width="0.9" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="1167.87" y="827.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>lock_timer_base.isra.35 (1 samples, 0.08%)</title><rect x="1155.0" y="561" width="0.9" height="15.0" fill="rgb(232,132,0)" rx="2" ry="2" />
<text text-anchor="" x="1158.00" y="571.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/buffer/PooledUnsafeDirectByteBuf:.setBytes (42 samples, 3.19%)</title><rect x="62.9" y="945" width="37.7" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="65.94" y="955.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >io/..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>sun/nio/cs/UTF_8$Encoder:.init (3 samples, 0.23%)</title><rect x="196.6" y="737" width="2.7" height="15.0" fill="rgb(88,235,88)" rx="2" ry="2" />
<text text-anchor="" x="199.65" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/channel/ChannelDuplexHandler:.read (1 samples, 0.08%)</title><rect x="893.9" y="945" width="0.9" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="896.88" y="955.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>java/lang/String:.hashCode (1 samples, 0.08%)</title><rect x="547.5" y="737" width="0.9" height="15.0" fill="rgb(77,224,77)" rx="2" ry="2" />
<text text-anchor="" x="550.51" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>account_entity_dequeue (1 samples, 0.08%)</title><rect x="1015.0" y="961" width="0.9" height="15.0" fill="rgb(236,136,0)" rx="2" ry="2" />
<text text-anchor="" x="1018.02" y="971.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/channel/AbstractChannelHandlerContext:.executor (1 samples, 0.08%)</title><rect x="891.2" y="897" width="0.9" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="894.19" y="907.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptableObject:.getSlot (17 samples, 1.29%)</title><rect x="452.4" y="737" width="15.2" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="455.39" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/handler/codec/http/HttpObjectEncoder:.encode (17 samples, 1.29%)</title><rect x="214.6" y="673" width="15.2" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="217.59" y="683.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptableObject:.addKnownAbsentSlot (2 samples, 0.15%)</title><rect x="588.8" y="705" width="1.8" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="591.78" y="715.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>ksize (1 samples, 0.08%)</title><rect x="867.9" y="593" width="0.9" height="15.0" fill="rgb(223,123,0)" rx="2" ry="2" />
<text text-anchor="" x="870.86" y="603.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>[unknown] (4 samples, 0.30%)</title><rect x="1034.8" y="1121" width="3.5" height="15.0" fill="rgb(243,112,112)" rx="2" ry="2" />
<text text-anchor="" x="1037.76" y="1131.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptRuntime:.toObjectOrNull (2 samples, 0.15%)</title><rect x="300.7" y="721" width="1.8" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="303.74" y="731.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>fget_light (1 samples, 0.08%)</title><rect x="1046.4" y="1073" width="0.9" height="15.0" fill="rgb(240,140,0)" rx="2" ry="2" />
<text text-anchor="" x="1049.43" y="1083.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>sched_clock_idle_sleep_event (1 samples, 0.08%)</title><rect x="941.4" y="1121" width="0.9" height="15.0" fill="rgb(236,136,0)" rx="2" ry="2" />
<text text-anchor="" x="944.44" y="1131.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>sock_aio_write (185 samples, 14.07%)</title><rect x="709.9" y="657" width="166.0" height="15.0" fill="rgb(239,139,0)" rx="2" ry="2" />
<text text-anchor="" x="712.92" y="667.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >sock_aio_write</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/gen/file__root_vert_x_2_1_5_sys_mods_io_vertx_lang_js_1_1_0_vertx_streams_j (1 samples, 0.08%)</title><rect x="19.0" y="1153" width="0.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="21.97" y="1163.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>smp_call_function_single (5 samples, 0.38%)</title><rect x="905.5" y="993" width="4.5" height="15.0" fill="rgb(225,125,0)" rx="2" ry="2" />
<text text-anchor="" x="908.54" y="1003.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/TopLevel:.getBuiltinPrototype (1 samples, 0.08%)</title><rect x="306.1" y="689" width="0.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="309.12" y="699.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>ip_rcv (91 samples, 6.92%)</title><rect x="766.5" y="369" width="81.6" height="15.0" fill="rgb(230,130,0)" rx="2" ry="2" />
<text text-anchor="" x="769.46" y="379.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >ip_rcv</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>tcp_sendmsg (176 samples, 13.38%)</title><rect x="716.2" y="625" width="157.9" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="719.21" y="635.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >tcp_sendmsg</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>release_sock (1 samples, 0.08%)</title><rect x="1167.6" y="977" width="0.9" height="15.0" fill="rgb(243,143,0)" rx="2" ry="2" />
<text text-anchor="" x="1170.57" y="987.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>ep_poll_callback (27 samples, 2.05%)</title><rect x="818.5" y="193" width="24.2" height="15.0" fill="rgb(239,139,0)" rx="2" ry="2" />
<text text-anchor="" x="821.50" y="203.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >e..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>update_min_vruntime (1 samples, 0.08%)</title><rect x="1019.5" y="961" width="0.9" height="15.0" fill="rgb(232,132,0)" rx="2" ry="2" />
<text text-anchor="" x="1022.51" y="971.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>java/lang/Integer:.toString (1 samples, 0.08%)</title><rect x="195.7" y="753" width="0.9" height="15.0" fill="rgb(77,224,77)" rx="2" ry="2" />
<text text-anchor="" x="198.75" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>itable stub (1 samples, 0.08%)</title><rect x="153.6" y="833" width="0.9" height="15.0" fill="rgb(237,104,104)" rx="2" ry="2" />
<text text-anchor="" x="156.57" y="843.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>do_softirq_own_stack (37 samples, 2.81%)</title><rect x="1132.6" y="833" width="33.2" height="15.0" fill="rgb(243,143,0)" rx="2" ry="2" />
<text text-anchor="" x="1135.57" y="843.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >do..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/buffer/AbstractByteBufAllocator:.heapBuffer (1 samples, 0.08%)</title><rect x="172.4" y="769" width="0.9" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="175.42" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptableObject:.getSlot (3 samples, 0.23%)</title><rect x="419.2" y="737" width="2.7" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="422.19" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptableObject:.addKnownAbsentSlot (1 samples, 0.08%)</title><rect x="466.7" y="705" width="0.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="469.75" y="715.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>sched_clock_cpu (1 samples, 0.08%)</title><rect x="941.4" y="1105" width="0.9" height="15.0" fill="rgb(236,136,0)" rx="2" ry="2" />
<text text-anchor="" x="944.44" y="1115.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/handler/codec/http/HttpHeaders:.encodeAscii0 (2 samples, 0.15%)</title><rect x="225.4" y="641" width="1.8" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="228.36" y="651.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptRuntime:.toObjectOrNull (1 samples, 0.08%)</title><rect x="591.5" y="785" width="0.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="594.48" y="795.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>inet_sendmsg (1 samples, 0.08%)</title><rect x="709.0" y="657" width="0.9" height="15.0" fill="rgb(239,139,0)" rx="2" ry="2" />
<text text-anchor="" x="712.03" y="667.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>VMThread::run (1 samples, 0.08%)</title><rect x="902.9" y="1153" width="0.8" height="15.0" fill="rgb(218,218,65)" rx="2" ry="2" />
<text text-anchor="" x="905.85" y="1163.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/handler/codec/http/DefaultHttpHeaders:.init (1 samples, 0.08%)</title><rect x="626.5" y="881" width="0.9" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="629.47" y="891.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>java/util/HashMap:.getNode (2 samples, 0.15%)</title><rect x="631.0" y="881" width="1.8" height="15.0" fill="rgb(77,224,77)" rx="2" ry="2" />
<text text-anchor="" x="633.96" y="891.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__run_hrtimer (1 samples, 0.08%)</title><rect x="1038.3" y="1057" width="0.9" height="15.0" fill="rgb(228,128,0)" rx="2" ry="2" />
<text text-anchor="" x="1041.35" y="1067.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>java/nio/channels/spi/AbstractInterruptibleChannel:.begin (1 samples, 0.08%)</title><rect x="694.7" y="753" width="0.9" height="15.0" fill="rgb(77,224,77)" rx="2" ry="2" />
<text text-anchor="" x="697.67" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/handler/codec/http/HttpHeaders:.hash (1 samples, 0.08%)</title><rect x="647.1" y="865" width="0.9" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="650.11" y="875.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>sun/nio/ch/EPollSelectorImpl:.updateSelectedKeys (1 samples, 0.08%)</title><rect x="902.0" y="961" width="0.9" height="15.0" fill="rgb(88,235,88)" rx="2" ry="2" />
<text text-anchor="" x="904.95" y="971.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>x86_pmu_enable (4 samples, 0.30%)</title><rect x="914.5" y="993" width="3.6" height="15.0" fill="rgb(248,148,0)" rx="2" ry="2" />
<text text-anchor="" x="917.52" y="1003.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>thread_main (237 samples, 18.02%)</title><rect x="977.3" y="1169" width="212.7" height="15.0" fill="rgb(232,97,97)" rx="2" ry="2" />
<text text-anchor="" x="980.33" y="1179.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >thread_main</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>enqueue_hrtimer (1 samples, 0.08%)</title><rect x="1006.0" y="1025" width="0.9" height="15.0" fill="rgb(248,148,0)" rx="2" ry="2" />
<text text-anchor="" x="1009.05" y="1035.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>ep_poll (4 samples, 0.30%)</title><rect x="898.4" y="881" width="3.6" height="15.0" fill="rgb(239,139,0)" rx="2" ry="2" />
<text text-anchor="" x="901.37" y="891.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>sock_aio_read (7 samples, 0.53%)</title><rect x="1051.8" y="1025" width="6.3" height="15.0" fill="rgb(239,139,0)" rx="2" ry="2" />
<text text-anchor="" x="1054.81" y="1035.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/buffer/AbstractByteBuf:.checkSrcIndex (3 samples, 0.23%)</title><rect x="182.3" y="737" width="2.7" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="185.29" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>sock_aio_read (22 samples, 1.67%)</title><rect x="76.4" y="801" width="19.7" height="15.0" fill="rgb(239,139,0)" rx="2" ry="2" />
<text text-anchor="" x="79.40" y="811.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/handler/codec/http/HttpObjectDecoder$LineParser:.parse (6 samples, 0.46%)</title><rect x="632.8" y="897" width="5.3" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="635.75" y="907.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>sys_epoll_ctl (5 samples, 0.38%)</title><rect x="1181.0" y="1073" width="4.5" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="1184.03" y="1083.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>java/lang/String:.init (4 samples, 0.30%)</title><rect x="654.3" y="849" width="3.6" height="15.0" fill="rgb(77,224,77)" rx="2" ry="2" />
<text text-anchor="" x="657.29" y="859.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>rb_erase (1 samples, 0.08%)</title><rect x="1008.7" y="993" width="0.9" height="15.0" fill="rgb(221,121,0)" rx="2" ry="2" />
<text text-anchor="" x="1011.74" y="1003.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>select_estimate_accuracy (5 samples, 0.38%)</title><rect x="1023.1" y="1073" width="4.5" height="15.0" fill="rgb(240,140,0)" rx="2" ry="2" />
<text text-anchor="" x="1026.10" y="1083.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>link_path_walk (1 samples, 0.08%)</title><rect x="10.0" y="1073" width="0.9" height="15.0" fill="rgb(226,126,0)" rx="2" ry="2" />
<text text-anchor="" x="13.00" y="1083.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>sock_aio_read.part.8 (22 samples, 1.67%)</title><rect x="76.4" y="785" width="19.7" height="15.0" fill="rgb(239,139,0)" rx="2" ry="2" />
<text text-anchor="" x="79.40" y="795.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>local_bh_enable_ip (1 samples, 0.08%)</title><rect x="1167.6" y="945" width="0.9" height="15.0" fill="rgb(232,132,0)" rx="2" ry="2" />
<text text-anchor="" x="1170.57" y="955.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptableObject:.putImpl (3 samples, 0.23%)</title><rect x="509.8" y="753" width="2.7" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="512.82" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__tcp_select_window (1 samples, 0.08%)</title><rect x="1057.2" y="945" width="0.9" height="15.0" fill="rgb(226,126,0)" rx="2" ry="2" />
<text text-anchor="" x="1060.19" y="955.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>fget_light (1 samples, 0.08%)</title><rect x="1101.2" y="1057" width="0.9" height="15.0" fill="rgb(240,140,0)" rx="2" ry="2" />
<text text-anchor="" x="1104.16" y="1067.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/buffer/AbstractReferenceCountedByteBuf:.release (4 samples, 0.30%)</title><rect x="105.1" y="913" width="3.6" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="108.12" y="923.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>acct_account_cputime (1 samples, 0.08%)</title><rect x="1038.3" y="961" width="0.9" height="15.0" fill="rgb(236,136,0)" rx="2" ry="2" />
<text text-anchor="" x="1041.35" y="971.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/IdScriptableObject:.put (1 samples, 0.08%)</title><rect x="339.3" y="721" width="0.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="342.32" y="731.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__perf_event_enable (4 samples, 0.30%)</title><rect x="711.7" y="577" width="3.6" height="15.0" fill="rgb(230,130,0)" rx="2" ry="2" />
<text text-anchor="" x="714.72" y="587.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>ip_finish_output (46 samples, 3.50%)</title><rect x="1124.5" y="881" width="41.3" height="15.0" fill="rgb(230,130,0)" rx="2" ry="2" />
<text text-anchor="" x="1127.49" y="891.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >ip_..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__tick_nohz_idle_enter (6 samples, 0.46%)</title><rect x="941.4" y="1137" width="5.4" height="15.0" fill="rgb(226,126,0)" rx="2" ry="2" />
<text text-anchor="" x="944.44" y="1147.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__skb_clone (4 samples, 0.30%)</title><rect x="851.7" y="545" width="3.6" height="15.0" fill="rgb(227,127,0)" rx="2" ry="2" />
<text text-anchor="" x="854.70" y="555.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptableObject:.getPrototype (1 samples, 0.08%)</title><rect x="592.4" y="785" width="0.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="595.37" y="795.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__switch_to (1 samples, 0.08%)</title><rect x="979.1" y="1105" width="0.9" height="15.0" fill="rgb(227,127,0)" rx="2" ry="2" />
<text text-anchor="" x="982.13" y="1115.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/channel/AbstractChannelHandlerContext:.write (35 samples, 2.66%)</title><rect x="204.7" y="737" width="31.4" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="207.72" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >io..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>_raw_spin_lock (1 samples, 0.08%)</title><rect x="751.2" y="401" width="0.9" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="754.20" y="411.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>_raw_spin_unlock_irqrestore (1 samples, 0.08%)</title><rect x="1159.5" y="545" width="0.9" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="1162.49" y="555.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptRuntime:.setObjectProp (3 samples, 0.23%)</title><rect x="242.4" y="801" width="2.7" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="245.41" y="811.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/IdScriptableObject:.has (1 samples, 0.08%)</title><rect x="307.9" y="737" width="0.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="310.92" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptableObject:.getSlot (2 samples, 0.15%)</title><rect x="273.8" y="689" width="1.8" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="276.82" y="699.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>java/lang/String:.hashCode (1 samples, 0.08%)</title><rect x="352.8" y="689" width="0.9" height="15.0" fill="rgb(77,224,77)" rx="2" ry="2" />
<text text-anchor="" x="355.78" y="699.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>idle_cpu (2 samples, 0.15%)</title><rect x="826.6" y="97" width="1.8" height="15.0" fill="rgb(242,142,0)" rx="2" ry="2" />
<text text-anchor="" x="829.58" y="107.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/IdScriptableObject:.get (1 samples, 0.08%)</title><rect x="502.6" y="769" width="0.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="505.64" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/util/Recycler:.get (1 samples, 0.08%)</title><rect x="671.3" y="913" width="0.9" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="674.34" y="923.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>native_write_msr_safe (4 samples, 0.30%)</title><rect x="906.4" y="865" width="3.6" height="15.0" fill="rgb(234,134,0)" rx="2" ry="2" />
<text text-anchor="" x="909.44" y="875.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptableObject:.getSlot (2 samples, 0.15%)</title><rect x="163.4" y="785" width="1.8" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="166.44" y="795.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>ip_output (119 samples, 9.05%)</title><rect x="742.2" y="529" width="106.8" height="15.0" fill="rgb(230,130,0)" rx="2" ry="2" />
<text text-anchor="" x="745.23" y="539.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >ip_output</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/buffer/AbstractByteBuf:.forEachByteAsc0 (3 samples, 0.23%)</title><rect x="635.4" y="881" width="2.7" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="638.44" y="891.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>skb_network_protocol (1 samples, 0.08%)</title><rect x="753.9" y="433" width="0.9" height="15.0" fill="rgb(230,130,0)" rx="2" ry="2" />
<text text-anchor="" x="756.89" y="443.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>enqueue_entity (5 samples, 0.38%)</title><rect x="829.3" y="49" width="4.5" height="15.0" fill="rgb(248,148,0)" rx="2" ry="2" />
<text text-anchor="" x="832.27" y="59.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>tcp_established_options (1 samples, 0.08%)</title><rect x="1174.7" y="945" width="0.9" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="1177.75" y="955.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>update_process_times (1 samples, 0.08%)</title><rect x="1038.3" y="1009" width="0.9" height="15.0" fill="rgb(232,132,0)" rx="2" ry="2" />
<text text-anchor="" x="1041.35" y="1019.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/channel/AbstractChannelHandlerContext:.read (3 samples, 0.23%)</title><rect x="891.2" y="913" width="2.7" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="894.19" y="923.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>update_rq_clock.part.63 (1 samples, 0.08%)</title><rect x="833.8" y="65" width="0.9" height="15.0" fill="rgb(232,132,0)" rx="2" ry="2" />
<text text-anchor="" x="836.76" y="75.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>sun/nio/ch/EPollArrayWrapper:.epollWait (4 samples, 0.30%)</title><rect x="898.4" y="945" width="3.6" height="15.0" fill="rgb(88,235,88)" rx="2" ry="2" />
<text text-anchor="" x="901.37" y="955.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>sock_poll (2 samples, 0.15%)</title><rect x="900.2" y="833" width="1.8" height="15.0" fill="rgb(239,139,0)" rx="2" ry="2" />
<text text-anchor="" x="903.16" y="843.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized (949 samples, 72.17%)</title><rect x="45.0" y="977" width="851.6" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="48.00" y="987.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>java_start (985 samples, 74.90%)</title><rect x="19.9" y="1169" width="883.8" height="15.0" fill="rgb(224,86,86)" rx="2" ry="2" />
<text text-anchor="" x="22.87" y="1179.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >java_start</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>mprotect_fixup (1 samples, 0.08%)</title><rect x="902.9" y="1057" width="0.8" height="15.0" fill="rgb(226,126,0)" rx="2" ry="2" />
<text text-anchor="" x="905.85" y="1067.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>ep_scan_ready_list.isra.9 (20 samples, 1.52%)</title><rect x="987.2" y="1073" width="17.9" height="15.0" fill="rgb(239,139,0)" rx="2" ry="2" />
<text text-anchor="" x="990.20" y="1083.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>tcp_v4_do_rcv (23 samples, 1.75%)</title><rect x="1143.3" y="657" width="20.7" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="1146.34" y="667.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>sk_stream_alloc_skb (7 samples, 0.53%)</title><rect x="1168.5" y="977" width="6.2" height="15.0" fill="rgb(222,122,0)" rx="2" ry="2" />
<text text-anchor="" x="1171.46" y="987.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>update_curr (2 samples, 0.15%)</title><rect x="832.0" y="33" width="1.8" height="15.0" fill="rgb(232,132,0)" rx="2" ry="2" />
<text text-anchor="" x="834.96" y="43.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>tcp_wfree (2 samples, 0.15%)</title><rect x="752.1" y="433" width="1.8" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="755.10" y="443.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>user_path_at_empty (1 samples, 0.08%)</title><rect x="10.0" y="1105" width="0.9" height="15.0" fill="rgb(227,127,0)" rx="2" ry="2" />
<text text-anchor="" x="13.00" y="1115.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/handler/codec/http/DefaultHttpHeaders:.init (1 samples, 0.08%)</title><rect x="201.1" y="737" width="0.9" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="204.13" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/buffer/AbstractByteBuf:.writeBytes (1 samples, 0.08%)</title><rect x="206.5" y="673" width="0.9" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="209.52" y="683.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>sun/nio/ch/NativeThread:.current (1 samples, 0.08%)</title><rect x="879.5" y="753" width="0.9" height="15.0" fill="rgb(88,235,88)" rx="2" ry="2" />
<text text-anchor="" x="882.52" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/IdScriptableObject:.get (1 samples, 0.08%)</title><rect x="237.9" y="769" width="0.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="240.92" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>JavaThread::oops_do (3 samples, 0.23%)</title><rect x="42.3" y="1121" width="2.7" height="15.0" fill="rgb(203,203,60)" rx="2" ry="2" />
<text text-anchor="" x="45.30" y="1131.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptableObject:.getBase (2 samples, 0.15%)</title><rect x="512.5" y="769" width="1.8" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="515.51" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>ip_local_deliver (1 samples, 0.08%)</title><rect x="767.4" y="353" width="0.9" height="15.0" fill="rgb(230,130,0)" rx="2" ry="2" />
<text text-anchor="" x="770.35" y="363.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/vertx/java/core/http/impl/ServerConnection:.handleRequest (526 samples, 40.00%)</title><rect x="141.9" y="865" width="472.0" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="144.91" y="875.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >org/vertx/java/core/http/impl/ServerConnection:.handleRequest</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__hrtimer_start_range_ns (3 samples, 0.23%)</title><rect x="970.2" y="1057" width="2.6" height="15.0" fill="rgb(226,126,0)" rx="2" ry="2" />
<text text-anchor="" x="973.15" y="1067.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/NativeFunction:.initScriptFunction (10 samples, 0.76%)</title><rect x="598.7" y="753" width="8.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="601.65" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>hrtimer_start (1 samples, 0.08%)</title><rect x="966.6" y="1057" width="0.9" height="15.0" fill="rgb(232,132,0)" rx="2" ry="2" />
<text text-anchor="" x="969.56" y="1067.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>intel_idle (11 samples, 0.84%)</title><rect x="919.0" y="1105" width="9.9" height="15.0" fill="rgb(236,136,0)" rx="2" ry="2" />
<text text-anchor="" x="922.00" y="1115.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/IdScriptableObject:.has (3 samples, 0.23%)</title><rect x="493.7" y="753" width="2.7" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="496.67" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>ktime_get (1 samples, 0.08%)</title><rect x="956.7" y="1057" width="0.9" height="15.0" fill="rgb(222,122,0)" rx="2" ry="2" />
<text text-anchor="" x="959.69" y="1067.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>update_cfs_rq_blocked_load (1 samples, 0.08%)</title><rect x="831.1" y="33" width="0.9" height="15.0" fill="rgb(232,132,0)" rx="2" ry="2" />
<text text-anchor="" x="834.06" y="43.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/WrapFactory:.setJavaPrimitiveWrap (1 samples, 0.08%)</title><rect x="613.0" y="833" width="0.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="616.01" y="843.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>sun/nio/ch/NativeThread:.current (1 samples, 0.08%)</title><rect x="99.7" y="913" width="0.9" height="15.0" fill="rgb(88,235,88)" rx="2" ry="2" />
<text text-anchor="" x="102.73" y="923.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>system_call_fastpath (28 samples, 2.13%)</title><rect x="73.7" y="865" width="25.1" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="76.71" y="875.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >s..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptableObject:.getSlot (1 samples, 0.08%)</title><rect x="431.7" y="753" width="0.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="434.75" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptableObject:.getSlot (1 samples, 0.08%)</title><rect x="409.3" y="737" width="0.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="412.32" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>sched_clock_cpu (1 samples, 0.08%)</title><rect x="1020.4" y="961" width="0.9" height="15.0" fill="rgb(236,136,0)" rx="2" ry="2" />
<text text-anchor="" x="1023.40" y="971.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>lapic_next_deadline (3 samples, 0.23%)</title><rect x="947.7" y="1025" width="2.7" height="15.0" fill="rgb(220,120,0)" rx="2" ry="2" />
<text text-anchor="" x="950.72" y="1035.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/buffer/UnpooledHeapByteBuf:.init (1 samples, 0.08%)</title><rect x="187.7" y="753" width="0.9" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="190.67" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>filename_lookup (1 samples, 0.08%)</title><rect x="10.0" y="1089" width="0.9" height="15.0" fill="rgb(241,141,0)" rx="2" ry="2" />
<text text-anchor="" x="13.00" y="1099.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>jni_fast_GetIntField (1 samples, 0.08%)</title><rect x="878.6" y="737" width="0.9" height="15.0" fill="rgb(236,103,103)" rx="2" ry="2" />
<text text-anchor="" x="881.62" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/IdScriptableObject:.put (6 samples, 0.46%)</title><rect x="401.2" y="753" width="5.4" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="404.24" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>x86_pmu_commit_txn (4 samples, 0.30%)</title><rect x="914.5" y="1025" width="3.6" height="15.0" fill="rgb(248,148,0)" rx="2" ry="2" />
<text text-anchor="" x="917.52" y="1035.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__kfree_skb (1 samples, 0.08%)</title><rect x="1054.5" y="961" width="0.9" height="15.0" fill="rgb(223,123,0)" rx="2" ry="2" />
<text text-anchor="" x="1057.50" y="971.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/NativeJavaMethod:.call (10 samples, 0.76%)</title><rect x="480.2" y="785" width="9.0" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="483.21" y="795.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>process_backlog (34 samples, 2.59%)</title><rect x="1134.4" y="785" width="30.5" height="15.0" fill="rgb(242,142,0)" rx="2" ry="2" />
<text text-anchor="" x="1137.37" y="795.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >pr..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>all (1,315 samples, 100%)</title><rect x="10.0" y="1217" width="1180.0" height="15.0" fill="rgb(255,130,130)" rx="2" ry="2" />
<text text-anchor="" x="13.00" y="1227.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/handler/codec/http/HttpHeaders:.encodeAscii0 (2 samples, 0.15%)</title><rect x="227.2" y="657" width="1.8" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="230.16" y="667.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>system_call_after_swapgs (6 samples, 0.46%)</title><rect x="11.8" y="1137" width="5.4" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="14.79" y="1147.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>_raw_spin_unlock_bh (1 samples, 0.08%)</title><rect x="856.2" y="609" width="0.9" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="859.19" y="619.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptableObject:.getSlot (1 samples, 0.08%)</title><rect x="513.4" y="737" width="0.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="516.41" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>tcp_event_new_data_sent (6 samples, 0.46%)</title><rect x="728.8" y="577" width="5.4" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="731.77" y="587.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>_raw_spin_unlock_bh (1 samples, 0.08%)</title><rect x="1167.6" y="961" width="0.9" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="1170.57" y="971.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>tcp_schedule_loss_probe (3 samples, 0.23%)</title><rect x="735.0" y="577" width="2.7" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="738.05" y="587.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>tcp_check_space (3 samples, 0.23%)</title><rect x="808.6" y="257" width="2.7" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="811.63" y="267.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>dev_queue_xmit (4 samples, 0.30%)</title><rect x="1124.5" y="865" width="3.6" height="15.0" fill="rgb(243,143,0)" rx="2" ry="2" />
<text text-anchor="" x="1127.49" y="875.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>tick_nohz_restart (6 samples, 0.46%)</title><rect x="946.8" y="1137" width="5.4" height="15.0" fill="rgb(230,130,0)" rx="2" ry="2" />
<text text-anchor="" x="949.82" y="1147.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__tcp_ack_snd_check (5 samples, 0.38%)</title><rect x="783.5" y="257" width="4.5" height="15.0" fill="rgb(226,126,0)" rx="2" ry="2" />
<text text-anchor="" x="786.51" y="267.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>user_path_at (1 samples, 0.08%)</title><rect x="10.0" y="1121" width="0.9" height="15.0" fill="rgb(227,127,0)" rx="2" ry="2" />
<text text-anchor="" x="13.00" y="1131.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>socket_readable (60 samples, 4.56%)</title><rect x="1042.8" y="1121" width="53.9" height="15.0" fill="rgb(241,110,110)" rx="2" ry="2" />
<text text-anchor="" x="1045.84" y="1131.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >socke..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptableObject:.getSlot (4 samples, 0.30%)</title><rect x="403.0" y="721" width="3.6" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="406.03" y="731.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>OopMapSet::all_do (1 samples, 0.08%)</title><rect x="43.2" y="1089" width="0.9" height="15.0" fill="rgb(208,208,62)" rx="2" ry="2" />
<text text-anchor="" x="46.20" y="1099.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>socket_writeable (1 samples, 0.08%)</title><rect x="1188.2" y="1137" width="0.9" height="15.0" fill="rgb(241,110,110)" rx="2" ry="2" />
<text text-anchor="" x="1191.21" y="1147.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>internal_add_timer (1 samples, 0.08%)</title><rect x="735.9" y="529" width="0.9" height="15.0" fill="rgb(236,136,0)" rx="2" ry="2" />
<text text-anchor="" x="738.95" y="539.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>select_task_rq_fair (4 samples, 0.30%)</title><rect x="824.8" y="113" width="3.6" height="15.0" fill="rgb(240,140,0)" rx="2" ry="2" />
<text text-anchor="" x="827.78" y="123.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>loopback_xmit (5 samples, 0.38%)</title><rect x="749.4" y="449" width="4.5" height="15.0" fill="rgb(232,132,0)" rx="2" ry="2" />
<text text-anchor="" x="752.41" y="459.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>sys_epoll_wait (56 samples, 4.26%)</title><rect x="981.8" y="1105" width="50.3" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="984.82" y="1115.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >sys_e..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptableObject:.getSlot (1 samples, 0.08%)</title><rect x="335.7" y="705" width="0.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="338.73" y="715.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>clockevents_program_event (3 samples, 0.23%)</title><rect x="947.7" y="1041" width="2.7" height="15.0" fill="rgb(219,119,0)" rx="2" ry="2" />
<text text-anchor="" x="950.72" y="1051.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/buffer/PooledByteBuf:.deallocate (2 samples, 0.15%)</title><rect x="106.9" y="897" width="1.8" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="109.91" y="907.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/util/Recycler:.get (1 samples, 0.08%)</title><rect x="230.7" y="673" width="0.9" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="233.75" y="683.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>fsnotify (1 samples, 0.08%)</title><rect x="1102.1" y="1057" width="0.9" height="15.0" fill="rgb(241,141,0)" rx="2" ry="2" />
<text text-anchor="" x="1105.06" y="1067.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/NativeJavaMethod:.call (74 samples, 5.63%)</title><rect x="169.7" y="785" width="66.4" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="172.73" y="795.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >org/moz..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptRuntime:.setObjectProp (37 samples, 2.81%)</title><rect x="340.2" y="737" width="33.2" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="343.22" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >or..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>schedule_preempt_disabled (1 samples, 0.08%)</title><rect x="973.7" y="1121" width="0.9" height="15.0" fill="rgb(236,136,0)" rx="2" ry="2" />
<text text-anchor="" x="976.74" y="1131.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/channel/AbstractChannelHandlerContext:.flush (238 samples, 18.10%)</title><rect x="674.0" y="881" width="213.6" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="677.03" y="891.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >io/netty/channel/AbstractCha..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>tcp_queue_rcv (2 samples, 0.15%)</title><rect x="843.6" y="241" width="1.8" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="846.63" y="251.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptableObject:.createSlot (5 samples, 0.38%)</title><rect x="315.1" y="673" width="4.5" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="318.10" y="683.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>tcp_recvmsg (7 samples, 0.53%)</title><rect x="1051.8" y="977" width="6.3" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="1054.81" y="987.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>update_curr (1 samples, 0.08%)</title><rect x="1018.6" y="945" width="0.9" height="15.0" fill="rgb(232,132,0)" rx="2" ry="2" />
<text text-anchor="" x="1021.61" y="955.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>java/lang/String:.hashCode (1 samples, 0.08%)</title><rect x="399.4" y="721" width="0.9" height="15.0" fill="rgb(77,224,77)" rx="2" ry="2" />
<text text-anchor="" x="402.44" y="731.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>tcp_md5_do_lookup (1 samples, 0.08%)</title><rect x="778.1" y="289" width="0.9" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="781.12" y="299.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>smp_call_function_single_interrupt (4 samples, 0.30%)</title><rect x="711.7" y="625" width="3.6" height="15.0" fill="rgb(225,125,0)" rx="2" ry="2" />
<text text-anchor="" x="714.72" y="635.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>netif_rx (2 samples, 0.15%)</title><rect x="750.3" y="433" width="1.8" height="15.0" fill="rgb(244,144,0)" rx="2" ry="2" />
<text text-anchor="" x="753.30" y="443.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>enqueue_to_backlog (1 samples, 0.08%)</title><rect x="751.2" y="417" width="0.9" height="15.0" fill="rgb(248,148,0)" rx="2" ry="2" />
<text text-anchor="" x="754.20" y="427.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>_raw_spin_unlock_bh (1 samples, 0.08%)</title><rect x="90.8" y="721" width="0.9" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="93.76" y="731.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>perf_ioctl (5 samples, 0.38%)</title><rect x="905.5" y="1057" width="4.5" height="15.0" fill="rgb(242,142,0)" rx="2" ry="2" />
<text text-anchor="" x="908.54" y="1067.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>tick_program_event (3 samples, 0.23%)</title><rect x="947.7" y="1057" width="2.7" height="15.0" fill="rgb(230,130,0)" rx="2" ry="2" />
<text text-anchor="" x="950.72" y="1067.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/handler/codec/ByteToMessageDecoder:.channelRead (635 samples, 48.29%)</title><rect x="103.3" y="929" width="569.8" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="106.32" y="939.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >io/netty/handler/codec/ByteToMessageDecoder:.channelRead</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>put_prev_task_fair (1 samples, 0.08%)</title><rect x="1022.2" y="1009" width="0.9" height="15.0" fill="rgb(233,133,0)" rx="2" ry="2" />
<text text-anchor="" x="1025.20" y="1019.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptableObject:.createSlot (15 samples, 1.14%)</title><rect x="454.2" y="721" width="13.4" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="457.18" y="731.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>java/lang/String:.hashCode (1 samples, 0.08%)</title><rect x="325.0" y="689" width="0.9" height="15.0" fill="rgb(77,224,77)" rx="2" ry="2" />
<text text-anchor="" x="327.97" y="699.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>sys_mprotect (1 samples, 0.08%)</title><rect x="902.9" y="1073" width="0.8" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="905.85" y="1083.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>Monitor::wait (1 samples, 0.08%)</title><rect x="19.9" y="1121" width="0.9" height="15.0" fill="rgb(213,213,64)" rx="2" ry="2" />
<text text-anchor="" x="22.87" y="1131.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>skb_push (1 samples, 0.08%)</title><rect x="727.9" y="577" width="0.9" height="15.0" fill="rgb(230,130,0)" rx="2" ry="2" />
<text text-anchor="" x="730.87" y="587.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>java/lang/ThreadLocal:.get (1 samples, 0.08%)</title><rect x="232.5" y="673" width="0.9" height="15.0" fill="rgb(77,224,77)" rx="2" ry="2" />
<text text-anchor="" x="235.54" y="683.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>vtable stub (1 samples, 0.08%)</title><rect x="612.1" y="817" width="0.9" height="15.0" fill="rgb(231,96,96)" rx="2" ry="2" />
<text text-anchor="" x="615.11" y="827.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>x86_64_start_kernel (24 samples, 1.83%)</title><rect x="953.1" y="1185" width="21.5" height="15.0" fill="rgb(248,148,0)" rx="2" ry="2" />
<text text-anchor="" x="956.10" y="1195.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >x..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>[unknown] (1 samples, 0.08%)</title><rect x="1080.5" y="1089" width="0.9" height="15.0" fill="rgb(243,112,112)" rx="2" ry="2" />
<text text-anchor="" x="1083.52" y="1099.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>kfree (1 samples, 0.08%)</title><rect x="904.6" y="945" width="0.9" height="15.0" fill="rgb(229,129,0)" rx="2" ry="2" />
<text text-anchor="" x="907.65" y="955.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete (241 samples, 18.33%)</title><rect x="673.1" y="913" width="216.3" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="676.13" y="923.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >io/netty/channel/AbstractCha..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/buffer/AbstractByteBuf:.forEachByteAsc0 (3 samples, 0.23%)</title><rect x="642.6" y="881" width="2.7" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="645.62" y="891.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>Java_sun_nio_ch_FileDispatcherImpl_write0 (1 samples, 0.08%)</title><rect x="699.2" y="737" width="0.9" height="15.0" fill="rgb(228,91,91)" rx="2" ry="2" />
<text text-anchor="" x="702.16" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>do_execve_common.isra.22 (1 samples, 0.08%)</title><rect x="904.6" y="1057" width="0.9" height="15.0" fill="rgb(243,143,0)" rx="2" ry="2" />
<text text-anchor="" x="907.65" y="1067.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>group_sched_in (4 samples, 0.30%)</title><rect x="711.7" y="561" width="3.6" height="15.0" fill="rgb(239,139,0)" rx="2" ry="2" />
<text text-anchor="" x="714.72" y="571.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>socket_writeable (1 samples, 0.08%)</title><rect x="976.4" y="1169" width="0.9" height="15.0" fill="rgb(241,110,110)" rx="2" ry="2" />
<text text-anchor="" x="979.43" y="1179.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptableObject$RelinkedSlot:.getValue (1 samples, 0.08%)</title><rect x="325.9" y="721" width="0.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="328.86" y="731.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>java/lang/String:.hashCode (1 samples, 0.08%)</title><rect x="284.6" y="689" width="0.9" height="15.0" fill="rgb(77,224,77)" rx="2" ry="2" />
<text text-anchor="" x="287.59" y="699.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptableObject:.getTopLevelScope (1 samples, 0.08%)</title><rect x="146.4" y="849" width="0.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="149.40" y="859.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>kmem_cache_alloc_node (2 samples, 0.15%)</title><rect x="865.2" y="577" width="1.8" height="15.0" fill="rgb(220,120,0)" rx="2" ry="2" />
<text text-anchor="" x="868.16" y="587.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/IdScriptableObject:.has (12 samples, 0.91%)</title><rect x="277.4" y="705" width="10.8" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="280.41" y="715.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>getnstimeofday (1 samples, 0.08%)</title><rect x="807.7" y="209" width="0.9" height="15.0" fill="rgb(238,138,0)" rx="2" ry="2" />
<text text-anchor="" x="810.73" y="219.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>java/util/Arrays:.copyOf (1 samples, 0.08%)</title><rect x="196.6" y="705" width="0.9" height="15.0" fill="rgb(77,224,77)" rx="2" ry="2" />
<text text-anchor="" x="199.65" y="715.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>arch_cpu_idle (22 samples, 1.67%)</title><rect x="914.5" y="1153" width="19.8" height="15.0" fill="rgb(230,130,0)" rx="2" ry="2" />
<text text-anchor="" x="917.52" y="1163.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>http_parser_execute (30 samples, 2.28%)</title><rect x="1067.1" y="1105" width="26.9" height="15.0" fill="rgb(233,99,99)" rx="2" ry="2" />
<text text-anchor="" x="1070.06" y="1115.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >h..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>new_slab (2 samples, 0.15%)</title><rect x="1172.1" y="913" width="1.7" height="15.0" fill="rgb(242,142,0)" rx="2" ry="2" />
<text text-anchor="" x="1175.05" y="923.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/channel/ChannelDuplexHandler:.flush (1 samples, 0.08%)</title><rect x="887.6" y="881" width="0.9" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="890.60" y="891.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>generic_smp_call_function_single_interrupt (4 samples, 0.30%)</title><rect x="1128.1" y="817" width="3.6" height="15.0" fill="rgb(243,143,0)" rx="2" ry="2" />
<text text-anchor="" x="1131.08" y="827.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/handler/codec/http/DefaultHttpHeaders:.contains (1 samples, 0.08%)</title><rect x="212.8" y="673" width="0.9" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="215.80" y="683.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>java/lang/ThreadLocal:.get (1 samples, 0.08%)</title><rect x="144.6" y="849" width="0.9" height="15.0" fill="rgb(77,224,77)" rx="2" ry="2" />
<text text-anchor="" x="147.60" y="859.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>security_socket_sendmsg (1 samples, 0.08%)</title><rect x="874.1" y="641" width="0.9" height="15.0" fill="rgb(238,138,0)" rx="2" ry="2" />
<text text-anchor="" x="877.14" y="651.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>update_cpu_load_nohz (1 samples, 0.08%)</title><rect x="952.2" y="1137" width="0.9" height="15.0" fill="rgb(232,132,0)" rx="2" ry="2" />
<text text-anchor="" x="955.21" y="1147.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__perf_event_enable (4 samples, 0.30%)</title><rect x="1128.1" y="785" width="3.6" height="15.0" fill="rgb(230,130,0)" rx="2" ry="2" />
<text text-anchor="" x="1131.08" y="795.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>skb_clone (1 samples, 0.08%)</title><rect x="1113.7" y="945" width="0.9" height="15.0" fill="rgb(230,130,0)" rx="2" ry="2" />
<text text-anchor="" x="1116.73" y="955.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>start_thread (237 samples, 18.02%)</title><rect x="977.3" y="1185" width="212.7" height="15.0" fill="rgb(237,104,104)" rx="2" ry="2" />
<text text-anchor="" x="980.33" y="1195.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >start_thread</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>mod_timer (3 samples, 0.23%)</title><rect x="1114.6" y="897" width="2.7" height="15.0" fill="rgb(229,129,0)" rx="2" ry="2" />
<text text-anchor="" x="1117.62" y="907.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>tcp_data_queue (9 samples, 0.68%)</title><rect x="1155.9" y="625" width="8.1" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="1158.90" y="635.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptableObject:.getSlot (1 samples, 0.08%)</title><rect x="161.7" y="769" width="0.8" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="164.65" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>java/lang/String:.trim (1 samples, 0.08%)</title><rect x="666.9" y="897" width="0.8" height="15.0" fill="rgb(77,224,77)" rx="2" ry="2" />
<text text-anchor="" x="669.85" y="907.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>net_rx_action (35 samples, 2.66%)</title><rect x="1133.5" y="801" width="31.4" height="15.0" fill="rgb(244,144,0)" rx="2" ry="2" />
<text text-anchor="" x="1136.47" y="811.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >ne..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>inet_sendmsg (177 samples, 13.46%)</title><rect x="715.3" y="641" width="158.8" height="15.0" fill="rgb(239,139,0)" rx="2" ry="2" />
<text text-anchor="" x="718.31" y="651.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >inet_sendmsg</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>getnstimeofday (3 samples, 0.23%)</title><rect x="849.0" y="545" width="2.7" height="15.0" fill="rgb(238,138,0)" rx="2" ry="2" />
<text text-anchor="" x="852.01" y="555.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/buffer/AbstractByteBuf:.writeBytes (1 samples, 0.08%)</title><rect x="171.5" y="769" width="0.9" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="174.52" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptableObject:.getSlot (3 samples, 0.23%)</title><rect x="507.1" y="753" width="2.7" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="510.13" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>[unknown] (1 samples, 0.08%)</title><rect x="10.0" y="1185" width="0.9" height="15.0" fill="rgb(243,112,112)" rx="2" ry="2" />
<text text-anchor="" x="13.00" y="1195.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>hrtimer_try_to_cancel (4 samples, 0.30%)</title><rect x="946.8" y="1105" width="3.6" height="15.0" fill="rgb(232,132,0)" rx="2" ry="2" />
<text text-anchor="" x="949.82" y="1115.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>java/nio/charset/Charset:.lookup (2 samples, 0.15%)</title><rect x="199.3" y="753" width="1.8" height="15.0" fill="rgb(77,224,77)" rx="2" ry="2" />
<text text-anchor="" x="202.34" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/NativeJavaObject:.initMembers (1 samples, 0.08%)</title><rect x="483.8" y="769" width="0.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="486.79" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>start_thread (985 samples, 74.90%)</title><rect x="19.9" y="1185" width="883.8" height="15.0" fill="rgb(237,104,104)" rx="2" ry="2" />
<text text-anchor="" x="22.87" y="1195.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >start_thread</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>x86_pmu_commit_txn (4 samples, 0.30%)</title><rect x="711.7" y="545" width="3.6" height="15.0" fill="rgb(248,148,0)" rx="2" ry="2" />
<text text-anchor="" x="714.72" y="555.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptableObject:.getParentScope (1 samples, 0.08%)</title><rect x="150.0" y="817" width="0.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="152.98" y="827.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>system_call_fastpath (5 samples, 0.38%)</title><rect x="905.5" y="1105" width="4.5" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="908.54" y="1115.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>_raw_spin_lock (1 samples, 0.08%)</title><rect x="822.1" y="113" width="0.9" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="825.09" y="123.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>menu_select (4 samples, 0.30%)</title><rect x="929.8" y="1121" width="3.6" height="15.0" fill="rgb(229,129,0)" rx="2" ry="2" />
<text text-anchor="" x="932.77" y="1131.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/handler/codec/http/DefaultHttpHeaders:.add0 (3 samples, 0.23%)</title><rect x="622.0" y="897" width="2.7" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="624.98" y="907.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/buffer/PooledByteBuf:.deallocate (2 samples, 0.15%)</title><rect x="108.7" y="913" width="1.8" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="111.71" y="923.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/BaseFunction:.findInstanceIdInfo (4 samples, 0.30%)</title><rect x="330.3" y="705" width="3.6" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="333.35" y="715.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>rest_init (24 samples, 1.83%)</title><rect x="953.1" y="1137" width="21.5" height="15.0" fill="rgb(236,136,0)" rx="2" ry="2" />
<text text-anchor="" x="956.10" y="1147.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >r..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>ksoftirqd/3 (1 samples, 0.08%)</title><rect x="903.7" y="1201" width="0.9" height="15.0" fill="rgb(86,233,86)" rx="2" ry="2" />
<text text-anchor="" x="906.75" y="1211.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>group_sched_in (4 samples, 0.30%)</title><rect x="1128.1" y="769" width="3.6" height="15.0" fill="rgb(239,139,0)" rx="2" ry="2" />
<text text-anchor="" x="1131.08" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>account_user_time (1 samples, 0.08%)</title><rect x="1038.3" y="977" width="0.9" height="15.0" fill="rgb(236,136,0)" rx="2" ry="2" />
<text text-anchor="" x="1041.35" y="987.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptRuntime:.name (8 samples, 0.61%)</title><rect x="159.9" y="817" width="7.1" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="162.86" y="827.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>perf_event_for_each_child (5 samples, 0.38%)</title><rect x="905.5" y="1041" width="4.5" height="15.0" fill="rgb(242,142,0)" rx="2" ry="2" />
<text text-anchor="" x="908.54" y="1051.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/handler/codec/http/HttpObjectDecoder:.findWhitespace (1 samples, 0.08%)</title><rect x="660.6" y="881" width="0.9" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="663.57" y="891.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>java/lang/String:.init (1 samples, 0.08%)</title><rect x="662.4" y="865" width="0.9" height="15.0" fill="rgb(77,224,77)" rx="2" ry="2" />
<text text-anchor="" x="665.37" y="875.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>set_next_entity (2 samples, 0.15%)</title><rect x="939.6" y="1105" width="1.8" height="15.0" fill="rgb(233,133,0)" rx="2" ry="2" />
<text text-anchor="" x="942.64" y="1115.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>ip_local_deliver_finish (89 samples, 6.77%)</title><rect x="768.3" y="321" width="79.8" height="15.0" fill="rgb(230,130,0)" rx="2" ry="2" />
<text text-anchor="" x="771.25" y="331.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >ip_local_..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/NativeJavaMethod:.findCachedFunction (1 samples, 0.08%)</title><rect x="236.1" y="785" width="0.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="239.13" y="795.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/IdScriptableObject:.put (12 samples, 0.91%)</title><rect x="288.2" y="705" width="10.7" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="291.17" y="715.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>hrtimer_start_range_ns (2 samples, 0.15%)</title><rect x="950.4" y="1121" width="1.8" height="15.0" fill="rgb(232,132,0)" rx="2" ry="2" />
<text text-anchor="" x="953.41" y="1131.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__internal_add_timer (1 samples, 0.08%)</title><rect x="735.9" y="513" width="0.9" height="15.0" fill="rgb(225,125,0)" rx="2" ry="2" />
<text text-anchor="" x="738.95" y="523.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptRuntime:.getPropFunctionAndThisHelper (9 samples, 0.68%)</title><rect x="327.7" y="737" width="8.0" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="330.66" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>smp_call_function_single_interrupt (4 samples, 0.30%)</title><rect x="1128.1" y="833" width="3.6" height="15.0" fill="rgb(225,125,0)" rx="2" ry="2" />
<text text-anchor="" x="1131.08" y="843.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>_raw_spin_lock_bh (1 samples, 0.08%)</title><rect x="89.9" y="721" width="0.9" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="92.86" y="731.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__hrtimer_start_range_ns (1 samples, 0.08%)</title><rect x="966.6" y="1041" width="0.9" height="15.0" fill="rgb(226,126,0)" rx="2" ry="2" />
<text text-anchor="" x="969.56" y="1051.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>lock_sock_nested (1 samples, 0.08%)</title><rect x="857.1" y="609" width="0.9" height="15.0" fill="rgb(232,132,0)" rx="2" ry="2" />
<text text-anchor="" x="860.09" y="619.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/IdScriptableObject$PrototypeValues:.ensureId (1 samples, 0.08%)</title><rect x="334.8" y="705" width="0.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="337.84" y="715.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>sk_reset_timer (3 samples, 0.23%)</title><rect x="1144.2" y="593" width="2.7" height="15.0" fill="rgb(222,122,0)" rx="2" ry="2" />
<text text-anchor="" x="1147.24" y="603.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/gen/file__root_vert_x_2_1_5_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_2 (154 samples, 11.71%)</title><rect x="252.3" y="753" width="138.2" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="255.28" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >org/mozilla/javas..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/handler/codec/ByteToMessageDecoder:.channelRead (1 samples, 0.08%)</title><rect x="894.8" y="945" width="0.9" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="897.78" y="955.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>security_socket_sendmsg (2 samples, 0.15%)</title><rect x="1175.6" y="1009" width="1.8" height="15.0" fill="rgb(238,138,0)" rx="2" ry="2" />
<text text-anchor="" x="1178.64" y="1019.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/handler/codec/http/DefaultHttpHeaders:.add0 (1 samples, 0.08%)</title><rect x="192.2" y="753" width="0.9" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="195.16" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>system_call_after_swapgs (1 samples, 0.08%)</title><rect x="980.9" y="1121" width="0.9" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="983.92" y="1131.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>hrtimer_cancel (4 samples, 0.30%)</title><rect x="946.8" y="1121" width="3.6" height="15.0" fill="rgb(232,132,0)" rx="2" ry="2" />
<text text-anchor="" x="949.82" y="1131.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>ObjArrayKlass::oop_push_contents (2 samples, 0.15%)</title><rect x="35.1" y="1105" width="1.8" height="15.0" fill="rgb(193,193,56)" rx="2" ry="2" />
<text text-anchor="" x="38.13" y="1115.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/IdScriptableObject:.findInstanceIdInfo (1 samples, 0.08%)</title><rect x="276.5" y="705" width="0.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="279.51" y="715.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/IdScriptableObject:.has (1 samples, 0.08%)</title><rect x="400.3" y="753" width="0.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="403.34" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>schedule_hrtimeout_range (20 samples, 1.52%)</title><rect x="1005.1" y="1073" width="18.0" height="15.0" fill="rgb(236,136,0)" rx="2" ry="2" />
<text text-anchor="" x="1008.15" y="1083.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptableObject:.putImpl (24 samples, 1.83%)</title><rect x="351.0" y="705" width="21.5" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="353.99" y="715.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >o..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__fsnotify_parent (1 samples, 0.08%)</title><rect x="706.3" y="673" width="0.9" height="15.0" fill="rgb(228,128,0)" rx="2" ry="2" />
<text text-anchor="" x="709.33" y="683.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>vtable stub (1 samples, 0.08%)</title><rect x="372.5" y="721" width="0.9" height="15.0" fill="rgb(231,96,96)" rx="2" ry="2" />
<text text-anchor="" x="375.52" y="731.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__dev_queue_xmit (10 samples, 0.76%)</title><rect x="745.8" y="481" width="9.0" height="15.0" fill="rgb(230,130,0)" rx="2" ry="2" />
<text text-anchor="" x="748.82" y="491.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>sys_write (88 samples, 6.69%)</title><rect x="1100.3" y="1073" width="78.9" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="1103.27" y="1083.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >sys_write</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>detach_if_pending (1 samples, 0.08%)</title><rect x="786.2" y="193" width="0.9" height="15.0" fill="rgb(244,144,0)" rx="2" ry="2" />
<text text-anchor="" x="789.20" y="203.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>socket_writeable (99 samples, 7.53%)</title><rect x="1096.7" y="1121" width="88.8" height="15.0" fill="rgb(241,110,110)" rx="2" ry="2" />
<text text-anchor="" x="1099.68" y="1131.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >socket_wri..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/IdScriptableObject:.findInstanceIdInfo (2 samples, 0.15%)</title><rect x="519.7" y="769" width="1.8" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="522.69" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>epoll_ctl (6 samples, 0.46%)</title><rect x="1085.0" y="1073" width="5.4" height="15.0" fill="rgb(248,120,120)" rx="2" ry="2" />
<text text-anchor="" x="1088.01" y="1083.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/vertx/java/core/http/impl/AssembledFullHttpResponse:.toLastContent (1 samples, 0.08%)</title><rect x="201.1" y="753" width="0.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="204.13" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/NativeJavaObject:.get (1 samples, 0.08%)</title><rect x="503.5" y="769" width="0.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="506.54" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>lock_hrtimer_base.isra.19 (1 samples, 0.08%)</title><rect x="1012.3" y="1041" width="0.9" height="15.0" fill="rgb(232,132,0)" rx="2" ry="2" />
<text text-anchor="" x="1015.33" y="1051.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>intel_idle (3 samples, 0.23%)</title><rect x="954.0" y="1057" width="2.7" height="15.0" fill="rgb(236,136,0)" rx="2" ry="2" />
<text text-anchor="" x="957.00" y="1067.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>ip_local_deliver (31 samples, 2.36%)</title><rect x="1136.2" y="705" width="27.8" height="15.0" fill="rgb(230,130,0)" rx="2" ry="2" />
<text text-anchor="" x="1139.16" y="715.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >i..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/IdScriptableObject:.put (23 samples, 1.75%)</title><rect x="447.0" y="769" width="20.6" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="450.00" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete (242 samples, 18.40%)</title><rect x="673.1" y="929" width="217.2" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="676.13" y="939.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >io/netty/handler/codec/ByteT..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>tcp_event_data_recv (1 samples, 0.08%)</title><rect x="1161.3" y="609" width="0.9" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="1164.29" y="619.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptableObject:.getTopScopeValue (1 samples, 0.08%)</title><rect x="150.9" y="817" width="0.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="153.88" y="827.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>tcp_rearm_rto (3 samples, 0.23%)</title><rect x="1114.6" y="929" width="2.7" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="1117.62" y="939.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/NativeCall:.init (48 samples, 3.65%)</title><rect x="437.1" y="785" width="43.1" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="440.13" y="795.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >org/..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>tick_nohz_idle_enter (5 samples, 0.38%)</title><rect x="964.8" y="1105" width="4.5" height="15.0" fill="rgb(230,130,0)" rx="2" ry="2" />
<text text-anchor="" x="967.77" y="1115.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>system_call_fastpath (4 samples, 0.30%)</title><rect x="898.4" y="913" width="3.6" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="901.37" y="923.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>system_call (1 samples, 0.08%)</title><rect x="976.4" y="1137" width="0.9" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="979.43" y="1147.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>tick_nohz_idle_enter (6 samples, 0.46%)</title><rect x="941.4" y="1153" width="5.4" height="15.0" fill="rgb(230,130,0)" rx="2" ry="2" />
<text text-anchor="" x="944.44" y="1163.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>tick_program_event (1 samples, 0.08%)</title><rect x="945.0" y="1041" width="0.9" height="15.0" fill="rgb(230,130,0)" rx="2" ry="2" />
<text text-anchor="" x="948.03" y="1051.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/ScriptRuntime:.getPropFunctionAndThisHelper (1 samples, 0.08%)</title><rect x="271.1" y="721" width="0.9" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="274.13" y="731.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>remote_function (4 samples, 0.30%)</title><rect x="711.7" y="593" width="3.6" height="15.0" fill="rgb(242,142,0)" rx="2" ry="2" />
<text text-anchor="" x="714.72" y="603.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>tcp_clean_rtx_queue (1 samples, 0.08%)</title><rect x="1152.3" y="609" width="0.9" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="1155.31" y="619.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>tick_nohz_idle_exit (7 samples, 0.53%)</title><rect x="946.8" y="1153" width="6.3" height="15.0" fill="rgb(230,130,0)" rx="2" ry="2" />
<text text-anchor="" x="949.82" y="1163.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>org/mozilla/javascript/IdScriptableObject:.put (9 samples, 0.68%)</title><rect x="311.5" y="721" width="8.1" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="314.51" y="731.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>fsnotify (1 samples, 0.08%)</title><rect x="97.0" y="785" width="0.9" height="15.0" fill="rgb(241,141,0)" rx="2" ry="2" />
<text text-anchor="" x="100.04" y="795.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>pick_next_task_fair (2 samples, 0.15%)</title><rect x="939.6" y="1121" width="1.8" height="15.0" fill="rgb(239,139,0)" rx="2" ry="2" />
<text text-anchor="" x="942.64" y="1131.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>do_sync_write (82 samples, 6.24%)</title><rect x="1103.9" y="1041" width="73.5" height="15.0" fill="rgb(243,143,0)" rx="2" ry="2" />
<text text-anchor="" x="1106.86" y="1051.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >do_sync_..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>sys_write (195 samples, 14.83%)</title><rect x="701.8" y="705" width="175.0" height="15.0" fill="rgb(237,137,0)" rx="2" ry="2" />
<text text-anchor="" x="704.85" y="715.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >sys_write</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>common_file_perm (1 samples, 0.08%)</title><rect x="875.9" y="625" width="0.9" height="15.0" fill="rgb(232,132,0)" rx="2" ry="2" />
<text text-anchor="" x="878.93" y="635.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>account_process_tick (1 samples, 0.08%)</title><rect x="1038.3" y="993" width="0.9" height="15.0" fill="rgb(236,136,0)" rx="2" ry="2" />
<text text-anchor="" x="1041.35" y="1003.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
</svg>

ab;[unknown <7f26cab4c0b0>];__write_nocancel;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;inet_sendmsg;call_function_single_interrupt;smp_call_function_single_interrupt;generic_smp_call_function_single_interrupt;remote_function;__perf_event_enable;group_sched_in;x86_pmu_commit_txn;perf_pmu_enable;x86_pmu_enable;intel_pmu_enable_all;native_write_msr_safe 4
ab;[unknown <7f26cabd51d0>];__write_nocancel;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;inet_sendmsg;tcp_sendmsg;__tcp_push_pending_frames;tcp_write_xmit;tcp_transmit_skb;ip_queue_xmit;ip_local_out;ip_output;ip_finish_output;local_bh_enable;do_softirq;do_softirq_own_stack;__do_softirq;net_rx_action;process_backlog;__netif_receive_skb;__netif_receive_skb_core;ip_rcv;ip_rcv_finish;ip_local_deliver;ip_local_deliver_finish;tcp_v4_rcv;tcp_v4_do_rcv;tcp_rcv_established;tcp_ack;tcp_clean_rtx_queue;__kfree_skb 1
ab;[unknown <7f26cabe2590>];__write_nocancel;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;inet_sendmsg;tcp_sendmsg;sk_stream_alloc_skb;__alloc_skb;__kmalloc_reserve.isra.26 1
ab;[unknown <7f26cabf0e30>];__write_nocancel;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;inet_sendmsg;tcp_sendmsg;tcp_send_mss;tcp_current_mss;tcp_v4_md5_lookup 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/buffer/AbstractByteBuf:.writeBytes;sun/nio/ch/SocketChannelImpl:.read 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/buffer/AbstractByteBuf:.writeBytes;sun/nio/ch/SocketChannelImpl:.read;java/lang/Thread:.blockedOn 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/DefaultChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/DefaultChannelHandlerContext:.fireChannelRead 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/DefaultChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/DefaultChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/VertxHttpHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/vertx/java/core/http/impl/ServerConnection:.handleMessage 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/DefaultChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/DefaultChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/VertxHttpHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/vertx/java/core/http/impl/ServerConnection:.handleMessage;org/mozilla/javascript/WrapFactory:.wrap;call_function_single_interrupt;smp_call_function_single_interrupt;generic_smp_call_function_single_interrupt;remote_function;__perf_event_enable;group_sched_in;x86_pmu_commit_txn;perf_pmu_enable;x86_pmu_enable;intel_pmu_enable_all;native_write_msr_safe 4
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/DefaultChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/DefaultChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/VertxHttpHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/vertx/java/core/http/impl/ServerConnection:.handleMessage;org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_80:.call;org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_80:.call;org/mozilla/javascript/BaseFunction:.construct;org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_80:.call;org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_80:._c_anonymous_3;org/mozilla/javascript/BaseFunction:.construct;org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_80:.call;org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_80:._c_anonymous_21;org/mozilla/javascript/optimizer/OptRuntime:.call2;org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_streams_js_31:.call;org/mozilla/javascript/ScriptRuntime:.setObjectProp;org/mozilla/javascript/IdScriptableObject:.has 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/DefaultChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/DefaultChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/VertxHttpHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/vertx/java/core/http/impl/ServerConnection:.handleMessage;org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_90:.call;org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_90:.call;org/mozilla/javascript/BaseFunction:.construct;org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_90:.call;org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_90:._c_anonymous_3;org/mozilla/javascript/ScriptRuntime:.getPropFunctionAndThis 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/DefaultChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/DefaultChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/VertxHttpHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/vertx/java/core/http/impl/ServerConnection:.handleMessage;org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_90:.call;org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_90:.call;org/mozilla/javascript/BaseFunction:.construct;org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_90:.call;org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_90:._c_anonymous_3;org/mozilla/javascript/ScriptRuntime:.nameOrFunction;org/mozilla/javascript/IdScriptableObject:.get;org/mozilla/javascript/ScriptableObject:.getSlot 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/DefaultChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/DefaultChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/VertxHttpHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/vertx/java/core/http/impl/ServerConnection:.handleMessage;org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_90:.call;org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_90:.call;org/mozilla/javascript/BaseFunction:.construct;org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_90:.call;org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_90:._c_anonymous_3;org/mozilla/javascript/ScriptRuntime:.setObjectProp;org/mozilla/javascript/IdScriptableObject:.put;org/mozilla/javascript/ScriptableObject:.getSlot;org/mozilla/javascript/ScriptableObject:.createSlot 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/DefaultChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/DefaultChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/VertxHttpHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/vertx/java/core/http/impl/ServerConnection:.handleMessage;org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_90:.call;org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_90:.call;org/mozilla/javascript/gen/file__home_bgregg_bench_Server_js_js_4:.call;org/mozilla/javascript/gen/file__home_bgregg_bench_Server_js_js_4:._c_anonymous_1;org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_90:.call;org/mozilla/javascript/NativeJavaMethod:.call;org/mozilla/javascript/MemberBox:.invoke;java/lang/reflect/Method:.invoke 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/DefaultChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/DefaultChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/VertxHttpHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/vertx/java/core/http/impl/ServerConnection:.handleMessage;org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_91:.call;org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_91:.call;org/mozilla/javascript/BaseFunction:.construct;org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_91:.call;org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_91:._c_anonymous_3;org/mozilla/javascript/BaseFunction:.construct;org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_91:.call;org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_91:._c_anonymous_21;org/mozilla/javascript/optimizer/OptRuntime:.call2;org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_streams_js_49:.call;org/mozilla/javascript/optimizer/OptRuntime:.call2;org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_streams_js_49:.call;org/mozilla/javascript/ScriptRuntime:.setObjectProp;org/mozilla/javascript/ScriptableObject:.getSlot 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/DefaultChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/DefaultChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/VertxHttpHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/vertx/java/core/http/impl/ServerConnection:.handleMessage;org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_91:.call;org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_91:.call;org/mozilla/javascript/BaseFunction:.construct;org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_91:.call;org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_91:._c_anonymous_3;org/mozilla/javascript/ScriptRuntime:.setObjectProp;org/mozilla/javascript/IdScriptableObject:.has 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/DefaultChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/DefaultChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/VertxHttpHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/vertx/java/core/http/impl/ServerConnection:.handleMessage;org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_91:.call;org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_91:.call;org/mozilla/javascript/BaseFunction:.construct;org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_91:.call;org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_91:._c_anonymous_3;org/mozilla/javascript/ScriptRuntime:.setObjectProp;org/mozilla/javascript/IdScriptableObject:.put;org/mozilla/javascript/ScriptableObject:.getSlot;org/mozilla/javascript/ScriptableObject:.createSlot 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/DefaultChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/DefaultChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/VertxHttpHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/vertx/java/core/http/impl/ServerConnection:.handleMessage;org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_91:.call;org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_91:.call;org/mozilla/javascript/gen/file__home_bgregg_bench_Server_js_js_2:.call;org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_91:.call;org/mozilla/javascript/NativeJavaMethod:.call;org/mozilla/javascript/MemberBox:.invoke;java/lang/reflect/Method:.invoke;io/netty/handler/codec/http/DefaultHttpHeaders:.add0 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/DefaultChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/DefaultChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/VertxHttpHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/vertx/java/core/http/impl/ServerConnection:.handleMessage;org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93:.call;org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93:.call 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/DefaultChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/DefaultChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/VertxHttpHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/vertx/java/core/http/impl/ServerConnection:.handleMessage;org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93:.call;org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93:.call;org/mozilla/javascript/BaseFunction:.construct;org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93:.call;org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93:._c_anonymous_3;org/mozilla/javascript/BaseFunction:.construct;org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93:.call;org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93:._c_anonymous_21;org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93:.getParamAndVarCount 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/DefaultChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/DefaultChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/VertxHttpHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/vertx/java/core/http/impl/ServerConnection:.handleMessage;org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93:.call;org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93:.call;org/mozilla/javascript/BaseFunction:.construct;org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93:.call;org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93:._c_anonymous_3;org/mozilla/javascript/ScriptRuntime:.setObjectProp;org/mozilla/javascript/IdScriptableObject:.has;org/mozilla/javascript/ScriptableObject:.getSlot 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/DefaultChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/DefaultChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/VertxHttpHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/vertx/java/core/http/impl/ServerConnection:.handleMessage;org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93:.call;org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93:.call;org/mozilla/javascript/BaseFunction:.construct;org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93:.call;org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93:._c_anonymous_3;org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93:.<init>;org/mozilla/javascript/NativeFunction:.initScriptFunction;org/mozilla/javascript/TopLevel:.getBuiltinPrototype;call_function_single_interrupt;smp_call_function_single_interrupt;generic_smp_call_function_single_interrupt;remote_function;__perf_event_enable;group_sched_in;x86_pmu_commit_txn;perf_pmu_enable;x86_pmu_enable;intel_pmu_enable_all;native_write_msr_safe 4
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/DefaultChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/DefaultChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/VertxHttpHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/vertx/java/core/http/impl/ServerConnection:.handleMessage;org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93:.call;org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93:.call;org/mozilla/javascript/BaseFunction:.construct;org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93:.call;org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93:._c_anonymous_3;org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93:.getParamCount 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/DefaultChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/channel/DefaultChannelHandlerContext:.fireChannelRead;org/vertx/java/core/net/impl/VertxHandler:.channelRead;org/vertx/java/core/http/impl/VertxHttpHandler:.channelRead;org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;org/vertx/java/core/http/impl/ServerConnection:.handleMessage;org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93:.call;org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93:.call;org/mozilla/javascript/BaseFunction:.construct;org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93:.call;org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93:._c_anonymous_3;org/mozilla/javascript/optimizer/OptRuntime:.call2;org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_streams_js_47:.call;org/mozilla/javascript/ScriptRuntime:.setObjectProp;org/mozilla/javascript/ScriptableObject:.getSlot 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/DefaultChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/handler/codec/http/HttpObjectDecoder:.decode;io/netty/handler/codec/http/HttpMethod:.valueOf 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/DefaultChannelHandlerContext:.fireChannelRead;io/netty/handler/codec/ByteToMessageDecoder:.channelRead;io/netty/handler/codec/http/HttpObjectDecoder:.decode;io/netty/handler/codec/http/HttpObjectDecoder:.findWhitespace 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/DefaultChannelHandlerContext:.fireChannelReadComplete;io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;io/netty/channel/DefaultChannelHandlerContext:.fireChannelReadComplete;org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;io/netty/channel/DefaultChannelHandlerContext:.flush;io/netty/channel/ChannelDuplexHandler:.flush;io/netty/channel/DefaultChannelHandlerContext:.flush;io/netty/channel/ChannelOutboundHandlerAdapter:.flush;io/netty/channel/DefaultChannelHandlerContext:.flush;io/netty/channel/DefaultChannelPipeline$HeadHandler:.flush;io/netty/channel/nio/AbstractNioByteChannel:.doWrite;io/netty/buffer/PooledUnsafeDirectByteBuf:.getBytes;sun/nio/ch/SocketChannelImpl:.write;pthread_self 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/DefaultChannelHandlerContext:.fireChannelReadComplete;io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;io/netty/channel/DefaultChannelHandlerContext:.fireChannelReadComplete;org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;io/netty/channel/DefaultChannelHandlerContext:.flush;io/netty/channel/ChannelDuplexHandler:.flush;io/netty/channel/DefaultChannelHandlerContext:.flush;io/netty/channel/ChannelOutboundHandlerAdapter:.flush;io/netty/channel/DefaultChannelHandlerContext:.flush;io/netty/channel/DefaultChannelPipeline$HeadHandler:.flush;io/netty/channel/nio/AbstractNioByteChannel:.doWrite;io/netty/buffer/PooledUnsafeDirectByteBuf:.getBytes;sun/nio/ch/SocketChannelImpl:.write;sun/nio/ch/FileDispatcherImpl:.write0;[libpthread-2.19.so <7f6b42bfc35d>];system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;inet_sendmsg;tcp_sendmsg;__tcp_push_pending_frames;tcp_write_xmit;tcp_transmit_skb;ip_queue_xmit;ip_local_out;ip_output;ip_finish_output;local_bh_enable;do_softirq;do_softirq_own_stack;__do_softirq;net_rx_action;process_backlog;__netif_receive_skb;__netif_receive_skb_core;ip_rcv;ip_rcv_finish;ip_local_deliver;ip_local_deliver_finish;tcp_v4_rcv;tcp_v4_do_rcv;tcp_rcv_established;tcp_data_queue;sock_def_readable;__wake_up_sync_key;__wake_up_common;ep_poll_callback;__wake_up_locked;__wake_up_common;default_wake_function;try_to_wake_up;_raw_spin_unlock_irqrestore 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;io/netty/channel/nio/NioEventLoop:.processSelectedKey;io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;io/netty/channel/DefaultChannelHandlerContext:.fireChannelReadComplete;io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;io/netty/channel/DefaultChannelHandlerContext:.fireChannelReadComplete;org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;io/netty/channel/DefaultChannelHandlerContext:.flush;io/netty/channel/ChannelDuplexHandler:.flush;io/netty/channel/DefaultChannelHandlerContext:.flush;io/netty/channel/ChannelOutboundHandlerAdapter:.flush;io/netty/channel/DefaultChannelHandlerContext:.flush;io/netty/channel/DefaultChannelPipeline$HeadHandler:.flush;io/netty/channel/nio/AbstractNioByteChannel:.doWrite;io/netty/buffer/PooledUnsafeDirectByteBuf:.getBytes;sun/nio/ch/SocketChannelImpl:.write;sun/nio/ch/FileDispatcherImpl:.write0;[libpthread-2.19.so <7f6b42bfc35d>];system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;inet_sendmsg;tcp_sendmsg;__tcp_push_pending_frames;tcp_write_xmit;tcp_transmit_skb;tcp_v4_send_check;__tcp_v4_send_check 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;io/netty/channel/nio/NioEventLoop:.run;io/netty/channel/nio/NioEventLoop:.select;sun/nio/ch/SelectorImpl:.lockAndDoSelect;sun/nio/ch/EPollSelectorImpl:.doSelect;sun/nio/ch/EPollArrayWrapper:.poll;sun/nio/ch/EPollArrayWrapper:.epollWait;__libc_enable_asynccancel 1
perf;__libc_start_main;[perf <407a40>];[perf <4081a5>];[perf <415ad5>];__GI___ioctl;system_call_fastpath;sys_ioctl;do_vfs_ioctl;perf_ioctl;perf_event_for_each_child;perf_event_enable;cpu_function_call;smp_call_function_single;remote_function;__perf_event_enable;group_sched_in;x86_pmu_commit_txn;perf_pmu_enable;x86_pmu_enable;intel_pmu_enable_all;native_write_msr_safe 4
perf;__libc_start_main;[perf <407a40>];[perf <4081a5>];[perf <415c96>];page_fault 1
swapper;x86_64_start_kernel;x86_64_start_reservations;start_kernel;rest_init;cpu_startup_entry;rcu_idle_enter 1

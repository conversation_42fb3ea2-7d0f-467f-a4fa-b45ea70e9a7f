java;[perf-10939.map]_[j] 23
java;[perf-10939.map]_[j];OptoRuntime::multianewarray2_C;ObjArrayKlass::multi_allocate;TypeArrayKlass::multi_allocate;TypeArrayKlass::allocate_common;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;do_numa_page;migrate_misplaced_page;migrate_pages;try_to_unmap;try_to_unmap_anon;try_to_unmap_one;ptep_clear_flush;flush_tlb_page;native_flush_tlb_others;smp_call_function_many;xen_smp_send_call_function_ipi;__xen_send_IPI_mask;cpumask_next_and;find_next_bit 1
java;[perf-10939.map]_[j];OptoRuntime::multianewarray2_C;ObjArrayKlass::multi_allocate;TypeArrayKlass::multi_allocate;TypeArrayKlass::allocate_common;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;do_numa_page;migrate_misplaced_page;migrate_pages;try_to_unmap;try_to_unmap_anon;try_to_unmap_one;ptep_clear_flush;flush_tlb_page;native_flush_tlb_others;smp_call_function_many;xen_smp_send_call_function_ipi;__xen_send_IPI_mask;xen_send_IPI_one;xen_hypercall_event_channel_op 5
java;[perf-10939.map]_[j];page_fault;do_page_fault;__do_page_fault;handle_mm_fault;do_numa_page;migrate_misplaced_page;migrate_pages;try_to_unmap;try_to_unmap_anon;try_to_unmap_one;ptep_clear_flush;flush_tlb_page;native_flush_tlb_others;smp_call_function_many 1
java;[perf-10939.map]_[j];page_fault;do_page_fault;__do_page_fault;handle_mm_fault;do_numa_page;migrate_misplaced_page;migrate_pages;try_to_unmap;try_to_unmap_anon;try_to_unmap_one;ptep_clear_flush;flush_tlb_page;native_flush_tlb_others;smp_call_function_many;_raw_spin_unlock_irqrestore 1
java;[perf-10939.map]_[j];page_fault;do_page_fault;__do_page_fault;handle_mm_fault;do_numa_page;migrate_misplaced_page;migrate_pages;try_to_unmap;try_to_unmap_anon;try_to_unmap_one;ptep_clear_flush;flush_tlb_page;native_flush_tlb_others;smp_call_function_many;xen_smp_send_call_function_ipi;__xen_send_IPI_mask;xen_send_IPI_one;notify_remote_via_irq 1
java;[perf-10939.map]_[j];page_fault;do_page_fault;__do_page_fault;handle_mm_fault;do_numa_page;migrate_misplaced_page;migrate_pages;try_to_unmap;try_to_unmap_anon;try_to_unmap_one;ptep_clear_flush;flush_tlb_page;native_flush_tlb_others;smp_call_function_many;xen_smp_send_call_function_ipi;__xen_send_IPI_mask;xen_send_IPI_one;notify_remote_via_irq;evtchn_from_irq;irq_get_irq_data;irq_to_desc;radix_tree_lookup_element 1
java;[perf-10939.map]_[j];page_fault;do_page_fault;__do_page_fault;handle_mm_fault;do_numa_page;migrate_misplaced_page;migrate_pages;try_to_unmap;try_to_unmap_anon;try_to_unmap_one;ptep_clear_flush;flush_tlb_page;native_flush_tlb_others;smp_call_function_many;xen_smp_send_call_function_ipi;__xen_send_IPI_mask;xen_send_IPI_one;xen_hypercall_event_channel_op 23
java;[unknown];[perf-10939.map]_[j] 2
java;[unknown];[perf-10939.map]_[j];[perf-10939.map]_[j] 1
java;[unknown];[perf-10939.map]_[j];[perf-10939.map]_[j];page_fault;do_page_fault;__do_page_fault;handle_mm_fault;do_numa_page;migrate_misplaced_page;migrate_pages;try_to_unmap;try_to_unmap_anon;try_to_unmap_one;ptep_clear_flush;flush_tlb_page;native_flush_tlb_others;smp_call_function_many;xen_smp_send_call_function_ipi;__xen_send_IPI_mask;xen_send_IPI_one;xen_hypercall_event_channel_op 9
java;[unknown];[perf-10939.map]_[j];page_fault;do_page_fault;__do_page_fault 2
java;[unknown];[perf-10939.map]_[j];page_fault;do_page_fault;__do_page_fault;handle_mm_fault;do_numa_page;migrate_misplaced_page;migrate_pages;try_to_unmap;try_to_unmap_anon;try_to_unmap_one;ptep_clear_flush;flush_tlb_page;native_flush_tlb_others;smp_call_function_many;xen_smp_send_call_function_ipi;__xen_send_IPI_mask;xen_send_IPI_one;xen_hypercall_event_channel_op 53
java;[unknown];[perf-10939.map]_[j];page_fault;do_page_fault;__do_page_fault;handle_mm_fault;do_numa_page;migrate_misplaced_page;migrate_pages;try_to_unmap;try_to_unmap_anon;try_to_unmap_one;ptep_clear_flush;flush_tlb_page;native_flush_tlb_others;smp_call_function_many;xen_smp_send_call_function_ipi;find_next_bit 1
java;[unknown];[perf-10939.map]_[j];retint_signal;do_notify_resume;task_work_run;task_numa_work;change_prot_numa;change_protection;change_protection_range 1
swapper;start_secondary;cpu_startup_entry;arch_cpu_idle;default_idle;native_safe_halt 72
swapper;x86_64_start_kernel;x86_64_start_reservations;start_kernel;rest_init;cpu_startup_entry;arch_cpu_idle;default_idle;native_safe_halt 2

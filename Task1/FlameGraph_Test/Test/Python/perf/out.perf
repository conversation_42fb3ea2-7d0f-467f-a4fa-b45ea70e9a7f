python3  651223 1240705.829872:          1 cpu_core/cycles/P: 
	ffffffff8e3bd054 perf_ctx_enable+0x24 ([kernel.kallsyms])
	ffffffff8e3c5507 __perf_event_task_sched_in+0x157 ([kernel.kallsyms])
	ffffffff8e15e5bf finish_task_switch.isra.0+0x1ff ([kernel.kallsyms])
	ffffffff8f2a47f1 __schedule+0x281 ([kernel.kallsyms])
	ffffffff8f2a4c59 schedule+0x29 ([kernel.kallsyms])
	ffffffff8f2abd6c do_nanosleep+0x5c ([kernel.kallsyms])
	ffffffff8e2176d6 hrtimer_nanosleep+0xb6 ([kernel.kallsyms])
	ffffffff8e2233de common_nsleep_timens+0x4e ([kernel.kallsyms])
	ffffffff8e225540 __x64_sys_clock_nanosleep+0xf0 ([kernel.kallsyms])
	ffffffff8e00a9ed x64_sys_call+0xc0d ([kernel.kallsyms])
	ffffffff8f2957be do_syscall_64+0x7e ([kernel.kallsyms])
	ffffffff8f40012b entry_SYSCALL_64_after_hwframe+0x76 ([kernel.kallsyms])
	    7436e38eca7a __GI___clock_nanosleep+0x5a (inlined)
	          645444 pysleep+0x84 (inlined)
	          645444 time_sleep+0x84 (/home/<USER>/miniconda3/envs/DL/bin/python3.11)
	          526cfa cfunction_vectorcall_O+0x5a (/home/<USER>/miniconda3/envs/DL/bin/python3.11)
	          51ea30 _PyObject_VectorcallTstate+0x30 (inlined)
	          51ea30 PyObject_Vectorcall+0x30 (/home/<USER>/miniconda3/envs/DL/bin/python3.11)
	          511a75 _PyEval_EvalFrameDefault+0x6a5 (/home/<USER>/miniconda3/envs/DL/bin/python3.11)
	          5cc3a9 _PyEval_EvalFrame+0x159 (inlined)
	          5cc3a9 _PyEval_Vector+0x159 (/home/<USER>/miniconda3/envs/DL/bin/python3.11)
	          5cba7e PyEval_EvalCode+0x9e (/home/<USER>/miniconda3/envs/DL/bin/python3.11)
	          5ecba6 run_eval_code_obj+0x46 (/home/<USER>/miniconda3/envs/DL/bin/python3.11)
	          5e873f run_mod+0x5f (/home/<USER>/miniconda3/envs/DL/bin/python3.11)
	          5fd5f1 pyrun_file+0x81 (/home/<USER>/miniconda3/envs/DL/bin/python3.11)
	          5fc9be _PyRun_SimpleFileObject+0x19e (/home/<USER>/miniconda3/envs/DL/bin/python3.11)
	          5fc6e2 _PyRun_AnyFileObject+0x42 (/home/<USER>/miniconda3/envs/DL/bin/python3.11)
	          5f73fd pymain_run_file_obj+0x2ed (inlined)
	          5f73fd pymain_run_file+0x2ed (inlined)
	          5f73fd pymain_run_python+0x2ed (inlined)
	          5f73fd Py_RunMain+0x2ed (/home/<USER>/miniconda3/envs/DL/bin/python3.11)
	          5bc148 Py_BytesMain+0x38 (/home/<USER>/miniconda3/envs/DL/bin/python3.11)
	    7436e382a1c9 __libc_start_call_main+0x79 (/usr/lib/x86_64-linux-gnu/libc.so.6)
	    7436e382a28a __libc_start_main_impl+0x8a (inlined)
	          5bbf92 _start+0x28 (/home/<USER>/miniconda3/envs/DL/bin/python3.11)

python3  651223 1240705.829892:          1 cpu_core/cycles/P: 
	ffffffff8e3bd054 perf_ctx_enable+0x24 ([kernel.kallsyms])
	ffffffff8e3c5507 __perf_event_task_sched_in+0x157 ([kernel.kallsyms])
	ffffffff8e15e5bf finish_task_switch.isra.0+0x1ff ([kernel.kallsyms])
	ffffffff8f2a47f1 __schedule+0x281 ([kernel.kallsyms])
	ffffffff8f2a4c59 schedule+0x29 ([kernel.kallsyms])
	ffffffff8f2abd6c do_nanosleep+0x5c ([kernel.kallsyms])
	ffffffff8e2176d6 hrtimer_nanosleep+0xb6 ([kernel.kallsyms])
	ffffffff8e2233de common_nsleep_timens+0x4e ([kernel.kallsyms])
	ffffffff8e225540 __x64_sys_clock_nanosleep+0xf0 ([kernel.kallsyms])
	ffffffff8e00a9ed x64_sys_call+0xc0d ([kernel.kallsyms])
	ffffffff8f2957be do_syscall_64+0x7e ([kernel.kallsyms])
	ffffffff8f40012b entry_SYSCALL_64_after_hwframe+0x76 ([kernel.kallsyms])
	    7436e38eca7a __GI___clock_nanosleep+0x5a (inlined)
	          645444 pysleep+0x84 (inlined)
	          645444 time_sleep+0x84 (/home/<USER>/miniconda3/envs/DL/bin/python3.11)
	          526cfa cfunction_vectorcall_O+0x5a (/home/<USER>/miniconda3/envs/DL/bin/python3.11)
	          51ea30 _PyObject_VectorcallTstate+0x30 (inlined)
	          51ea30 PyObject_Vectorcall+0x30 (/home/<USER>/miniconda3/envs/DL/bin/python3.11)
	          511a75 _PyEval_EvalFrameDefault+0x6a5 (/home/<USER>/miniconda3/envs/DL/bin/python3.11)
	          5cc3a9 _PyEval_EvalFrame+0x159 (inlined)
	          5cc3a9 _PyEval_Vector+0x159 (/home/<USER>/miniconda3/envs/DL/bin/python3.11)
	          5cba7e PyEval_EvalCode+0x9e (/home/<USER>/miniconda3/envs/DL/bin/python3.11)
	          5ecba6 run_eval_code_obj+0x46 (/home/<USER>/miniconda3/envs/DL/bin/python3.11)
	          5e873f run_mod+0x5f (/home/<USER>/miniconda3/envs/DL/bin/python3.11)
	          5fd5f1 pyrun_file+0x81 (/home/<USER>/miniconda3/envs/DL/bin/python3.11)
	          5fc9be _PyRun_SimpleFileObject+0x19e (/home/<USER>/miniconda3/envs/DL/bin/python3.11)
	          5fc6e2 _PyRun_AnyFileObject+0x42 (/home/<USER>/miniconda3/envs/DL/bin/python3.11)
	          5f73fd pymain_run_file_obj+0x2ed (inlined)
	          5f73fd pymain_run_file+0x2ed (inlined)
	          5f73fd pymain_run_python+0x2ed (inlined)
	          5f73fd Py_RunMain+0x2ed (/home/<USER>/miniconda3/envs/DL/bin/python3.11)
	          5bc148 Py_BytesMain+0x38 (/home/<USER>/miniconda3/envs/DL/bin/python3.11)
	    7436e382a1c9 __libc_start_call_main+0x79 (/usr/lib/x86_64-linux-gnu/libc.so.6)
	    7436e382a28a __libc_start_main_impl+0x8a (inlined)
	          5bbf92 _start+0x28 (/home/<USER>/miniconda3/envs/DL/bin/python3.11)

python3  651223 1240705.829895:        272 cpu_core/cycles/P: 
	ffffffff8e3bd054 perf_ctx_enable+0x24 ([kernel.kallsyms])
	ffffffff8e3c5507 __perf_event_task_sched_in+0x157 ([kernel.kallsyms])
	ffffffff8e15e5bf finish_task_switch.isra.0+0x1ff ([kernel.kallsyms])
	ffffffff8f2a47f1 __schedule+0x281 ([kernel.kallsyms])
	ffffffff8f2a4c59 schedule+0x29 ([kernel.kallsyms])
	ffffffff8f2abd6c do_nanosleep+0x5c ([kernel.kallsyms])
	ffffffff8e2176d6 hrtimer_nanosleep+0xb6 ([kernel.kallsyms])
	ffffffff8e2233de common_nsleep_timens+0x4e ([kernel.kallsyms])
	ffffffff8e225540 __x64_sys_clock_nanosleep+0xf0 ([kernel.kallsyms])
	ffffffff8e00a9ed x64_sys_call+0xc0d ([kernel.kallsyms])
	ffffffff8f2957be do_syscall_64+0x7e ([kernel.kallsyms])
	ffffffff8f40012b entry_SYSCALL_64_after_hwframe+0x76 ([kernel.kallsyms])
	    7436e38eca7a __GI___clock_nanosleep+0x5a (inlined)
	          645444 pysleep+0x84 (inlined)
	          645444 time_sleep+0x84 (/home/<USER>/miniconda3/envs/DL/bin/python3.11)
	          526cfa cfunction_vectorcall_O+0x5a (/home/<USER>/miniconda3/envs/DL/bin/python3.11)
	          51ea30 _PyObject_VectorcallTstate+0x30 (inlined)
	          51ea30 PyObject_Vectorcall+0x30 (/home/<USER>/miniconda3/envs/DL/bin/python3.11)
	          511a75 _PyEval_EvalFrameDefault+0x6a5 (/home/<USER>/miniconda3/envs/DL/bin/python3.11)
	          5cc3a9 _PyEval_EvalFrame+0x159 (inlined)
	          5cc3a9 _PyEval_Vector+0x159 (/home/<USER>/miniconda3/envs/DL/bin/python3.11)
	          5cba7e PyEval_EvalCode+0x9e (/home/<USER>/miniconda3/envs/DL/bin/python3.11)
	          5ecba6 run_eval_code_obj+0x46 (/home/<USER>/miniconda3/envs/DL/bin/python3.11)
	          5e873f run_mod+0x5f (/home/<USER>/miniconda3/envs/DL/bin/python3.11)
	          5fd5f1 pyrun_file+0x81 (/home/<USER>/miniconda3/envs/DL/bin/python3.11)
	          5fc9be _PyRun_SimpleFileObject+0x19e (/home/<USER>/miniconda3/envs/DL/bin/python3.11)
	          5fc6e2 _PyRun_AnyFileObject+0x42 (/home/<USER>/miniconda3/envs/DL/bin/python3.11)
	          5f73fd pymain_run_file_obj+0x2ed (inlined)
	          5f73fd pymain_run_file+0x2ed (inlined)
	          5f73fd pymain_run_python+0x2ed (inlined)
	          5f73fd Py_RunMain+0x2ed (/home/<USER>/miniconda3/envs/DL/bin/python3.11)
	          5bc148 Py_BytesMain+0x38 (/home/<USER>/miniconda3/envs/DL/bin/python3.11)
	    7436e382a1c9 __libc_start_call_main+0x79 (/usr/lib/x86_64-linux-gnu/libc.so.6)
	    7436e382a28a __libc_start_main_impl+0x8a (inlined)
	          5bbf92 _start+0x28 (/home/<USER>/miniconda3/envs/DL/bin/python3.11)

python3  651223 1240705.829897:     159679 cpu_core/cycles/P: 
	ffffffff8e3bd054 perf_ctx_enable+0x24 ([kernel.kallsyms])
	ffffffff8e3c5507 __perf_event_task_sched_in+0x157 ([kernel.kallsyms])
	ffffffff8e15e5bf finish_task_switch.isra.0+0x1ff ([kernel.kallsyms])
	ffffffff8f2a47f1 __schedule+0x281 ([kernel.kallsyms])
	ffffffff8f2a4c59 schedule+0x29 ([kernel.kallsyms])
	ffffffff8f2abd6c do_nanosleep+0x5c ([kernel.kallsyms])
	ffffffff8e2176d6 hrtimer_nanosleep+0xb6 ([kernel.kallsyms])
	ffffffff8e2233de common_nsleep_timens+0x4e ([kernel.kallsyms])
	ffffffff8e225540 __x64_sys_clock_nanosleep+0xf0 ([kernel.kallsyms])
	ffffffff8e00a9ed x64_sys_call+0xc0d ([kernel.kallsyms])
	ffffffff8f2957be do_syscall_64+0x7e ([kernel.kallsyms])
	ffffffff8f40012b entry_SYSCALL_64_after_hwframe+0x76 ([kernel.kallsyms])
	    7436e38eca7a __GI___clock_nanosleep+0x5a (inlined)
	          645444 pysleep+0x84 (inlined)
	          645444 time_sleep+0x84 (/home/<USER>/miniconda3/envs/DL/bin/python3.11)
	          526cfa cfunction_vectorcall_O+0x5a (/home/<USER>/miniconda3/envs/DL/bin/python3.11)
	          51ea30 _PyObject_VectorcallTstate+0x30 (inlined)
	          51ea30 PyObject_Vectorcall+0x30 (/home/<USER>/miniconda3/envs/DL/bin/python3.11)
	          511a75 _PyEval_EvalFrameDefault+0x6a5 (/home/<USER>/miniconda3/envs/DL/bin/python3.11)
	          5cc3a9 _PyEval_EvalFrame+0x159 (inlined)
	          5cc3a9 _PyEval_Vector+0x159 (/home/<USER>/miniconda3/envs/DL/bin/python3.11)
	          5cba7e PyEval_EvalCode+0x9e (/home/<USER>/miniconda3/envs/DL/bin/python3.11)
	          5ecba6 run_eval_code_obj+0x46 (/home/<USER>/miniconda3/envs/DL/bin/python3.11)
	          5e873f run_mod+0x5f (/home/<USER>/miniconda3/envs/DL/bin/python3.11)
	          5fd5f1 pyrun_file+0x81 (/home/<USER>/miniconda3/envs/DL/bin/python3.11)
	          5fc9be _PyRun_SimpleFileObject+0x19e (/home/<USER>/miniconda3/envs/DL/bin/python3.11)
	          5fc6e2 _PyRun_AnyFileObject+0x42 (/home/<USER>/miniconda3/envs/DL/bin/python3.11)
	          5f73fd pymain_run_file_obj+0x2ed (inlined)
	          5f73fd pymain_run_file+0x2ed (inlined)
	          5f73fd pymain_run_python+0x2ed (inlined)
	          5f73fd Py_RunMain+0x2ed (/home/<USER>/miniconda3/envs/DL/bin/python3.11)
	          5bc148 Py_BytesMain+0x38 (/home/<USER>/miniconda3/envs/DL/bin/python3.11)
	    7436e382a1c9 __libc_start_call_main+0x79 (/usr/lib/x86_64-linux-gnu/libc.so.6)
	    7436e382a28a __libc_start_main_impl+0x8a (inlined)
	          5bbf92 _start+0x28 (/home/<USER>/miniconda3/envs/DL/bin/python3.11)

python3  651223 1240706.029985:  114693639 cpu_core/cycles/P: 
	          4efb80 _Py_dict_lookup+0x0 (/home/<USER>/miniconda3/envs/DL/bin/python3.11)
	    7436e3bbaabf [unknown] (//anon)


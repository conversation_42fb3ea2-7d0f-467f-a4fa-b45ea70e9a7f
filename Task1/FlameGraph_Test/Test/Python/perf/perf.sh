#!/bin/bash

# === 配置 ===
TARGET_CMD="python3 my_test.py"
DURATION=5
FREQ=99
PERF_DIR="./perf"
PERF_OUTPUT="$PERF_DIR/perf.data"
PERF_TXT="$PERF_DIR/out.perf"
COLLAPSED_TXT="$PERF_DIR/perf_collapsed.txt"
FLAMEGRAPH_DIR="../../FlameGraph"

# === 确保perf调试符号环境 ===
export DEBUGINFOD_URLS="https://debuginfod.elfutils.org/"

mkdir -p $PERF_DIR

# === 启动目标程序（后台） ===
$TARGET_CMD &
TARGET_PID=$!

echo "👉 目标程序 PID = $TARGET_PID"
sleep 1

# === 使用 perf 采样该进程 ===
echo "🎯 使用 perf 采样 PID $TARGET_PID，持续 $DURATION 秒..."
sudo perf record -F $FREQ -p $TARGET_PID -g --call-graph fp --output $PERF_OUTPUT -- sleep $DURATION

# === 等待目标程序退出 ===
# wait $TARGET_PID

# === 生成 perf script 文本堆栈 ===
echo "📄 转换 perf.data → perf script"
sudo perf script > $PERF_TXT

# === 使用 FlameGraph 工具生成 collapsed.txt ===
echo "📊 折叠调用栈 collapsed..."
$FLAMEGRAPH_DIR/stackcollapse-perf.pl $PERF_TXT > $COLLAPSED_TXT

echo "✅ 完成！已生成 perf_collapsed.txt（调用链数据）"


my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c 13735166
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci 1424029
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci 1398062
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci 4125480
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibon<PERSON>ci;fibon<PERSON><PERSON>;fibon<PERSON><PERSON>;fibonacci 4180344
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci 11012849
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci 10987384
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci 26427837
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci 34452872
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci 36007594
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci 81810398
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci 150283676
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;asm_sysvec_apic_timer_interrupt;sysvec_apic_timer_interrupt;__sysvec_apic_timer_interrupt;hrtimer_interrupt;__hrtimer_run_queues;tick_nohz_handler;update_process_times;sched_tick;task_tick_fair;__update_load_avg_se 1366130
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci 383730570
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci 653062737
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci 1409605422
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;asm_sysvec_apic_timer_interrupt 1368309
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;asm_sysvec_apic_timer_interrupt;sysvec_apic_timer_interrupt;__sysvec_apic_timer_interrupt;hrtimer_interrupt;__hrtimer_run_queues;tick_nohz_handler;update_process_times;sched_tick;perf_event_task_tick;perf_adjust_freq_unthr_context;perf_adjust_freq_unthr_events 1366631
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;asm_sysvec_apic_timer_interrupt;sysvec_apic_timer_interrupt;__sysvec_apic_timer_interrupt;hrtimer_interrupt;__hrtimer_run_queues;tick_nohz_handler;update_process_times;sched_tick;task_tick_fair;update_cfs_group 1365899
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;asm_sysvec_apic_timer_interrupt;sysvec_apic_timer_interrupt;__sysvec_apic_timer_interrupt;hrtimer_interrupt;__hrtimer_run_queues;tick_nohz_handler;update_process_times;sched_tick;task_tick_fair;update_curr;__cgroup_account_cputime 1366483
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci **********
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;asm_sysvec_apic_timer_interrupt;sysvec_apic_timer_interrupt;__sysvec_apic_timer_interrupt;hrtimer_interrupt;__hrtimer_run_queues;tick_nohz_handler;tick_do_update_jiffies64;update_wall_time;timekeeping_advance 1407222
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;asm_sysvec_apic_timer_interrupt;sysvec_apic_timer_interrupt;__sysvec_apic_timer_interrupt;hrtimer_interrupt;__hrtimer_run_queues;tick_nohz_handler;update_process_times;sched_tick;sched_balance_trigger;_find_next_and_bit 1369321
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;asm_sysvec_apic_timer_interrupt;sysvec_apic_timer_interrupt;__sysvec_apic_timer_interrupt;hrtimer_interrupt;ktime_get_update_offsets_now 1366406
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci 4704871636
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;__irqentry_text_end 1364120
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;asm_sysvec_apic_timer_interrupt;sysvec_apic_timer_interrupt;__sysvec_apic_timer_interrupt;hrtimer_interrupt;__hrtimer_run_queues;tick_nohz_handler;tick_do_update_jiffies64;update_wall_time;timekeeping_advance 1373526
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;asm_sysvec_apic_timer_interrupt;sysvec_apic_timer_interrupt;__sysvec_apic_timer_interrupt;hrtimer_interrupt;__hrtimer_run_queues;tick_nohz_handler;tick_do_update_jiffies64;update_wall_time;timekeeping_advance;update_fast_timekeeper 1366855
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;asm_sysvec_apic_timer_interrupt;sysvec_apic_timer_interrupt;__sysvec_apic_timer_interrupt;hrtimer_interrupt;__hrtimer_run_queues;tick_nohz_handler;update_process_times;sched_tick;perf_event_task_tick;perf_adjust_freq_unthr_context;x86_pmu_enable;intel_pmu_enable_all;native_write_msr 1365514
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;asm_sysvec_apic_timer_interrupt;sysvec_apic_timer_interrupt;__sysvec_apic_timer_interrupt;hrtimer_interrupt;__hrtimer_run_queues;tick_nohz_handler;update_process_times;sched_tick;sched_balance_trigger;nohz_balancer_kick 1389550
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;asm_sysvec_apic_timer_interrupt;sysvec_apic_timer_interrupt;__sysvec_apic_timer_interrupt;hrtimer_interrupt;__hrtimer_run_queues;tick_nohz_handler;update_process_times;sched_tick;sched_balance_trigger;nohz_balancer_kick;sched_use_asym_prio;idle_cpu 4110617
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;asm_sysvec_apic_timer_interrupt;sysvec_apic_timer_interrupt;__sysvec_apic_timer_interrupt;hrtimer_interrupt;__hrtimer_run_queues;tick_nohz_handler;update_process_times;sched_tick;task_tick_fair;update_load_avg;sched_clock 1371204
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;asm_sysvec_apic_timer_interrupt;sysvec_apic_timer_interrupt;__sysvec_apic_timer_interrupt;hrtimer_interrupt;_raw_spin_lock_irqsave;__raw_spin_lock_irqsave 1377645
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;asm_sysvec_apic_timer_interrupt;sysvec_apic_timer_interrupt;__sysvec_apic_timer_interrupt;hrtimer_interrupt;tick_program_event;clockevents_program_event;lapic_next_deadline 1363581
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;asm_sysvec_apic_timer_interrupt;sysvec_apic_timer_interrupt;irq_exit_rcu;__irq_exit_rcu;handle_softirqs;run_timer_softirq;tmigr_handle_remote 1381694
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci 7281977375
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;asm_sysvec_apic_timer_interrupt;sysvec_apic_timer_interrupt;__sysvec_apic_timer_interrupt 1365624
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;asm_sysvec_apic_timer_interrupt;sysvec_apic_timer_interrupt;__sysvec_apic_timer_interrupt;hrtimer_interrupt;__hrtimer_run_queues;tick_nohz_handler;tick_do_update_jiffies64;update_wall_time;timekeeping_advance;_raw_spin_lock_irqsave;__raw_spin_lock_irqsave 2738021
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;asm_sysvec_apic_timer_interrupt;sysvec_apic_timer_interrupt;__sysvec_apic_timer_interrupt;hrtimer_interrupt;__hrtimer_run_queues;tick_nohz_handler;update_process_times;sched_tick;_raw_spin_lock 1371159
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;asm_sysvec_apic_timer_interrupt;sysvec_apic_timer_interrupt;__sysvec_apic_timer_interrupt;hrtimer_interrupt;__hrtimer_run_queues;tick_nohz_handler;update_process_times;sched_tick;sched_balance_trigger;__rcu_read_unlock 1364120
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;asm_sysvec_apic_timer_interrupt;sysvec_apic_timer_interrupt;__sysvec_apic_timer_interrupt;hrtimer_interrupt;__hrtimer_run_queues;tick_nohz_handler;update_process_times;sched_tick;sched_balance_trigger;nohz_balancer_kick;_find_next_bit 1414620
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;asm_sysvec_apic_timer_interrupt;sysvec_apic_timer_interrupt;__sysvec_apic_timer_interrupt;hrtimer_interrupt;__hrtimer_run_queues;tick_nohz_handler;update_process_times;sched_tick;sched_balance_trigger;nohz_balancer_kick;arch_asym_cpu_priority 1417421
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;asm_sysvec_apic_timer_interrupt;sysvec_apic_timer_interrupt;__sysvec_apic_timer_interrupt;hrtimer_interrupt;__hrtimer_run_queues;tick_nohz_handler;update_process_times;sched_tick;task_tick_fair;update_load_avg;__update_load_avg_cfs_rq 1365130
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;asm_sysvec_apic_timer_interrupt;sysvec_apic_timer_interrupt;irq_exit_rcu;__irq_exit_rcu;handle_softirqs;run_timer_softirq;tmigr_handle_remote;tmigr_handle_remote_up;tmigr_handle_remote_cpu;timer_expire_remote;__run_timers;call_timer_fn;fq_flush_timeout;_raw_spin_lock_irqsave;__raw_spin_lock_irqsave 1363107
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;asm_sysvec_apic_timer_interrupt;sysvec_apic_timer_interrupt;irq_exit_rcu;__irq_exit_rcu;handle_softirqs;run_timer_softirq;tmigr_handle_remote;tmigr_handle_remote_up;tmigr_handle_remote_cpu;timer_expire_remote;__run_timers;tcp_write_timer 1403216
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci 9432872069
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;asm_sysvec_apic_timer_interrupt;sysvec_apic_timer_interrupt;__sysvec_apic_timer_interrupt;hrtimer_interrupt;__hrtimer_run_queues;tick_nohz_handler;tick_do_update_jiffies64;update_wall_time;timekeeping_advance 1372032
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;asm_sysvec_apic_timer_interrupt;sysvec_apic_timer_interrupt;__sysvec_apic_timer_interrupt;hrtimer_interrupt;__hrtimer_run_queues;tick_nohz_handler;update_process_times;sched_tick;sched_balance_trigger;nohz_balancer_kick;idle_cpu 1362378
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;asm_sysvec_apic_timer_interrupt;sysvec_apic_timer_interrupt;__sysvec_apic_timer_interrupt;hrtimer_interrupt;__hrtimer_run_queues;tick_nohz_handler;update_process_times;sched_tick;sched_balance_trigger;nohz_balancer_kick;kick_ilb;smp_call_function_single_async;generic_exec_single;llist_add_batch 1356729
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;asm_sysvec_apic_timer_interrupt;sysvec_apic_timer_interrupt;__sysvec_apic_timer_interrupt;hrtimer_interrupt;__hrtimer_run_queues;tick_nohz_handler;update_process_times;sched_tick;sched_balance_trigger;nohz_balancer_kick;sched_use_asym_prio;idle_cpu 1403183
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;asm_sysvec_apic_timer_interrupt;sysvec_apic_timer_interrupt;__sysvec_apic_timer_interrupt;hrtimer_interrupt;__hrtimer_run_queues;tick_nohz_handler;update_process_times;sched_tick;task_tick_fair 1383393
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;asm_sysvec_apic_timer_interrupt;sysvec_apic_timer_interrupt;__sysvec_apic_timer_interrupt;hrtimer_interrupt;__hrtimer_run_queues;tick_nohz_handler;update_process_times;sched_tick;task_tick_fair;update_cfs_group;reweight_entity;avg_vruntime 1369599
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;asm_sysvec_apic_timer_interrupt;sysvec_apic_timer_interrupt;__sysvec_apic_timer_interrupt;hrtimer_interrupt;__hrtimer_run_queues;tick_nohz_handler;update_process_times;sched_tick;task_tick_fair;update_curr;update_curr_se 1370497
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;asm_sysvec_apic_timer_interrupt;sysvec_apic_timer_interrupt;__sysvec_apic_timer_interrupt;hrtimer_interrupt;__hrtimer_run_queues;tick_nohz_handler;update_process_times;sched_tick;task_tick_fair;update_load_avg;sched_clock 1423209
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci 10124092064
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;asm_sysvec_apic_timer_interrupt 1352298
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;asm_sysvec_apic_timer_interrupt;sysvec_apic_timer_interrupt;__sysvec_apic_timer_interrupt;hrtimer_interrupt;__hrtimer_run_queues;tick_nohz_handler;update_process_times;sched_tick;perf_event_task_tick;perf_adjust_freq_unthr_context;perf_adjust_freq_unthr_events 1341108
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;asm_sysvec_apic_timer_interrupt;sysvec_apic_timer_interrupt;__sysvec_apic_timer_interrupt;hrtimer_interrupt;__hrtimer_run_queues;tick_nohz_handler;update_process_times;sched_tick;sched_balance_trigger;_find_next_and_bit 1372949
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;asm_sysvec_apic_timer_interrupt;sysvec_apic_timer_interrupt;__sysvec_apic_timer_interrupt;hrtimer_interrupt;__hrtimer_run_queues;tick_nohz_handler;update_process_times;sched_tick;sched_balance_trigger;nohz_balancer_kick;kick_ilb;idle_cpu 1376560
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;asm_sysvec_apic_timer_interrupt;sysvec_apic_timer_interrupt;__sysvec_apic_timer_interrupt;hrtimer_interrupt;__hrtimer_run_queues;tick_nohz_handler;update_process_times;sched_tick;sched_balance_trigger;nohz_balancer_kick;kick_ilb;smp_call_function_single_async;generic_exec_single;__smp_call_single_queue;native_send_call_func_single_ipi;x2apic_send_IPI;native_write_msr 1364769
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;asm_sysvec_apic_timer_interrupt;sysvec_apic_timer_interrupt;__sysvec_apic_timer_interrupt;hrtimer_interrupt;__hrtimer_run_queues;tick_nohz_handler;update_process_times;sched_tick;sched_balance_trigger;nohz_balancer_kick;kick_ilb;smp_call_function_single_async;generic_exec_single;llist_add_batch 1364608
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;asm_sysvec_apic_timer_interrupt;sysvec_apic_timer_interrupt;__sysvec_apic_timer_interrupt;hrtimer_interrupt;__hrtimer_run_queues;tick_nohz_handler;update_process_times;sched_tick;sched_balance_trigger;nohz_balancer_kick;sched_use_asym_prio 2750449
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;asm_sysvec_apic_timer_interrupt;sysvec_apic_timer_interrupt;__sysvec_apic_timer_interrupt;hrtimer_interrupt;__hrtimer_run_queues;tick_nohz_handler;update_process_times;sched_tick;sched_balance_trigger;nohz_balancer_kick;sched_use_asym_prio;idle_cpu 2663359
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;asm_sysvec_apic_timer_interrupt;sysvec_apic_timer_interrupt;__sysvec_apic_timer_interrupt;hrtimer_interrupt;__hrtimer_run_queues;tick_nohz_handler;update_process_times;sched_tick;task_tick_fair;hrtimer_active 1382292
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;asm_sysvec_apic_timer_interrupt;sysvec_apic_timer_interrupt;__sysvec_apic_timer_interrupt;hrtimer_interrupt;__hrtimer_run_queues;tick_nohz_handler;update_process_times;sched_tick;task_tick_fair;update_cfs_group 1365165
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;asm_sysvec_apic_timer_interrupt;sysvec_apic_timer_interrupt;__sysvec_apic_timer_interrupt;hrtimer_interrupt;__hrtimer_run_queues;tick_nohz_handler;update_process_times;sched_tick;task_tick_fair;update_load_avg 1373376
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;asm_sysvec_apic_timer_interrupt;sysvec_apic_timer_interrupt;__sysvec_apic_timer_interrupt;hrtimer_interrupt;__hrtimer_run_queues;tick_nohz_handler;update_process_times;sched_tick;task_tick_fair;update_min_vruntime 1358521
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;asm_sysvec_apic_timer_interrupt;sysvec_apic_timer_interrupt;__sysvec_apic_timer_interrupt;hrtimer_interrupt;tick_program_event;clockevents_program_event;native_write_msr 1369537
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;asm_sysvec_apic_timer_interrupt;sysvec_apic_timer_interrupt;irqentry_exit;irqentry_exit_to_user_mode;schedule;__schedule;finish_task_switch.isra.0 39182
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci 8542287102
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;asm_sysvec_apic_timer_interrupt;sysvec_apic_timer_interrupt;__sysvec_apic_timer_interrupt;hrtimer_interrupt;__hrtimer_run_queues;tick_nohz_handler 1369136
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;asm_sysvec_apic_timer_interrupt;sysvec_apic_timer_interrupt;__sysvec_apic_timer_interrupt;hrtimer_interrupt;__hrtimer_run_queues;tick_nohz_handler;tick_do_update_jiffies64;update_wall_time;timekeeping_advance;timekeeping_update;update_vdso_data.constprop.0 1423075
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;asm_sysvec_apic_timer_interrupt;sysvec_apic_timer_interrupt;__sysvec_apic_timer_interrupt;hrtimer_interrupt;__hrtimer_run_queues;tick_nohz_handler;update_process_times;run_posix_cpu_timers 1370861
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;asm_sysvec_apic_timer_interrupt;sysvec_apic_timer_interrupt;__sysvec_apic_timer_interrupt;hrtimer_interrupt;__hrtimer_run_queues;tick_nohz_handler;update_process_times;sched_tick;sched_balance_trigger;nohz_balancer_kick 1369906
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;asm_sysvec_apic_timer_interrupt;sysvec_apic_timer_interrupt;__sysvec_apic_timer_interrupt;hrtimer_interrupt;__hrtimer_run_queues;tick_nohz_handler;update_process_times;sched_tick;sched_balance_trigger;nohz_balancer_kick;sched_use_asym_prio 1414853
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;asm_sysvec_apic_timer_interrupt;sysvec_apic_timer_interrupt;__sysvec_apic_timer_interrupt;hrtimer_interrupt;__hrtimer_run_queues;tick_nohz_handler;update_process_times;sched_tick;sched_balance_trigger;nohz_balancer_kick;sched_use_asym_prio;idle_cpu 4115213
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;asm_sysvec_apic_timer_interrupt;sysvec_apic_timer_interrupt;__sysvec_apic_timer_interrupt;hrtimer_interrupt;__hrtimer_run_queues;tick_nohz_handler;update_process_times;sched_tick;task_tick_fair;update_cfs_group;update_min_vruntime 1407939
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;asm_sysvec_apic_timer_interrupt;sysvec_apic_timer_interrupt;__sysvec_apic_timer_interrupt;hrtimer_interrupt;__hrtimer_run_queues;tick_nohz_handler;update_process_times;sched_tick;task_tick_fair;update_load_avg;__update_load_avg_cfs_rq 1365677
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;asm_sysvec_apic_timer_interrupt;sysvec_apic_timer_interrupt;__sysvec_apic_timer_interrupt;hrtimer_interrupt;__hrtimer_run_queues;tick_nohz_handler;update_process_times;sched_tick;task_tick_fair;update_load_avg;__update_load_avg_se 1367409
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;asm_sysvec_apic_timer_interrupt;sysvec_apic_timer_interrupt;irqentry_exit;irqentry_exit_to_user_mode 68349
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;asm_sysvec_apic_timer_interrupt;sysvec_apic_timer_interrupt;irqentry_exit;irqentry_exit_to_user_mode;schedule;__schedule;finish_task_switch.isra.0;__perf_event_task_sched_in;perf_ctx_enable 4086
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci 5604307036
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;asm_sysvec_apic_timer_interrupt;irqentry_enter 1380444
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;asm_sysvec_apic_timer_interrupt;sysvec_apic_timer_interrupt;__sysvec_apic_timer_interrupt;hrtimer_interrupt;__hrtimer_run_queues;tick_nohz_handler;_raw_spin_lock 1392787
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;asm_sysvec_apic_timer_interrupt;sysvec_apic_timer_interrupt;__sysvec_apic_timer_interrupt;hrtimer_interrupt;__hrtimer_run_queues;tick_nohz_handler;tick_do_update_jiffies64;update_wall_time;timekeeping_advance;update_fast_timekeeper 1369906
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;asm_sysvec_apic_timer_interrupt;sysvec_apic_timer_interrupt;__sysvec_apic_timer_interrupt;hrtimer_interrupt;__hrtimer_run_queues;tick_nohz_handler;update_process_times;sched_balance_trigger 1393334
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;asm_sysvec_apic_timer_interrupt;sysvec_apic_timer_interrupt;__sysvec_apic_timer_interrupt;hrtimer_interrupt;__hrtimer_run_queues;tick_nohz_handler;update_process_times;sched_tick;sched_balance_trigger;nohz_balancer_kick;_find_next_bit 2749341
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;asm_sysvec_apic_timer_interrupt;sysvec_apic_timer_interrupt;__sysvec_apic_timer_interrupt;hrtimer_interrupt;__hrtimer_run_queues;tick_nohz_handler;update_process_times;sched_tick;sched_balance_trigger;sched_use_asym_prio 1373963
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;asm_sysvec_apic_timer_interrupt;sysvec_apic_timer_interrupt;__sysvec_apic_timer_interrupt;hrtimer_interrupt;__hrtimer_run_queues;tick_nohz_handler;update_process_times;sched_tick;task_tick_fair;update_cfs_group 1364813
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;asm_sysvec_apic_timer_interrupt;sysvec_apic_timer_interrupt;__sysvec_apic_timer_interrupt;hrtimer_interrupt;__hrtimer_run_queues;tick_nohz_handler;update_process_times;sched_tick;task_tick_fair;update_load_avg;__update_load_avg_cfs_rq 1407400
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;asm_sysvec_apic_timer_interrupt;sysvec_apic_timer_interrupt;__sysvec_apic_timer_interrupt;hrtimer_interrupt;tick_program_event;clockevents_program_event;lapic_next_deadline 1417368
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;asm_sysvec_apic_timer_interrupt;sysvec_apic_timer_interrupt;irq_exit_rcu;__irq_exit_rcu;handle_softirqs;run_timer_softirq;__run_timers;call_timer_fn;delayed_work_timer_fn;__queue_work.part.0 1350365
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci 2697258176
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;asm_sysvec_apic_timer_interrupt;sysvec_apic_timer_interrupt;__sysvec_apic_timer_interrupt;hrtimer_interrupt;__hrtimer_run_queues;tick_nohz_handler;update_process_times;sched_tick;task_tick_fair;update_load_avg;__update_load_avg_se 1284279
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;asm_sysvec_apic_timer_interrupt;sysvec_apic_timer_interrupt;irq_exit_rcu;__irq_exit_rcu;handle_softirqs;run_timer_softirq;tmigr_handle_remote;tmigr_handle_remote_up;tmigr_handle_remote_cpu;timer_expire_remote;__run_timers;call_timer_fn;_raw_spin_unlock_irqrestore 1362368
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;asm_sysvec_apic_timer_interrupt;sysvec_apic_timer_interrupt;irq_exit_rcu;__irq_exit_rcu;handle_softirqs;run_timer_softirq;tmigr_handle_remote;tmigr_handle_remote_up;tmigr_handle_remote_cpu;timer_expire_remote;__run_timers;call_timer_fn;delayed_work_timer_fn;__queue_work.part.0;kick_pool;wake_up_process;try_to_wake_up;select_task_rq;kthread_is_per_cpu 1366179
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci 1011793347
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci 272262105
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;asm_sysvec_apic_timer_interrupt;sysvec_apic_timer_interrupt;irq_exit_rcu;handle_softirqs 1366351
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci 62620799
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci 8334783
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci 1389209
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;cpu_work;deep_function_a;deep_function_b;deep_function_c;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;fibonacci;sync_regs 1395421
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;main;std::chrono::duration<long, std::ratio<1l, 1l> > std::chrono::__duration_cast_impl<std::chrono::duration<long, std::ratio<1l, 1l> >, std::ratio<1l, 1000000000l>, long, true, false>::__cast<long, std::ratio<1l, 1000000000l> > 1381616
my_test;_start;__libc_start_main@@GLIBC_2.34;__libc_start_call_main;std::common_type<std::chrono::duration<long, std::ratio<1l, 1000000000l> >, std::chrono::duration<long, std::ratio<1l, 1000000000l> > >::type std::chrono::operator-<std::chrono::_V2::steady_clock, std::chrono::duration<long, std::ratio<1l, 1000000000l> >, std::chrono::duration<long, std::ratio<1l, 1000000000l> > > 1368860

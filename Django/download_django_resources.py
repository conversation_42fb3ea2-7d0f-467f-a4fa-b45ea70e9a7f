#!/usr/bin/env python3
"""
Django资源下载器
下载Django框架源代码、SDK二进制文件和符号表
"""

import os
import subprocess
import requests
import zipfile
import tarfile
import json
from pathlib import Path

class DjangoResourceDownloader:
    def __init__(self, base_dir="Django"):
        self.base_dir = Path(base_dir)
        self.source_dir = self.base_dir / "source"
        self.sdk_dir = self.base_dir / "sdk"
        self.symbols_dir = self.base_dir / "symbols"
        
        # 创建目录结构
        for dir_path in [self.source_dir, self.sdk_dir, self.symbols_dir]:
            dir_path.mkdir(parents=True, exist_ok=True)
    
    def download_file(self, url: str, filepath: Path, description: str = ""):
        """下载文件"""
        print(f"📥 下载 {description}: {url}")
        try:
            response = requests.get(url, stream=True)
            response.raise_for_status()
            
            with open(filepath, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            print(f"✅ 下载完成: {filepath}")
            return True
        except Exception as e:
            print(f"❌ 下载失败: {e}")
            return False
    
    def clone_django_source(self):
        """克隆Django源代码"""
        print("🔄 克隆Django源代码...")
        
        django_repo = "https://github.com/django/django.git"
        source_path = self.source_dir / "django"
        
        if source_path.exists():
            print(f"📁 源代码目录已存在: {source_path}")
            # 更新现有仓库
            try:
                subprocess.run(["git", "pull"], cwd=source_path, check=True)
                print("✅ 源代码更新完成")
            except subprocess.CalledProcessError as e:
                print(f"⚠️  更新失败: {e}")
        else:
            # 克隆新仓库
            try:
                subprocess.run([
                    "git", "clone", "--depth", "1", 
                    django_repo, str(source_path)
                ], check=True)
                print("✅ Django源代码克隆完成")
            except subprocess.CalledProcessError as e:
                print(f"❌ 克隆失败: {e}")
                return False
        
        return True
    
    def download_django_releases(self):
        """下载Django发布版本"""
        print("📦 下载Django发布版本...")
        
        # Django最新稳定版本
        releases = [
            {
                "version": "5.1.4",
                "url": "https://github.com/django/django/archive/refs/tags/5.1.4.tar.gz",
                "filename": "django-5.1.4.tar.gz"
            },
            {
                "version": "4.2.17",
                "url": "https://github.com/django/django/archive/refs/tags/4.2.17.tar.gz", 
                "filename": "django-4.2.17.tar.gz"
            }
        ]
        
        for release in releases:
            filepath = self.sdk_dir / release["filename"]
            if not filepath.exists():
                success = self.download_file(
                    release["url"], 
                    filepath, 
                    f"Django {release['version']}"
                )
                if success:
                    # 解压
                    self.extract_archive(filepath, self.sdk_dir / f"django-{release['version']}")
    
    def extract_archive(self, archive_path: Path, extract_to: Path):
        """解压归档文件"""
        print(f"📂 解压 {archive_path.name} 到 {extract_to}")
        
        try:
            if archive_path.suffix == '.zip':
                with zipfile.ZipFile(archive_path, 'r') as zip_ref:
                    zip_ref.extractall(extract_to.parent)
            elif archive_path.name.endswith('.tar.gz'):
                with tarfile.open(archive_path, 'r:gz') as tar_ref:
                    tar_ref.extractall(extract_to.parent)
            
            print(f"✅ 解压完成: {extract_to}")
        except Exception as e:
            print(f"❌ 解压失败: {e}")
    
    def download_python_debug_symbols(self):
        """下载Python调试符号"""
        print("🔍 下载Python调试符号...")
        
        # Python调试符号下载链接
        python_versions = ["3.11", "3.12", "3.13"]
        
        for version in python_versions:
            # 对于Linux系统，通常可以通过包管理器安装调试符号
            print(f"📋 Python {version} 调试符号信息:")
            print(f"   Ubuntu/Debian: sudo apt install python{version}-dbg")
            print(f"   CentOS/RHEL: sudo yum install python{version}-debuginfo")
            print(f"   Arch Linux: sudo pacman -S python-debug")
    
    def create_django_wheel(self):
        """创建Django wheel包"""
        print("🛠️  创建Django wheel包...")
        
        django_source = self.source_dir / "django"
        if not django_source.exists():
            print("❌ Django源代码不存在，请先克隆源代码")
            return
        
        try:
            # 安装构建依赖
            subprocess.run(["pip", "install", "build", "wheel"], check=True)
            
            # 构建wheel
            subprocess.run([
                "python", "-m", "build", "--wheel", "--outdir", str(self.sdk_dir)
            ], cwd=django_source, check=True)
            
            print("✅ Django wheel包构建完成")
        except subprocess.CalledProcessError as e:
            print(f"❌ 构建失败: {e}")
    
    def download_django_dependencies(self):
        """下载Django依赖包"""
        print("📚 下载Django依赖包...")
        
        dependencies = [
            "asgiref",
            "sqlparse", 
            "tzdata",
            "psycopg2-binary",  # PostgreSQL
            "mysqlclient",      # MySQL
            "pillow",           # 图像处理
            "redis",            # Redis缓存
            "celery"            # 任务队列
        ]
        
        deps_dir = self.sdk_dir / "dependencies"
        deps_dir.mkdir(exist_ok=True)
        
        for dep in dependencies:
            try:
                subprocess.run([
                    "pip", "download", "--dest", str(deps_dir), 
                    "--no-deps", dep
                ], check=True)
                print(f"✅ 下载依赖: {dep}")
            except subprocess.CalledProcessError as e:
                print(f"⚠️  下载依赖失败 {dep}: {e}")
    
    def generate_symbol_info(self):
        """生成符号表信息"""
        print("📊 生成符号表信息...")
        
        symbol_info = {
            "django_version": self.get_django_version(),
            "python_version": self.get_python_version(),
            "system_info": self.get_system_info(),
            "installed_packages": self.get_installed_packages(),
            "debug_symbols": {
                "django_source": str(self.source_dir / "django"),
                "python_symbols": "Use system package manager to install python-dbg",
                "c_extensions": "Compile with -g flag for debug symbols"
            }
        }
        
        symbol_file = self.symbols_dir / "symbol_info.json"
        with open(symbol_file, 'w', encoding='utf-8') as f:
            json.dump(symbol_info, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 符号表信息保存到: {symbol_file}")
    
    def get_django_version(self):
        """获取Django版本"""
        try:
            import django
            return django.get_version()
        except ImportError:
            return "Not installed"
    
    def get_python_version(self):
        """获取Python版本"""
        import sys
        return sys.version
    
    def get_system_info(self):
        """获取系统信息"""
        import platform
        return {
            "system": platform.system(),
            "release": platform.release(),
            "machine": platform.machine(),
            "processor": platform.processor()
        }
    
    def get_installed_packages(self):
        """获取已安装包列表"""
        try:
            result = subprocess.run(
                ["pip", "list", "--format=json"], 
                capture_output=True, text=True, check=True
            )
            return json.loads(result.stdout)
        except:
            return []
    
    def create_directory_structure(self):
        """创建目录结构说明"""
        structure = {
            "Django/": "Django框架资源根目录",
            "Django/source/": "源代码目录",
            "Django/source/django/": "Django主源代码",
            "Django/sdk/": "SDK和二进制文件",
            "Django/sdk/dependencies/": "依赖包",
            "Django/symbols/": "符号表和调试信息",
            "Django/symbols/symbol_info.json": "符号表信息文件"
        }
        
        readme_content = "# Django框架资源\n\n## 目录结构\n\n"
        for path, desc in structure.items():
            readme_content += f"- `{path}`: {desc}\n"
        
        readme_content += """
## 使用说明

### 源代码
- Django主源代码位于 `source/django/`
- 包含完整的Django框架源码

### SDK和二进制文件
- 发布版本的tar.gz包
- wheel包
- 依赖包

### 符号表和调试信息
- `symbol_info.json`: 包含版本和系统信息
- 调试符号安装说明

### 调试符号安装
```bash
# Ubuntu/Debian
sudo apt install python3-dbg

# CentOS/RHEL  
sudo yum install python3-debuginfo

# 编译时添加调试符号
export CFLAGS="-g -O0"
pip install --no-binary :all: django
```
"""
        
        readme_file = self.base_dir / "README.md"
        with open(readme_file, 'w', encoding='utf-8') as f:
            f.write(readme_content)
        
        print(f"✅ 目录结构说明保存到: {readme_file}")
    
    def download_all(self):
        """下载所有资源"""
        print("🚀 开始下载Django框架资源...")
        print("=" * 50)
        
        # 1. 克隆源代码
        self.clone_django_source()
        
        # 2. 下载发布版本
        self.download_django_releases()
        
        # 3. 下载依赖包
        self.download_django_dependencies()
        
        # 4. 创建wheel包
        self.create_django_wheel()
        
        # 5. 下载调试符号信息
        self.download_python_debug_symbols()
        
        # 6. 生成符号表信息
        self.generate_symbol_info()
        
        # 7. 创建目录结构说明
        self.create_directory_structure()
        
        print("\n" + "=" * 50)
        print("🎉 Django框架资源下载完成！")
        print(f"📁 资源位置: {self.base_dir.absolute()}")

def main():
    downloader = DjangoResourceDownloader()
    downloader.download_all()

if __name__ == "__main__":
    main()
